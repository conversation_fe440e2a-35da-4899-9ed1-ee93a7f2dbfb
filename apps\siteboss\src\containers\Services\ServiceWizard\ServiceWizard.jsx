import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Button } from '@mui/material';
import { SuccessB<PERSON>, Wizard } from '@siteboss-frontend/shared/components';

import Step1Name from './Steps/Step1Name';
import Step2Availability from './Steps/Step2Availability';
import Step3Increments from './Steps/Step3Increments';
import Step4Location from './Steps/Step4Location';
import Step5Manager from './Steps/Step5Manager';
import Step6Payment from './Steps/Step6Payment';
import Step7Cancellation from './Steps/Step7Cancellation';
import Step8Summary from './Steps/Step8Summary';

import * as stepList from './Steps/stepList';
import { useWizard } from './useWizard';

export const ServiceWizard = () => {
    const { t } = useOutletContext();
    const [error, setError] = useState(null);

    // Use a ref to track if we've already set an error to avoid infinite loops
    const errorSetRef = useRef(false);

    // Use the hook outside of try/catch to avoid React Hook issues
    const wizardData = useWizard();

    // Handle any errors from the hook
    useEffect(() => {
        if (!wizardData && !errorSetRef.current) {
            setError(new Error('Failed to initialize service wizard'));
            errorSetRef.current = true;
        }
    }, [wizardData]);

    if (!wizardData) {
        return (
            <Container>
                <Typography variant="h4" color="error" gutterBottom>
                    Error loading service wizard
                </Typography>
                <Typography variant="body1">
                    {error?.message || 'An unknown error occurred'}
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => window.location.href = '/services'}
                    sx={{ mt: 2 }}
                >
                    Return to Services
                </Button>
            </Container>
        );
    }

    const {
        handleErrors,
        handleSubmit,
        setActiveStep,
        loading,
        success,
        setSuccess,
        errorBars,
        serviceId,
        resetForm,
        completedSteps,
        serviceWizard
    } = wizardData;

    try {
        return (
            <Container>
                {success && <SuccessBar message={t('service:success')} onClose={() => setSuccess(false)} />}
                {errorBars && errorBars.filter(Boolean).map((ErrorBarComponent, i) =>
                    React.isValidElement(ErrorBarComponent)
                        ? React.cloneElement(ErrorBarComponent, { key: i })
                        : ErrorBarComponent && <ErrorBarComponent key={i} />
                )}
                <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '1rem' }}>
                    <Button
                        variant="outlined"
                        color="secondary"
                        onClick={resetForm}
                        sx={{ mr: 1 }}
                    >
                        Reset Form
                    </Button>
                </div>
                <Wizard
                    title={serviceId ? t('service:editService') : t('service:newService')}
                    onSubmit={handleSubmit}
                    onError={handleErrors}
                    onChangeStep={setActiveStep}
                    loading={loading}
                    externalVisitedSteps={completedSteps}
                    isEditing={!!serviceId}
                    slots={{
                        breadcrumbs: [
                            {title: t('dashboard:dashboard'), to: '/'},
                            {title: t('service:services'), to: '/services'},
                            {title: serviceId ? t('service:editService') : t('service:newService')}],
                        stepList,
                        steps: [
                            Step1Name,
                            Step2Availability,
                            Step3Increments,
                            Step4Location,
                            Step5Manager,
                            Step6Payment,
                            Step7Cancellation,
                            Step8Summary
                        ],
                    }}
                    slotProps={{
                        steps: {
                            data: serviceWizard?.serviceData || null,
                            loading: loading || false,
                            formData: serviceWizard?.formData || {},
                            serviceId: serviceId || null,
                        }
                    }}
                />
            </Container>
        );
    } catch (err) {
        return (
            <Container>
                <Typography variant="h4" color="error" gutterBottom>
                    Error rendering service wizard
                </Typography>
                <Typography variant="body1">
                    {err.message}
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => window.location.href = '/services'}
                    sx={{ mt: 2 }}
                >
                    Return to Services
                </Button>
            </Container>
        );
    }
};

export default ServiceWizard;
