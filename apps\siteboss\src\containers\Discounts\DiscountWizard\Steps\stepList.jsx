// Helper function to get data value from the API response
const getDataValue = ({ data, field, defaultValue = '' }) => {
    // If no data, return default
    if (!data) {
        return defaultValue;
    }

    // If field doesn't exist in data, return default
    if (data[field] === undefined) {
        return defaultValue;
    }

    // Special handling for params field
    if (field === 'params') {
        // Return empty object as default for params
        if (!data[field]) {
            return {};
        }

        // If params is a string, try to parse it
        if (typeof data[field] === 'string') {
            try {
                const parsedParams = JSON.parse(data[field]);
                return parsedParams;
            } catch (error) {
                console.error('Error parsing params:', error);
                return {};
            }
        }

        // If params is already an object, return it
        return data[field];
    }

    // For all other fields, return the value or default
    return data[field];
};

// The list of steps for the stepper
export const stepList = [
    {
        key: 0,
        slug: 'discount:wizard.name.title',
        label: 'Name & Description',
        short_description: 'discount:wizard.name.subtitle',
        description: 'discount:wizard.name.description',
    },
    {
        key: 1,
        slug: 'discount:wizard.auto.title',
        label: 'Application Method',
        short_description: 'discount:wizard.auto.subtitle',
        description: 'discount:wizard.auto.description',
    },
    {
        key: 2,
        slug: 'discount:wizard.maxUses.title',
        label: 'Usage Limits',
        short_description: 'discount:wizard.maxUses.subtitle',
        description: 'discount:wizard.maxUses.description',
    },
    {
        key: 3,
        slug: 'discount:wizard.dates.title',
        label: 'Valid Dates',
        short_description: 'discount:wizard.dates.subtitle',
        description: 'discount:wizard.dates.description',
    },
    {
        key: 4,
        slug: 'discount:wizard.type.title',
        label: 'Discount Type',
        short_description: 'discount:wizard.type.subtitle',
        description: 'discount:wizard.type.description',
    },
    {
        key: 5,
        slug: 'discount:wizard.applyTo.title',
        label: 'Application Scope',
        short_description: 'discount:wizard.applyTo.subtitle',
        description: 'discount:wizard.applyTo.description',
    },
    {
        key: 6,
        slug: 'discount:wizard.combo.title',
        label: 'Combination Rules',
        short_description: 'discount:wizard.combo.subtitle',
        description: 'discount:wizard.combo.description',
    },
    {
        key: 7,
        slug: 'discount:wizard.conditions.title',
        label: 'Conditions',
        short_description: 'discount:wizard.conditions.subtitle',
        description: 'discount:wizard.conditions.description',
    },
    {
        key: 8,
        slug: 'discount:wizard.summary.title',
        label: 'Summary',
        short_description: 'discount:wizard.summary.subtitle',
        description: 'discount:wizard.summary.description',
    },
];

// Step 1: Name & Description
export const step1 = data => [
    {
        id: 1,
        title: 'Discount Details',
        slug: 'discount:wizard.name.title',
        description: 'discount:wizard.name.description',
        fields: [
            {
                name: 'name',
                type: 'text',
                label: 'discount:wizard.name.label',
                required: true,
                value: getDataValue({data, field: 'name'}),
                component: "TextField",
                margin: "normal",
                rowId: 1
            },
            {
                name: 'description',
                type: 'text',
                label: 'discount:wizard.description.label',
                required: false,
                value: getDataValue({data, field: 'description'}),
                component: "TextField",
                minRows: 4,
                margin: "normal",
                rowId: 1
            },
        ],
    },
];

// Step 2: Auto Apply or Coupon Code
export const step2 = data => [
    {
        id: 2,
        title: 'Application Method',
        slug: 'discount:wizard.auto.title',
        description: 'discount:wizard.auto.description',
        fields: [
            {
                name: 'auto_apply',
                type: 'radio',
                label: 'discount:wizard.auto.label',
                required: true,
                value: getDataValue({data, field: 'auto_apply', defaultValue: -1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 2,
                options: [
                    {value: 1, label: 'discount:wizard.auto.autoApply'},
                    {value: 0, label: 'discount:wizard.auto.couponCode'},
                ]
            },
            {
                name: 'coupon_code',
                type: 'text',
                label: 'discount:wizard.auto.codeLabel',
                required: data?.auto_apply === 0,
                value: getDataValue({data, field: 'coupon_code'}),
                component: "TextField",
                margin: "normal",
                rowId: 2,
                hidden: data?.auto_apply !== 0,
                helperText: 'discount:wizard.auto.codeHelper'
            },
        ],
    },
];

// Step 3: Max Uses
export const step3 = data => [
    {
        id: 3,
        title: 'Usage Limits',
        slug: 'discount:wizard.maxUses.title',
        description: 'discount:wizard.maxUses.description',
        fields: [
            {
                name: 'unlimited',
                type: 'radio',
                label: 'discount:wizard.maxUses.label',
                required: true,
                value: getDataValue({data, field: 'unlimited', defaultValue: -1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 3,
                options: [
                    {value: 1, label: 'discount:wizard.maxUses.unlimited'},
                    {value: 0, label: 'discount:wizard.maxUses.limited'},
                ]
            },
            {
                name: 'max_uses',
                type: 'number',
                label: 'discount:wizard.maxUses.maxLabel',
                required: data?.unlimited === 0,
                value: getDataValue({data, field: 'max_uses'}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                hidden: data?.unlimited !== 0,
                helperText: 'discount:wizard.maxUses.maxHelper',
                inputProps: { min: 1 }
            },
        ],
    },
];

// Step 4: Valid Dates
export const step4 = data => [
    {
        id: 4,
        title: 'Valid Dates',
        slug: 'discount:wizard.dates.title',
        description: 'discount:wizard.dates.description',
        fields: [
            {
                name: 'valid_from',
                type: 'date',
                label: 'discount:wizard.dates.fromLabel',
                required: true,
                value: getDataValue({data, field: 'valid_from'}),
                component: "DatePicker",
                margin: "normal",
                rowId: 4
            },
            {
                name: 'no_end_date',
                type: 'checkbox',
                label: 'discount:wizard.dates.noEndDate',
                required: false,
                value: getDataValue({data, field: 'no_end_date', defaultValue: 0}),
                component: "Checkbox",
                margin: "normal",
                rowId: 4
            },
            {
                name: 'valid_until',
                type: 'date',
                label: 'discount:wizard.dates.untilLabel',
                required: !data?.no_end_date,
                value: getDataValue({data, field: 'valid_until'}),
                component: "DatePicker",
                margin: "normal",
                rowId: 4,
                hidden: data?.no_end_date === 1
            },
        ],
    },
];

// Step 5: Discount Type
export const step5 = data => {

    return [
    {
        id: 5,
        title: 'Discount Type',
        slug: 'discount:wizard.type.title',
        description: 'discount:wizard.type.description',
        fields: [
            {
                name: 'discount_type',
                type: 'radio',
                label: 'discount:wizard.type.label',
                required: true,
                value: getDataValue({data, field: 'discount_type', defaultValue: -1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 5,
                options: [
                    {value: 0, label: 'discount:wizard.type.percentage'},
                    {value: 1, label: 'discount:wizard.type.fixed'},
                ]
            },
            {
                name: 'discount_amount',
                type: 'number',
                label: data?.discount_type === 0 ? 'discount:wizard.type.percentLabel' : 'discount:wizard.type.amountLabel',
                required: true,
                value: getDataValue({data, field: 'discount_amount', defaultValue: ''}),
                component: "TextField",
                margin: "normal",
                rowId: 5,
                inputProps: {
                    min: 0,
                    step: data?.discount_type === 0 ? 1 : 0.01,
                    max: data?.discount_type === 0 ? 100 : undefined
                },
                startAdornment: data?.discount_type === 1 ? '$' : undefined,
                endAdornment: data?.discount_type === 0 ? '%' : undefined
            },
        ],
    },
];
};

// Step 6: Apply To
export const step6 = data => [
    {
        id: 6,
        title: 'Application Scope',
        slug: 'discount:wizard.applyTo.title',
        description: 'discount:wizard.applyTo.description',
        fields: [
            {
                name: 'apply_to_all',
                type: 'radio',
                label: 'discount:wizard.applyTo.label',
                required: true,
                value: getDataValue({data, field: 'apply_to_all', defaultValue: -1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 6,
                options: [
                    {value: 1, label: 'discount:wizard.applyTo.entireOrder'},
                    {value: 0, label: 'discount:wizard.applyTo.specificItems'},
                ]
            },
        ],
    },
];

// Step 7: Combinable
export const step7 = data => [
    {
        id: 7,
        title: 'Combination Rules',
        slug: 'discount:wizard.combo.title',
        description: 'discount:wizard.combo.description',
        fields: [
            {
                name: 'combinable',
                type: 'radio',
                label: 'discount:wizard.combo.label',
                required: true,
                value: getDataValue({data, field: 'combinable', defaultValue: -1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 7,
                options: [
                    {value: 1, label: 'discount:wizard.combo.yes'},
                    {value: 0, label: 'discount:wizard.combo.no'},
                ]
            },
        ],
    },
];

// Step 8: Conditions
export const step8 = data => [
    {
        id: 8,
        title: 'Conditions',
        slug: 'discount:wizard.conditions.title',
        description: 'discount:wizard.conditions.description',
        fields: [
            {
                name: 'params',
                type: 'custom',
                label: 'discount:wizard.conditions.label',
                required: false,
                value: getDataValue({data, field: 'params', defaultValue: {}}),
                component: "ConditionsSelector",
                margin: "normal",
                rowId: 8,
            },
        ],
    },
];

// Step 9: Summary
export const step9 = data => [
    {
        id: 9,
        title: 'Summary',
        slug: 'discount:wizard.summary.title',
        description: 'discount:wizard.summary.description',
        fields: [
            {
                name: 'status',
                type: 'radio',
                label: 'discount:wizard.summary.statusLabel',
                required: true,
                value: getDataValue({data, field: 'status', defaultValue: 1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 9,
                options: [
                    {value: 1, label: 'discount:wizard.summary.active'},
                    {value: 0, label: 'discount:wizard.summary.inactive'},
                ]
            },
        ],
    },
];
