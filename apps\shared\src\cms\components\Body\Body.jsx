import React from 'react';
import { Stack, Grid2, Box, Typography } from '@mui/material';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts } from './layouts';
import { properties } from './properties';

export const Body = React.forwardRef(({
    id,
    cols = 1,
    colsXs = 1,
    colsSm = 1,
    colsMd = 1,
    colsLg = 1,
    colsXl = 1,
    colsXxl = 1,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        row: {},            // MUI grid2 container props
        column: {},         // MUI grid2 item props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}, ref) => {
    const parentBuilder = isBuilder; // this is used to determine if the page that is loading this component is in a builder
    if (+props.pageTypeId !== 9) isBuilder = false; // only templates can interact

    const { slotProps: updatedSlotProps, canRender, isMobile, t, customCss } = prepareComponent({name: "body", layoutId, layouts, slotProps, isBuilder: parentBuilder, condition});
    slotProps = updatedSlotProps;
    if (!canRender) return null;

    const size = {
        xs: 12 / (colsXs || cols),
        sm: 12 / (colsSm || cols),
        md: 12 / (colsMd || cols),
        lg: 12 / (colsLg || cols),
        xl: 12 / (colsXl || cols),
        xxl: 12 / (colsXxl || cols),
    };

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            ref={ref}
            {...builderProps}
            sx={{...slotProps?.cmsContainer?.sx, flexGrow: 1}}            
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                <Box sx={{
                    width: '100%',
                    minHeight: props?.headerRef?.current ? `calc(100vh - ${props?.headerRef?.current?.offsetHeight || "0"}px - ${props?.footerRef?.current?.offsetHeight || "0"}px - 5px)` : undefined,
                    height: '100%',
                    ...children ? {} : {
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                    }
                }}>
                    {/*{children}*/
                    children ?
                        <Grid2 container {...slotProps?.row}>
                            {React.Children.map(children, (child, i) => (
                                <Grid2 key={i} size={size} {...slotProps?.column}>
                                    <Stack spacing={0} direction="column" useFlexGap sx={{width: '100%'}}>
                                        {child}
                                    </Stack>
                                </Grid2>
                            ))}
                        </Grid2>
                    : 
                        <Box sx={{display:'flex', alignItems: 'center', justifyContent: 'center'}}>
                            <Typography component="span" variant={isMobile ? "body1" : "h4"} textAlign="center">
                                {t("builder:contentGoesHere")}
                            </Typography>
                        </Box>
                    }
                </Box>
            </Stack>
        </CmsContainer>
    );
});