import { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useApi, useApiContext } from '@siteboss-frontend/shared';

const apiParams = [
    {enableCache: false, params: {endpoint: '/event', method: 'POST', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'desc',
        is_program: 1,
    }}},
];

// Define page size options as a constant to ensure consistency
export const pageSizeOptions = [10, 20, 30, 50];

export const useProgramList = ({setSelected, fetchCounter, params}) => {
    const { t, language } = useOutletContext();
    const { updateCacheCounter } = useApiContext();

    const [rows, setRows] = useState();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(pageSizeOptions[0]); // Always use a valid option
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'desc'});
    const [searchText, setSearchText] = useState('');

    const { fetchData, data, loading, ErrorBar, LoadingBar } = useApi(apiParams[0]);

    const columns = useMemo(() => [
        { field: 'id', headerName: `${t('general:id')} #`, width: 90 },
        { field: 'name', headerName: t('program:name'), minWidth: 200, flex: 1, },
    ], [t, language]);

    const handleRowSelection = model => {
        const _model = [];
        rows?.forEach(row => {
            if (model.includes(row.id)){
                _model.push(row);
            }
        })
        setSelected(_model);
    }

    // Update cache counter when fetchCounter changes
    useEffect(() => {
        if (fetchCounter > 0) {
            // Force a global cache invalidation
            updateCacheCounter();
        }
    }, [fetchCounter, updateCacheCounter]);

    // This effect handles fetching the program list data
    useEffect(() => {
        const _loadData = async () => {
            try {
                // Always force disable cache to ensure fresh data
                const result = await fetchData({
                    page_no: Math.round(page) || 1,
                    max_records: pageSize > 10 ? pageSize : 10,
                    sort_col: order?.column || 'id',
                    sort_direction: order.direction || 'desc',
                    is_program: 1,
                    id: params?.id ? [params.id] : undefined,
                    search: searchText,
                    enableCache: false, // Explicitly disable cache
                    _nocache: Date.now() // Add a timestamp to force a fresh request
                });

                if (result?.data) {
                    const _rows = result.data.events.map(event => ({
                        id: event.id,
                        name: event.name,
                        metadata: event,
                    }));
                    setRows(_rows);
                    // Don't update pageSize from API response, keep the user's selection
                    // This prevents the "out-of-range value" warning
                    setPage(result.data.this_page);
                    setTotalRows(result.data.total_record_count);
                    setTotalPages(Math.min(result.data.total_record_count / result.data.page_record_count));
                }
            } catch (error) {
                console.error("Error fetching program list:", error);
            }
        };

        _loadData();
    }, [page, pageSize, order, fetchCounter, params.id, searchText, fetchData]);

    return {
        data,
        rows,
        columns,
        order,
        page,
        pageSize,
        totalPages,
        totalRows,
        searchText,
        loading,
        ErrorBar,
        LoadingBar,
        handleRowSelection,
        setSearchText,
        setOrder,
        setPage,
        setPageSize,
        setTotalPages,
        setTotalRows,
    };
}