import React, { useEffect, useContext } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { Box, Collapse, Container,Typography } from '@mui/material';
import { Checkbox, Radio, Button } from './Layouts';
import { useProductAddons } from './useProductAddons';
import { PosProductDetailContext } from '../../../../hooks/PosProductDetailContext';

export const ProductAddons = ({ layoutType = "checkbox", product, selectedAddons, onSelect, slotProps, ...props }) => {
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);
    const { addons, loading:addonLoading, errors:addonErrors, selectedAddons: selected, handleAddonSelection } = useProductAddons({             
        variantId: currentShopItem?.productVariantId, 
        product,
        onSelect, 
        selectedAddons 
    });
    const { loading, setLoading, setErrors } = useContext(PosProductDetailContext) || {};

    let Component = Checkbox;
    switch(layoutType) {
        case 'radio':
            Component = Radio;
            break;
        case 'button':
            Component = Button;
            break;
        default:
            break;
    }

    useEffect(() => {
        setLoading(addonLoading);
        setErrors(addonErrors);
    }, [addonLoading, setLoading, addonErrors, setErrors]);

    if (!product || !addons || !addons?.length > 0) return null;

    return (
        <Container disableGutters sx={{my: 1}}>
            <Collapse in={!addonLoading && addons?.length > 0}>
                {addons?.filter(a=> a?.add_ons?.length > 0)?.map(addon => (
                    <Box key={addon.category_id} sx={{':not(:last-of-type)': {mb: 3}}}>
                        <Typography variant="subtitle2" component="div">
                            <Typography variant="bold" component="div">{addon.category_name}</Typography>
                        </Typography>
                        <Component 
                            items={addon?.add_ons || []} 
                            selected={layoutType === "radio" ? selected?.[0] : selected} 
                            onSelect={handleAddonSelection}
                            disabled={loading} 
                            slotProps={slotProps}
                        />
                    </Box>
                ))}
            </Collapse>
        </Container>
    );
}