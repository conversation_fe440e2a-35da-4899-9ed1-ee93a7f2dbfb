import React from 'react';
import { Stack } from '@mui/material';

export const MediaOverlay = ({ type, slots, slotProps }) => {
    return (
        <Stack direction="column" className="VideoOverlay" position="relative" {...slotProps?.cmsStack}>
            {+type === 1 
                ? slots?.images?.({ ...slotProps?.content?.images }) 
                : slots?.video?.({ ...slotProps?.content?.video })
            }
            <Stack 
                spacing={2} 
                className="VideoOverlay_textbox"
                sx={{
                    position: 'absolute',
                    bottom: 0,
                    p: 2,
                    bgcolor: theme => theme.palette.action.disabledBackground,
                    width: '100%',
                }}
                {...slotProps?.overlay}
            >            
                {slots?.title?.({ ...slotProps?.title })}
                {slots?.subtitle?.({ ...slotProps?.subtitle })}
                {slots?.body?.({ ...slotProps?.body })}
                {slots?.ctas?.({ ...slotProps?.ctas })}                
            </Stack>
        </Stack>
    );
};