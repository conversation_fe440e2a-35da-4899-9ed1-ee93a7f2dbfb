const typeTitle = [
    { id: 'h1', slug: 'builder:component.heading.types.h1' },
    { id: 'h2', slug: 'builder:component.heading.types.h2' },
    { id: 'h3', slug: 'builder:component.heading.types.h3' },
    { id: 'h4', slug: 'builder:component.heading.types.h4' },
    { id: 'h5', slug: 'builder:component.heading.types.h5' },
    { id: 'h6', slug: 'builder:component.heading.types.h6' },
];

const typeText = [
    { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
    { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
    { id: 'body1', slug: 'builder:component.heading.types.body1' },
    { id: 'body2', slug: 'builder:component.heading.types.body2' },
    { id: 'caption', slug: 'builder:component.heading.types.caption' },
    { id: 'overline', slug: 'builder:component.heading.types.overline' },
    { id: 'p', slug: 'builder:component.heading.types.p' },
    { id: 'code', slug: 'builder:component.heading.types.code' },
];

export const properties = [
    {
        name: 'mediaType',
        label: 'builder:component.core.mediaType',
        component: "RadioGroup",
        value: '1',
        options: [{ id: '1', slug: 'builder:component.core.mediaTypes.image' }, { id: '2', slug: 'builder:component.core.mediaTypes.video' }],
        size: "small",
        margin: "normal",
    },
    {
        name: 'videoUrl',
        component: 'VideoURL',
        label: 'builder:component.core.videoUrl',
        placeholder: 'Enter YouTube or Vimeo URL',
        value: '',
        size: "small",
        margin: "normal",
        condition: { field: 'mediaType', value: '2'}
    },
    {
        name: 'autoplay',
        label: 'builder:component.core.autoplay',
        component: "Switch",
        value: 1,
        checked: false,
        size: "small",
        margin: "normal",
        condition: { field: 'mediaType', value: '2'},
        sx: {ml: 1},
    },
    {
        name: 'muted',
        label: 'builder:component.core.muted',
        component: "Switch",
        value: 1,
        checked: false,
        size: "small",
        margin: "normal",
        condition: { field: 'mediaType', value: '2'},
        sx: {ml: 1},
    },
    {
        name: 'controls',
        label: 'builder:component.core.controls',
        component: "Switch",
        value: 1,
        checked: true,
        size: "small",
        margin: "normal",
        condition: { field: 'mediaType', value: '2'},
        sx: {ml: 1},
    },
    {
        name: 'images',
        label: 'builder:component.gallery.images',
        component: "MediaManager",
        value: [],
        size: "large",
        margin: "normal",
        multiple: false,
        returnMultiple: false,
        mediaType: 1,
        accept: 'image/*',
        showThumbs: true,
        condition: { field: 'mediaType', value: '1'}
    },
    {
        name: 'imageSize',
        label: 'builder:component.gallery.imageSize',
        component: "Measurement",
        measurements: ['width', 'height'],
        value: { width: '100%', height: 200 },
        size: "small",
        margin: "normal",
        condition: { field: 'mediaType', value: '1'}
    },
    {
        name: 'title',
        label: 'builder:component.heading.title',
        component: "TextField",
        value: '',
        size: "small",
        margin: "normal",
        dividerTop: true,
    },
    {
        name: 'titleVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'h1',
        size: "small",
        margin: "normal",
        options: [...typeTitle, ...typeText]
    },
    {
        name: 'body',
        label: 'builder:component.heading.body',
        component: "TextField",
        minRows: 3,
        value: '',
        size: "small",
        margin: "normal",
    },
    {
        name: 'bodyVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'body1',
        size: "small",
        margin: "normal",
        options: [...typeText]
    },
    {
        name: 'ctas',
        label: 'builder:component.core.buttons',
        component: "local.MultiField",
        multiple: true,
        value: [],
        children: [
            {
                name: 'label',
                label: 'builder:component.button.label',
                component: "TextField",
                value: '',
                size: "small",
                margin: "normal",
            },
            {
                name: 'url',
                label: 'builder:component.button.url',
                component: "TextField",
                value: '',
                size: "small",
                margin: "normal",
            },
            {
                name: 'target',
                label: 'builder:component.button.target.title',
                component: "Select",
                options: [
                    {id: '_self', slug: 'builder:component.button.target.self'},
                    {id: '_blank', slug: 'builder:component.button.target.blank'},
                    {id: '_popup', slug: 'builder:component.button.target.popup'},
                ],
                value: '_self',
                size: "small",
                margin: "normal",
            },
            {
                name: 'icon',
                label: 'builder:component.button.icon.title',
                component: "IconSelector",
                value: '',
                size: "medium",
                fullWidth: true,
                variant: 'outlined',
                margin: "normal",
            },
        ]
    },
];
