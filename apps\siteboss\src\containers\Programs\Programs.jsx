import React, { useRef } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Drawer, Stack, Box, Typography, Tooltip, IconButton, Button } from '@mui/material';
import { ArrowBackOutlined as BackIcon } from '@mui/icons-material';
import { Title, Confirm, WithDetails, SuccessBar } from '@siteboss-frontend/shared/components';

import List from './List';
import Form from './Form';
import ProgramData from './ProgramData';

import { usePrograms } from './usePrograms';

export const Programs = (props) => {
    const { t } = useOutletContext();
    const ref = useRef(null);

    const {
        open,
        isNew,
        isEdit,
        setIsEdit,
        showConfirm,
        selectedPrograms,
        setSelectedPrograms,
        deleteParams,
        fetchCounter,
        setFetchCounter,
        toggleDrawer,
        handleDelete,
        handleUpdate,
        handleEdit,
        handleSuccess,
        handleEventTypeSelect,
        handleNewProgram,
        handleConfirmDelete,
        handleDeclineDelete,
        eventTypes,
        groupTypes,
        loading,
        success,
        errorBars,
        typeId,
    } = usePrograms();

    return (
        <Container>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title
                    title={t('program:programs')}
                    breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('program:programs')}]}
                />
                <Box>
                    <Button
                        id="new-program-button"
                        onClick={handleNewProgram}
                        variant="contained"
                        color="primary"
                    >
                        {t('program:newProgram')}
                    </Button>
                </Box>
            </Stack>
            {success && (
                <SuccessBar
                    message={t(`program:success`)}
                    onClose={() => {
                        // Force a refresh when closing the success message
                        setFetchCounter(prev => prev + 1);
                        toggleDrawer(false)();
                    }}
                />
            )}
            {errorBars.map((ErrorBar, i) => ErrorBar && <ErrorBar key={i} />)}
            {showConfirm && deleteParams.length > 0 &&
                <Confirm
                    title={t('program:deleteTitle')}
                    message={t('program:deleteMessage')}
                    onConfirm={handleConfirmDelete}
                    onDecline={handleDeclineDelete}
                >
                    <ul>
                        {deleteParams?.map(program => (
                            <Typography key={`delete-program-${program.id}`} variant="body2" component="li">
                                {program.name}
                            </Typography>
                        ))}
                    </ul>
                </Confirm>
            }

            <List
                setSelected={setSelectedPrograms}
                selected={selectedPrograms}
                onExpand={toggleDrawer(true)}
                onDelete={handleDelete}
                fetchCounter={fetchCounter}
                loading={loading}
            />

            <Drawer
                details={1} // this is to flag the drawer as a details drawer in the theme
                anchor='right'
                open={open}
                onClose={toggleDrawer(false)}
            >
                <WithDetails
                    ref={ref}
                    itemIds={selectedPrograms.map(a => ({id: a.id, label: (
                        <>
                            <span>{a.name}</span> {/* we use a span so that it inherits the default typography variant defined in WithDetails */}
                            <Typography variant="subtitle3">{a.type_name}</Typography>
                        </>
                    )}))}
                    onClose={toggleDrawer(false)}
                    onSuccess={handleSuccess}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    isNew={isNew}
                    isEdit={isEdit}
                    titles={{
                        new: t('program:newProgram'),
                        edit: t('program:editProgram'),
                        view: t('program:programs'),
                    }}
                    slots={{
                        headerButtons: props => isEdit && (
                            <Tooltip title={t("general:back")}>
                                <IconButton size="small" onClick={()=>setIsEdit(false)} {...props}>
                                    <BackIcon fontSize='inherit' />
                                </IconButton>
                            </Tooltip>
                        ),
                        form: props => (
                            <Form
                                groupTypes={groupTypes}
                                eventTypes={eventTypes}
                                typeId={typeId}
                                loading={loading}
                                {...props}
                                id={isEdit ? selectedPrograms[0].id : null}
                            />
                        ),
                        details: props => (
                            <ProgramData
                                parentRef={ref}
                                loading={loading}
                                onDelete={handleDelete}
                                onEdit={handleEdit}
                                onUpdate={handleUpdate}
                                fetchCounter={fetchCounter}
                                setFetchCounter={setFetchCounter}
                                selectedPrograms={selectedPrograms}
                                {...props}
                            />
                        ),
                    }}
                />
            </Drawer>
        </Container>
    );
}