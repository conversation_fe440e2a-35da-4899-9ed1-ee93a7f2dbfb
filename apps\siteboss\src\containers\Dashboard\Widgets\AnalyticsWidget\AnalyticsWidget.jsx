import React, { useState, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Box, Typography, Card, CardContent, ToggleButtonGroup, ToggleButton } from '@mui/material';
import { LineChart } from '@siteboss-frontend/shared/components';

// Mock data - in a real app, this would come from an API
const mockData = {
    day: {
        headers: ["Time", "Visitors", "Page Views"],
        data: [
            {
                id: "visitors",
                data: Array.from({ length: 24 }, (_, i) => ({
                    x: `${i}:00`,
                    y: Math.floor(Math.random() * 100)
                }))
            },
            {
                id: "pageViews",
                data: Array.from({ length: 24 }, (_, i) => ({
                    x: `${i}:00`,
                    y: Math.floor(Math.random() * 200)
                }))
            }
        ]
    },
    week: {
        headers: ["Day", "Visitors", "Page Views"],
        data: [
            {
                id: "visitors",
                data: Array.from({ length: 7 }, (_, i) => {
                    const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
                    return {
                        x: days[i],
                        y: Math.floor(Math.random() * 500)
                    };
                })
            },
            {
                id: "pageViews",
                data: Array.from({ length: 7 }, (_, i) => {
                    const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
                    return {
                        x: days[i],
                        y: Math.floor(Math.random() * 1000)
                    };
                })
            }
        ]
    },
    month: {
        headers: ["Date", "Visitors", "Page Views"],
        data: [
            {
                id: "visitors",
                data: Array.from({ length: 30 }, (_, i) => ({
                    x: i + 1,
                    y: Math.floor(Math.random() * 800)
                }))
            },
            {
                id: "pageViews",
                data: Array.from({ length: 30 }, (_, i) => ({
                    x: i + 1,
                    y: Math.floor(Math.random() * 1500)
                }))
            }
        ]
    }
};

export const AnalyticsWidget = ({ settings = {}, isEditing, ...props }) => {
    const { t } = useOutletContext();
    const [dataRange, setDataRange] = useState(settings.dataRange || 'week');

    const handleRangeChange = (event, newRange) => {
        if (newRange !== null) {
            setDataRange(newRange);
        }
    };

    const data = useMemo(() => mockData[dataRange], [dataRange]);

    const totalVisitors = useMemo(() => {
        return data.data[0].data.reduce((sum, item) => sum + item.y, 0);
    }, [data]);

    const totalPageViews = useMemo(() => {
        return data.data[1].data.reduce((sum, item) => sum + item.y, 0);
    }, [data]);

    return (
        <Box data-cy="analytics-widget" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">{t('widget:analytics.title')}</Typography>
                <ToggleButtonGroup
                    value={dataRange}
                    exclusive
                    onChange={handleRangeChange}
                    size="small"
                >
                    <ToggleButton value="day" aria-label="day">
                        {t('widget:analytics.day')}
                    </ToggleButton>
                    <ToggleButton value="week" aria-label="week">
                        {t('widget:analytics.week')}
                    </ToggleButton>
                    <ToggleButton value="month" aria-label="month">
                        {t('widget:analytics.month')}
                    </ToggleButton>
                </ToggleButtonGroup>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Card variant="outlined" sx={{ flex: 1 }}>
                    <CardContent>
                        <Typography variant="subtitle2" color="text.secondary">
                            {t('widget:analytics.visitors')}
                        </Typography>
                        <Typography variant="h4">{totalVisitors.toLocaleString()}</Typography>
                    </CardContent>
                </Card>
                <Card variant="outlined" sx={{ flex: 1 }}>
                    <CardContent>
                        <Typography variant="subtitle2" color="text.secondary">
                            {t('widget:analytics.pageViews')}
                        </Typography>
                        <Typography variant="h4">{totalPageViews.toLocaleString()}</Typography>
                    </CardContent>
                </Card>
            </Box>

            <Box sx={{ flex: 1, minHeight: 200 }}>
                <LineChart
                    data={data}
                    curve={settings.chartType === 'line' ? 'linear' : (settings.chartType || "natural")}
                    area={{ opacity: 0.32 }}
                    legend={{
                        anchor: 'bottom-right',
                        direction: 'row',
                        translateY: 0,
                        itemWidth: 100
                    }}
                />
            </Box>
        </Box>
    );
};

export default AnalyticsWidget;
