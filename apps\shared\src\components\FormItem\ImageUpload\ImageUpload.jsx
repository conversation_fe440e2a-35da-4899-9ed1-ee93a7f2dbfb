import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { MediaUpload as FileUpload } from '../../FileUpload';

export const  ImageUpload = ({
    label, // the label for the drop zone
    name, // the name of the drop zone
    fileSize = 2097152, // the file size
    errors, // the errors for the drop zone
    loading, // the loading status of the form so we can disable the drop zone
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onRemove, // the function to be called when the file is removed
    multiple = false, // if the drop zone should accept multiple files
    height = 200, // the height of the drop zone
    value,
    ...props
}) => {
    const { t } = useTranslation();
    const [values, setValues] = useState(value || null);

    const handleChange = useCallback(e => {
        setValues([e.target]);
        onChange(e);
    }, [onChange]);

    return (
        <FileUpload 
            onFileDrop={file => handleChange({target: {name, value: file.map(f => f.file)}})}
            onFileRemove={() => handleChange({target: {name, value: null}})}
            url={values ? values?.find(a => a.name === name)?.value || null : null}
            multiple={multiple}
            sx={{
                height
            }}
        />
    );
}