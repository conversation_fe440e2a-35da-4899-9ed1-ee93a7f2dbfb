export const properties = [
    {
        name: 'postType',
        label: 'builder:component.blogPost.type',
        component: "RadioGroup",
        value: '1',
        options: [{ id: '1', slug: 'builder:component.blogPost.types.automatic' }, { id: '2', slug: 'builder:component.blogPost.types.manual' }],
        size: "small",
        margin: "normal",
        helperText: 'builder:component.blogPost.types.helperText',
    },
    {
        name: 'postId',
        label: 'builder:component.blogPost.id',
        component: 'NumberField',
        value: 0,
        size: 'small',
        margin: 'normal',
        condition: { field: 'postType', value: '2' }
    },
    /*
    {
        name: 'title',
        label: 'builder:component.blogPost.title',
        component: 'TextField',
        value: generateTitle(),
        size: 'small',
        required: true,
        margin: 'normal',
    },
    {
        name: 'content',
        label: 'builder:component.blogPost.content',
        component: 'TextField',
        value: generateParagraph(10),
        size: 'small',
        multiple: true,
        required: true,
        margin: 'normal',
    },
    {
        name: 'author',
        label: 'builder:component.blogPost.author',
        component: 'TextField',
        value: [generateTitle()],
        size: 'small',
        margin: 'normal',
    },
    {
        name: 'publishDate',
        label: 'builder:component.blogPost.publishDate',
        component: 'DatePicker',
        value: new Date(),
        size: 'small',
        margin: 'normal',
    },
    {
        name: 'featuredImage',
        label: 'builder:component.blogPost.featuredImage',
        component: 'MediaManager',
        value: ['https://placehold.co/300?font=source-sans-pro'],
        size: 'large',
        mediaType: 1,
        accept: 'image/*',
        showThumbs: true,
        multiple: true,
        returnMultiple: true,        
        margin: 'normal',
    }
    */
];