import { useCallback, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { uuid, getBase64FromUrl } from '../../utils';

const urlToFile = async ({url, filename, mimeType, signal}) => {
    if (!url || signal?.aborted) return null;
    try {
        let base64 = url;
        if (!url.startsWith('blob:')){
            base64 = await getBase64FromUrl(url, signal);
        }
        if (!signal?.aborted) {
            const response = await fetch(base64, { signal });
            if (response.ok){            
                const blob = await response.blob();
                return new File([blob], filename, { type: mimeType });
            }
        }
    } catch (error) {
        console.error('Error converting URL to File:', error);
    }
    return null;
}

export const useFileUpload = ({ 
    onFileDrop,     // function to handle the files dropped
    onFileRemove,   // function to handle the files removed
    accept,         // string with the file types accepted separated by commas, for example: image/*, .pdf
    size,           // number with the maximum file size in bytes
    multiple,       // boolean to allow multiple files
    previews,       // array with the previews of the files dropped, structure should be the same as the one returned by the processFiles ({name, url, type})
    loading,        // the loading state of the parent component
    fileInputRef,   // the ref of the file input element
    ...props
}) => {
    const { t } = useTranslation();
    
    const [isDragging, setIsDragging] = useState(false);
    const [files, setFiles] = useState({processed:[], originals:[]});
    const [filePreviews, setFilePreviews] = useState([]);
    const [error, setError] = useState(null);
    const [imageToCrop, setImageToCrop] = useState(null);
    const [sendFile, setSendFile] = useState(false);
    
    // process the files to show previews
    const processFiles = useCallback(f => {
        if (!f.length) return;
        const _previews = Array.from(f).map(file => {
            const id = file?.id || uuid();
            if (file.file.type.startsWith('image/')) {
                return { name: file?.description || file.file.name, url: URL.createObjectURL(file.file), type: file.file?.type || 'image', id };
            } else {
                return { name: file?.description || file.file.name, type: file.file?.type || 'other', id };
            }
        });
        return _previews;
    }, [multiple]);

    // validate that the file is of the correct type and size
    const validateFile = f => {
        if (!f || !f.length) return false;
        let valid = true;
        if (accept) valid = Array.from(f).every(file => accept.split(',').some(type => file.file.type.match(type.trim())));
        if (size && valid) valid = Array.from(f).every(file => file.file.size <= size) ? true : -1;
        return valid;
    }

    // this deals with the final cropped image
    const handleCrop = file => (blob, imageUrl) => {
        if (!blob || !imageUrl) return;
        const croppedFile = new File([blob], file.name, { type: blob.type, id: file.id });
        setFiles(prev => ({
            ...prev, 
            processed: prev.processed.map(f => f.id === file.id ? croppedFile : f)
        }));
        setFilePreviews(prev => prev.map(f => f.id === file.id ? {...f, url: imageUrl} : f));
        setSendFile(true);
    }

    // this shows the image cropper
    const handleFileCrop = useCallback(f => e => {
        e.stopPropagation();
        if (!f) return;
        setError(null);
        const preview = processFiles([f]);
        setImageToCrop(preview[0]);
    }, [processFiles]);

    // this removes a file from the list
    const handleFileRemove = useCallback(file => e => {
        e.stopPropagation();
        if (!file) return;
        setError(null);
        
        // clear the file input value to allow the same file to be selected again
        if (fileInputRef?.current) {
            fileInputRef.current.value = '';
        }
        
        setFilePreviews(prev => prev.filter(f => {
            if (f.id && file.id) return f.id !== file.id;
            return f.name !== file.name
        }));

        setFiles(prev => {
            const _processed = prev.processed.filter(f => {
                if (f.id && file.id) return f.id !== file.id;
                return f.name !== file.name
            });
            return ({
                processed: _processed,
                originals: prev.originals.filter(f => {
                    if (!_processed.find(a => a.id === f.id)) return false;
                    return true;
                })
            });
        });
        if (file?.url) URL.revokeObjectURL(file.url);
        if (onFileRemove) onFileRemove(file);
    }, [onFileRemove]);

    // this is called when a file is dropped or selected
    const handleFiles = useCallback((f, send = true) => {
        if (f.length){
            const valid = validateFile(f);
            if (valid === true) {
                setError(null);
                f.forEach(file => { // add an id to each file
                    if (!file.id) file.id = uuid();
                });
                const _previews = processFiles(f);
                setFilePreviews(prev => [...(multiple ? prev : []), ..._previews]);
                
                setFiles(prev => ({
                    processed: [...(multiple ? prev.processed : []), ...f], 
                    originals: [...(multiple ? prev.originals : []), ...f]
                }));
                if (send) setSendFile(true);
            } else {
                if (valid === -1) setError(t('error:invalidFileSize'));
                else setError(t('error:invalidFileType'));
            }
        }
    }, [multiple, processFiles, validateFile, t]);

    const handleDragOver = useCallback(e => {
        e.preventDefault();
        if (!loading) setIsDragging(true);
    }, [loading]);

    const handleDrop = useCallback(e => {
        e.preventDefault();
        if (!loading && e?.dataTransfer?.types?.includes('Files')){
            handleFiles(Array.from(e.dataTransfer.files).map(f => ({file: f})));
        }
        setIsDragging(false);
    }, [loading, handleFiles]);

    const handleHideImageCropper = useCallback(() => setImageToCrop(null), []);
    const handleDragLeave = useCallback(() => setIsDragging(false), []);
    const handleButtonClick = () => fileInputRef?.current?.click();
    const handleChange = e => handleFiles(Array.from(e.target.files).map(f => ({file: f})));
    const handleErrors = errors => setError(errors);

    useEffect(() => {
        const abortController = new AbortController();
        const signal = abortController.signal;

        const _processPreviews = async () => {
            const _getMimeType = type => { 
                let mimeType = '';
                switch (type){
                    case 'image':
                        mimeType = 'image/jpeg';
                        break;
                    case 'pdf':
                        mimeType = 'application/pdf';
                        break;
                    case 'video':
                        mimeType = 'video/mp4';
                        break;
                    case 'audio':
                        mimeType = 'audio/mpeg';
                        break;
                    default:
                        mimeType = 'application/octet-stream';
                        break;
                }
                return mimeType;
            }

            let _files = [];
            for (const preview of previews) {
                if (signal.aborted) break; 
                if (!filePreviews.some(f => f.id === preview.id)) {
                    const file = await urlToFile({url: preview.url, filename: preview.name, mimeType: _getMimeType(preview.type), signal});
                    if (file && !signal.aborted) _files.push({id: preview.id, file});
                }
            }
            if (_files.length) handleFiles(_files, false);
        }

        if (previews) _processPreviews();

        return () => {
            abortController.abort(); // clean up by aborting ongoing operations
        }        
    }, [previews, filePreviews, handleFiles]);

    // sends the processed files to the parent component when there is a change
    useEffect(() => {
        if (onFileDrop && sendFile) {
            onFileDrop(files.processed);
            setSendFile(false);
        }
    }, [onFileDrop, sendFile, files.processed]);    

    // cleanup
    useEffect(() => {
        return () => {
            filePreviews.forEach((file) => {
                if (file.type === 'image') URL.revokeObjectURL(file.url);
            });
        }
    }, [filePreviews]);

    return {
        isDragging,
        files,
        filePreviews,
        error,
        imageToCrop,
        sendFile,
        setSendFile,
        handleDragOver,
        handleDrop,
        handleDragLeave,
        handleButtonClick,
        handleChange,
        handleFileCrop,
        handleFileRemove,
        handleCrop,
        handleHideImageCropper,
        handleErrors,
        processFiles,
        validateFile,
    }
}
