import { useCallback, useMemo, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { WithDetailsToolbar } from '@siteboss-frontend/shared/components';
import { TabPanel, TabContext } from '@mui/lab';
import {
	InfoOutlined as InfoIcon,
	SettingsOutlined as SetupIcon,
	ContactPhoneOutlined as ContactsIcon,
	VerifiedOutlined as LicensesIcon,
	PaymentOutlined as BillingIcon,
	LocalOfferOutlined as BrandsIcon,
	LocalShippingOutlined as ShippingIcon,
	InventoryOutlined as PackagingIcon,
	EmailOutlined as EmailIcon
} from '@mui/icons-material';

import BasicInfo from '../BasicInfo';
import Contacts from '../Contacts';
import Licenses from '../Licenses';
import EmailTemplates from '../EmailTemplates';
import Brands from '../Brands';
import Shipping from '../Shipping';
import { fieldsByTab } from '../fields';

export const Tabs = ({ contentRef, values, errors, onChange, loading, merchantId }) => {
	const { isMobile } = useOutletContext();

	const [selectedTab, setSelectedTab] = useState('info');

	const formData = useMemo(() => {
		const _data = {};
		if (values) {
			values.forEach(field => {
				if (field && field.name !== undefined) {
					_data[field.name] = field.value;
				}
			});
		}
		return _data;
	}, [values]);

	// Handle tab selection
	const handleTabClick = useCallback((e, action) => {
		if (!action) return;
		setSelectedTab(`${action}`);
	}, []);

	// Define tabs with icons and components following WebsiteData pattern
	const tabs = useMemo(() => {
		const _tabs = [];

		// Always add info tab first with the main form
		_tabs.push({
			id: 'info',
			index: 0,
			slug: 'merchant:tabs.info',
			moduleId: 25,
			icon: <InfoIcon/>,
			component: props => <BasicInfo {...props} fields={fieldsByTab?.info || []} />
		});
		_tabs.push(
			{ id: 'setup', index: 1, slug: 'merchant:tabs.setup', moduleId: 25, icon: <SetupIcon />,
				component: props => <BasicInfo tabId="setup" {...props} fields={fieldsByTab?.setup || []} /> },
			{ id: 'contacts', index: 1, slug: 'merchant:tabs.contacts', moduleId: 25, icon: <ContactsIcon />,
				component: props => <Contacts {...props} /> },
			{ id: 'licenses', index: 1, slug: 'merchant:tabs.licenses', moduleId: 25, icon: <LicensesIcon />,
				component: props => <Licenses {...props} /> },
			{ id: 'billing', index: 2, slug: 'merchant:tabs.billing', moduleId: 25, icon: <BillingIcon />,
				component: props => <BasicInfo tabId="billing" {...props} fields={fieldsByTab?.billing || []} /> },
			{ id: 'brands', index: 3, slug: 'merchant:tabs.brands', moduleId: 25, icon: <BrandsIcon />,
				component: props => <Brands {...props} /> },
			{ id: 'shipping', index: 4, slug: 'merchant:tabs.shipping', moduleId: 25, icon: <ShippingIcon />,
				component: props => <Shipping {...props} /> },
			{ id: 'packaging', index: 5, slug: 'merchant:tabs.packaging', moduleId: 25, icon: <PackagingIcon />,
				component: props => <BasicInfo tabId="packaging" {...props} fields={fieldsByTab?.packaging || []} /> },
			{ id: 'email', index: 6, slug: 'merchant:tabs.email', moduleId: 25, icon: <EmailIcon />,
				component: props => <EmailTemplates {...props} /> },
		);

		return _tabs;
	}, []);

	return (
		<TabContext value={selectedTab ?? tabs?.[0]?.id ?? 'info'}>
			<div style={{minHeight: '100%', display: 'flex', flexDirection: 'row-reverse', width: '100%'}}>
				<WithDetailsToolbar 
					tabs={tabs}
					parentRef={contentRef} 
					onSelection={handleTabClick} 
					selectedTab={selectedTab}
					setSelectedTab={setSelectedTab}
					sx={{mt: 4, ml: 'auto', width: 'auto', alignSelf: 'flex-start'}} 
					stickyTop={68}
					hideIcons={false}
					loading={loading}
					slotProps={{
						wrapper: {sx: {width: 'auto', display: 'flex', flexDirection: 'row-reverse'}},
						tabs: {orientation: "vertical", slotProps: {root: {sx: {borderLeft: theme => `1px solid ${theme.palette.divider}`, borderRight: 0}}, indicator: {sx: {left: 0, right: 'auto'}}}},
						tab: {sx: { justifyContent: 'flex-start', textAlign: 'left'}},
					}}
				/>
				<div style={{flex: 1}}>
					{tabs.map(tab => (
						<TabPanel key={tab.id} value={tab.id} sx={{ 
							position:'relative', 
							px: isMobile ? 1 : undefined, 
						}}>
							{tab?.component({
								values: formData || {},
								merchantId,
								errors,
								onChange,
								loading,
								parentRef: contentRef,
							})}
						</TabPanel>
					))}
				</div>
			</div>
		</TabContext>
	);
};