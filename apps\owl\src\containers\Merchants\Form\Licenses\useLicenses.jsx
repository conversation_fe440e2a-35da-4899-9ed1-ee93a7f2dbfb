import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { format } from 'date-fns';
import { useApi } from '@siteboss-frontend/shared';
import { useForm } from '@siteboss-frontend/shared/components';
import { uuid } from '@siteboss-frontend/shared/utils';

import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

export const useLicenses = ({ merchantId, onMainFormChange, values }) => {
    const { t } = useOutletContext();
    const fixedData = useSelector(state => state.fixedData);

    const fields = useMemo(() => [
        {name: 'issuing_state_code', label: null, required: true, value: null, component: "HierarchySelector", margin: "normal", rowSize: {xs: 12},
            levels: [
                { id: 'country', label: 'merchant:country', parent: null, parentField: 'country_id' },
                { id: 'state', label: 'merchant:state', parent: 'country', parentField: 'country_id', parentIdField: 'country_id' },
            ],            
            isAsync: false,
            data: { country: fixedData.countries, state: fixedData.states },
            /*getOptionLabel: {
                country: option => option ? (option?.name || option) : '',
                state: option => option ? (option?.name || option) : '',
            },
            renderOption: {
                country: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
                state: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
            },*/
        },
        /*{name: 'license_brand', type: 'text', label: 'merchant:licenseBrand', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12}},*/
        {name: 'license_name', type: 'text', label: 'merchant:licenseName', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12}},
        {name: 'expiration_date', type: 'date', label: 'merchant:licenseExpirationDate', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}, InputLabelProps: { shrink: true }},
    ], [fixedData]);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/licenses`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/licenses`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/licenses`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/licenses`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);

    const [modalOpen, setModalOpen] = useState(false);
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const handleToggleModal = useCallback(open => e => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({page: page, per_page: pageSize});
            if (res?.data) return res.data;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);


    // Form submission handler for useForm
    const formSubmit = useCallback(async (formValues, setErrors) => {
        if (!formValues) return false;

        if (merchantId) {
            // when a merchantId is defined, save directly
            const apiCall = formValues.id ? updateData : createData;
            try {
                const res = await apiCall(formValues);
                if (res?.data) {
                    fetchData();
                    setModalOpen(false);
                    return true;
                }
                return false;
            } catch (e) {
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            if (!formValues.id) formValues.id = uuid();
            onMainFormChange({
                preventDefault: () => {},
                target: {
                    name: 'licenses',
                    value: { ...formValues }
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [merchantId, updateData, createData, fetchData, onMainFormChange]);

    // Initialize useForm hook
    const { values: formValues, errors: formErrors, handleChange, handleSubmit, setFormValues, resetForm } = useForm(fields, {onSubmit: formSubmit});

    const formData = useMemo(() => {
        const _data = {};
        if (formValues) {
            formValues.forEach(field => {
                if (field && field.name !== undefined) {
                    _data[field.name] = field.value;
                }
            });
        }
        return _data;
    }, [formValues]);

    const handleEditItem = useCallback(() => {
        // a stupid function to look for the right country because the data is a mess
        const findCountry = state_id => {
            const country_id = fixedData?.states.find(a => a.id === state_id)?.country_id;
            return fixedData?.countries.find(a => a.country_id === country_id)?.id || "US";
        }

        if (!selected?.length) return;

        let country_id = "US";
        if (typeof selected[0]?.issuing_state_code === "string") {
            country_id = findCountry(selected[0]?.issuing_state_code);
        } else {
            if (selected[0]?.issuing_state_code?.country?.id) country_id = selected[0]?.issuing_state_code?.country?.id;
            else country_id = findCountry(selected[0]?.issuing_state_code?.state?.id);
        }

        const _data = {
            id: selected[0]?.id || uuid(),
            issuing_state_code: {country: country_id, state: selected[0]?.issuing_state_code?.state?.id || selected[0]?.issuing_state_code},
            license_name: selected[0]?.license_name,
            expiration_date: selected[0]?.expiration_date ? format(new Date(selected[0]?.expiration_date), 'yyyy-MM-dd') : null,
        }

        setFormValues(_data);
        setModalOpen(true);
    }, [selected, setFormValues, fixedData]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                const res = await deleteData({endpoint: `/clients/${merchantId}/licenses/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData();
        }
    }, [deleteData, fetchData, merchantId]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        const _data = data?.items || values;
        _data.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data, values]);

    const handleResetForm = useCallback(() => {
        resetForm();
    }, [resetForm]);

    // DataTable columns
    const columns = useMemo(() => [
        {
            field: 'issuing_state_code',
            headerName: t('geographic:state'),
            valueGetter: (value, row) => row?.issuing_state_code || '-',
        },
        {
            field: 'license_name',
            headerName: t('merchant:licenseName'),
            flex: 1,
        },
        {
            field: 'expiration_date',
            headerName: t('merchant:licenseExpirationDate'),
            width: 150,
            valueGetter: (value, row) => {
                const date = row.expiration_date;
                return date ? new Date(date).toLocaleDateString() : '-';
            }
        }
    ], [t]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
    }, [handleLoadItems]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem: handleSubmit,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: formErrors || errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: fields || [],
    };
};