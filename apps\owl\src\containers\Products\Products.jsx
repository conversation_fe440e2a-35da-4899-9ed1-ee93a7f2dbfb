import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import { Container, Drawer, Stack, Button, Box, Typography, IconButton, Tooltip, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { ArrowBackOutlined as BackIcon, InventoryOutlined as InventoryIcon, ListAltOutlined as SkuListIcon } from '@mui/icons-material';
//import { usePermission } from '@siteboss-frontend/shared/permissions';
import { useApi } from '@siteboss-frontend/shared';
import { Title, Confirm, WithDetails } from '@siteboss-frontend/shared/components';

import List from './List';
import Inventory from './Inventory';
import Form from './Form';
//import ProductData from './ProductData';

const apiParams = [
    {params: {endpoint: `/owl/product/delete`, method: 'DELETE'}},
];

export const Products = (props) => {
    const { t } = useOutletContext();
    const params = useParams();

    /*const { permissions } = usePermission({moduleIds: [74, 204]});*/
    const { fetchData:processDelete, loading:deleteLoading, ErrorBar:DeleteErrorBar } = useApi(apiParams[0]);

    const ref = useRef(null);

    const [open, setOpen] = useState(false);
    const [isNew, setIsNew] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('sku-list');

    const toggleDrawer = useCallback((open) => (e) => {
        if (e?.type === 'keydown' && (e?.key === 'Tab' || e?.key === 'Shift')) {
          return;
        }
        if (!open && params.id) {
            setSelectedItems([]);
            params.id=null;
        }
        setIsNew(false);
        setIsEdit(false);
        setOpen(open);
    }, [isNew, isEdit, params.id]);

    const handleNewClick = () => {
        setIsNew(true);
        setIsEdit(false);
        setOpen(true);
    }

    const handleDelete = useCallback((e, item) => {
        if (item) setDeleteParams([item]);
        else setDeleteParams(selectedItems);
        if (item || selectedItems.length > 0) setShowConfirm(true);
    }, [selectedItems]);

    const handleEdit = useCallback((e, user) => {
        setIsNew(false);
        setIsEdit(true);
        setOpen(true);
    }, []);

    const handleTabChange = useCallback((event, newValue) => {
        setActiveTab(newValue);
    }, []);

    useEffect(() => {
        setLoading(deleteLoading);
    }, [deleteLoading]);


    useEffect(() => {
        if (selectedItems.length <= 0) {
            setOpen(false);
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedItems);
            setShowConfirm(false);
        }
    }, [selectedItems]);

    return (
        <Container>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title
                    title={t('product:products')}
                    breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('product:products')}]}
                />
                <Box>
                    <Button size="large" variant="contained" color="primary" onClick={handleNewClick}>{t('product:newProduct')}</Button>
                </Box>
            </Stack>

            {showConfirm && deleteParams.length > 0 &&
                <>
                    <Confirm
                        title={t('product:deleteTitle')}
                        message={t('product:deleteMessage')}
                        onConfirm={async () => {
                            if (deleteParams.length > 0) {
                                const _ids = deleteParams.map(s => s.id);
                                const result = await processDelete({ endpoint: "/owl/product/"+_ids.join(','), id: _ids });
                                if (result?.data) {
                                    setDeleteParams([]);
                                    setShowConfirm(false);
                                }
                            }
                        }}
                        onDecline={() => {
                            setShowConfirm(false);
                            setDeleteParams([]);
                        }}
                    >
                        <ul>
                            {deleteParams?.map(param => (
                                <Typography key={`delete-product-${param.id}`} variant="body2" component="li">
                                    {param.sku} - {param.name}
                                </Typography>
                            ))}
                        </ul>
                    </Confirm>
                </>
            }
            <DeleteErrorBar />

            <TabContext value={activeTab}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                    <TabList
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        aria-label="product tabs"
                        sx={{
                            '& .MuiTab-root': {
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                minHeight: '48px',
                            }
                        }}
                    >
                        <Tab
                            label={t('product:skuList')}
                            value="sku-list"
                            icon={<SkuListIcon />}
                            iconPosition="start"
                        />
                        <Tab
                            label={t('product:inventory')}
                            value="inventory"
                            icon={<InventoryIcon />}
                            iconPosition="start"
                        />
                    </TabList>
                </Box>

                <TabPanel value="sku-list" sx={{ p: 0 }}>
                    <List
                        setSelected={setSelectedItems}
                        selected={selectedItems}
                        onExpand={toggleDrawer(true)}
                        onDelete={handleDelete}
                        loading={loading}
                    />
                </TabPanel>

                <TabPanel value="inventory" sx={{ p: 0 }}>
                    <Inventory
                        setSelected={setSelectedItems}
                        selected={selectedItems}
                        onExpand={toggleDrawer(true)}
                        onDelete={handleDelete}
                        loading={loading}
                    />
                </TabPanel>
            </TabContext>

            <Drawer
                details={1} // this is to flag the drawer as a details drawer in the theme
                anchor='right'
                open={open}
                onClose={toggleDrawer(false)}
            >
                <WithDetails
                    ref={ref}
                    itemIds={selectedItems.map(a => ({id: a.id, label: `${a.name}`}))}
                    onClose={toggleDrawer(false)}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    isNew={isNew}
                    isEdit={isEdit}
                    titles={{
                        new: t('product:newProduct'),
                        edit: t('product:editProduct'),
                        view: t('product:products'),
                    }}
                    slots={{
                        headerButtons: props => isEdit && (
                            <Tooltip title={t("general:back")}>
                                <IconButton size="small" onClick={()=>setIsEdit(false)} {...props}>
                                    <BackIcon fontSize='inherit' />
                                </IconButton>
                            </Tooltip>
                        ),
                        form: props => <Form loading={loading} {...props} />,
                        details: props => null /*<ProductData parentRef={ref} selectedItems={selectedItems} loading={loading} {...props} />*/,
                    }}
                />
            </Drawer>
        </Container>
    );
}