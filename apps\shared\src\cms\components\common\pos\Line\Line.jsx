import React from 'react';
import { Stack, Typography, useMediaQuery } from '@mui/material';

export const Line = ({ 
    caption, // line caption or title
    captionVariant = 'caption',
    text,  // line text
    textVariant = 'subtitle2',
    icon,
    color,
    slotProps, // an object containing the properties for the caption and text slots
    ...props // additional props
}) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    textVariant = textVariant || (isMobile ? 'body1' : 'subtitle2');

    return (
        <Stack direction="row" useFlexGap spacing={2}>
            {icon}
            <Stack direction="column" useFlexGap spacing={0} sx={{mb: 2, ...(props?.sx || {})}}>
                <Typography variant={captionVariant || "caption"} color={color || 'text.secondary'} sx={{...(slotProps?.caption || {})}}>
                    {caption}
                </Typography>
                <Typography 
                    variant={textVariant} 
                    component="div" 
                    sx={{...(slotProps?.text || {})}} 
                    dangerouslySetInnerHTML={{__html: text}}
                    color={color || 'inherit'}
                />
            </Stack>
        </Stack>
    );
}