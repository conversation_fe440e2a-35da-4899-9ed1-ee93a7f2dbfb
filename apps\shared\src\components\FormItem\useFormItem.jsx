import { useMemo, useCallback, useRef, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { TextField, InputAdornment } from '@mui/material';
import { capitalize, uuid } from '../../utils';

/*
Hook to handle the form fields
*/
export const useFormItem = ({
    component,  // the component to use for the field, it could be a TextField, a select, or a custom component (TextField, PasswordField, Checkbox, TileButton, TileButtonGroup)
    id,         // the id of the field
    type,       // the type of the field, it could be text, email, password,
    label,      // the label for the field
    name,       // the name of the field
    required,   // if the field is required
    errors,     // the errors for the field
    loading,    // the loading status of the form so we can disable the fields
    value,      // the value of the field
    onChange,   // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onBlur,     // the function to be called when the field is blurred
    options,    // the options for the select field
    parentRef,  // the ref of the parent component
    InputProps, // additional props to pass to the input field
    ...props
}) => {
    const { t } = useTranslation();
    const itemRef = useRef(null);

    const handleChange = useCallback(eventType => e => {
        if (!eventType) eventType = "change";

        let value = e.target.value;
        if (type === "number" || component === "NumberField"){
            const regex = /^[0-9\b]+$/;
            if (!(value === '' || regex.test(value) || (+value >= (+props.min || -value) && +value <= (+props.max || +value)))) return false;

            if (value !== '' || +value !== 0){
                if (!value) value = 0;
                if (props.min !== undefined && +value < +props.min) return false;
                if (props.max !== undefined && +value > +props.max) return false;
            }
        }
        if (eventType === "change" && onChange) onChange(e);
        if (eventType === "blur" && onBlur) onBlur(e);
    }, [onChange, onBlur, type, component, props.min, props.max]);

    // handle the arrow up and down keys for the number field
    const handleKeyDown = useCallback(e => {
        let _value = e.target.value;
        if (!_value) _value = 0;
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            e.preventDefault();
            
            let step = +props.step || 1;
            if (e.key === 'ArrowDown') step = -step;

            // increment or decrement the value based on the step
            _value = parseFloat(_value || 0) + step;
            if (props.min !== undefined && +_value < +props.min) _value = +props.min;
            if (props.max !== undefined && +_value > +props.max) _value = +props.max;
            if (onChange) onChange({ target: { name, value: _value } });
        }
        
    }, [props.step, props.min, props.max, onChange, name]);

    // dynamically load the component
    const LazyComponent = useMemo(() => {
        if (typeof component !== "string") return component;
        
        let _component = component;        
        // get the correct component name (some form items share the same component)
        switch (component) {
            case "Switch":
                _component = "Checkbox";
                break;
            case "DateRangePicker":
                _component = "DatePicker";
                break;
            case "TimeRangePicker":
                _component = "TimePicker";
                break;
            case "DateTimeRangePicker":
                _component = "DateTimePicker";
                break;
            /*case "NumberField":
                _component = "TextField";
                break;*/
            default:
                _component = component || "TextField";
                break;
        }
        if (type === "select") _component = "Select";
        
        if (_component === "TextField") return TextField;

        return lazy(() => import(`./${_component}/index.js`));
    },[component, type]);

    // sets up the props for the fields
    const commonProps = useMemo(() =>{
        if (typeof component !== "string") return {
            id: id ? `${id}` : uuid(),
            name,
            value,
            errors,
            ...props
        };

        let _label = t(label, label);
        if (_label && _label[0] === _label[0].toLowerCase()) _label = capitalize(_label);
        let extraProps = {
            id: id ? `${id}` : uuid(),
            label: _label,
            name,
            type,
            required: required || false,
            errors,
            value: value /*|| props?.min*/ || "",
            onChange: handleChange("change"),
            onBlur: handleChange("blur"),
            disabled: loading,
            loading,
            inputRef: itemRef,
            slotProps: {
                ...props?.slotProps,
                input: {
                    ...props?.slotProps?.input,
                    ...InputProps
                }
            }
        };

        if (["TextField", "NumberField", "MoneyField", "PasswordField", "Select", "VideoURL"].includes(component)){
            if (type) extraProps.type = type;
            if (props.minRows > 1) extraProps.multiline = true;
            if (props.maxRows) extraProps.multiline = true;
            if (props.multiline) extraProps.multiline = true;
            if (props.margin) extraProps.margin = props.margin;
            if (props.helperText) {
                extraProps.helperText = t(props.helperText, props.helperText);
                extraProps.FormHelperTextProps = { sx: { whiteSpace: "pre-line" } };
            }
            if (errors) {
                extraProps.error = !!errors;
                extraProps.helperText = errors;
            }
            extraProps.minRows = props.minRows || 1;
            extraProps.variant = props.variant || "outlined";
            extraProps.fullWidth = props.fullWidth || true;
            extraProps.onFocus = e => e.target.select();

            if (props?.startAdornment) {
                extraProps.slotProps = {...extraProps.slotProps, input: {...extraProps?.slotProps?.input, startAdornment: 
                    <InputAdornment position="start">
                        {props.startAdornment}
                    </InputAdornment>
                }};
                delete props.startAdornment;
            }            
            if (props?.endAdornment) {
                extraProps.slotProps = {...extraProps.slotProps, input: {...extraProps?.slotProps?.input, endAdornment: 
                    <InputAdornment position="end">
                        {props.endAdornment}
                    </InputAdornment>
                }};
                delete props.endAdornment;
            }

            if (props?.showControls) extraProps.showControls = props.showControls;

            // for bk compatibility with mui 5
            if (props?.InputProps) extraProps.slotProps = {...extraProps.slotProps, input: {...extraProps?.slotProps?.input, ...props.InputProps}};
            if (props?.inputProps) extraProps.slotProps = {...extraProps.slotProps, htmlInput: {...extraProps?.slotProps?.htmlinput, ...props.inputProps}};
            if (props?.InputLabelProps) extraProps.slotProps = {...extraProps.slotProps, inputLabel: {...extraProps?.slotProps?.inputLabel, ...props.InputLabelProps}};
            if (props?.FormHelperTextProps) extraProps.slotProps = {...extraProps.slotProps, formHelperText: {...extraProps?.slotProps?.formHelperText, ...props.FormHelperTextProps}};
            if (props?.SelectProps) extraProps.slotProps = {...extraProps.slotProps, select: {...extraProps?.slotProps?.select, ...props.SelectProps}};
            if (props?.min) extraProps.slotProps = {...extraProps.slotProps, htmlInput: {...extraProps?.slotProps?.htmlInput, min: props.min}};
            if (props?.max) extraProps.slotProps = {...extraProps.slotProps, htmlInput: {...extraProps?.slotProps?.htmlInput, min: props.max}};
            if (props?.step) extraProps.slotProps = {...extraProps.slotProps, htmlInput: {...extraProps?.slotProps?.htmlInput, min: props.step}};
            
            delete extraProps.loading; // we don't want to pass loading to MUI components
            
        } else {
            extraProps = {...extraProps, ...props};
        }

        if (component === "Switch") extraProps.isSwitch = true;
        if (component === "DateRangePicker" || component === "DateTimeRangePicker" || component === "TimeRangePicker") extraProps.isRangePicker = true;

        if (options) extraProps.options = options;
        if (props.sx) extraProps.sx = props.sx;
        if (props.size) extraProps.size = props.size;
        if (props.color) extraProps.color = props.color;

        // right align the text in the number field
        if (/*component === "NumberField" ||*/ component === "MoneyField" || type === "number" ) {
            extraProps.sx={...extraProps.sx, '& .MuiInputBase-input': {textAlign: 'right'}};
            extraProps.onKeyDown = handleKeyDown;
        }

        // remove props that are not needed
        delete extraProps.rowSize;

        return {...extraProps};
    }, [label, id, name, type, required, errors, loading, value, handleChange, options, InputProps, props, t]);

    return {
        Component: LazyComponent,
        componentProps: commonProps,
        itemRef,
        handleChange,
        handleKeyDown,
    };
}
