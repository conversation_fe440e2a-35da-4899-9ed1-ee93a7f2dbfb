import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Container, Stack, FormHelperText } from '@mui/material';

import { buildTree } from '../../utils';
import Tree from './Tree';

export const ItemSelector = ({ 
    items, // an array of objects [{id, name}, ...] or strings ['tag1', 'tag2']
    selectedItems, // an array of selected items, must match the structure of items array
    onSelect, // callback function to handle selected items
    selectedColor, // color of selected items (default, primary, secondary, error, warning, info, success)
    color, // color of unselected items (default, primary, secondary, error, warning, info, success)
    variant, // variant of the chip (filled, outlined, default)
    disabled, // disable the item selector
    type = "chip", // the component to use for the field, it could be a chip, a button, a checkbox, or an autocomplete
    errors, // errors for the field
    label, // the label for the field (if autocomplete)
    name, // the name of the field (if autocomplete)
    children,
    ...props
}) => {
    const [selected, setSelected] = useState([]);

    const handleSelect = useCallback(item => e => {
        const removeItem = (items, itemToRemove) => {
            let updatedSelected = items.filter(t => itemToRemove?.id ? (t?.id ? t.id !== itemToRemove.id : t !== itemToRemove.id) : (t !== itemToRemove));
            if (Array.isArray(itemToRemove?.children)) {
                itemToRemove.children.forEach(child => {
                    updatedSelected = removeItem(updatedSelected, child);
                });
            }
            return updatedSelected;
        }

        if (item === null) {
            setSelected([]);
            if (onSelect) onSelect([]);
            return;
        }

        // if e.target.value exists, it's coming from an autocomplete which returns the final values
        if (e?.target?.value !== undefined) {
            setSelected(e.target.value);
            if (onSelect) onSelect(e.target.value);
            return;
        }

        // logic for non-autocomplete selections
        let _selected = [...selected];
        const index = selected.findIndex(a => item?.id ? (a?.id ? a?.id === item.id : a === item.id) : (a === item));
        if (index > -1) _selected = removeItem(_selected, item);
        else _selected.push(item);

        setSelected(_selected);
        if (onSelect) onSelect(_selected);
    }, [onSelect, selected]);

    const treeItems = useMemo(() => items?.length > 0 ? buildTree(items) : [], [items]);

    useEffect(() => {
        if (selectedItems?.length && items?.length) {
            setSelected(prev => {
                const _prev = [...prev];
                selectedItems.forEach(selectedItem => {
                    const existingItem = _prev.find(item => item.id === (selectedItem?.id || selectedItem));
                    if (!existingItem) {
                        const itemInItems = items.find(item => item.id === (selectedItem?.id || selectedItem));
                        if (itemInItems) _prev.push(itemInItems);
                    }
                });
                return _prev;
            });
        }
    }, [selectedItems, items]);

    return (
        <Container disableGutters>
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                <Tree 
                    items={treeItems}
                    onSelect={handleSelect} 
                    type={type}
                    variant={variant}
                    color={color}
                    selected={selected}
                    selectedColor={selectedColor}
                    disabled={disabled} 
                    label={label}
                    name={name}
                    {...props}
                />
                {children}
            </Stack>
            {errors && <FormHelperText error>{errors}</FormHelperText>}
        </Container>
    );
}