import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Container } from '@mui/material';
import { eventFlatten } from '../../../../../utils/events';
import { setInfo } from '../../../../../store/reducers/currentShopItemSlice';

import Event from './Event';
/*
This fella takes the events and formats the appropiatelly for the calendar component or the event list
*/
export const ProductEvents = ({ layouts, events, showInfo, slotProps, selectedUsers, onSelect, ...props }) => {
    const dispatch = useDispatch();

    useEffect(() => {
        if (events?.length > 0) dispatch(setInfo({hasEvent: true, eventId: events[0].id}));
    }, [events, dispatch]);

    if (!events || events.length === 0 ) return null;

    const flatEvents = eventFlatten(events);
    const showBasicInfo = showInfo !== undefined ? showInfo : flatEvents?.metaEvents?.length > 1 || flatEvents?.events?.length > 1; // show event info if there is more than one event

    const renderEvents = (events, type=null) => {
        return events?.map(event => (
            <Event 
                key={event.id} 
                event={event} 
                type={type} 
                slotProps={slotProps} 
                showBasicInfo={showBasicInfo} 
                layouts={layouts} 
                onUserSelect={onSelect} 
                selectedUsers={selectedUsers}
            />
        ));
    }

    return (
        <Container disableGutters sx={{my: 1}}>
            {renderEvents(flatEvents?.metaEvents, "meta")}
            {renderEvents(flatEvents?.events)}
        </Container>
    );
};