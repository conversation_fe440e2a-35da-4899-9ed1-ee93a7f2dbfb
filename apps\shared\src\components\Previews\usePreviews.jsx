import React, { useMemo, useState, useCallback } from 'react';
import { usePrint } from '../usePrint';
import { toKebabCase } from '../../utils';

import Print from './Print';

export const usePreviews = ({formats, data, slots, slotProps, styles, ...props}) => {
    const [printRef, handlePrint] = usePrint();
    
    const [format, setFormat] = useState(null); // setting the format here will trigger printing right away
    const [printCount, setPrintCount] = useState(0);

    const handleChangePrintFormat = useCallback(_format => {
        setFormat(_format);
        setPrintCount(prev => prev + 1);
    }, []);


    const printData = useMemo(() => (
        <Print 
            printRef={printRef} 
            format={format} 
            onPrint={handlePrint}
            className={styles[toKebabCase(format)]}
            slots={slots} 
            slotProps={{
                header: {data, className: styles.header}, 
                body: {data, className: styles.body}, 
                footer: {data, className: styles.footer}, 
                portal: {printCount},  /* this will trigger a re-render when the print count changes */    
            }}
        /> 
    ), [printRef, format, handlePrint, styles, slots, data, printCount]);


    return {
        printRef,
        handlePrint,
        format,
        setFormat,
        printCount,
        setPrintCount,
        handleChangePrintFormat,
        printData,
    };
}