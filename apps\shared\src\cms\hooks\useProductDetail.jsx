import { useEffect, useState, useMemo, useCallback } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useApi } from '../../api/useApi';
import { setInfo } from '../../store/reducers/currentShopItemSlice';

export const useProductDetail = ({cartItemId, productId, variantId, ...props}) => {
    const dispatch = useDispatch();
    const cart = useSelector(state => state.cart, shallowEqual);

    const [media, setMedia] = useState([]);

    const [variant, setVariant] = useState(null);
    const [addons, setAddons] = useState(null);
    const [forUserIds, setForUserIds] = useState(null);
    const [giftCardRecipient, setGiftCardRecipient] = useState(null);

    const apiParams = useMemo(() => ({
        enableCache: true, params: {endpoint: `/product`, method: 'POST', data: {
            max_records: 1, product_status_id: 1, include_child_categories: 1, id: productId, include_media: 1,
        }}
    }), [productId]);

    const { fetchData, data, loading, errors} = useApi(apiParams);

    const handleMemoSave = useCallback(text => {
        dispatch(setInfo({memo: text}));
    }, [dispatch]);

    const handleGiftCardChange = useCallback(info => {
        if (!info) return;

        dispatch(setInfo({giftCardRecipient: info}));
        setGiftCardRecipient(info);
    }, [dispatch]);

    const handleVariantChange = useCallback(variant => {
        const _variantId = variant?.id || variant || null;
        if (!_variantId) return;

        const _variant = data?.products?.[0]?.product_variants.find(v => +v.id === +_variantId) || null;
        if (!_variant) return;

        dispatch(setInfo({productVariantId: _variantId, productCustomPrice: null}));
        setVariant(_variant);        
    }, [data?.products?.[0]?.product_variants, dispatch]);

    const handleAddonChange = useCallback(addon => {
        const _addons = addon?.length ? addon : [];
        dispatch(setInfo({addons: _addons.map(a => ({
            id: a.id, 
            name: a.name, 
            variantId: a.variantId || a.variant_id, 
            productId: a.productId || a.product_id, 
            price: +a.price || 0, 
            salePrice: +a.sale_price || 0, 
            tokenPrice: +a.token_price || 0, 
            metadata: a.metadata || {...a}
        }))}));
        setAddons(_addons); 
    }, [dispatch]);

    const handleForUserChange = useCallback(user => {
        const _userIds = user?.length ? user : [];
        dispatch(setInfo({forUserIds: _userIds.map(a => ({
            id: a.id, 
            firstName: a.first_name, 
            lastName: a.last_name, 
            role: a.role_name, dob: a.dob
        })), qty: _userIds.length}));
        setForUserIds(_userIds); 
    }, [dispatch]);

    useEffect(() => {
        if (productId) fetchData();
        if (cartItemId && cart?.cart?.length){
            const _cartItem = cart?.cart?.find(i => i.id === cartItemId) || null;
            if (_cartItem){
                dispatch(setInfo({..._cartItem, productCustomPrice: _cartItem?.metadata?.price || null}));
                setVariant({id: _cartItem.productVariantId});
                setAddons(_cartItem.addons);
                setForUserIds(_cartItem.forUserIds);
                setGiftCardRecipient(_cartItem.giftCardRecipient);
            }
        }
    }, [productId, cartItemId, cart?.cart, dispatch, fetchData]);

    // unify the media to display a single gallery
    useEffect(() => {
        const _media = [];
        // transform product media data so it matches the gallery component
        data?.products?.[0]?.media?.forEach(m => {
            if (m.preview_url) _media.push(m);
            else {
                _media.push({
                    id: m.id,
                    description: m.description,
                    preview_url: m.path,
                    type: m.media_type,
                    metadata: m.metadata,
                });
            }
        });
        // transform product variant media data so it matches the gallery component
        data?.products?.[0]?.product_variants?.forEach(v => {
            if (v?.media){
                v.media.forEach(m => {
                    if (m.preview_url) _media.push(m);
                    else {
                        _media.push({
                            id: m.id,
                            description: m.description,
                            preview_url: m.path,
                            type: m.media_type,
                            metadata: m.metadata,
                        });
                    }
                });
            }
        });
        data?.products?.[0]?.events?.forEach(e => {
            if (e.images) _media.push(...e.images);
        });
        setMedia(_media);
    }, [data?.products?.[0]]);

    // selects the first variant by default
    useEffect(() => {
        if (!variant && data?.products?.[0]?.product_variants?.length){
            let _variant = null;

            if (variantId) _variant = data?.products?.[0].product_variants.find(v => +v.id === +variantId) || null;
            else _variant = data?.products?.[0].product_variants?.[0] || null;

            if (_variant){
                setVariant(_variant);
                dispatch(setInfo({productId: data?.products?.[0]?.id, productVariantId: _variant.id}));
            }
        }
    }, [data?.products?.[0], variantId, variant, dispatch]);    

    return {
        product: data?.products?.[0],
        media,
        productLoading: loading,
        productErrors: errors,
        variant,
        setVariant,
        handleVariantChange,
        forUserIds,
        setForUserIds,
        handleForUserChange,
        giftCardRecipient,
        setGiftCardRecipient,
        handleGiftCardChange,
        addons,
        setAddons,
        handleAddonChange,
        handleMemoSave,
    };
};
