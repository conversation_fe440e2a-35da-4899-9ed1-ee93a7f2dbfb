import { FilterNoneOutlined as VideoIcon } from '@mui/icons-material';

import CmsIcon from '../../../utils/CmsIcon';
import { BasicCentered } from './BasicCentered';
import { MediaFirst } from './MediaFirst';
import { MediaRight } from './MediaRight';
import { MediaLeft } from './MediaLeft';
import { MediaOverlay } from './MediaOverlay';

export const widgetIcon = VideoIcon;

export const layouts = [
    {
        id: 1,
        name: 'Centered',
        component: BasicCentered,
        icon: CmsIcon({
            iconProps: {height: 48, width: 48, direction: 'column', spacing: 2},
            elements: [
                {type: 'text', width: '70%', mx: 'auto'},
                {type: 'text', width: '50%', mx: 'auto'},
                {type: 'rectangle', width: '100%', mx: 'auto', height: 30},
            ],
        }),
        slotProps: {
            cmsStack: {
                spacing: 2,
            },
            title: {
                textAlign: 'center',
            },
            subtitle: {
                textAlign: 'center',
            },
            body: {
                textAlign: 'center',
            }
        }
    },
    {
        id: 2,
        name: 'Media First',
        component: MediaFirst,
        icon: CmsIcon({
            iconProps: {height: 48, width: 48, direction: 'column', spacing: 2},
            elements: [
                {type: 'rectangle', width: '100%', height: 30},
                {type: 'text', width: '70%'},
                {type: 'text', width: '50%'},
            ],
        }),
        slotProps: {
            cmsStack: {
                spacing: 2,
            }
        }
    },
    {
        id: 3,
        name: 'Media Right',
        component: MediaRight,
        icon: CmsIcon({
            iconProps: {height: 48, width: 56, direction: 'row', spacing: 2},
            elements: [
                {type: 'group', direction: 'column', spacing: 2, width: '50%', children: [
                    {type: 'text', width: '90%'},
                    {type: 'text', width: '70%'},
                    {type: 'text', width: '50%'},
                ]},
                {type: 'rectangle', width: '50%', height: 30, alignSelf: 'center'},
            ],
        }),
        slotProps: {
            cmsStack: {
                spacing: 4,
            }
        }
    },
    {
        id: 4,
        name: 'Media Left',
        component: MediaLeft,
        icon: CmsIcon({
            iconProps: {height: 48, width: 56, direction: 'row', spacing: 2},
            elements: [
                {type: 'rectangle', width: '50%', height: 30, alignSelf: 'center'},
                {type: 'group', direction: 'column', spacing: 2, width: '50%', children: [
                    {type: 'text', width: '90%'},
                    {type: 'text', width: '70%'},
                    {type: 'text', width: '50%'},
                ]},
            ],
        }),
        slotProps: {
            cmsStack: {
                spacing: 4,
            }
        }
    },
    {
        id: 5,
        name: 'Media Overlay',
        component: MediaOverlay,
        icon: CmsIcon({
            solid: false,
            iconProps: {height: 48, width: 48, direction: 'column', spacing: 2},
            elements: [
                {type: 'rectangle', width: '100%', height: 30, alignSelf: 'center', position: 'relative'} ,
                {type: 'group', direction: 'column', spacing: 2, width: '100%', sx: {position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: '100%', alignItems: 'center'}, children: [
                    {type: 'text', width: '40%'},
                    {type: 'text', width: '20%'},
                ]},
            ],
        }),
        slotProps: {
            overlay: {
                sx: {
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    p: 3
                }
            },
            title: {
                color: 'white'
            },
            subtitle: {
                color: 'white'
            },
            body: {
                color: 'white'
            }
        }
    }
];