import { combineReducers, configureStore, createReducer } from '@reduxjs/toolkit';
import cmsStore, { rootReducer as cmsReducer } from '../../apps/cms/src/store/store';
import sharedStore, { rootReducer as sharedReducer } from '../../apps/shared/src/store/store';
import sitebossStore, { rootReducer as sitebossReducer } from '../../apps/siteboss/src/store/store';
import posStore, { rootReducer as posReducer } from '../../apps/pos/src/store/store';
import websiteStore, { rootReducer as websiteReducer } from '../../apps/website/src/store/store';

export const mockStore=(title, init= null)=>{
    let initialState;
    let rootReducer;
    if(title?.toLowerCase()?.includes("cms")) {
        initialState = {...cmsStore.getState(), ...init};
        rootReducer = createReducer(init, ()=>{
            cmsReducer, init
        })
    }
    else if(title?.toLowerCase()?.includes("shared")){
        initialState = {...sharedStore.getState(), ...init}
        rootReducer = createReducer(init, ()=>{
            sharedReducer, init
        })
    }
    else if(title?.toLowerCase()?.includes("siteboss")){ 
        initialState = {...sitebossStore.getState(), ...init}
        rootReducer = createReducer(init, ()=>{
            sitebossReducer, init
        })
    }
    else if(title?.toLowerCase()?.includes("pos")){
        initialState = {...posStore.getState(), ...init}
        rootReducer = createReducer(init, ()=>{
            posReducer, init
        })
    }
    else if (title?.toLowerCase()?.includes("website")){
        initialState = {...websiteStore.getState(), ...init}
        rootReducer = createReducer(init, ()=>{
            websiteReducer, init
        })
    }
    else{
        initialState={
            ...cmsStore.getState(),
            ...sharedStore.getState(),
            ...sitebossStore.getState(),
            ...posStore.getState(),
            ...websiteStore.getState(),
            ...init
        }
        rootReducer = createReducer(init, ()=>{
            cmsReducer,
            sharedReducer,
            sitebossReducer,
            posReducer,
            websiteReducer,
            init
        })
    }

    return configureStore({
        reducer: rootReducer,
        preloadedState: initialState
    })
}