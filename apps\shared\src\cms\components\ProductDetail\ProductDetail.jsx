import React from 'react';
import { Stack } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { PosProductDetailProvider } from '../../hooks';

import { layouts, widgetIcon } from './Layouts';
import { properties } from './properties';

import { useProductDetail } from './useProductDetail';

/*
This is the product detail view, which should show a picture, prices, etc. 
In the POS, it should load in a modal, but it could also be used in a regular page like for an e-commerce shop. 
The layout is controlled via slotProps, which is passed to the children components. For example you may want to load the product variants as buttons (on the POS) or as radio buttons (for an ecommerse site).
*/
export const ProductDetail = ({
    id,
    loadType = 2, // 1 = automatic, 2 = manual
    productId,
    variantId,
    onAddToCart,
    modalSize = null,
    fullPage = true,
    gallery = {
        layoutType: 'carousel', // carousel, list, masonry, 
    },
    variants = {
        layoutType: 'radio', // radio, button
    },
    addons = {
        layoutType: 'checkbox', // radio, checkbox, button
    },
    eventUsers = {
        layoutType: 'checkbox', // button, radio, checkbox
    },
    memo = {
        layoutType: 'link', // link, button
    },
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        body: {},           // MUI stack props
        events: {
            caption: {},    // MUI typography props
            text: {},       // MUI typography props
        },
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    builderProps,
    replacePageTitle,       // the page title to be replaced with the item's name
    children,
    ...props
}) => {
    const {
        productDetailId,
        productDetailSlug,
        cartItemId,
        reduxStore,
        handleAddToCart,
        Layout,
    } = useProductDetail({ onAddToCart, layouts, layoutId });
    
    const { slotProps: updatedSlotProps, isMobile, t, canRender, customCss, noContent } = prepareComponent({name: 'product-detail', layoutId, layouts, slotProps, isBuilder, condition, widgetIcon, localState: {
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;


    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={2} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {((!productDetailId && !productId) || isBuilder) ? noContent :
                    <PosProductDetailProvider 
                        cartItemId={cartItemId} 
                        productId={+loadType === 2 ? productId : productDetailId} 
                        variantId={variantId} 
                        fullPage={fullPage} 
                        modalSize={modalSize || "sm"}
                    >
                        <Layout
                            slotProps={{...slotProps, 
                                header: {
                                    ...slotProps?.header, 
                                    elevation: fullPage ? 0 : undefined, 
                                    sx: {...slotProps?.header?.sx, backgroundColor: fullPage ? 'transparent' : undefined}
                                }, 
                                footer: {
                                    ...slotProps?.footer, 
                                    elevation: fullPage ? 0 : undefined, 
                                    sx: {...slotProps?.footer?.sx, backgroundColor: fullPage ? 'transparent' : undefined}
                                },
                                gallery,
                                variants,
                                addons,
                                eventUsers,
                                memo
                            }}
                            onAddToCart={handleAddToCart}
                            isEdit={Boolean(cartItemId)}
                            replacePageTitle={replacePageTitle || false}
                        />
                    </PosProductDetailProvider>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};