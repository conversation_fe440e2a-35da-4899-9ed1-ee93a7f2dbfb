/* ORDER FORMAT */
.order {
    &.header{
        flex-direction: row !important;
    }
    
    &.footer{
        flex-direction: row !important;
    }
}

/* TICKET FORMAT */
.ticket,
.kitchen {
    width: 320px !important;

    &.header {
        flex-direction: column !important;
        font-size: 1rem;

        * {
            font-size: 1rem !important;
        }

        svg {
            display:none;
        }

        div{
            &:last-of-type {
                width: 100%;
                order: 1;
                
            }
            &:first-of-type {
                width: 100%;
                order: 2;
            }
        }
    }

    &.body{
        * {
            font-size: 1rem !important;
        }

        td {
            border-color: #e0e0e0 !important;
            word-wrap: break-all;
            white-space: break-word;
        }        

        thead{
            tr {
                th {
                    padding-top: 4px;
                    padding-bottom: 4px;
                    background-color: #fff !important;
                    color: #000 !important;
                    border: 0;
                    border-bottom: 1px solid #e0e0e0;
                }
            }
        }
    }

    &.footer{
        flex-direction: column !important;

        *{
            font-size: 1rem !important;
        }


        td {
            border-color: #e0e0e0 !important;
            word-wrap: break-word;
            white-space: break-word;
        }        

        thead{
            tr {
                th {
                    padding-top: 4px;
                    padding-bottom: 4px;
                    background-color: #fff !important;
                    color: #000 !important;
                    border: 0;
                    border-bottom: 1px solid #e0e0e0;
                }
            }
        }

        > div{
            width: 100%;
            
            &:last-of-type {
                width: 100%;
                order: 1;                
            }
            &:first-of-type {
                width: 100%;
                order: 2;
            }
        }

    }
}

/* KITCHEN FORMAT */
.kitchen{
    *{
        font-size: 1.2rem;
    }

    &.header {
        *{
            font-size: 1.2rem;
        }
    
        div{
            &:last-of-type{
                display: none;
            }

            &:first-of-type{
                h6:not(:global(.always-visible)){
                   display:none;
                }
            }
        }
    }

    &.body{
        *{
            font-size: 1.2rem;
        }    
    }

    &.footer {
        display:none;
    }
}
