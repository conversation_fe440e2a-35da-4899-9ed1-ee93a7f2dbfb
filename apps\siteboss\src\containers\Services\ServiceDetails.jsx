import React, { useState } from 'react';
import { useNavigate, useOutletContext } from 'react-router-dom';
import { 
    Paper, 
    Box, 
    Typography, 
    Grid, 
    Button, 
    Chip, 
    Divider, 
    List, 
    ListItem, 
    ListItemText,
    ListItemIcon,
    Skeleton,
    Card,
    CardContent,
    CardHeader
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import EventIcon from '@mui/icons-material/Event';
import { Confirm } from '@siteboss-frontend/shared/components';
import { formatDate } from '@siteboss-frontend/shared/utils';

const ServiceDetails = ({ service, loading, onEdit, onDelete }) => {
    const { t } = useOutletContext();
    const navigate = useNavigate();
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

    // Handle delete confirmation
    const handleDeleteClick = () => {
        setDeleteConfirmOpen(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = () => {
        onDelete();
        setDeleteConfirmOpen(false);
    };

    // Loading skeleton
    if (loading || !service) {
        return (
            <Paper sx={{ p: 3 }}>
                <Skeleton variant="text" height={60} width="50%" />
                <Skeleton variant="text" height={30} width="70%" />
                <Box sx={{ mt: 3 }}>
                    <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                            <Skeleton variant="rectangular" height={200} />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Skeleton variant="rectangular" height={200} />
                        </Grid>
                    </Grid>
                </Box>
            </Paper>
        );
    }

    return (
        <>
            <Paper sx={{ p: 3, mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                        <Typography variant="h4" component="h1" gutterBottom>
                            {service.name}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Chip 
                                label={service.status === 1 ? t('service:active') : t('service:inactive')}
                                color={service.status === 1 ? 'success' : 'default'}
                                sx={{ mr: 1 }}
                            />
                            <Typography variant="body2" color="text.secondary">
                                ID: {service.id}
                            </Typography>
                        </Box>
                        <Typography variant="body1" paragraph>
                            {service.description}
                        </Typography>
                    </Box>
                    <Box>
                        <Button 
                            variant="outlined" 
                            startIcon={<EditIcon />} 
                            onClick={onEdit}
                            sx={{ mr: 1 }}
                        >
                            {t('general:edit')}
                        </Button>
                        <Button 
                            variant="outlined" 
                            color="error" 
                            startIcon={<DeleteIcon />}
                            onClick={handleDeleteClick}
                        >
                            {t('general:delete')}
                        </Button>
                    </Box>
                </Box>

                <Divider sx={{ my: 3 }} />

                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                        <Card>
                            <CardHeader title={t('service:serviceDetails')} />
                            <CardContent>
                                <List>
                                    <ListItem>
                                        <ListItemIcon>
                                            <AccessTimeIcon />
                                        </ListItemIcon>
                                        <ListItemText 
                                            primary={t('service:duration')} 
                                            secondary={`${service.block_minutes} ${t('service:minutes')}`} 
                                        />
                                    </ListItem>
                                    <ListItem>
                                        <ListItemIcon>
                                            <AttachMoneyIcon />
                                        </ListItemIcon>
                                        <ListItemText 
                                            primary={t('service:price')} 
                                            secondary={`$${service.default_price}`} 
                                        />
                                    </ListItem>
                                    <ListItem>
                                        <ListItemIcon>
                                            <EventIcon />
                                        </ListItemIcon>
                                        <ListItemText 
                                            primary={t('service:bookingNotice')} 
                                            secondary={`${service.min_booking_notice} ${t('service:minutes')}`} 
                                        />
                                    </ListItem>
                                </List>
                            </CardContent>
                        </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Card>
                            <CardHeader title={t('service:availability')} />
                            <CardContent>
                                <List>
                                    {service.start_date && (
                                        <ListItem>
                                            <ListItemIcon>
                                                <EventIcon />
                                            </ListItemIcon>
                                            <ListItemText 
                                                primary={t('service:startDate')} 
                                                secondary={formatDate(service.start_date)} 
                                            />
                                        </ListItem>
                                    )}
                                    {service.end_date && (
                                        <ListItem>
                                            <ListItemIcon>
                                                <EventIcon />
                                            </ListItemIcon>
                                            <ListItemText 
                                                primary={t('service:endDate')} 
                                                secondary={formatDate(service.end_date)} 
                                            />
                                        </ListItem>
                                    )}
                                    <ListItem>
                                        <ListItemIcon>
                                            <LocationOnIcon />
                                        </ListItemIcon>
                                        <ListItemText 
                                            primary={t('service:locations')} 
                                            secondary={
                                                service.location_ids && service.location_ids.length > 0 
                                                    ? service.location_ids.join(', ') 
                                                    : t('service:noLocations')
                                            } 
                                        />
                                    </ListItem>
                                </List>
                            </CardContent>
                        </Card>
                    </Grid>
                    <Grid item xs={12}>
                        <Card>
                            <CardHeader title={t('service:managers')} />
                            <CardContent>
                                {service.managers && service.managers.length > 0 ? (
                                    <List>
                                        {service.managers.map((manager) => (
                                            <ListItem key={manager.id}>
                                                <ListItemIcon>
                                                    <PersonIcon />
                                                </ListItemIcon>
                                                <ListItemText 
                                                    primary={`${manager.first_name} ${manager.last_name}`} 
                                                    secondary={manager.email} 
                                                />
                                            </ListItem>
                                        ))}
                                    </List>
                                ) : (
                                    <Typography variant="body2" color="text.secondary">
                                        {t('service:noManagers')}
                                    </Typography>
                                )}
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </Paper>

            <Confirm
                open={deleteConfirmOpen}
                onClose={() => setDeleteConfirmOpen(false)}
                onConfirm={handleDeleteConfirm}
                title={t('service:deleteConfirmTitle')}
                content={t('service:deleteConfirmMessage', { name: service.name })}
            />
        </>
    );
};

export default ServiceDetails;
