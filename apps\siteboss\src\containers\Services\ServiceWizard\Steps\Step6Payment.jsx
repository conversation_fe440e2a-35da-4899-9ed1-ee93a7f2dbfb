import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, TextField, Box, InputAdornment } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step6 } from './stepList';

const Step6Payment = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const [localFormData, setLocalFormData] = useState(formData || {});

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        // Convert string values to numbers for price field
        const parsedValue = field === 'default_price' 
            ? parseFloat(value) || 0 
            : value;

        setLocalFormData(prev => {
            const updated = { ...prev, [field]: parsedValue };
            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Get step fields
    const fields = step6(localFormData);

    return (
        <Grid container spacing={3}>
            {fields.map(section => (
                <Grid item xs={12} key={section.id}>
                    <Typography variant="h6" gutterBottom>
                        {section.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                        {section.description}
                    </Typography>
                    <Grid container spacing={3}>
                        {section.fields.map(field => (
                            <Grid item xs={12} md={6} key={field.name}>
                                {field.component === "TextField" && (
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {field.label}
                                        </Typography>
                                        <TextField
                                            fullWidth
                                            name={field.name}
                                            type={field.name === 'default_price' ? 'number' : 'text'}
                                            value={localFormData[field.name] !== undefined ? localFormData[field.name] : ''}
                                            onChange={(e) => handleChange(field.name, e.target.value)}
                                            required={field.required}
                                            margin={field.margin}
                                            disabled={loading}
                                            error={field.error}
                                            helperText={field.helperText}
                                            InputProps={field.name === 'default_price' ? {
                                                startAdornment: (
                                                    <InputAdornment position="start">
                                                        <AttachMoneyIcon />
                                                    </InputAdornment>
                                                ),
                                                inputProps: { step: 0.01, min: 0 }
                                            } : undefined}
                                        />
                                    </Box>
                                )}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default Step6Payment;
