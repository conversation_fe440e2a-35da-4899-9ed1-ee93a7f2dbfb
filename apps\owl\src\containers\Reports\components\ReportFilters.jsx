import {
  Paper,
  Box,
  Typography,
  Grid2 as Grid,
  <PERSON><PERSON>,
  Chip,
  Stack
} from '@mui/material';
import {
  FilterAltOutlined as FilterIcon,
  ClearOutlined as ClearIcon
} from '@mui/icons-material';
import { FormItem } from '@siteboss-frontend/shared/components';

export const ReportFilters = ({
  filters = {},
  onFiltersChange,
  onClearFilters,
  dateRange = true,
  statusFilter = false,
  customerFilter = false,
  locationFilter = false,
  customFilters = [],
  ...props
}) => {
  const handleFilterChange = (key, value) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const handleClearAll = () => {
    onClearFilters();
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => 
      value !== null && value !== undefined && value !== ''
    ).length;
  };

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <Paper sx={{ p: 3, mb: 3 }} {...props}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FilterIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            Filters
          </Typography>
          {getActiveFiltersCount() > 0 && (
            <Chip
              label={`${getActiveFiltersCount()} active`}
              size="small"
              color="primary"
              sx={{ ml: 1 }}
            />
          )}
        </Box>
        {getActiveFiltersCount() > 0 && (
          <Button
            startIcon={<ClearIcon />}
            onClick={handleClearAll}
            size="small"
            variant="outlined"
          >
            Clear All
          </Button>
        )}
      </Box>

        <Grid container spacing={2}>
          {/* Date Range */}
          {dateRange && (
            <>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <FormItem
                  component="DatePicker"
                  label="Start Date"
                  name="startDate"
                  value={filters.startDate || null}
                  onChange={(value) => handleFilterChange('startDate', value)}
                  size="small"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <FormItem
                  component="DatePicker"
                  label="End Date"
                  name="endDate"
                  value={filters.endDate || null}
                  onChange={(value) => handleFilterChange('endDate', value)}
                  size="small"
                />
              </Grid>
            </>
          )}

          {/* Status Filter */}
          {statusFilter && (
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <FormItem
                component="Select"
                label="Status"
                name="status"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                size="small"
                options={[
                  { id: '', name: 'All Statuses' },
                  ...statusOptions.map(option => ({ id: option.value, name: option.label }))
                ]}
              />
            </Grid>
          )}

          {/* Customer Filter */}
          {customerFilter && (
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <FormItem
                component="TextField"
                label="Customer"
                name="customer"
                value={filters.customer || ''}
                onChange={(e) => handleFilterChange('customer', e.target.value)}
                size="small"
                placeholder="Search customers..."
              />
            </Grid>
          )}

          {/* Location Filter */}
          {locationFilter && (
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <FormItem
                component="Select"
                label="Location"
                name="location"
                value={filters.location || ''}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                size="small"
                options={[
                  { id: '', name: 'All Locations' },
                  { id: 'warehouse-1', name: 'Warehouse 1' },
                  { id: 'warehouse-2', name: 'Warehouse 2' },
                  { id: 'distribution-center', name: 'Distribution Center' }
                ]}
              />
            </Grid>
          )}

          {/* Custom Filters */}
          {customFilters.map((filter, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
              <FormItem
                component={filter.type === 'select' ? 'Select' : 'TextField'}
                label={filter.label}
                name={filter.key}
                value={filters[filter.key] || ''}
                onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                size="small"
                placeholder={filter.placeholder}
                options={filter.type === 'select' ? [
                  { id: '', name: `All ${filter.label}` },
                  ...filter.options.map(option => ({ id: option.value, name: option.label }))
                ] : undefined}
              />
            </Grid>
          ))}
        </Grid>

      {/* Quick Date Filters */}
      {dateRange && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Quick Filters:
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {[
              { label: 'Today', days: 0 },
              { label: 'Yesterday', days: 1 },
              { label: 'Last 7 days', days: 7 },
              { label: 'Last 30 days', days: 30 },
              { label: 'This month', days: 'month' },
              { label: 'Last month', days: 'lastMonth' }
            ].map((quick) => (
              <Chip
                key={quick.label}
                label={quick.label}
                size="small"
                variant="outlined"
                clickable
                onClick={() => {
                  const today = new Date();
                  let startDate, endDate;
                  
                  if (quick.days === 0) {
                    startDate = endDate = today;
                  } else if (quick.days === 1) {
                    startDate = endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
                  } else if (typeof quick.days === 'number') {
                    startDate = new Date(today.getTime() - quick.days * 24 * 60 * 60 * 1000);
                    endDate = today;
                  } else if (quick.days === 'month') {
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = today;
                  } else if (quick.days === 'lastMonth') {
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                  }
                  
                  onFiltersChange({
                    ...filters,
                    startDate,
                    endDate
                  });
                }}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Paper>
  );
};

export default ReportFilters;
