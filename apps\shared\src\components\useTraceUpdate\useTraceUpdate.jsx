import { useEffect, useRef } from 'react';

/* 
This component is used to track changes in props 
Use:
useTraceUpdate({prop1, prop2, prop3});
*/
export const useTraceUpdate = props => {
    const prev = useRef(props);
    
    useEffect(() => {
        const changedProps = Object.entries(props).reduce((ps, [k, v]) => {
            if (prev.current[k] !== v) ps[k] = [prev.current[k], v];
            return ps;
        }, {});

        if (Object.keys(changedProps).length > 0) {
            console.log('Changed:', changedProps);
        }

        prev.current = props;
    });
}