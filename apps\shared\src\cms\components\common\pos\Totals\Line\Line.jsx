import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack, Typography, Collapse, useMediaQuery } from '@mui/material';
import { ChevronRightOutlined as ExpandIcon } from '@mui/icons-material';
import { createCurrencyFormatter } from '../../../../../../utils/currency';

import styles from './Line.module.scss';

export const Line = ({label, amount, metadata, variant, slots, slotProps}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const [open, setOpen] = useState(false);
    const handleClick = () => setOpen(!open);

    const hasOptions = Boolean(slots?.options);
    const Icon = slots?.expandIcon || ExpandIcon;

    return (
        <>
            {label &&
                <Stack 
                    direction="row" 
                    justifyContent="space-between" 
                    alignItems="center" 
                    style={hasOptions ? {cursor: 'pointer'} : undefined} 
                    onClick={hasOptions ? handleClick : undefined}
                >
                    <div className={hasOptions ? styles.wrapper : undefined}>
                        {hasOptions && 
                            <div className={styles.icon}>
                                <Icon fontSize='small' sx={{
                                    transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.3s ease-in-out',
                                }} />
                            </div>
                        }
                        <Typography component="span" variant={variant || "subtitle1"} {...slotProps}>{t(label, label)}</Typography>
                    </div>
                    <Typography variant={variant || "subtitle1"} {...slotProps}>{currencyFormatter.format(amount, currency)}</Typography>
                </Stack>
            }
            {slots?.options &&
                <Collapse in={open} timeout="auto" unmountOnExit>
                    {slots?.options(metadata)}
                </Collapse>
            }
        </>
    );
}