import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { apiSlice, languageReducer, userReducer, colorModeReducer, companyReducer, permissionReducer, apiCacheReducer } from '@siteboss-frontend/shared/store';
import fixedDataReducer from './reducers/fixedDataSlice';

const rootReducer = combineReducers({
    api: apiSlice.reducer,
    colorMode: colorModeReducer,
    language: languageReducer,
    user: userReducer,
    company: companyReducer,
    permission: permissionReducer,
    apiCache: apiCacheReducer,
    fixedData: fixedDataReducer,
});

export default configureStore({
reducer: rootReducer,
middleware: getDefaultMiddleware =>
    getDefaultMiddleware().concat(apiSlice.middleware),
});
