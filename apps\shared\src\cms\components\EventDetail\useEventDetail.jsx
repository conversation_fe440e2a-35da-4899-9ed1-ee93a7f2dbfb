import { useMemo, useContext, useEffect, useState, useCallback, useLayoutEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';

import { useEventData } from '../../../components';
import { formatSlug, isUuid } from '../../../utils';
import { PosContext, PosProductDetailContext } from '../../hooks';
import { setInfo } from '../../../store/reducers/currentShopItemSlice';

export const useEventDetail = ({
    //shopId, // this will tell us the routes of the other pages we need to see event datails, cart page, etc
    eventId, // upcoming, past, all
    eventType, // automatic or manual
    redirectToCart = true,
    isBuilder,
    ...props
}) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const { id: eventDetailId, slug: eventDetailSlug, extra: cartItemId } = useParams();
    const user = useSelector(state => state.user);

    const { handleAddToCart: handleAddToShopCart = () => {}, handleUserSelection = () => {}, loadLatestOrder = () => {}, reduxStore, selectedUser, router, registerId, cart } = useContext(PosContext) || {};
    const { handleSelectProduct = () => {} } = useContext(PosProductDetailContext) || {};
    
    const [formattedEvent, setFormattedEvent] = useState(null);
    const [eventProductId, setEventProductId] = useState(null);

    /*const realId = useMemo(() => {
        if (eventDetailId){
            const isEdit = isUuid(eventDetailId);
            if (isEdit) {
                // get the eventId from the cart
                const item = cart?.cart?.find(a => a.id === eventDetailId);
                if (item?.eventId) {
                    console.log(item)
                    dispatch(setInfo(item));
                    return item.eventId;
                }
            } else if (+eventType === 1) return eventDetailId;
        } 
        return +eventType === 2 ? eventId : null;
    }, [eventId, eventDetailId, eventType, cart?.cart, dispatch]);*/

    const eventFilters = useMemo(() => ({
        id: +eventType === 2 ? eventId : eventDetailId,
        event_status_id: [2, 3, 4], 
        max_records: 1, 
        include_media: 1, 
    }), [eventType, eventId, eventDetailId]);

    const { eventData, ErrorBar, loading } = useEventData({initialDate: (!isBuilder && (+eventType === 2 && !isEdit ? eventId : eventDetailId)) ? new Date(2000, 1, 1) : undefined, enableCache: true, filters: eventFilters});

    const cartRoute = useMemo(() => {
        let route = null;
        if (router?.cart) {
            route = `/${formatSlug(`${router?.slug?.value}/${router?.cart?.value}`)}`;
        }
        return route;
    }, [router]);

    const handleSelectUser = useCallback(user => {
        handleUserSelection(user);
    }, [handleUserSelection, selectedUser]);

    const handleAddToCart = useCallback(async (product, reduxState) => {
        await handleAddToShopCart(product, reduxState);
        if (redirectToCart && cartRoute) navigate(cartRoute);
    }, [handleAddToShopCart, navigate, cartRoute, redirectToCart]);

    useEffect(() => {
        if ((eventData?.events?.length > 0 || eventData?.metaEvents?.length > 0) && !formattedEvent){
            const _formattedEvent = [...eventData?.events, ...eventData?.metaEvents];
            setFormattedEvent(_formattedEvent);
            setEventProductId(_formattedEvent?.[0]?.metadata?.product_id);
            handleSelectProduct({id: _formattedEvent?.[0]?.metadata?.product_id, eventId: _formattedEvent?.[0]?.metadata?.id});
            dispatch(setInfo({hasEvent: true, eventId: _formattedEvent?.[0].id}));
        }
    }, [eventData, formattedEvent, handleSelectProduct, dispatch]);

    useEffect(() => {
        if (!cart?.fakeOrderId) {
            loadLatestOrder(selectedUser?.id, registerId);
        }
    }, [loadLatestOrder, selectedUser, registerId, cart?.fakeOrderId]);    

    useEffect(() => {
        if (user?.token && !selectedUser) {
            handleUserSelection(user?.profile);
        }
    }, [user, handleUserSelection, selectedUser]);

    useLayoutEffect(() => {
        if (props?.replacePageTitle && (eventData?.events?.length > 0 || eventData?.metaEvents?.length > 0)) {
            document.title = props.replacePageTitle?.replace(/\{([^}]+)\}/g, [...eventData?.events, ...eventData?.metaEvents]?.[0]?.title || '');
        }
    }, [eventData, props?.replacePageTitle]);

    return {
        eventProductId,
        formattedEvent,
        cartRoute,
        handleAddToCart,
        handleSelectUser,
        loading,
        ErrorBar,
        reduxStore,
        selectedUser,
        cartItemId: isUuid(cartItemId) && cart?.cart?.find(a => a.id === cartItemId) ? cartItemId : null,
    };
};
