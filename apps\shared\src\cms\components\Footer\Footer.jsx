import React from 'react';
import { Stack, Typography } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts } from './layouts';
import { properties } from './properties';

export const Footer = React.forwardRef(({
    id,
    title,
    copyRight,
    showMenu,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        title: {},          // MUI typography props
        copyRight: {},      // MUI typography props
        menu: {},           // MUI typography props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    children,
    builderProps,
    ...props
}, ref) => {
    const parentBuilder = isBuilder; // this is used to determine if the page that is loading this component is in a builder
    if (+props.pageTypeId !== 9) isBuilder = false; // only templates can interact
    const { slotProps: updatedSlotProps, isMobile, canRender, customCss } = prepareComponent({name: "footer", layoutId, layouts, slotProps, isBuilder: parentBuilder, condition});
    slotProps = updatedSlotProps;

    if (!canRender) return null;
    
    return (
        <CmsContainer 
            ref={ref}
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                <Stack spacing={2} direction="row" useFlexGap>
                </Stack>
                <Typography variant="h5" {...slotProps?.title}>
                    {title}
                </Typography>
                <Typography variant="body3" {...slotProps?.copyRight}>
                    {copyRight}
                </Typography>
            </Stack>
            {children}
        </CmsContainer>
    );
});