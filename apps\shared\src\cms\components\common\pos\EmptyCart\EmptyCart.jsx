import React, { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Container, Button, Typography, useMediaQuery } from '@mui/material';

import { PosContext } from '../../../../hooks/PosContext';
import { formatSlug } from '../../../../../utils/cms';

export const EmptyCart = ({showBackButton}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));        
    const { router } = useContext(PosContext) || {};

    const productsRoute = useMemo(() => {
        let route = null;
        if (router) route=`/${formatSlug(`${router?.slug?.value}/${router?.products?.value}`)}`;
        return route;
    }, [router]);

    return (
        <Container sx={{display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", height: 200}}>
            <Typography variant={isMobile ? "h4" : "h4"}>{t("order:cartEmpty")}</Typography>
            {showBackButton &&
                <Button component={Link} variant={isMobile? "contained" : "text"} color="primary" to={productsRoute || "#!"} sx={{my: 2}}>
                    {t("order:continueShopping")}
                </Button>
            }
        </Container>
    );
}