import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { 
	Card, 
	CardActionArea, 
	CardContent, 
	CardMedia, 
	CardActions,
	Typography, 
	Checkbox,
	Chip,
	Stack,
	Box
} from '@mui/material';
import { formatDateTime } from '@siteboss-frontend/shared/utils';

export const ProgramCard = ({ 
	program, 
	selected, 
	onSelect, 
	onExpand, 
	onDelete,
	...props 
}) => {
	const { t, language } = useOutletContext();
	
	const handleCardClick = (e) => {
		if (onExpand) onExpand(e, program);
	};
	
	const handleCheckboxClick = (e) => {
		e.stopPropagation();
		if (onSelect) onSelect(e, program);
	};
	
	const handleDeleteClick = (e) => {
		e.stopPropagation();
		if (onDelete) onDelete(e, program);
	};

	// Get image URL or use placeholder
	const imageUrl = program.metadata.images?.[0]?.preview_url || 
		`https://placehold.co/300?font=source-sans-pro&text=${encodeURIComponent(program.name)}`;

	return (
		<Card 
			sx={{ 
				height: '100%', 
				display: 'flex', 
				flexDirection: 'column',
				position: 'relative',
				border: selected ? '2px solid' : '1px solid',
				borderColor: selected ? 'primary.main' : 'divider',
			}}
			data-cy={`program-card-${program.id}`}
		>
			<Box sx={{ position: 'absolute', top: 8, left: 8, zIndex: 1 }}>
				<Checkbox 
					checked={selected}
					onClick={handleCheckboxClick}
					color="primary"
					sx={{ 
						bgcolor: 'rgba(255, 255, 255, 0.7)', 
						borderRadius: '50%',
						'&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' } 
					}}
				/>
			</Box>
			
			<CardActionArea 
				onClick={handleCardClick}
				sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
			>
				<CardMedia
					component="img"
					height="140"
					image={imageUrl}
					alt={program.name}
					sx={{ objectFit: 'cover' }}
				/>
				<CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
					<Typography gutterBottom variant="h6" component="div" noWrap>
						{program.name}
					</Typography>
					
					{program.metadata.short_description && (
						<Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
							{program.metadata.short_description}
						</Typography>
					)}
					
					{program.metadata.group_types?.length > 0 && (
						<Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 'auto' }}>
							{program.metadata.group_types.map(group => (
								<Chip 
									key={`group-${group.id}`} 
									label={group.name} 
									size="small" 
									variant="outlined"
									sx={{ mb: 1 }}
								/>
							))}
						</Stack>
					)}
					
					{program.metadata.children?.length > 0 && (
						<Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
							{t('program:childEvents', { count: program.metadata.children.length })}
						</Typography>
					)}
				</CardContent>
			</CardActionArea>
			
			<CardActions sx={{ justifyContent: 'space-between', borderTop: '1px solid', borderColor: 'divider' }}>
				<Typography variant="caption" color="text.secondary">
					ID: {program.id}
				</Typography>
				<Typography variant="caption" color="text.secondary">
					{program.metadata.event_status_name}
				</Typography>
			</CardActions>
		</Card>
	);
};

export default ProgramCard;
