import { useState, useEffect, useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useForm } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import { useCollateral, useWine, useFood, useMerchandise, useKit } from './ProductTypes';
import { MerchantSelector } from '../../../components/MerchantSelector';

const rows = [
    {id: 1, title: 'product:toolbar.basic', size: {xs: 12}},
    {id: 2, title: 'product:toolbar.properties', size: {xs: 12, md: 6}},
    {id: 3, title: 'product:toolbar.handling', size: {xs: 12, sm: 6, md: 4, lg: 3}},
];

const fields = [
    {name: 'sku', type: 'text', label: 'product:sku', required: true, value: '', component: "CustomSKU", margin: "normal", row: rows[0].id},
    {name: 'name', type: 'text', label: 'product:name', required: true, value: '', component: "TextField", margin: "normal", row: rows[0].id},
    {name: 'brand', type: 'text', label: 'product:brand', required: true, value: '', component: "TextField", margin: "normal", row: rows[0].id, rowSize: {xs: 12, md: 6}},
    {name: 'product_type', label: 'product:productType', required: true, value: '', component: "Select", options: [
        {id: 'wine', slug: 'product:productTypes.wine'},
        {id: 'food', slug: 'product:productTypes.food'},
        {id: 'merchandise', slug: 'product:productTypes.merchandise'},
        {id: 'collateral', slug: 'product:productTypes.collateral'},
        {id: 'kit', slug: 'product:productTypes.kit'},
        {id: 'speciality', slug: 'product:productTypes.speciality'},
    ], margin: "normal", row: rows[0].id, rowSize: {xs: 12, md: 6}},
    {name: 'merchant_id', label: 'product:merchant', required: true, value: '', component: MerchantSelector, margin: "normal", row: rows[0].id, rowSize: {xs: 12, md: 6}},
    {name: 'price', label: 'product:retailPrice', required: true, value: 0, component: "MoneyField", margin: "normal", row: rows[0].id, rowSize: {xs: 12, md: 6}},

    /*{name: 'short_description', type: 'text', label: 'product:shortDescription', required: false, value: '', component: "TextField", margin: "normal", multiline: true, row: rows[1].id},
    {name: 'long_description', type: 'text', label: 'product:longDescription', required: false, value: '', component: "TextField", margin: "normal", multiline: true, row: rows[1].id},
    {name: 'label_description', type: 'text', label: 'product:labelDescription', required: false, value: '', component: "TextField", margin: "normal", multiline: true, row: rows[1].id},*/

    {name: 'container', label: 'product:handling.container', required: false, value: '', component: "Select", margin: "normal", options: [], row: rows[2].id},
    {name: 'container_amount', label: 'product:handling.amountPerContainer', required: false, value: 0, component: "NumberField", margin: "normal", row: rows[2].id},
    {name: 'service_fee', label: 'product:handling.serviceFee', required: false, value: 0, component: "MoneyField", margin: "normal", row: rows[2].id},

];

export const useFormLogic = ({loading:parentLoading, id, onSuccess}) => {
    const { t } = useOutletContext();

    const [success, setSuccess] = useState(false);
    const [loading, setLoading] = useState(false);
    const [allData, setAllData] = useState(null);

    // these are the api hooks
    const apiParams = useMemo(() => [
        {params: {endpoint: '/product/add', method: 'POST'}},
        {params: {endpoint: `/product/edit/${id}`, method: 'PUT'}},
        {params: {endpoint: `/product/product/${id}`, method: 'GET'}},
    ], [id]);

    const { fetchData:saveData, loading:formLoading, ErrorBar, LoadingBar } = useApi(apiParams[0]);
    const { fetchData:UpdateData, loading:updateLoading, ErrorBar:UpdateErrorBar, LoadingBar:UpdateLoadingBar } = useApi(apiParams[1]);
    const { fetchData:fetchProductData, data:productData, errors:productErrors, ErrorBar:ProductErrorBar, loading:productLoading } = useApi(apiParams[2]);

    const { fields: collateralFields } = useCollateral({rowId: rows[1].id});
    const { fields: wineFields } = useWine({rowId: rows[1].id});
    const { fields: foodFields } = useFood({rowId: rows[1].id});
    const { fields: merchandiseFields } = useMerchandise({rowId: rows[1].id});
    const { fields: kitFields } = useKit({rowId: rows[1].id});

    const formFields = useMemo(() => [
        ...fields,
        ...collateralFields,
        ...wineFields,
        ...foodFields,
        ...merchandiseFields,
        ...kitFields,
    ], [collateralFields, wineFields, foodFields, merchandiseFields, kitFields]);

    // we send this function to the useForm hook to add the password confirmation field and the id (if it's an update) to the form values
    const formBeforeSubmit = formValues => {
        if (formValues) {
            const _values = [];
            if (id && !values.find(a=>a.name === 'id')) {
                _values.push({name: 'id', value: id});
            }
            // filter out fields that dont belong to the selected product_type
            const _productType = formValues.find(a=>a.name === 'product_type');
            if (_productType) {
                formValues.filter(a => !a?.group || a.group === _productType.value);
            }
            return [...formValues, ..._values];
        }
        return formValues;
    }

    // this is the function that gets called when the form is submitted, it sets the params (form values) for the api call. The api is called on a useEffect below
    const formSubmit = useCallback(async (formValues, setErrors) => {
        //if (formValues) setParams(formValues);
        if (formValues) {
            try {
                setLoading(true);
                const apiCall = id ? UpdateData : saveData;
                const response = await apiCall(formValues);
                if (response.data) {
                    setSuccess(true);
                }
            } catch (error) {
                setErrors({form: t('error:default')});
            } finally {
                setLoading(false);
            }
        } else setErrors({form: t('error:default')});
    }, [t, saveData, UpdateData, id]);


    // this is the form hook, it handles the form state and actions like validation and submission
    const {values, errors, handleChange:handleFieldChange, handleSubmit, setFormValues, setValues } = useForm(fields, { onSubmit: formSubmit, onBeforeSubmit: formBeforeSubmit });

    const handleChange = useCallback(e => {
        const { name, value } = e.target;

        // filter out fields that dont belong to the selected product_type
        if (name === "product_type") {
            const _values = values.filter(a => !a?.group || a.group === value);
            const hasMatchingGroupFields = _values.some(field => field?.group === value);
            if (!hasMatchingGroupFields) {
                formFields.forEach(field => {
                    if (field.group === value) {
                        _values.push({...field, value: allData?.[field.name] || field.value });
                    }
                });
            }
            setValues(_values);
        }
        handleFieldChange(e);
    }, [handleFieldChange, setValues, values, formFields, allData]);

    useEffect(() => {
        if (formLoading || updateLoading|| productLoading || parentLoading) setLoading(true);
        else setLoading(false);
    }, [formLoading, updateLoading, productLoading, parentLoading]);

    // fetch the data if the id is set
    useEffect(() => {
        const _loadProduct = async () => {
            if (id) {
                const result = await fetchProductData();
                if (result?.data) {
                    setFormValues({...result.data[0]});
                    setAllData(result.data[0]);
                }
            }
        }
        _loadProduct();
    }, [id, fetchProductData, setFormValues]);


    // the stuff we'll be using in the form
    return {
        values,
        errors,
        success,
        loading,
        handleChange,
        handleSubmit,
        productErrors,
        productData,
        ErrorBar,
        LoadingBar,
        UpdateLoadingBar,
        ProductErrorBar,
        UpdateErrorBar,
        rows,
    }
}