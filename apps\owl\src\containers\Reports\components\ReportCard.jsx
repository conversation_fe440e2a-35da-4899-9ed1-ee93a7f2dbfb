import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Chip
} from '@mui/material';
import {
  DownloadOutlined as DownloadIcon,
  PrintOutlined as PrintIcon,
  VisibilityOutlined as ViewIcon
} from '@mui/icons-material';

export const ReportCard = ({
  title,
  description,
  icon,
  color = '#2196F3',
  metrics = [],
  onView,
  onDownload,
  onPrint,
  tags = [],
  lastUpdated,
  ...props
}) => {
  return (
    <Card 
      sx={{ 
        height: '100%', 
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': { 
          transform: 'translateY(-2px)',
          boxShadow: 4
        }
      }}
      {...props}
    >
      <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{
            bgcolor: color,
            color: 'white',
            borderRadius: '8px',
            p: 1,
            mr: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: 40,
            minHeight: 40
          }}>
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="div" sx={{ mb: 0.5 }}>
              {title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          </Box>
        </Box>

        {/* Metrics */}
        {metrics.length > 0 && (
          <Box sx={{ mb: 2 }}>
            {metrics.map((metric, index) => (
              <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="body2" color="text.secondary">
                  {metric.label}
                </Typography>
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {metric.value}
                </Typography>
              </Box>
            ))}
          </Box>
        )}

        {/* Tags */}
        {tags.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
            {tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.75rem' }}
              />
            ))}
          </Box>
        )}

        {/* Last Updated */}
        {lastUpdated && (
          <Typography variant="caption" color="text.secondary" sx={{ mb: 2 }}>
            Last updated: {lastUpdated}
          </Typography>
        )}

        {/* Actions */}
        <Box sx={{ mt: 'auto', display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {onView && (
            <Button
              size="small"
              startIcon={<ViewIcon />}
              onClick={onView}
              variant="contained"
              sx={{ bgcolor: color, '&:hover': { bgcolor: color + 'DD' } }}
            >
              View
            </Button>
          )}
          {onDownload && (
            <Button
              size="small"
              startIcon={<DownloadIcon />}
              onClick={onDownload}
              variant="outlined"
            >
              Export
            </Button>
          )}
          {onPrint && (
            <Button
              size="small"
              startIcon={<PrintIcon />}
              onClick={onPrint}
              variant="outlined"
            >
              Print
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ReportCard;
