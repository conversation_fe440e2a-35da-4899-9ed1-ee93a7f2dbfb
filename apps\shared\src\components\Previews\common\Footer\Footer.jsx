import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { createCurrencyFormatter, toCamelCase } from '../../../../utils';

import styles from './Footer.module.scss';

export const Footer = ({
    data, // the data to display
    fields: fieldDefinitions, // field definition ({key: the key in the data object, slug: the slug to send to the translator, valueFormatter: a function to format the value to be displayed, variant: the typography variant to use})
    className, // additional class names to apply to the wrapper (used for different print formats)
    children, // additional children to render under the fields
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));    
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    const fields = useMemo(() => {
        if (fieldDefinitions) return fieldDefinitions;
        else {
            return [
                { key: 'subtotal_price', slug: 'order:subtotal', valueFormatter: value => currencyFormatter.format(value)},
                { key: 'tax_total', slug: 'order:tax', valueFormatter: value => currencyFormatter.format(value)},
                { key: 'tip', slug: 'order:tip', valueFormatter: value => currencyFormatter.format(value)},
                ...(data?.price_adjustments?.map(adjustment => ({
                    key: 'price_adjustments', 
                    slug: `order:${toCamelCase(adjustment.price_adjustment_type_name)}`, 
                    valueFormatter: value => currencyFormatter.format(adjustment.amount),
                }))),
                { key: 'total_price', slug: 'order:total', variant: 'h6', valueFormatter: value => currencyFormatter.format(value)},
            ];
        }
    }, [fieldDefinitions, data?.price_adjustments]);

    const processField = (field, data) => {
        let processedField = {field: null, value: null};

        if (!field.key) return processedField;
        let value = data?.[field.key];

        if (!value) return processedField;
        if (field?.valueFormatter) value = field.valueFormatter(value);

        processedField = {field: field.slug ? t(field.slug) : null, value};
        return processedField;
    }

    if (!data || data.length <= 0) return null;
    
    return (
        <Box className={clsx(styles.wrapper, className)} sx={{order: isMobile ? 0 : 2, px: 2 }}>
            {fields.map(field => {
                const {field:key, value} = processField(field, data);
                if (!key && !value) return null;
                return (
                    <Stack 
                        className={styles.totals}
                        key={`header-field-${field.key}`} 
                        direction='row'
                        justifyContent='space-between'
                        alignItems='center'
                        spacing={1}
                        sx={{
                            ml: 'auto',
                            maxWidth: isMobile ? '100%' : '200px',
                        }}
                    >
                        {key && <Typography variant={field.variant || 'subtitle2'}>{key}:</Typography>}
                        {value && <Typography variant={field.variant || 'subtitle2'}>{value}</Typography>}
                    </Stack>
                );
            })}
            {children}
        </Box>
    );
}