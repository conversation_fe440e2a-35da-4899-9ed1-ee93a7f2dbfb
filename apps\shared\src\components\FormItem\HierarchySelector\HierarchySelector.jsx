import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHelperText, Box, Typography, Grid2 } from '@mui/material';

import { flattenTree } from '../../../utils/tree';
import Autocomplete from '../Autocomplete';

import useApi from '../../../api';

const marginSx = {
	'dense': { mt: 0.5, mb: 0.5 },
	'normal': { mt: 1, mb: 0.5 },
	'none': { m: 0 },
};

/**
 * HierarchySelector component for selecting items in a hierarchical structure.
 *
 * @param {Object} data - The data to populate the selectors. Can be hierarchical (array with children) or grouped (object with arrays for each level).
 * @param {Array} levels - Configuration for each level of selection. Each item in the array should have:
 *   - id: Unique identifier for this level. Also used as the key in grouped data.
 *   - label: Display label for this level.
 *   - parent: ID of the parent level. `null` for top level.
 *   - parentField: Field from parent selection to match with this level's parentIdField.
 *   - parentIdField: Field in this level's data that contains the reference to the parent.
 * @param {Object} value - The currently selected values for each level.
 * @param {Function} onChange - Callback when selections change. Receives an event with `target.value` as the updated selections.
 * @param {Boolean} loading - Whether the component is in a loading state.
 * @param {Boolean} disabled - Whether the component is disabled.
 * @param {String|Object} errors - Error message(s) to display.
 * @param {Boolean} required - Whether all fields are required to be selected.
 * @param {String} name - The name of the field.
 * @param {String} label - The label for the field.
 * @param {Boolean} isAsync - Whether to fetch data asynchronously from an API.
 * @param {Function} fetchData - Function to fetch data from an API.
 * @param {Object} fetchParams - Base parameters to send with all API requests.
 * @param {Function} getSearchParams - Function that accepts a search term and returns parameters to be sent to the API.
 * @param {String} dataField - Field in the API response that contains the data.
 * @param {String} margin - Controls the spacing. Options: 'none', 'dense', 'normal'.
 * @param {Number} gridSize - Controls the width of each level in the grid. If not provided, calculated based on the number of levels.
 * @param {Object} slots - Custom components for different parts of the component. Available slots: container, label, gridContainer, gridItem, error.
 * @param {Object} slotProps - Props to pass to the slot components. Available slots: container, label, gridContainer, gridItem, error, formItem (props for each level's Autocomplete).
 */

export const HierarchySelector = ({
	data,
	levels,
	value = {},
	onChange,
	loading = false,
	disabled = false,
	errors = null,
	required = false,
	name = "hierarchy-selector",
	label = null,
	isAsync = false,
	fetchData = null,
	fetchParams = {},
	getSearchParams = null,
	getOptionLabel = null,
	renderOption = null,
	dataField = null,
	margin = 'normal',
	gridSize,
	slots = {},
	slotProps = {},
	...props
}) => {
	const { t } = useTranslation();
	const maxGridSize = gridSize || ((12 / levels.length) < 4 ? 4 : (12 / levels.length));

	const { fetchData: fetchApiData, loading: apiLoading, errors: apiErrors } = useApi({params: {endpoint: '/dummy'}});

	const [selections, setSelections] = useState(value || {});

	const slotComponents = useMemo(() => ({
		Container: slots?.container || Box,
		Label: slots?.label || Typography,
		GridContainer: slots?.gridContainer || Grid2,
		GridItem: slots?.gridItem || Grid2,
		Error: slots?.error || FormHelperText
	}), [slots]);

	const slotProperties = useMemo(() => ({
		container: slotProps?.container || {},
		label: slotProps?.label || {},
		gridContainer: slotProps?.gridContainer || {},
		gridItem: slotProps?.gridItem || {},
		error: slotProps?.error || {},
		formItem: slotProps?.formItem || {}
	}), [slotProps]);

	const isHierarchical = useMemo(() => Array.isArray(data), [data]);

	const processedData = useMemo(() => {
		if (!data) return {};
		if (!isHierarchical) return data;
		return flattenTree(data);
	}, [data, isHierarchical]);

	const getOptionsForLevel = useCallback(level => {
		if (!processedData || !level?.id) return [];

		if (!isHierarchical) {
			const _data = processedData?.[level.id] || [];
			const parentValue = selections?.[level?.parent]?.[level?.parentField];			
			if (parentValue) return _data.filter(item => item?.[level?.parentIdField] === parentValue);
			return _data;
		}

		// first level or no parent selected
		if (!level?.parent) {
			return processedData.filter(item => item?.parentId === null);
		}

		const parentValue = selections?.[level?.parent]?.[level?.parentField];
		return processedData.filter(item => item.parentId === parentValue);
	}, [processedData, selections, isHierarchical]);

	const handleLevelChange = useCallback((_, levelId, levelIndex, newValue) => {
		setSelections(prev => {
			if (newValue?.children) delete newValue.children;
			const updated = { ...prev, [levelId]: newValue };
			levels.forEach((level, index) => {
				if (index > levelIndex) {
					updated[level.id] = null;
				}
			});
			return updated;
		});
	}, [levels]);

	// Use useEffect to call onChange after selections state has been updated
	// Only call onChange when selections actually change, not on initial render
	const selectionsRef = useRef(value || {});
	useEffect(() => {
		if (onChange && selections && JSON.stringify(selections) !== JSON.stringify(selectionsRef.current)) {
			selectionsRef.current = selections;
			const newEvent = {
				preventDefault: () => {},
				stopPropagation: () => {},
				target: { name, value: selections }
			};
			onChange(newEvent);
		}
	}, [selections, onChange, name]);


	// Sync internal selections state with value prop changes
	// Use a ref to track the previous value to avoid infinite loops
	const prevValueRef = useRef();
	useEffect(() => {
		const prevValue = prevValueRef.current;
		const currentValueStr = JSON.stringify(value);
		const prevValueStr = JSON.stringify(prevValue);

		if (currentValueStr !== prevValueStr && currentValueStr !== JSON.stringify(selections)) {
			setSelections(value || {});
		}

		prevValueRef.current = value;
	}, [value, selections]);

	return (
		<slotComponents.Container sx={{  ...slotProperties.container?.sx }} {...slotProperties.container}>
			{label && 
				<slotComponents.Label variant="caption" {...slotProperties.label} sx={{ mt: 1, ...slotProperties.label?.sx }}>
					{t(label, label)}
				</slotComponents.Label>
			}
			<slotComponents.GridContainer container spacing={margin === 'dense' ? 1 : (margin === 'normal' ? 2 : 0)} {...slotProperties.gridContainer}>
				{levels.map((level, i) => {
					const options = isAsync ? [] : getOptionsForLevel(level);
					const levelDataField = dataField?.[level.id] || dataField;
					const levelFetchParams = { ...(fetchParams?.[level.id] || {}) };
					const levelOptionLabel = getOptionLabel?.[level.id] || getOptionLabel;
					const levelRenderOption = renderOption?.[level.id] || renderOption;
					if (level?.parent && selections?.[level.parent]) {
						const parentValue = selections?.[level.parent]?.[level.parentField] || null;
						const paramName = level?.parentIdField || `${level.parent}_id` || `parent_id`;
						levelFetchParams[paramName] = parentValue;
					}

					return (
						<slotComponents.GridItem size={{xs: 12, md: maxGridSize}} key={level.id} {...slotProperties.gridItem}>
							<Autocomplete
								label={level.label}
								id={level.id}
								name={`${name}-${level.id}`}
								value={selections?.[level.id] || null}
								onChange={e => handleLevelChange(e, level.id, i, e.target.value)}
								options={options}
								loading={loading}
								disabled={disabled || (level?.parent && !selections?.[level.parent])}
								required={required}
								fullWidth
								filterSelectedOptions
								margin={margin}
								isAsync={isAsync}
								fetchData={isAsync && fetchData ? fetchData : fetchApiData}
								fetchParams={levelFetchParams}
								getSearchParams={getSearchParams}
								getOptionLabel={levelOptionLabel}
								renderOption={levelRenderOption}
								dataField={levelDataField}
								errors={errors?.[level.id] || errors}
								{...slotProperties.formItem}
								{...slotProps?.[level.id]}
								
							/>
						</slotComponents.GridItem>
					);
				})}
				{errors && <slotComponents.Error error {...slotProperties?.error}>{errors}</slotComponents.Error>}
			</slotComponents.GridContainer>
		</slotComponents.Container>
	);
};