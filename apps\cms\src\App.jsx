import React, { useState} from 'react';
import { StyledEngineProvider } from '@mui/material/styles';
//import { LicenseInfo } from '@mui/x-license';
import { ApiProvider, PermissionProvider, ThemeProvider, Router } from '@siteboss-frontend/shared';
import { ErrorBoundary } from '@siteboss-frontend/shared/components';

import { Routes } from './Routes';

function App() {
	//LicenseInfo.setLicenseKey('YOUR_LICENSE_KEY');
	const [ loading, setLoading ] = useState(true);
	const [ savedTheme, setSavedTheme ] = useState(null);

	return (
		<StyledEngineProvider injectFirst>
			<ApiProvider>
				<ThemeProvider onLoading={setLoading} onThemeLoaded={setSavedTheme}>
					<ErrorBoundary>
						<PermissionProvider /*modules={[1, 2, 3]}*/>
							{savedTheme &&
								<Router 
									basename={import.meta.env.VITE_BASE_PATH} 
									themeData={savedTheme} 
									loading={loading} 
								>
									<Routes />
								</Router>
							}
						</PermissionProvider>
					</ErrorBoundary>
				</ThemeProvider>
			</ApiProvider>
		</StyledEngineProvider>
	);
}

export default App;
