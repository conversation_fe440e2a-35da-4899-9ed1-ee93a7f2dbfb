import React, { useContext, useCallback } from 'react';
import { Container } from '@mui/material';
import { Radio, Button } from './Layouts';
import { PosProductDetailContext } from '../../../../hooks/PosProductDetailContext';

export const ProductVariants = ({ layoutType = "radio", product, variants, selectedVariantId, onSelect, slotProps, ...props }) => {
    const { setUpdateRootView, loading } = useContext(PosProductDetailContext) || {};
    const Component = layoutType === 'button' ? Button : Radio; 

    const handleSelect = useCallback(variant => {
        setUpdateRootView(true);
        const id = variant?.id || variant;
        if (onSelect && id) {
            const item = variants.find(a => +a.id === +id);
            onSelect(item);
        }
    }, [onSelect, variants, setUpdateRootView]);

    if (!product || !variants || variants.length <= 1 ) return null;

    return (
        <Container disableGutters sx={{my: 1}}>
            <Component 
                items={variants} 
                product_type_id={product?.product_type_id}
                selected={selectedVariantId} 
                onSelect={handleSelect}
                disabled={loading} 
                slotProps={slotProps}
            />
        </Container>
    );
};