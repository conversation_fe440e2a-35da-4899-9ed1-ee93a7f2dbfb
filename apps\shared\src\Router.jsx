import React, { useEffect, useState, useCallback, useRef, useMemo, memo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { parseISO, differenceInHours } from 'date-fns';
import { Backdrop, Box, Typography } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';

import { ErrorBar } from './components';
import { getCompanyVar } from './utils';
import useApi from './api';
import { setInfo as setUserInfo, setToken } from './store/reducers/userSlice';
import { setInfo as setCompanyInfo } from './store/reducers/companySlice';

import spinner from './assets/images/infinite-spinner.svg';

/*
    let _user = localStorage.getItem("user");
    if (_user) {
        _user = JSON.parse(_user);
        let _date = new Date();
        if (_user?.date) _date = parseISO(_user.date);
        // if the user is within 12 hours of login, keep the user data
        if (_user && differenceInHours(new Date(), _date) < 12 && _user.token) _userData = _user;
    }
    // if there is no user data, redirect to login
    if (!_userData && (currentPage!=="login" && currentPage!=="logout" && currentPage!=="register")) {
        if (!localStorage.getItem('prevUrl')) localStorage.setItem('prevUrl', window.location.pathname + window.location.search);
        window.location.href = currentLocation.slice(0, currentLocation.length - 1).join("/") + "/login";
        //window.location.reload();
    }
    _companyData = localStorage.getItem("company");
    if (_companyData) _companyData = JSON.parse(_companyData);
*/

const Errors = ({errors, setErrors, onRetry }) => (
    <>
        <ErrorBar open={Boolean(errors?.config)} message={errors?.config} onClose={() => setErrors(prev => ({...prev, config: null}))} retry={onRetry} />
        <ErrorBar open={Boolean(errors?.theme)} message={errors?.theme} onClose={() => setErrors(prev => ({...prev, theme: null}))} retry={onRetry} />
        <ErrorBar open={Boolean(errors?.general)} message={errors?.general} onClose={() => setErrors(prev => ({...prev, general: null}))} retry={onRetry} />
    </>
);

export const InfiniteLoader = memo(({open, errors, setErrors, onRetry, message, children }) => {
    return (
        <Backdrop sx={{ zIndex: theme => theme.zIndex.drawer + 1 }} open={open}>
            <Errors errors={errors} setErrors={setErrors} onRetry={onRetry} />
            <Box sx={{
                width: '100vw',
                height: '100vh',
                backgroundImage: `url(${spinner})`,
                backgroundSize: '300px',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                filter: theme => theme.palette.mode === 'light' ? 'invert(1)' : undefined
            }}>
                {message &&
                    <Typography variant="caption" color="textPrimary" sx={{position: 'absolute', bottom: '20px', left: '50%', transform: 'translateX(-50%)'}}>
                        {message}
                    </Typography>
                }
                {children}
            </Box>
        </Backdrop>
    );    
});

// LOADER
export const Loader = ({ themeData, onLoadComplete, noLogin = false, endpointOverrides = null }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const loaded = useRef(false);

    const user = useSelector(state => state.user);
    const company = useSelector(state => state.company);

    const [loading, setLoading] = useState(true);
    const [errors, setErrors] = useState({});

    const apiParams = useMemo(() => [
        {enableCache: true, keepCache: true, params: {endpoint: "/company_config", method: "POST", data: {is_active: 1, with_company_data: true}, ...endpointOverrides?.companyConfig}},
        {enableCache: true, keepCache: true, params: {endpoint: "/cms/my_theme", params: {cms_version: 2}, ...endpointOverrides?.theme}},
    ], [endpointOverrides]);

    const { fetchData: fetchConfigData } = useApi(apiParams[0]);
    const { fetchData: fetchThemeData } = useApi(apiParams[1]);

    const checkUser = useCallback(() => {
        let _userData = null;
        let _user = localStorage.getItem("user");
        if (_user) {
            _user = JSON.parse(_user);
            const _date = _user?.date ? parseISO(_user.date) : new Date();
            if (differenceInHours(new Date(), _date) < 12 && _user.token) {
                _userData = _user;
            }
        }

        if (!noLogin){
            const currentPage = location.pathname.split('/').pop();
            if (!_userData && !['login', 'logout', 'register'].includes(currentPage)) {
                localStorage.setItem('prevUrl', location.pathname + location.search);
                navigate('/login', { replace: true });
            }
        }

        return _userData;
    }, [location, navigate, noLogin]);

    const processCompanyData = useCallback(async () => {
        let _companyData = localStorage.getItem("company");
        if (_companyData && _companyData !== 'undefined') {
            try {
                _companyData = JSON.parse(_companyData);
            } catch (error) {
                console.error('Error parsing company data:', error);
                _companyData = null;
            }
        } else {
            _companyData = null;
        }

        try {
            setLoading(true);
            const shouldFetchNewData = !loaded.current && (!_companyData || differenceInHours(new Date(), parseISO(_companyData.ts || "")) > 12 || !company.id);
            if (shouldFetchNewData) {
                const promises = [fetchConfigData()];
                if (!themeData) promises.push(fetchThemeData());

                // load the theme, config and data (which is included in the config data)
                const results = await Promise.all(promises).catch(error => {
                    setErrors(prev => ({...prev, general: error}));
                });



                /*
                const [resConfig, resTheme] = await Promise.all([fetchConfigData(), fetchThemeData()]).catch(error => {
                    setErrors(prev => ({...prev, general: error}));
                });*/

                let configData = null, currentThemeData = null;
                if (results[0]?.errors) setErrors(prev => ({...prev, config: results[0].errors}));
                else if (results[0]?.data) {
                    configData = results[0].data;
                    if (configData?.[0]) configData = configData[0];
                }

                if (themeData) currentThemeData = themeData;
                else {
                    if (results?.[1]?.errors) setErrors(prev => ({...prev, theme: results[1].errors}));
                    else if (results?.[1]?.data) currentThemeData = results[1].data;
                }

                if (currentThemeData && configData && (configData?.company || configData?.tenant)) {
                    try {
                        const _companyInfo = JSON.parse(JSON.stringify(configData?.company || configData?.tenant));
                        const _companyConfig = JSON.parse(JSON.stringify(configData))?.map?.(item => {
                            const { company, company_id, ...rest } = item;
                            return rest;
                        }) || JSON.parse(JSON.stringify(configData));

                        // sort config by sort_order
                        if (Array.isArray(_companyConfig)) _companyConfig.sort((a, b) => a.sort_order - b.sort_order);
                        _companyInfo.config = _companyConfig;

                        // get relevant data from the theme
                        const loginBg = getCompanyVar(currentThemeData?.[0]?.content?.variables, "background-image");
                        if (loginBg) _companyInfo.login_bg = loginBg.value;
                        if (currentThemeData?.[0]?.logo) _companyInfo.logo = currentThemeData[0].logo;
                        if (currentThemeData?.[0]?.website_id) _companyInfo.website_id = currentThemeData[0].website_id;

                        // save to localstorage and update the store
                        localStorage.setItem("company", JSON.stringify({ ..._companyInfo, ts: new Date() }));
                        dispatch(setCompanyInfo(_companyInfo));
                    } catch (error) {
                        console.error('Error processing company info:', error);
                    }
                }
            } else {
                if (_companyData && !company.id) dispatch(setCompanyInfo(_companyData));
            }
        } catch (error) {
            setErrors(prev => ({...prev, general: error}));
            console.error('Error processing company data:', error);
        } finally {
            loaded.current = true;
            setLoading(false);
            if (onLoadComplete) onLoadComplete();
        }
    }, [dispatch, company.id, user.token, onLoadComplete, themeData, fetchConfigData, fetchThemeData]);

    // if the company data is loaded, call the onLoadComplete function
    useEffect(() => {
        if (company.id && onLoadComplete) onLoadComplete();
    }, [company.id, onLoadComplete]);

    // load the company data
    useEffect(() => {
        if (!loaded.current && !company.id) processCompanyData();
    }, [processCompanyData, company.id]);

    // check the user data
    useEffect(() => {
        const _userData = checkUser();
        if (_userData && !user.token) {
            if (_userData.token) dispatch(setToken(_userData.token));
            dispatch(setUserInfo(_userData));
        }
    }, [dispatch, checkUser, user.token]);
    if (!loading && company.id) return null;

    return <InfiniteLoader open={loading && !company.id} errors={errors} setErrors={setErrors} onRetry={processCompanyData} />;
};

// ROUTER
export const Router = memo(({basename, themeData, loading, children, noLogin = false, endpointOverrides = null }) => {
    const [loadingComplete, setLoadingComplete] = useState(false);

    const handleLoadComplete = useCallback(() => {
        setLoadingComplete(true);
    }, []);

    if (loading) return <InfiniteLoader open={true} errors={null} setErrors={null} onRetry={null} message="Loading look up data..." />;

    return (
        <BrowserRouter basename={basename || import.meta.env.VITE_BASE_PATH} future={{v7_startTransition: true, v7_relativeSplatPath:true}}>
            {loadingComplete 
                ? children 
                : <Loader noLogin={noLogin} onLoadComplete={handleLoadComplete} themeData={themeData} endpointOverrides={endpointOverrides} message="Loading company information..." />
            }
        </BrowserRouter>
    );
})