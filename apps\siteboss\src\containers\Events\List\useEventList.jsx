import { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { formatDate, toCamelCase, createCurrencyFormatter } from '@siteboss-frontend/shared/utils';
import { useApi } from '@siteboss-frontend/shared';
import { Box, Chip, Stack, Typography, CardMedia } from '@mui/material';
import { PeopleOutlineOutlined as PeopleIcon } from '@mui/icons-material';
import { isSameDay, format } from 'date-fns';

const apiParams = [
    {params: {endpoint: `/event`, method: 'POST', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'desc',
    }}},
];

export const useEventList = ({setSelected, fetchCounter, params}) => {
    const { t, language, isMobile, currency } = useOutletContext();

    const [rows, setRows] = useState();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'desc'});
    const [searchText, setSearchText] = useState('');

    const currencyFormatter = useMemo(() => createCurrencyFormatter(language, currency), [language, currency]);
    const { fetchData, data, loading, ErrorBar, LoadingBar } = useApi(apiParams[0]);

    const columns = useMemo(() => [
        {
            field: 'id',
            headerName: `${t('general:id')} #`,
            maxWidth: 70,
            sortable: true,
        },
        {
            field: 'type_name',
            headerName: `${t(`event:type`)}`,
            width: 80,
            sortable: true,
            renderCard: () => null,
        },
        {
            field: 'image',
            headerName: '',
            width: 50,
            sortable: false,
            renderCell: () => null,
            renderCard: params => {
                <CardMedia
					component="img"
					height="140"
					image={params.value}
                    alt={params.row.name}
					sx={{ objectFit: 'cover' }}
				/>

            }
        },
        {
            field: 'name',
            headerName: t('event:name'),
            minWidth: 200,
            flex: 1,
            renderCard: params => (                
                <>
                    <Stack direction="row" useFlexGap spacing={1} alignItems="center" justifyContent="space-between">
                        {params?.row?.type_name && <Typography variant="caption" component="div">{params.row.type_name}</Typography>}
                        <Chip
                            size="small                                                 "
                            label={t(`event:statuses.${toCamelCase(params.row.status_name)}`, params.row.status_name)}
                            color={params?.row?.metadata?.event_status_id === 2 ? "success" :
                                    params?.row?.metadata?.event_status_id === 4 ? "error" : "default"}
                        />
                    </Stack>
                    <Typography variant="h5" component="div">
                        {params.value}
                    </Typography>
                    <Stack direction="column" spacing={0.5} useFlexGap sx={{mt: 2}}>
                        {params?.row?.default_variant_price &&
                            <Typography variant="caption" component="div">
                                {t('calendar:eventFee')}
                                <Typography variant="h5">
                                    {currencyFormatter.format(params.row.default_variant_price, currency)}
                                </Typography>
                            </Typography>
                        }
                        {params?.row?.variants?.length > 1 &&
                            <Typography variant="caption" component="span" color="textSecondary">
                                +{params.row.variants.length - 1} {t('event:additionalVariants')}
                            </Typography>
                        }
                    </Stack>
                </>
            )
        },
        {
            field: 'location',
            headerName: t('event:location'),
            flex: 1,
            minWidth: 150,
        },
        {
            field: 'date_info',
            headerName: t('event:dateTime'),
            flex: 1,
            minWidth: 120,
            renderCell: params => {
                const startDate = params.row.start_date;
                const endDate = params.row.end_date;
                const sameDayEvent = isSameDay(startDate, endDate);

                if (sameDayEvent) {
                    // Same day event: show date with start and end times
                    const formattedDate = formatDate(startDate, language, "short");
                    const startTime = format(startDate, "h:mmaaa").toLowerCase();
                    const endTime = format(endDate, "h:mmaaa").toLowerCase();

                    return (
                        <Stack direction="column" spacing={0} justifyContent="center" useFlexGap>
                            <Typography variant="body2" component="span">{`${formattedDate}`}</Typography>
                            <Typography variant="caption" component="span" color="text.secondary">
                                {`${startTime} - ${endTime}`}
                            </Typography>
                        </Stack>
                    );
                } else {
                    // Different days: show only dates without times
                    return `${formatDate(startDate, language, "short")} - ${formatDate(endDate, language, "short")}`;
                }
            }
        },
        {
            field: 'attendees',
            headerName: t('event:attendeesColumn'),
            width: 100,
            renderCell: params => (
                <Stack direction="row" alignItems="center" spacing={0.5} height="100%">
                    <PeopleIcon fontSize="small" color="action" />
                    <span>{params.value || 0}</span>
                </Stack>
            )
        },
        {
            field: 'variants',
            headerName: t('event:pricing'),
            minWidth: 80,
            align: 'right',
            valueGetter: (value, row) => row.default_variant_price || 0,
            sortComparator: (v1, v2) => v1 - v2,
            renderCell: params => (
                <Stack direction="column" spacing={0.5} height="100%" alignItems="flex-end" useFlexGap>
                    {(!params?.value || !params.value?.length) && <>-</>}

                    {params.row.default_variant_price &&
                        <span>
                            {currencyFormatter.format(params.row.default_variant_price, currency)}
                        </span>
                    }
                    {params?.value?.length > 1 &&
                        <Typography variant="caption" component="span" color="textSecondary">
                            +{params.value.length - 1} {t('event:additionalVariants')}
                        </Typography>
                    }
                </Stack>
            ),
            renderCard: () => null,
        },
        {
            field: 'status_name',
            headerName: t('event:status'),
            width: 100,
            sortable: false,
            renderCell: params => (
                <Chip
                    label={t(`event:statuses.${toCamelCase(params.value)}`, params.value)}
                    size="xs"
                    variant="outlined"
                    color={params.value === "Confirmed" ? "success" :
                            params.value === "Cancelled" ? "error" : "default"}
                />
            ),
            renderCard: () => null,
        }
    ], [t, language, currencyFormatter]);

    const handleRowSelection = model => {
        // If model is empty, clear selection
        if (!model || model.length === 0) {
            setSelected([]);
            return;
        }

        // Find the selected row
        const selectedRow = rows?.find(row => model.includes(row.id));

        // Set only one row as selected
        if (selectedRow) {
            setSelected([selectedRow]);
            // Log for debugging
            console.log('Selected row:', selectedRow);
        } else {
            setSelected([]);
        }
    }

    useEffect(() => {
        const _loadData = async () => {
            const result = await fetchData({
                page_no: Math.round(page) || 1,
                max_records: pageSize > 10 ? pageSize : 10,
                sort_col: order?.column || 'id',
                sort_direction: order.direction || 'desc',
                id: params?.id ? [params.id] : undefined,
                search: searchText
            });
            if (result?.data) {
                const _rows = result.data.events.map(event => {
                    // Strip HTML tags from description for display
                    const plainDescription = event.description ?
                        event.description.replace(/<\/?[^>]+(>|$)/g, "") :
                        event.short_description || "";

                    return {
                        id: event.id,
                        name: event.name,
                        description: plainDescription,
                        type_name: event.event_type_name,
                        status_name: event.event_status_name,
                        start_date: new Date(event.start_datetime),
                        end_date: new Date(event.end_datetime),
                        location: event.location_name,
                        image: event.image || (event.images && event.images.length > 0 ? event.images[0].preview_url : null),
                        attendees: event.orders ? event.orders.length : 0,
                        variants: event.variants || [],
                        default_variant_price: event.default_variant_price,
                        media: event.images ? event.images.length : 0,
                        // We'll generate the date_info display in the renderCell function
                        date_info: '',
                        metadata: event,
                    };
                });
                setRows(_rows);
                setPageSize(result.data.page_record_count);
                setPage(result.data.this_page);
                setTotalRows(result.data.total_record_count);
                setTotalPages(Math.min(result.data.total_record_count / result.data.page_record_count));
            }
        };
        _loadData();
    }, [page, pageSize, order, fetchCounter, params.id, searchText, fetchData, language]);

    return {
        data,
        rows,
        columns,
        order,
        page,
        pageSize,
        totalPages,
        totalRows,
        searchText,
        loading,
        ErrorBar,
        LoadingBar,
        handleRowSelection,
        setSearchText,
        setOrder,
        setPage,
        setPageSize,
        setTotalPages,
        setTotalRows,
    };
}