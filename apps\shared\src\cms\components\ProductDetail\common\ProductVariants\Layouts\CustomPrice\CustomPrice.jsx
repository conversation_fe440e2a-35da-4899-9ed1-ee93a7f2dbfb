import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { Box } from '@mui/material';
import FormItem from '../../../../../../../components/FormItem';
import { setInfo } from '../../../../../../../store/reducers/currentShopItemSlice';

export const CustomPrice = ({ label, item, onSelect, slotProps, ...props }) => {
    const dispatch = useDispatch();
    
    const handleChange = useCallback(e => {
        if (+e.target.value) onSelect(item);
        dispatch(setInfo({productCustomPrice: +e.target.value || 0}));
    }, [onSelect, item, dispatch]);
    
    return (
        <Box sx={{width:"85px", ...slotProps?.container?.sx}}>
            <FormItem 
                name="custom_amount"
                fullWidth
                required
                hiddenLabel={!label}
                size="small"
                variant="standard"
                color="secondary"
                component="MoneyField"
                aria-label="giftCard:customAmount"
                label={label}
                min={1}
                onChange={handleChange}
                slotProps={{
                    inputLabel: { shrink: true },
                }}
                {...slotProps?.customAmount}
            />
        </Box>
    );
};