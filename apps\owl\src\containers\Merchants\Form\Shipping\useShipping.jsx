import { useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';

const fieldsByTab = {
    parameters: [
        {
            name: 'hold_multi_parcel_orders_at_qa', 
            label: 'shipping:holdMultiParcelOrdersAtQA', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'adult_signature', 
            label: 'shipping:adultSignature', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'allow_saturday_delivery', 
            label: 'shipping:allowSaturdayDelivery', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'receive_inventory_on_return', 
            label: 'shipping:receiveInventoryOnReturn', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'ice_packs_for_all_dtc_tc', 
            label: 'shipping:icePacksForAllDTCTC', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'return_management', 
            label: 'shipping:returnManagement', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'manually_manage', 
            label: 'shipping:manuallyManage', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        }
    ],

    packingSlip: [
        {
            name: 'packing_slip', 
            label: 'shipping:packingSlip', 
            required: false, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'standard', slug: 'shipping:packingSlipTypes.standard' },
                { id: 'detailed', slug: 'shipping:packingSlipTypes.detailed' },
                { id: 'minimal', slug: 'shipping:packingSlipTypes.minimal' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'print_gift_message_separately', 
            label: 'shipping:printGiftMessageSeparately', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'packing_slip_email', 
            type: 'email',
            label: 'shipping:packingSlipEmail', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'packing_slip_website', 
            type: 'url',
            label: 'shipping:packingSlipWebsite', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'shipper_message', 
            type: 'text',
            label: 'shipping:shipperMessage', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            multiline: true,
            rows: 4,
            rowSize: {xs: 12}
        }
    ],
    labels: [
        {
            name: 'return_address', 
            label: 'shipping:returnAddress', 
            required: false, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'warehouse', slug: 'shipping:returnAddressTypes.warehouse' },
                { id: 'corporate', slug: 'shipping:returnAddressTypes.corporate' },
                { id: 'custom', slug: 'shipping:returnAddressTypes.custom' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'shipping_company', 
            type: 'text',
            label: 'shipping:shippingCompany', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'shipping_contact_name', 
            type: 'text',
            label: 'shipping:shippingContactName', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'shipping_email', 
            type: 'email',
            label: 'shipping:shippingEmail', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'shipping_phone_number', 
            type: 'tel',
            label: 'shipping:shippingPhoneNumber', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        }
    ],
    compliance: [
        {
            name: 'proposition_65_label_zpl', 
            type: 'text',
            label: 'shipping:proposition65LabelZPL', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            multiline: true,
            rows: 6,
            rowSize: {xs: 12}
        }
    ],
    insurance: [
        {
            name: 'insure_packages', 
            label: 'shipping:insurePackages', 
            required: false, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'none', slug: 'shipping:insuranceTypes.none' },
                { id: 'basic', slug: 'shipping:insuranceTypes.basic' },
                { id: 'full', slug: 'shipping:insuranceTypes.full' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'percent_to_insure', 
            type: 'number',
            label: 'shipping:percentToInsure', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            inputProps: { min: 0, max: 100, step: 1 },
            rowSize: {xs: 12, md: 6}
        }
    ]
};

export const useShipping = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();

    return {
        fieldsByTab,
        loading: false,
        errorBars: [],
    };
};
