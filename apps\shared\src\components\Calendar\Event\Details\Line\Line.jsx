import React from 'react';
import { Stack, Typography, useMediaQuery } from '@mui/material';

export const Line = ({ 
    caption, // line caption or title
    text,  // line text
    slotProps, // an object containing the properties for the caption and text slots
    ...props // additional props
}) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    return (
        <Stack direction="column" useFlexGap spacing={0} sx={{mb: 2, ...(props?.sx || {})}}>
            <Typography variant="caption" sx={{...(slotProps?.caption || {})}}>
                {caption}
            </Typography>
            <Typography 
                variant={isMobile ? 'body1' : 'subtitle2'} 
                component="div" 
                sx={{...(slotProps?.text || {})}} 
                dangerouslySetInnerHTML={{__html: text}} 
            />
        </Stack>
    );
}