import { useEffect, useState, useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useOutletContext } from 'react-router-dom';
import { Paper } from '@mui/material';
import { usePermission } from '@siteboss-frontend/shared/permissions';

import { widgetList, GeneralSettings, NotificationsWidget, WeatherWidget, WeatherWidgetSettings, SalesChart, CalendarWidget, CalendarWidgetSettings, AnalyticsWidget, UsersWidget, TasksWidget } from './Widgets';
import { MODULES } from './constants';

const permissionParams = {moduleIds: Object.values(MODULES)};

let localWidgets = localStorage.getItem('_siteboss') || '{}';
if (localWidgets) localWidgets = JSON.parse(localWidgets);

export const useDashboard = () => {
	const { t } = useOutletContext();

	const [items, setItems] = useState(null);
	const [isEditing, setIsEditing] = useState(false);
	const [openAddModal, setOpenAddModal] = useState(false);
	const [openSettingsModal, setOpenSettingsModal] = useState(false);
	const [selectedWidget, setSelectedWidget] = useState(null);
	const [currentSettings, setCurrentSettings] = useState({});
	const [gridLayout, setGridLayout] = useState({rows: 3, cols: 12, ...(localWidgets?.dashboardHome?.layout || {})});
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState('');

	// Get user profile and roles from Redux store
	const userProfile = useSelector(state => state.user.profile);

	// Get permissions for dashboard modules
	const { permissions, userRole } = usePermission(permissionParams);

	const widgetSetup = useCallback((widget, i = 0) => {
		const type = widget.type || widget.id;
		let Settings = GeneralSettings, Widget = null;

		// Handle both numeric and string IDs for widgets
		switch (type) {
			// Legacy widgets with numeric IDs
			case 1:
			case 'weather':
				Settings = WeatherWidgetSettings;
				Widget = WeatherWidget;
				break;
			case 2:
			case 'sales':
				Widget = SalesChart;
				break;
			case 3:
			case 'calendar':
				Settings = CalendarWidgetSettings;
				Widget = CalendarWidget;
				break;

			// New widgets with string IDs
			case 'analytics':
				Widget = AnalyticsWidget;
				break;
			case 'users':
				Widget = UsersWidget;
				break;
			case 'tasks':
				Widget = TasksWidget;
				break;
			case 'notifications':
				Widget = NotificationsWidget;
				break;
			default:
				break;
		}

		return ({
			...widget,
			id: i + 1, // unique id for the widget
			type: widget.type || widget.id, // the type of widget (the widget id defined in the widgets list)
			// used to render the widget settings on the modal
			renderSettings: widget => <Settings {...widget} isEditing={isEditing} onSave={s => setCurrentSettings(s)} />,
			Widget,
			state: {
				isMinimized: false, // used to toggle the minimized state of the widget
			}
		});
	}, [isEditing]);

	// widget renderer component
	const WidgetRenderer = useCallback(({ item, isEditing }) => {
		if (!item || item.state.isMinimized) return null;

		const Widget = item.Widget;
		if (!Widget) return null;

		return (
			<Paper
				variant='outlined'
				elevation={0}
				sx={{
					p: 2,
					//height: '100%',
					width: '100%',
					overflow: 'hidden',
				}}
			>
				<Widget {...item} isEditing={isEditing} />
			</Paper>
		);
	}, [isEditing]);

    const handleEditMode = useCallback(() => {
		setIsEditing(true);
	}, []);

	const openAddWidgetModal = useCallback(state => () => {
		setOpenAddModal(state);
		if (!state) setIsEditing(false);
	}, []);

	const openWidgetSettingsModal = useCallback(state => () => {
		setOpenSettingsModal(state);
		if (!state) {
			setSelectedWidget(null);
			setCurrentSettings({});
		}
	}, []);

    const handleAddWidget = useCallback(widget => {
        setItems(prev => {
			if (!widget) return prev;
			const _widget = widgetSetup(widget, prev.length);
			return [...prev, _widget];
		});
        setOpenAddModal(false);
		setSelectedWidget(null);
    }, [widgetSetup]);

	const handleMinimize = useCallback(id => {
		setItems(prev => prev.map(a => a.id === id ? { ...a, state: { ...a.state, isMinimized: !a.state.isMinimized } } : a));
		setSelectedWidget(null);
	}, []);

	const handleOpenSettings = useCallback(id => {
		setSelectedWidget(items.find(a => a.id === id));
		setOpenSettingsModal(true);
	}, [items]);

    const handleRemoveWidget = useCallback(id => {
		setItems(prev => prev.filter(a => a.id !== id));
		setSelectedWidget(null);
	}, []);

    const handleSaveSettings = useCallback((id, newSettings) => {
		setItems(prev => prev.map(a => a.id === id ? { ...a, settings: newSettings } : a));
		setOpenSettingsModal(false);
		setSelectedWidget(null);
		setCurrentSettings({});
	}, []);

	const handleLayoutChange = useCallback(layout => {
		setGridLayout(layout);
		if (!localWidgets.dashboardHome) localWidgets.dashboardHome = {};
		localWidgets.dashboardHome.layout = layout;
		localStorage.setItem('_siteboss', JSON.stringify(localWidgets));
	}, []);

	// Handler for setting default widgets based on role
	const handleSetDefaultWidgets = useCallback((defaultWidgets) => {
		if (!defaultWidgets || defaultWidgets.length === 0) return;

		// Filter widgets from widgetList that match the default widgets for the role
		const roleWidgets = widgetList.filter(widget =>
			defaultWidgets.some(defaultWidget => defaultWidget.type === widget.id || defaultWidget.type === widget.slug)
		);

		// Setup widgets
		const widgets = roleWidgets.map((widget, i) => widgetSetup(widget, i));
		setItems(widgets);

		// Show notification
		setSnackbarMessage(t('dashboard:defaultWidgetsLoaded'));
		setSnackbarOpen(true);
	}, [t]);

	// Handler for setting default layout based on role
	const handleSetDefaultLayout = useCallback((defaultLayout) => {
		if (!defaultLayout) return;

		setGridLayout(defaultLayout);

		// Update localStorage
		if (!localWidgets.dashboardHome) localWidgets.dashboardHome = {};
		localWidgets.dashboardHome.layout = defaultLayout;
		localStorage.setItem('_siteboss', JSON.stringify(localWidgets));
	}, []);

	const handleCloseSnackbar = useCallback(() => {
		setSnackbarOpen(false);
	}, []);	

	// Get user preferences from localStorage
	const userPreferences = useMemo(() => {
		return localWidgets?.dashboardHome || null;
	}, []);

	useEffect(() => {
		if (items && localWidgets) {
			if (!localWidgets?.dashboardHome) localWidgets.dashboardHome = {};
			localWidgets.dashboardHome.widgets = items.map(a => ({ id: a.id, type: a.type, settings: a.settings }));
			localStorage.setItem('_siteboss', JSON.stringify(localWidgets));
		}
	}, [items]);

	// Force load default widgets for testing
	useEffect(() => {
		const defaultWidgets = [
			{ id: 'analytics', type: 'analytics' },
			{ id: 'users', type: 'users' },
			{ id: 'tasks', type: 'tasks' },
			{ id: 'weather', type: 'weather' }
		];

		const widgets = [];
		widgetList.forEach((widget, i) => {
			if (defaultWidgets.some(dw => dw.type === widget.id || dw.type === widget.slug)) {
				widgets.push(widgetSetup(widget, i));
			}
		});

		setItems(widgets);
	}, [widgetSetup]);

	// Original user preferences loading logic
	/*useEffect(() => {
		// If we have user preferences, load them
		if (userPreferences?.widgets && userPreferences.widgets.length > 0) {
			const widgets = [];
			// Find matching widgets from widgetList
			userPreferences.widgets.forEach((prefWidget, i) => {
				const matchedWidget = widgetList.find(w => w.id === prefWidget.id || w.id === prefWidget.type);
				if (matchedWidget) {
					// Apply saved settings
					const widgetWithSettings = {
						...matchedWidget,
						settings: { ...matchedWidget.settings, ...prefWidget.settings }
					};
					widgets.push(widgetSetup(widgetWithSettings, i));
				}
			});
			setItems(widgets);
		} else if (widgetList.length) {
			// If no user preferences, load widgets based on role
			// This will be handled by RoleBasedLayout component
		}
	}, []);*/

	return 	{
		items, 
		setItems,
		userProfile, 
		permissions,
		userRole,
		widgetList,
		widgetSetup,
		WidgetRenderer,
		handleEditMode,
		openAddWidgetModal,
		openWidgetSettingsModal,
		handleAddWidget,
		handleMinimize,
		handleOpenSettings,
		handleRemoveWidget,
		handleSaveSettings,
		handleLayoutChange,
		handleSetDefaultWidgets,
		handleSetDefaultLayout,
		userPreferences,
		snackbarOpen,
		setSnackbarOpen,
		snackbarMessage,
		setSnackbarMessage,
		handleCloseSnackbar,
		openAddModal, 
		setOpenAddModal,
		openSettingsModal, 
		setOpenSettingsModal,
		selectedWidget, 
		setSelectedWidget,
		currentSettings, 
		setCurrentSettings,
		gridLayout, 
		setGridLayout,
		isEditing, 
		setIsEditing,
	};
}