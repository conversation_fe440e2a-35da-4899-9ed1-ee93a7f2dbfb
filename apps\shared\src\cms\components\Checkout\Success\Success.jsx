import React, { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, Navigate } from 'react-router-dom';
import { Stack, Container, Button, Typography, useMediaQuery, Divider, Grid2, Paper } from '@mui/material';
import { CheckCircleOutlineOutlined as CheckIcon } from '@mui/icons-material';

import { PosContext } from '../../../hooks';

import { LoadingBar, Title, PrintFormats } from '../../../../components';
import { formatSlug, toCamelCase, createCurrencyFormatter } from '../../../../utils';

const Line = ({label, value, children}) => {
    const { t } = useTranslation();
    return (
        <Stack direction="column" spacing={0} useFlexGap sx={{mb: 1}}>
            <Typography variant="caption">{t(label, label)}</Typography>
            <Typography variant="body1">{value}</Typography>
        </Stack>
    );
}

export const Success = ({loading, slotProps, orderFullfilled, showOrderPreview = true, cartRoute, children}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);    
    const currencyFormatter = createCurrencyFormatter(language, currency, 0);

    //const { printData, loading: previewLoading } = PrintFormats.orders.usePrint({id: orderFullfilled?.order?.id, formats: [{id: 'order', slug: 'order:formats.fullPage'}]});

    const { registerId, selectedUser, cart, router } = useContext(PosContext) || {};

    const indexPage = useMemo(() => `/${router?.index ? formatSlug(router?.index?.value) : ""}`, [router]);
    
    if (!orderFullfilled) return <Navigate to={cartRoute || indexPage} />;

    return (
        <Container component={Stack} direction="column" spacing={1} useFlexGap {...slotProps?.container}>
            {loading ? <LoadingBar {...slotProps?.loadingBar} /> :
                <Stack direction="column" spacing={2} useFlexGap {...slotProps?.container}>
                    <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={2} {...slotProps?.thankYouBar}>
                        <CheckIcon fontSize="large" />
                        <Stack direction="column" spacing={0} useFlexGap>
                            <Typography component="div" variant="subtitle2">{`${t("order:order")} #${orderFullfilled?.order?.id || ""}`}</Typography>
                            <Title {...slotProps?.title} title={t("pos:success.thankYou")} />
                        </Stack>
                    </Stack>
                    <Typography component="div" variant="body1" dangerouslySetInnerHTML={{__html: t("pos:success.description")}} />
                    {!showOrderPreview &&
                        <Grid2 container spacing={2} {...slotProps?.orderInfo}>
                            {orderFullfilled?.payments?.map((payment, i) => (
                                <Grid2 size={{xs: 12, lg: 6}} key={`payment-${payment.transaction_id}`}>
                                    <Line label={`order:paymentMethod`} value={payment.payment_type} />
                                    {payment?.cc_number && <Line label="order:creditCard" value={payment.cc_number} />}
                                    <Line label="order:transactionId" value={payment.transaction_id} />
                                    {payment?.nmi_transaction_id && <Line label="creditCard:gatewayTransaction" value={payment.nmi_transaction_id} />}
                                    {payment?.check_number && <Line label="check:number" value={payment.check_number} />}
                                    {payment?.check_name && <Line label="check:name" value={payment.check_name} />}
                                    {payment?.check_number && <Line label="check:number" value={payment.check_number} />}
                                    {payment?.change_amount > 0 ?
                                        <>
                                            <Line label={"order:tendered"} value={currencyFormatter.format(payment.amount, currency)} />
                                            <Line label={"order:change"} value={currencyFormatter.format(payment.change_amount, currency)} />
                                        </>
                                    : <Line label="status:paid" value={currencyFormatter.format(payment.amount, currency)} />}
                                    {i < orderFullfilled?.payments?.length - 1 && <Divider orientation={{xs: "horizontal", lg: "vertical"}} />}
                                </Grid2>
                            ))}
                        </Grid2>
                    }
                    <Stack direction="row" spacing={1} useFlexGap {...slotProps?.buttonBar}>
                        {!showOrderPreview &&
                            <Button {...slotProps?.button} variant="contained" size={isMobile ? "large" : undefined}>{t('pos:printReceipt')}</Button>
                        }
                        <Button {...slotProps?.button} component={Link} to={indexPage} variant="contained" size={isMobile ? "large" : undefined}>{t('pos:success.continueShopping')}</Button>
                    </Stack>
                    {orderFullfilled?.order?.id && showOrderPreview &&
                        <>
                            <Divider />
                            <Paper sx={{p: 2}}>
                                <PrintFormats.orders.Preview id={orderFullfilled?.order?.id} showPayNow={false} formats={[{id: 'order', slug: 'order:formats.fullPage'}]} />
                            </Paper>
                        </>
                    }
                    {children}
                </Stack>
            }
        </Container>
    );
}
