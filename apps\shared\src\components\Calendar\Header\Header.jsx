import React, { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Grid2, Stack, Button, IconButton, Menu, MenuItem, useMediaQuery, AppBar, Tooltip } from '@mui/material';
import { NavigateBeforeOutlined as PrevIcon, NavigateNextOutlined as NextIcon } from '@mui/icons-material';
import { formatDate } from '../../../utils/date';
import Title from '../../Title';
import LoadingBar from '../../Snacks/LoadingBar';
import DataTableSearchInput from '../../DataTable/SearchInput';

const calendarTypes = [
    { slug: 'month', value: 'month' },
    { slug: 'week', value: 'week' },
    { slug: 'day', value: 'day' },
    { slug: 'year', value: 'year' },
    { slug: 'schedule', value: 'schedule' }
];

export const Header = ({
    onPrev, // function to handle previous button click
    onNext, // function to handle next button click
    onToday, // function to handle today button click
    onCalendarTypeChange, // function to handle calendar type change
    onSearchTextChange, // function to handle search change
    showSearchBar = true, // whether to show the search bar
    showCalendarType = true, // whether to show the calendar type selector
    calendarType, // the current calendar type (day, week, month, year, schedule)
    currentDate, // the current date (Date object)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    ...props // additional props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);

    const [typeEl, setTypeEl] = useState(null);
    const typeOpen = Boolean(typeEl);
    
    const handleTypeClick = e => setTypeEl(e.currentTarget);
    const handleTypeClose = () => setTypeEl(null);

    const calendarTitle = useMemo(() => {
        switch (calendarType) {
            case 'schedule':
            case 'month': 
            case 'week': 
                /*const startWeek= startOfWeek(currentDate);
                const endWeek = endOfWeek(currentDate);
                if (startWeek.getMonth() === endWeek.getMonth()) {
                    return formatDate(startWeek, language.code, 'MMM do') + ' - ' + formatDate(endWeek, language.code, 'do');
                } else {
                    return `${formatDate(startWeek, language.code, 'MMM do')} - ${formatDate(endWeek, language.code, 'MMM do')}`
                }*/
                return formatDate(new Date(currentDate), language.code, 'MMMM yyyy');
            case 'day':
                return formatDate(new Date(currentDate), language.code, 'MMM do yyyy');
            case 'year':
                return formatDate(new Date(currentDate), language.code, 'yyyy');
            default:
                return t('calendar:schedule');
        }
    }, [calendarType, currentDate, language]);

    /* TODO: 
    - add menu button that shows the list of groups you're in, so you can filter by those.
    - add switch to include family member events.
    */

    if (!currentDate) return null;

    return (
        <AppBar position='sticky' sx={{zIndex: theme => theme.zIndex.appBar - 2, mb: 2, boxShadow: 'none'}} elevation={24}>
            <Grid2 container spacing={0} sx={{position: 'relative'}}>
                <Grid2 size={{xs: "auto"}} order={0} sx={{p: 2}}>
                    <Button 
                        variant='outlined' 
                        color='inherit' 
                        onClick={onToday} 
                        disabled={loading || disabled}
                    >
                        {t('calendar:today')}
                    </Button>
                </Grid2>
                <Grid2 size={{xs: "grow"}} order={isMobile ? 2 : 1} sx={{p: 2, pl: 0}}>
                    <Stack direction='row' justifyContent='flex-start' alignItems='center' >
                        <Tooltip title={t('general:back')}>
                            <span>
                                <IconButton onClick={onPrev} size='small' disabled={loading || disabled}>
                                    <PrevIcon />
                                </IconButton>
                            </span>
                        </Tooltip>
                        <Tooltip title={t("general:next")}>
                            <span>
                                <IconButton onClick={onNext} size='small' disabled={loading || disabled}>
                                    <NextIcon />
                                </IconButton>
                            </span>
                        </Tooltip>
                        <Title sx={{ml:1}} title={calendarTitle} />
                    </Stack>
                </Grid2>
                {showCalendarType &&
                    <Grid2 size={{xs: 6, lg: "auto"}} order={isMobile ? 1 : 2} sx={{textAlign: 'right', p: 2}}>
                        <Button
                            id='calendar-type-button'
                            aria-controls={typeOpen ? 'calendar-type-menu' : undefined}
                            aria-haspopup='true'
                            aria-expanded={typeOpen ? 'true' : undefined}
                            onClick={handleTypeClick}
                            variant='outlined'
                            color='inherit'
                            disabled={loading || disabled}
                        >
                            {t(`calendar:${calendarType}`)}
                        </Button>
                        <Menu
                            id='calendar-type-menu'
                            anchorEl={typeEl}
                            open={typeOpen}
                            onClose={handleTypeClose}
                            MenuListProps={{'aria-labelledby': 'calendar-type-button'}}
                        >
                            {calendarTypes.map((option) => (
                                <MenuItem 
                                    key={`calendar-type-option-${option.value}`} 
                                    onClick={e => {
                                        onCalendarTypeChange(option.value);
                                        handleTypeClose();
                                    }}
                                >
                                    {t(`calendar:${option.slug}`)}
                                </MenuItem>
                            ))}
                        </Menu>
                    </Grid2>
                }
                {showSearchBar &&
                    <Grid2 size={{xs: 12}} order={3} sx={{pb: 2, pr: 2}}>
                        <DataTableSearchInput disabled={disabled} onSearchChange={value => onSearchTextChange(value)} variant="standard" sx={{mt: 1}} />
                    </Grid2>
                }
                {loading && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', bottom: 0, zIndex: theme => theme.zIndex.appBar}} />}
            </Grid2>
        </AppBar>
    );
}