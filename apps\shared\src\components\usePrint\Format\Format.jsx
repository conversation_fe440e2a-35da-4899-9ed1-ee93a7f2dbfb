import React, { useCallback, useEffect } from 'react';
import clsx from 'clsx';
import Portal from '../../Portal';

/*
Creates a portal to render a document for printing outside of the main screen
*/
export const Format = React.forwardRef(({ 
    format = "",  // the print format
    slots, // the slots to render ({header: the header slot, body: the body slot, footer: the footer slot})
    slotProps, // the props to pass to the slots
    onRender, // the callback to trigger when the component is rendered
    preview = false, // whether to render the preview or not, if not, it will render the format inside a portal
    className, // additional class names to apply to the wrapper (used for different print formats)
    ...props // additional props
}, ref) => {

    // renders a slot and inject a class name (that we use for styling the format)
    const renderSlot = useCallback((slot, slotProps = {}, className = "") => {
        if (!slot) return null;
        if (typeof slot === 'function') slot = slot({...slotProps});
        return React.cloneElement(slot, {...slotProps, className: clsx(slot.props.className, className)});
    }, [format]);

    const Component = (
        <div ref={ref} className={className}>
            {slots?.header && renderSlot( // header
                slots.header, 
                slotProps?.header || {}, 
                clsx(className, slotProps?.header?.className)
            )}
            {slots?.body && renderSlot( // body
                slots.body, 
                slotProps?.body || {}, 
                clsx(className, slotProps?.body?.className)
            )}
            {slots?.footer && renderSlot( // footer
                slots.footer, 
                slotProps?.footer || {},
                clsx(className, slotProps?.footer?.className)
            )}
        </div>
    );

    useEffect(() => {
        if (preview) onRender();
    }, [preview, onRender]);

    if (!format || !slots) return null;
    else if (preview) return Component;
    else return (
        <Portal onRender={onRender} {...props} portalProps={slotProps?.portal} iframeProps={slotProps?.iframe}>
            {Component}
        </Portal>
    );
});