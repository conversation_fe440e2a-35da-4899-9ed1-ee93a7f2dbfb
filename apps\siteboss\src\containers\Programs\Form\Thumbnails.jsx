import React, { useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { WithSortable } from '@siteboss-frontend/shared/components';
import { uuid } from '@siteboss-frontend/shared/utils';
import { Stack, Paper, IconButton, Tooltip, Box } from '@mui/material';
import { DeleteOutlined as DeleteIcon } from '@mui/icons-material';

const Thumb = ({ item, alt, listeners }) => {
    if (!item || !item?.src) return null;

    return (
        <Stack
            useFlexGap
            sx={{width: '100%', height: '100%', order: 1}}
            justifyContent="center"
            alignItems="center"
            {...listeners}
        >
            <img src={item.src} alt={alt} style={{maxWidth: 96, maxHeight: 96, objectFit: "contain"}} />
        </Stack>
    );
};

const Toolbar = ({ id, onDelete, ...props }) => {
    const { t } = useOutletContext();
    return (
        <Stack direction='row' justifyContent='flex-end' alignItems='center' sx={{...props?.sx}}>
            <Tooltip title={t('general:remove')}>
                <IconButton onClick={e => onDelete(id)} size='small'>
                    <DeleteIcon fontSize='inherit' />
                </IconButton>
            </Tooltip>
        </Stack>
    );
};

export const Thumbnails = ({ urls, fieldName, onChange }) => {
    const { colorMode } = useOutletContext();

    const items = useMemo(() => {
        if (!urls || !Array.isArray(urls)) return [];
        return urls.map(url => {
            // Handle different formats of URLs
            let src = '';
            if (typeof url === 'string') {
                src = url;
            } else if (url && url.preview_url) {
                src = url.preview_url;
            } else if (url && url.url) {
                src = url.url;
            }
            return {
                id: uuid(),
                src: src,
                original: url
            };
        }).filter(a => a.src);
    }, [urls]);

    const handleItemsReorder = useCallback(reorder => {
        if (onChange) {
            const reorderedItems = reorder(items);
            onChange({
                target: {
                    name: fieldName,
                    value: reorderedItems.map(item => item.original)
                }
            });
        }
    }, [fieldName, onChange, items]);

    const handleItemDelete = useCallback(id => {
        const updatedItems = items.filter(item => item.id !== id);
        if (onChange) {
            onChange({
                target: {
                    name: fieldName,
                    value: updatedItems.map(item => item.original)
                }
            });
        }
    }, [fieldName, onChange, items]);

    if (!items || items.length === 0) {
        return (
            <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: 1, textAlign: 'center' }}>
                No images selected
            </Box>
        );
    }

    return (
        <Box>
            <WithSortable
                id={`${fieldName}-thumbnails`}
                items={items}
                setItems={handleItemsReorder}
                isEditing={true}
                rows={1}
                cols={4}
                gap={1}
                component={React.forwardRef((props, ref) => (
                    <Stack
                        ref={ref}
                        component={Paper}
                        direction="column"
                        square
                        alignItems="center"
                        justifyContent="center"
                        variant="outlined"
                        {...props}
                        sx={{mt: 0.5, ...props?.sx}}
                    />
                ))}
                slots={{
                    toolbar: slot => <Toolbar {...slot} id={slot.item.id} />,
                    item: slot => <Thumb item={slot.item} alt={fieldName} listeners={slot.listeners} />,
                }}
                slotProps={{
                    toolbar: {
                        isEditing: true,
                        onDelete: id => handleItemDelete(id),
                        sx: {order: 2},
                    },
                    sortableItem: {
                        hideOnDrag: false,
                    },
                    board: {
                        sx: {
                            p: 1,
                            minHeight: 145,
                            background: colorMode === "dark" ? null : theme => theme.palette.action.disabledBackground
                        }
                    },
                    itemContainer: {
                        width: () => 1,
                        height: () => 1,
                        display: 'flex',
                        flexDirection: 'column',
                    }
                }}
            />
        </Box>
    );
};

export default Thumbnails;
