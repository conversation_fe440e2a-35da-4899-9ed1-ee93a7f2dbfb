import { useState, useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useApi } from '../../api/useApi';

const apiParams = {
    enableCache: true, 
    params: {
        endpoint: `/public/product`, 
        method: 'POST', 
        data: {product_status_id: 1, max_records: 999, include_child_categories: 1, include_media: 1},
    }
};

export const useProducts = ({
    itemsToLoad = null, 
    ...props
}) => {
    const currentShopItemCategory = useSelector(state => state.currentShopItem?.productCategoryId);
    const currentShopItemType = useSelector(state => state.currentShopItem?.productTypeId);
    const currentShopItemSearch = useSelector(state => state.currentShopItem?.search);

    const [allProducts, setAllProducts] = useState([]);
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);

    const {fetchData: fetchProducts, errors: productsErrors, ErrorBar, LoadingBar} = useApi(apiParams);

    const fetchData = useCallback(async extraParams => {
        const params = {...extraParams};
        if (currentShopItemCategory) params.categories = Array.isArray(currentShopItemCategory) ? [...currentShopItemCategory] : [currentShopItemCategory];
        if (currentShopItemType) params.product_types = Array.isArray(currentShopItemType) ? [...currentShopItemType] : [currentShopItemType];
        if (currentShopItemSearch) params.search = currentShopItemSearch;

        setLoading(true);
        try{
            const res = await fetchProducts(params);
            if (res.errors) setErrors(res.errors);
            else if (res.data?.products){
                setAllProducts(res.data?.products);
                if (itemsToLoad) {
                    setFilteredProducts(res.data?.products.slice(0, itemsToLoad));
                }
                return res.data?.products;
            }
        } catch (error){
            console.log(error)
        } finally {
            setLoading(false);
        }
        return null;
    }, [currentShopItemSearch, currentShopItemType, currentShopItemCategory, itemsToLoad, fetchProducts]);

    const handleSelectProduct = useCallback(value => {
        setSelectedProduct(value);
    }, []);

    useEffect(() => {
        if (productsErrors) setErrors(productsErrors);
    }, [productsErrors]);

    return {
        products: itemsToLoad ? filteredProducts : allProducts,
        allProducts,
        loading,
        errors,
        ErrorBar,
        LoadingBar,
        fetchData,
        setFilteredProducts,
        setLoading,
        handleSelectProduct,
        selectedProduct,
    };
};