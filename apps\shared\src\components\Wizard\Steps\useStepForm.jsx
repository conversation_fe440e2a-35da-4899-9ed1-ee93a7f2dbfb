import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useForm from '../../useForm';
import { useFormContext } from '../../useForm/FormContext';

export const useStepForm = ({fields, stepKey, onError, ...props}) => {
    const { t } = useTranslation();
    const { formData, saveData } = useFormContext();
    const [ errors, setErrors ] = useState({});

    /*const handleLocalSubmit = useCallback(async (formValues) => {
        if (formValues) saveData(stepKey, formValues); // saves the form values to the context
    }, [saveData, stepKey]);*/

    const { values, errors: formErrors, handleChange: handleFormChange, setFormValues } = useForm(fields, { validate: () => {/*we disable the validate because we're doing it inline in handleChange */} });

    const handleBlur = useCallback(e => {
        let validationErrors = {};

        if (values){
            const _fields = values.filter(a => a && a.display !== false && a.required && (Array.isArray(a.value) ? !a.value.length : !a.value));
            _fields.forEach(field => {
                validationErrors[field.name] = "error:required";
            });
        }

        onError(validationErrors || {});
    }, [values, onError]);

    const handleChange = useCallback(e => {
        // when doing a bulk update, we send the array of fields to update in place of the e(vent)
        if (Array.isArray(e)){
            const _values = e.reduce((acc, curr) => {
                if (curr.name) acc.push(curr);
                else {
                    const name = Object.keys(curr)[0];
                    acc.push({ name, value: curr[name] });
                }
                return acc;
            }, []);
            setFormValues(_values);
            saveData(stepKey, _values);
            return;
        }

        // when its a single update we proceed as normal and validate the field
        let _errored = false;
        const { name, value, type, checked } = e.target;

        handleFormChange(e); // we call the change function on the form hook to update the field
        saveData(stepKey, { name, value, checked: (type === "checkbox" || type === "radio") ? !!checked : undefined });

        if (fields.find(a => a && a.name === name)?.required) { // validate the field
            if (!value || ((type === "checkbox" || type === "radio" ) && !checked)) {
                _errored = true;
                setErrors(prev => ({...prev, [name]: t("error:required")}));
                onError({[name]: t("error:required")});
                return;
            } else {
                setErrors(prev => {
                    delete prev[name];
                    return prev;
                });
            }
        }

        if (!_errored) {
            //handleBlur(e);
        }
    }, [handleFormChange, saveData, stepKey, onError, t, fields]);


    // load the form values from the context
    useEffect(() => {
        if (formData) {
            setFormValues(formData?.[stepKey] || null);
        }
    }, [formData, stepKey, setFormValues]);

    useEffect(() => {
        if (formErrors) {
            setErrors(prev => ({...prev, ...formErrors}));
            onError(formErrors);
        }
    }, [formErrors]);

    useEffect(() => { // ¯\_(ツ)_/¯
        handleBlur();
    }, [handleBlur]);

    return {
        values,
        errors,
        handleChange,
        handleBlur,
        setFormValues,
        formData,
    };
}