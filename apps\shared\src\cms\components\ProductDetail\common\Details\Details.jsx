import React, { useCallback, useContext, useRef, useMemo, useLayoutEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Box, Paper, useMediaQuery } from '@mui/material';
import { Gallery, Title, LoadingBar, ErrorBar, ErrorBox } from '../../../../../components';
import { toCamelCase } from '../../../../../utils/string';

import { PosProductDetailContext } from '../../../../hooks/PosProductDetailContext';
import ProductVariants from '../ProductVariants';
import ProductAddons from '../ProductAddons';
import ProductEvents from '../ProductEvents';
import ProductGiftCards from '../ProductGiftCards';
import ProductMemo from '../ProductMemo';
import AddToCart from '../AddToCart';
import EventInfo from '../ProductEvents/Event/EventInfo';

/*
This is the product detail view, which should show a picture, prices, etc. 
In the POS, it should load in a modal, but it could also be used in a regular page like for an e-commerce shop. 
The layout is controlled via slotProps, which is passed to the children components. For example you may want to load the product variants as buttons (on the POS) or as radio button (for an ecommerse site).
*/
export const Details = ({
    gallery: galleryProps,          // props for the gallery component
    variants: variantsProps,        // props for the variants component
    addons: addonsProps,            // props for the addons component
    events: eventsProps,            // props for the events component
    eventUsers: eventUsersProps,    // props for the event users component
    giftCards: giftCardsProps,      // props for the giftcard component
    memo: memoProps,                // props for the memo component
    cartButton: cartButtonProps,    // props for the cart button component
    onAddToCart,
    slotProps,
    isEdit = false,
    replacePageTitle,               // the page title to be replaced with the item's name
    ...props 
    // everything else comes from the context
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    //const { handleAddToCart } = useContext(PosContext);
    const { 
        product,
        media,
        variant,
        forUserIds,
        giftCardRecipient,
        addons,
        modalSize,
        setErrors, 
        errors,
        loading,
        currentShopItem,
        handleVariantChange = () => {},
        handleForUserChange = () => {},
        handleGiftCardChange = () => {},
        handleAddonChange = () => {},
        handleMemoSave = () => {},
    } = useContext(PosProductDetailContext) || {};

    const headerRef = useRef(null);
    const footerRef = useRef(null);
    const hideQuantity = Boolean((product?.events && product?.events?.length > 0) || product?.product_type_id === 12);

    const allProps = useMemo(() => {
        let hides = {}, rest = {};
        const {hide: hideGallery, ...galleryRest} = galleryProps || {};
        const {hide: hideVariants, ...variantsRest} = variantsProps || {};
        const {hide: hideAddons, ...addonsRest} = addonsProps || {};
        const {hide: hideEvents, ...eventsRest} = eventsProps || {};
        const {hide: hideEventUsers, ...eventUsersRest} = eventUsersProps || {};
        const {hide: hideGiftCards, ...giftCardsRest} = giftCardsProps || {};
        const {hide: hideMemo, ...memoRest} = memoProps || {};

        rest = {
            gallery: galleryRest,
            variants: variantsRest,
            addons: addonsRest,
            events: eventsRest,
            eventUsers: eventUsersRest,
            giftCards: giftCardsRest,
            memo: memoRest,
        };
        hides = {
            gallery: hideGallery,
            variants: hideVariants,
            addons: hideAddons,
            events: hideEvents,
            eventUsers: hideEventUsers,
            giftCards: hideGiftCards,
            memo: hideMemo,
        };
        return {...rest, hides};
    }, [galleryProps, variantsProps, addonsProps, eventsProps, eventUsersProps, giftCardsProps, memoProps]);
    
    const handleAddToCartClick = useCallback(() => {
        if (!variant || !product) return;
        if (onAddToCart) onAddToCart(product, currentShopItem);
    }, [product, onAddToCart, currentShopItem, variant]);

    const bodySx = useMemo(() => ({
        ...slotProps?.body?.sx, 
        //maxHeight: (modalSize && !isMobile) ? `calc(100vh - ${(headerRef.current?.clientHeight || 0) + (footerRef.current?.clientHeight || 0) + (12*16)}px)` : '100%',
        //overflowY: 'auto',
        mb: isMobile ? 10 : undefined,
    }), [slotProps?.body?.sx, headerRef.current?.clientHeight, footerRef.current?.clientHeight, isMobile, modalSize]);

    useLayoutEffect(() => {
        if (replacePageTitle && product?.name) {
            document.title = replacePageTitle?.replace(/\{([^}]+)\}/g, product.name || '');
        }
    }, [product?.name, replacePageTitle]);

    if (!product && !loading) {
        return (
            <Stack direction={isMobile ? "column" : "row"} spacing={2} useFlexGap alignItems="center" justifyContent="center" pb={3}>
                <ErrorBox width={200} />
                <Title title={t("error:oops")} subtitle={t("error:contactUs")}></Title>
            </Stack>
        );
    }

    return (
        <>
            <Paper 
                elevation={24}
                square
                ref={headerRef}                
                {...slotProps?.header}
                sx={{...slotProps?.header?.sx, width:'100%', boxShadow:'none', pb:2, position: (modalSize && !isMobile) ? 'sticky' : 'relative', top: 0, zIndex: theme => theme.zIndex.appBar - 1}}
            >
                <Title 
                    title={`${product?.name}`} 
                    subtitle={product?.events?.length > 0 ? undefined : (product?.description || t(`product:productTypes.${toCamelCase(product?.product_type_name)}`, product?.product_type_name))}
                >
                    {/*
                    <Box sx={{mt: 2, width: '100%', overflow: 'hidden'}}>
                        <Stack direction='row' spacing={1/2}  sx={{maxWidth: '100%', mb: 2}} flexWrap='wrap' useFlexGap>
                            {product?.categories?.map(cat => <Chip key={cat.id} label={cat.name} />)}
                            {product?.events?.[0]?.tags?.map(cat => <Chip key={cat.id} label={cat.name} color="success" />)}
                            {/*product?.events?.map(event => <Chip key={event.id} color="success" label={event.location.name} />)*//*}
                        </Stack>
                    </Box>
                    */}
                </Title>
            </Paper>
            <Stack direction="column" spacing={2} useFlexGap {...slotProps?.body} sx={bodySx} position="relative">
                {loading &&
                    <LoadingBar type='linear' sx={{height: '2px', position: 'sticky', top: 0, zIndex: theme => theme.zIndex.appBar}} />
                }
                <ErrorBar open={Boolean(errors)} message={errors} onClose={()=>setErrors(null)} />
                {product?.events?.length === 1 &&
                    <Box sx={{width: '100%', overflow: 'hidden'}}>
                        <EventInfo events={product?.events || null} slotProps={slotProps?.events} />
                    </Box>
                }
                {media?.length > 0 && !allProps.hides.gallery &&
                    <Box {...slotProps?.gallery}>
                        <Gallery images={media} {...allProps.gallery} />
                    </Box>
                }
                {!allProps.hides.variants &&
                    <ProductVariants 
                        {...allProps.variants}
                        product={product}                    
                        variants={product?.product_variants || null} 
                        selectedVariantId={variant?.id || null} 
                        onSelect={handleVariantChange} 
                        slotProps={slotProps?.variants}
                    />
                }
                {!allProps.hides.addons &&
                    <ProductAddons
                        {...allProps.addons}
                        product={product}
                        selectedAddons={addons} 
                        onSelect={handleAddonChange} 
                        slotProps={slotProps?.addons}
                    />
                }
                <ProductEvents
                    events={product?.events || null} 
                    selectedUsers={forUserIds}
                    onSelect={handleForUserChange}
                    slotProps={{events: slotProps?.events, eventUsers: slotProps?.eventUsers}}
                    layouts={{events: {...allProps.events, hide: allProps.hides.events}, eventUsers: {...allProps.eventUsers, hide: allProps.hides.eventUsers}}}
                />
                {!allProps.hides.giftCards &&
                    <ProductGiftCards
                        {...allProps.giftCards}
                        recipient={giftCardRecipient}
                        product={product}
                        onSelect={handleGiftCardChange}
                        slotProps={slotProps?.giftcards}
                    />
                }
                {/* this should always be the last item*/
                !allProps.hides.memo &&
                    <ProductMemo 
                        {...allProps.memo}
                        product={product}
                        slotProps={slotProps?.memo}
                        onSave={handleMemoSave}
                    />
                }
            </Stack>
            
            <Stack 
                ref={footerRef} 
                direction="column" 
                spacing={2} 
                sx={{position: (modalSize && !isMobile) ? 'sticky' : isMobile ? 'fixed' : 'relative', left: 0, bottom: 0, zIndex: theme => theme.zIndex.appBar, width: '100%', mt: 2}}
            >
                <AddToCart 
                    {...cartButtonProps}
                    {...slotProps?.footer}
                    variantId={variant?.id || null} 
                    amount={(+currentShopItem?.productCustomPrice || +variant?.price || 0) + (+currentShopItem?.addons?.reduce((acc, a) => +acc + +a.price, 0) || 0)}
                    showQuantity={!hideQuantity} 
                    onAddToCart={handleAddToCartClick} 
                    isEdit={isEdit}
                    loading={loading}
                />
            </Stack>
        </>
    );
};