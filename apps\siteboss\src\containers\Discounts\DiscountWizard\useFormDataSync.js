import { useEffect } from 'react';
import { useFormContext } from '@siteboss-frontend/shared/components';
import { useDispatch, useSelector } from 'react-redux';
import { updateFormData } from '../../../store/reducers/discountWizardSlice';

/**
 * Custom hook to sync form data with Redux store
 * This hook listens for changes in the form context and updates the Redux store
 */
export const useFormDataSync = () => {
    const { formData } = useFormContext();
    const dispatch = useDispatch();
    const reduxFormData = useSelector(state => state.discountWizard.formData);

    // Update Redux store when form data changes
    useEffect(() => {
        console.log('useFormDataSync - Form Context Data:', formData);
        console.log('useFormDataSync - Current Redux Data:', reduxFormData);

        if (formData && JSON.stringify(formData) !== JSON.stringify(reduxFormData)) {
            console.log('useFormDataSync - Updating Redux with:', formData);
            dispatch(updateFormData(formData));
        }
    }, [formData, reduxFormData, dispatch]);

    return null;
};

export default useFormDataSync;
