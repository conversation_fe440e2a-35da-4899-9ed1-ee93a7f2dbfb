import React from 'react';
import clsx from 'clsx';
import { Box, useMediaQuery } from '@mui/material';
import { getMimeTypeFromExtension } from '@siteboss-frontend/shared/utils';

import styles from './Background.module.scss';

export const Background = (props) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    let bgMimeType = null;
    if (props.url) bgMimeType = getMimeTypeFromExtension(props.url)
    if (!props.url || isMobile) return null;

    return (
        <>
            {bgMimeType && bgMimeType.includes('image') &&
                <Box className={clsx(styles.wrapper, styles.image)} sx={{backgroundImage: ` url(${props.url})` }} />
            }
            {bgMimeType && bgMimeType.includes('video') &&
                <video autoPlay muted loop className={styles.wrapper}>
                    <source src={props.url} type={bgMimeType} />
                </video>
            }
        </>
    );
}