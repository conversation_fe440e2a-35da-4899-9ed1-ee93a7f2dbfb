import { promises as fs, createWriteStream } from 'fs';
import express from 'express';
import dotenv from 'dotenv';
import { createGzip } from 'zlib';
import { SitemapStream, streamToPromise } from 'sitemap';
import { createServer as createViteServer } from 'vite';
import * as utils from './src/utils.js';

dotenv.config();
const isProduction = process.env.NODE_ENV === 'production';
const port = process.env.VITE_APP_PORT || 3000;
const base = process.env.VITE_APP_BASE || '/';
const apiUrl = process.env.VITE_API_URL || 'https://api-dev.siteboss.net/api';

const errorLogStream = createWriteStream('errors.log', { flags: 'a' });
const dataLogStream = createWriteStream('data.log', { flags: 'a' });
const showConsoleLogs = !isProduction;

(async () => {
	let vite, sitemap, infoData, infoError, websiteUrl;
	const templateHtml = isProduction ? await fs.readFile('./dist/client/index.html', 'utf-8') : '';
	const ssrManifest = isProduction ? await fs.readFile('./dist/client/.vite/ssr-manifest.json', 'utf-8') : undefined;

	const logError = (error, writeToFile = true) => {
		const errorMessage = `${new Date().toISOString()} - ${error.stack || error}\n`;
		if (writeToFile) errorLogStream.write(errorMessage);
		console.error(errorMessage);
	};
	
	const logData = (...args) => {
		if (!showConsoleLogs) return;		
		const timestamp = new Date().toISOString();
		const dataMessage = args.map(arg => JSON.stringify(arg)).join('\n');		
		const formattedMessage = `${timestamp}\n${dataMessage}\n`;
		dataLogStream.write(formattedMessage);
		console.log(formattedMessage);
	};

	process.on('uncaughtException', error => {
		logError(`Uncaught Exception: ${error.stack || error}`);
	}).on('unhandledRejection', (reason, promise) => {
		logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
	}).on('exit', () => {
		errorLogStream.end();
		dataLogStream.end();
	});

	const app = express();

	// Move the theme info fetch into middleware so we have access to req.headers.host
	app.use(async (req, res, next) => {
		if (!infoData) {
			const host = req.headers.host.split(':')[0];
			const result = await utils.getThemeInfo(apiUrl, logData, logError, host);
			infoData = result.data;
			infoError = result.errors;
			if (infoData) websiteUrl = `https://${infoData?.company?.url || "siteboss.net"}`;
			if (infoError) logError(infoError);
		}
		next();
	});

	if (isProduction) {
		logData("Production mode: Serving compressed static assets.");
		const compression = (await import('compression')).default;
		const sirv = (await import('sirv')).default;
		app.use(compression()).use(base, sirv('./dist/client', { extensions: [] }));
	} else {
		logData("Development mode: Initializing Vite middleware.");
		vite = await createViteServer({
			server: { middlewareMode: 'html' },
			appType: 'custom',
			base,
		})
		logData("Development mode: Initialized.");
		app.use(vite.middlewares);
	}

	app.get('/llms.txt', async (req, res) => {
		/* LLMS.TXT
		-------- */	
		if (!infoData) {
			logError('No infoData in llms.txt');
			res.status(500).end();
		}
		logData('Llms.txt:', websiteUrl);

		let llmsTxt = `# ${infoData.company?.website_name || infoData.company?.name }`;	
		if (infoData.company.website_description) llmsTxt += `\n\n> ${infoData.company.website_description}`;
		if (infoData?.pages?.length > 0) {
			llmsTxt += `\n\n## Pages\n`;
			infoData.pages.forEach(page => {
				llmsTxt += `\n- [${page.title}](${websiteUrl+page.slug})${page.description ? `: ${page.description}` : ''}`;
			});
		}

		logData('Llms.txt generated.');

		res.header('Content-Type', 'text/plain');
		res.send(llmsTxt);
		return;	
	}).get('/robots.txt', async (req, res) => {
		/* ROBOTS.TXT
		---------- */
		if (!infoData) {
			logError('No infoData in robots.txt');
			res.status(500).end();
		}
		logData('Robots.txt:', websiteUrl);

	const robotsTxt = 
`User-agent: *
Allow: /
Sitemap: ${websiteUrl}/sitemap.xml

User-agent: *
User-agent: AdsBot-Google
Disallow: /p/`;

		logData('Robots.txt generated.');

		res.header('Content-Type', 'text/plain');
		res.send(robotsTxt);
		return;	
	}).get('/sitemap.xml', async (req, res) => {
		/* SITEMAP.XML
		----------- */
		res.header('Content-Type', 'application/xml');
		res.header('Content-Encoding', 'gzip');	
		
		if (sitemap) {
			res.send(sitemap);
			return;
		}

		if (!infoData) {
			logError('No infoData in sitemap.xml');
			res.status(500).end();
		}
		const hasIndex = false;
		let urls = [];
		if (infoData?.pages?.length > 0) {
			infoData.pages.forEach(page => {
				let changefreq = 'weekly';
				if (page.changefreq){
					switch(page.changefreq){
						case 1:
							changefreq = 'always';
							break;
						case 2:
							changefreq = 'hourly';
							break;
						case 3:
							changefreq = 'daily';
							break;
						case 5:
							changefreq = 'monthly';
							break;
						case 6:
							changefreq = 'yearly';
							break;
						case 7:
						case 0:
							changefreq = 'never';
							break;
						case 4:
						default:
							changefreq = 'weekly'
							break;
					}
				}

				const slug = `/${utils.removeFirstSlash(page.slug)}`;
				if (slug === `/${utils.removeFirstSlash(infoData?.index_page || "")}`) hasIndex = true;

				urls.push({ url: slug, changefreq, priority: page?.priority || 0.5 });
			});
			if (!hasIndex) urls = [{ url: '/', changefreq: 'daily', priority: 1 }, ...urls];
		}
		logData('Sitemap URLs:', websiteUrl, urls);

		const sm = new SitemapStream({ hostname: websiteUrl });
		const pipeline = sm.pipe(createGzip());
		urls.forEach(url => sm.write(url));
		streamToPromise(pipeline).then(s => sitemap = s);
		logData('Sitemap generated.');
		sm.end();
		pipeline.pipe(res).on('error', (e) => {logError(e)});
	}).use('*', async (req, res) => {
		/* WEB PAGE
		-------- */
		logData(`Received request for: ${req.originalUrl}`);
		try {
			const url = req.originalUrl.replace(base, '');
			const host = req.headers.host.split(':')[0];
			logData(`Processing URL: ${url}, Host: ${host}`);

			let template, render;
			if (isProduction) {
				if (showConsoleLogs) {
					logData('Running in production mode...');
					logData('Loaded production template.');
				}
				template = templateHtml;
				render = (await import('./dist/server/entry-server.js')).render;
			} else {
				logData('Running in development mode...');
				template = await fs.readFile('./index.html', 'utf-8');
				logData('Read index.html template in development.');
				template = await vite.transformIndexHtml(url, template);
				logData('Transformed template using Vite.');
				render = (await vite.ssrLoadModule('/src/entry-server.jsx')).render;
			}

			logData('Fetching page data...');
			const {data, error} = await utils.getInfo({url, infoData, host}, apiUrl, logData, logError);

			if (error) {
				logError(error);
				if (error.stack && !isProduction) vite?.ssrFixStacktrace(error);
				res.status(500).end(isProduction ? "Internal Server Error" : (error.stack || error));
			} else if (data) {
				logData('Rendering SSR content...');
				const {html: renderedHtml, css: emotionCss, title} = await render({...data, url});
				//logData('SSR Rendered HTML:', renderedHtml);
				//logData('SSR Rendered CSS:', emotionCss);

				const html = template
					.replace(`<!--app-head-->`, emotionCss || '')
					.replace(`<!--app-html-->`, renderedHtml || '')
					.replace(`<!--app-title-->`, title || `${data?.metadata?.title} - ${data.company?.website_name ? `${data.company?.website_name} | ` : ""}${data.company?.name || ""}` || 'SiteBoss')
					.replace(`<!--app-icon-->`, data.company?.logo || '/favicon.svg')
					.replace(`<!--app-description-->`, data?.metadata?.description || data.company?.website_description || '')
					.replace(`<!--app-keywords-->`, data?.metadata?.keywords || data.company?.website_keywords || '');

				logData('Final HTML response constructed.');
				res.status(200).set({ 'Content-Type': 'text/html' }).send(html);
			} else {
				logError("No page data found.");
				res.status(404).end();
			}
		} catch (e) {
			logError(e);
			if (isProduction) res.status(500).end("Internal Server Error");
			else {
				vite?.ssrFixStacktrace(e);
				res.status(500).end(e.stack);
			}
		}
	}).listen(port, () => {
		if (showConsoleLogs) console.log(`Server started at http://localhost:${port}`);
	});

})();
