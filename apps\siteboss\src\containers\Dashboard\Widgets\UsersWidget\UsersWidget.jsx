import React, { useCallback, useMemo, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Stack, Box, Typography, List, Divider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, FormItem } from '@siteboss-frontend/shared/components';

import Header from '../common/Header';
import { Item } from './Item';

// Mock data - in a real app, this would come from an API
const mockUsers = [
    { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', role: 'Staff', status: 'active', profile_img_path: "https://randomuser.me/api/portraits/men/1.jpg", last_login: '2023-10-15T14:30:00Z' },
    { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', role: 'Company Admin', status: 'active', profile_img_path: "https://randomuser.me/api/portraits/women/2.jpg", last_login: '2023-10-16T09:15:00Z' },
    { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', role: '<PERSON>', status: 'inactive', profile_img_path: "https://randomuser.me/api/portraits/men/3.jpg", last_login: '2023-09-30T11:45:00Z' },
    { id: 4, first_name: 'Emily', last_name: 'Davis', email: '<EMAIL>', role: 'Company Owner', status: 'active', profile_img_path: "https://randomuser.me/api/portraits/women/4.jpg", last_login: '2023-10-16T16:20:00Z' },
    { id: 5, first_name: 'Michael', last_name: 'Wilson', email: '<EMAIL>', role: 'Staff', status: 'active', profile_img_path: "https://randomuser.me/api/portraits/men/5.jpg", last_login: '2023-10-14T08:10:00Z' },
    { id: 6, first_name: 'Sarah', last_name: 'Brown', email: '<EMAIL>', role: 'Non-staff Manager', status: 'inactive', profile_img_path: "https://randomuser.me/api/portraits/women/6.jpg", last_login: '2023-10-01T13:25:00Z' },
];

// Role distribution data for pie chart
const roleDistributionData = {
    headers: ["Role", "Count"],
    data: [
        { id: "Staff", label: "Staff", value: 3 },
        { id: "Company Admin", label: "Company Admin", value: 1 },
        { id: "Company Owner", label: "Company Owner", value: 1 },
        { id: "Non-staff Manager", label: "Non-staff Manager", value: 1 }
    ]
};

export const UsersWidget = ({ settings = {}, isEditing, ...props }) => {
    const { t, language } = useOutletContext();
    const [showActive, setShowActive] = useState(settings.showActive !== undefined ? settings.showActive : true);
    const [showInactive, setShowInactive] = useState(settings.showInactive !== undefined ? settings.showInactive : false);

    const handleActiveChange = useCallback(e => {
        setShowActive(e.target.checked);
    }, []);

    const handleInactiveChange = useCallback(e => {
        setShowInactive(e.target.checked);
    }, []);

    // Filter users based on status settings
    const filteredUsers = useMemo(() => mockUsers.filter(user =>
        (showActive && user.status === 'active') ||
        (showInactive && user.status === 'inactive')
    ), [showActive, showInactive]);

    return (
        <Stack data-cy="users-widget" direction="column" spacing={2} useFlexGap sx={{height: "100%"}}>
            <Header title={t('widget:users.title')}>
                <Stack direction="row" spacing={1} useFlexGap>
                    <FormItem 
                        component="Switch"
                        name="showActive"
                        label={t('widget:users.showActive')}
                        value="1"
                        checked={showActive}
                        onChange={handleActiveChange}
                        labelPlacement="start"
                        size="small"
                    />
                    <FormItem 
                        component="Switch"
                        name="showInactive"
                        label={t('widget:users.showInactive')}
                        value="1"
                        checked={showInactive}
                        onChange={handleInactiveChange}
                        labelPlacement="start"
                        size="small"
                    />
                </Stack>
            </Header>

            <Box sx={{ display: 'flex' }}>
                <Box sx={{ flex: 3, overflow: 'auto', pr: 2 }}>
                    <List component="ul" sx={{ width: '100%' }}>
                        {filteredUsers.map((user, i) => (                            
                            <Item key={user.id} user={user} />
                        ))}
                    </List>
                </Box>

                <Box sx={{ flex: 2, display: { xs: 'none', md: 'block' } }}>
                    <Typography variant="subtitle2" align="center" gutterBottom>
                        {t('widget:users.roleDistribution')}
                    </Typography>
                    <PieChart
                        data={roleDistributionData}
                        height={200}
                        margin={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        innerRadius={0.5}
                        padAngle={0.5}
                        cornerRadius={3}
                        activeOuterRadiusOffset={8}
                        colors={{ scheme: 'paired' }}
                        borderWidth={1}
                        borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
                        enableArcLinkLabels={false}
                        arcLabelsSkipAngle={10}
                        arcLabelsTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
                    />
                </Box>
            </Box>
        </Stack>
    );
};