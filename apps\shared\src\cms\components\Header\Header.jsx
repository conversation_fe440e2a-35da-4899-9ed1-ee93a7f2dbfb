import React, { useMemo } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { Stack, Typography, Box, Toolbar, AppBar } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { MenuProvider, Menu, ToggleButton } from '../common/Menu';
import { layouts } from './layouts';
import { properties } from './properties';

const Link = ({to = "/", children, isBuilder, ...props}) => {
    if (isBuilder) return <React.Fragment>{children}</React.Fragment>;
    
    return (
        <RouterLink to={to} {...props}>
            {children}
        </RouterLink>
    );
}

export const Header = React.forwardRef(({
    id,
    logo,
    title,
    logoPosition,
    showMenu,
    fetchPages,
    showCart,
    showProfile,
    cartType,
    profileType,
    shopId,
    sticky,
    menuItems = [],
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        toolbar: {},        // MUI toolbar props
        title: {},          // MUI typography props
        logo: {},           // M<PERSON> typography props
        menu: {},           // MUI typography props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    children,
    builderProps,
    ...props
}, ref) => {
    const parentBuilder = isBuilder; // this is used to determine if the page that is loading this component is in a builder
    if (+props.pageTypeId !== 9) isBuilder = false; // only templates can interact

    const { slotProps: updatedSlotProps, canRender, isMobile, customCss } = prepareComponent({name: "header", layoutId, layouts, slotProps, isBuilder: parentBuilder, condition});
    slotProps = updatedSlotProps;

    if (Array.isArray(logo)) logo = logo[0];

    const bgColor = useMemo(() => {
        let color = undefined;
        if (slotProps?.cmsContainer?.sx?.backgroundColor) color = slotProps?.cmsContainer?.sx?.backgroundColor;
        if (slotProps?.cmsContainer?.sx?.bgcolor) color = slotProps?.cmsContainer?.sx?.bgcolor;
        if (slotProps?.cmsStack?.sx?.backgroundColor) color = slotProps?.cmsStack?.sx?.backgroundColor;
        if (slotProps?.cmsStack?.sx?.bgcolor) color = slotProps?.cmsStack?.sx?.bgcolor;
        if (slotProps?.toolbar?.sx?.backgroundColor) color = slotProps?.toolbar?.sx?.backgroundColor;
        if (slotProps?.toolbar?.sx?.bgcolor) color = slotProps?.toolbar?.sx?.bgcolor;
        return color;
    }, [slotProps]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            ref={ref}
            {...builderProps}
        >
            {customCss}
            <Stack component={AppBar} direction={isMobile ? "column" : "row"} position={sticky ? "sticky" : "relative"} enableColorOnDark spacing={0} useFlexGap {...slotProps?.cmsStack} sx={{
                ...slotProps?.cmsStack?.sx,
                bgcolor: bgColor,                
            }}>
                <MenuProvider 
                    items={menuItems} 
                    type={showMenu && isMobile ? "responsive" : "horizontal"} 
                    fetchPages={fetchPages} 
                    isBuilder={parentBuilder}
                    showCart={showCart} 
                    cartType={cartType} 
                    showProfile={showProfile} 
                    profileType={profileType}
                    shopId={shopId}
                >
                    <Stack direction={"row"} spacing={0} useFlexGap alignItems={"center"} sx={{width: '100%'}}>
                        { showMenu && isMobile && 
                            <div><ToggleButton isBuilder={parentBuilder}/></div>
                        }
                        <Toolbar disableGutters {...slotProps?.toolbar} sx={{width: '100%', ...slotProps?.toolbar?.sx}}>
                            <Link to={parentBuilder ? "#!" : "/"} isBuilder={parentBuilder}>
                                <Box 
                                    component="img" 
                                    src={logo ? logo : (isBuilder ? "https://placehold.co/96?font=source-sans-pro&text=LOGO" : undefined)} 
                                    alt={title} 
                                    ml={{xs: 0, md: 2}}
                                    mr={{xs: 5, md: 2}}
                                    {...slotProps?.logo} 
                                />
                            </Link>
                            <Typography variant="h1" {...slotProps?.title}>
                                {title}
                            </Typography>
                            
                            { showMenu && !isMobile && 
                                <>
                                    <Box sx={{ flexGrow: 1 }} />
                                    <Menu slotProps={slotProps?.menu} disabled={parentBuilder} />
                                </>
                            }
                        </Toolbar>
                    </Stack>
                    { showMenu && isMobile && <Menu slotProps={slotProps?.menu} disabled={parentBuilder} /> }
                </MenuProvider>
            </Stack>
            {children}            
        </CmsContainer>
    );
});