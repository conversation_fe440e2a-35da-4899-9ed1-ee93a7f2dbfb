import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Paper, Stack, Typography, Table, TableContainer, TableHead, TableBody, TableRow, TableCell, Button } from '@mui/material';
import { TagOutlined as MarkdownIcon } from '@mui/icons-material';

import { sampleText, cheatSheet } from './sampleText';
import Preview from '../Preview';

export const Help = ({onSampleClick}) => {
    const { t } = useTranslation();

    const handleCopy = useCallback(e => {
        if (onSampleClick) {
            onSampleClick(e, sampleText());
        }
    }, [onSampleClick]);

    return (
        <Stack spacing={1} direction="column" useFlexGap sx={{overflow: 'auto', maxHeight: '100%', width: '100%'}}>
            <div style={{alignSelf: 'flex-end'}}>
                <Button variant="text" color="secondary" onClick={handleCopy} startIcon={<MarkdownIcon />}>{t("markdown:useSample")}</Button>
            </div>
            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: '100%', overflow: 'auto'}}>
                <Table stickyHeader>
                    <TableHead>
                        <TableRow>
                            <TableCell>{t("markdown:type")}</TableCell>
                            <TableCell>{t("markdown:toGet")}</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {cheatSheet.map(({ slug, code, helper }) => {
                            let _slug = slug;
                            if (!Array.isArray(_slug)) _slug = [slug];
                            return (
                            <TableRow key={slug}>
                                <TableCell>
                                    <Typography component="div" variant="code" dangerouslySetInnerHTML={{ __html: helper(_slug.map(s => t(s)))}} />
                                </TableCell>
                                <TableCell>
                                    <Preview value={code(_slug.map(s => t(s))).replace(/<br\/>/g, '\n')} />
                                </TableCell>
                            </TableRow>
                            )
                        })}
                    </TableBody>
                </Table>
            </TableContainer>
        </Stack>
    );
};