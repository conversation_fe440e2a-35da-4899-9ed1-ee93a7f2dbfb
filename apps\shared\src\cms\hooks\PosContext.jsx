import React, { createContext, useMemo } from 'react';
import { usePos } from './usePos';

export const PosContext = createContext();
export const PosProvider = ({ children, isBuilder = false, selectLoggedInUser = false, ...props }) => {
    const posHook = usePos({isBuilder, selectLoggedInUser, ...props});
    
    const value = useMemo(() => ({
        ...posHook,
        isBuilder,
        selectLoggedInUser,
        ...props
    }), [posHook, props]);

    return (
        <PosContext.Provider value={value}>
            {children}
        </PosContext.Provider>
    );
};
