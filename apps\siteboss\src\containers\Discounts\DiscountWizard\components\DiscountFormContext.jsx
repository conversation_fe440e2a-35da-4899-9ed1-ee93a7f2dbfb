import React, { createContext, useContext, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';

const DiscountFormContext = createContext();

export const DiscountFormProvider = ({ children, initialData = {} }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData) || initialData;

    const resetForm = useCallback(() => {
        dispatch(updateFormData({}));
    }, [dispatch]);

    const saveData = useCallback((key, value) => {
        dispatch(updateFormData({
            ...formData,
            [key]: value
        }));
    }, [dispatch, formData]);

    return (
        <DiscountFormContext.Provider value={{ formData, saveData, resetForm }}>
            {children}
        </DiscountFormContext.Provider>
    );
};

export const useDiscountFormContext = () => useContext(DiscountFormContext);
