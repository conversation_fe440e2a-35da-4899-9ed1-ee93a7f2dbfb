import { createSlice } from '@reduxjs/toolkit';
import { uuid } from '../../utils/string';

const initial = {
	tmpId: uuid(),
	productTypeId: null,
	productCategoryId: null,
	productId: null,
	productName: null,
	productVariantId: null,		
	productCustomPrice: null,
	eventId: null,
	eventName: null,
	forUserIds: [],	
	customFields: [],
	addons: [],
	giftCardRecipient: {
		/*
			full_name: '<PERSON><PERSON><PERSON>',
			email: '<EMAIL>
			message: 'Just for you buddy, be nice!',
			delivery_date: new Date().toString(),
		*/
	},
	memo: null,
	qty: 1,	
	hasEvent: false,
	hasGiftCard: false,
	hasServiceBooking: false,
	productMedia: [],
	variantMedia: [],
	eventMedia: [],
};

export const currentShopItemSlice = createSlice({
	name: 'currentShopItem',
	initialState: {...initial},
	reducers: {
        setInfo: (state, action) => {
			state.hasEvent = false;
			state.hasGiftCard = false;
			state.hasServiceBooking = false;
			if ('tmpId' in action.payload) state.tmpId = action.payload.tmpId;
			if ('productTypeId' in action.payload) state.productTypeId = action.payload.productTypeId;
			if ('productCategoryId' in action.payload) state.productCategoryId = action.payload.productCategoryId;
			if ('productId' in action.payload) state.productId = action.payload.productId;
			if ('productName' in action.payload) state.productName = action.payload.productName;
			if ('productVariantId' in action.payload) state.productVariantId = action.payload.productVariantId;
			if ('addons' in action.payload) state.addons = action.payload.addons;
			if ('eventId' in action.payload) state.eventId = action.payload.eventId;
			if ('eventName' in action.payload) state.eventName = action.payload.eventName;
			if ('forUserIds' in action.payload) {
				state.forUserIds = action.payload.forUserIds;
				// remove custom fields for users that are not in the list
				state.customFields = state.customFields.filter(u => action.payload.forUserIds.find(fu => +fu.id === +u.userId));
			}
			if ('qty' in action.payload) state.qty = action.payload.qty;
			if ('hasEvent' in action.payload) state.hasEvent = action.payload.hasEvent;
			if ('hasGiftCard' in action.payload) state.hasGiftCard = action.payload.hasGiftCard;
			if ('hasServiceBooking' in action.payload) state.hasServiceBooking = action.payload.hasServiceBooking;
			if ('giftCardRecipient' in action.payload) state.giftCardRecipient = action.payload.giftCardRecipient;
			if ('productCustomPrice' in action.payload) state.productCustomPrice = action.payload.productCustomPrice;
			if ('memo' in action.payload) state.memo = action.payload.memo;
			if ('productMedia' in action.payload) state.productMedia = action.payload.productMedia;
			if ('variantMedia' in action.payload) state.variantMedia = action.payload.variantMedia;
			if ('eventMedia' in action.payload) state.eventMedia = action.payload.eventMedia;
        },
		setCustomFields: (state, action) => {
			const index = state.customFields.findIndex(u => +u.userId === +action.payload.userId);
			if (index > -1) state.customFields[index] = action.payload
			else state.customFields.push(action.payload);
		},
		resetProduct: (state) => {
			state.tmpId = uuid();
			state.productId = null;
			state.productName = null;
			state.productVariantId = null;
			state.productCustomPrice = null;
			state.eventId = null;
			state.eventName = null;
			state.forUserIds = [];
			state.customFields = [];
			state.addons = [];
			state.giftCardRecipient = {};
			state.qty = 1;
			state.hasEvent = false;
			state.hasGiftCard = false;
			state.hasServiceBooking = false;
			state.memo = null;
			state.productMedia = [];
			state.variantMedia = [];
			state.eventMedia = [];
		},
		removeForUser: (state, action) => {
			state.forUserIds = state.forUserIds.filter(u => +u.id !== +action.payload);
			state.customFields = state.customFields.filter(u => +u.userId !== +action.payload);
		},
		resetCustomFields: (state) => {
			state.customFields = [];
		},
		resetInfo: (state) => {
			for (const key in initial) {
				state[key] = initial[key];
			}
		},
	},
});

export const { setInfo, resetInfo, resetProduct, removeForUser, setCustomFields, resetCustomFields } = currentShopItemSlice.actions;
export default currentShopItemSlice.reducer;