import { Button } from '@mui/material';


describe("It will mount the component",()=>{

    context("it will mount and test",()=>{
        beforeEach(()=>{
            cy.log("Rawr")
            cy.mount(<Button children={"Rawr"} />, null)
        })

        it("will mount",()=>{
            cy.get('.MuiButtonBase-root')
                .should('exist')
            cy.get('button')
        })
    
        it("will check the text",()=>{
            cy.get('.MuiButtonBase-root')
                .invoke('text')
                .should("contain", "Rawr")
        })

    })
})