import React from 'react';
import { Stack } from '@mui/material';
import { FormItem } from '../../FormItem';

export const Form = ({ fields = [], errors, onChange, loading }) => {
    return (
        <Stack direction="column" spacing={0} useFlexGap>
            {fields.map(field => (
                <FormItem 
                    {...field}
                    key={field.name}
                    onChange={onChange}
                    errors={errors?.[field.name]}
                    loading={loading}
                />
            ))}
        </Stack>
    );
};