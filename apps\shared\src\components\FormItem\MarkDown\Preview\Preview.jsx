import React from 'react';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import rehypeHighlight from 'rehype-highlight';
import remarkBreaks from 'remark-breaks';
import { lighten, Box, Typography, Link, Paper, TableContainer, Divider, Table, TableHead, TableBody, TableRow, TableCell, TableFooter } from '@mui/material';

import styles from './Preview.module.scss';
import 'highlight.js/styles/github.css';

export const Preview = ({ value, enableHtml = true }) => {
    
    // map elements to MUI components
    const components = {
        h1: ({ node, ...props }) => <Typography variant="h1" gutterBottom sx={{mt: 2}} {...props} />,
        h2: ({ node, ...props }) => <Typography variant="h2" gutterBottom sx={{mt: 2}} {...props} />,
        h3: ({ node, ...props }) => <Typography variant="h3" gutterBottom sx={{mt: 2}} {...props} />,
        h4: ({ node, ...props }) => <Typography variant="h4" gutterBottom sx={{mt: 2}} {...props} />,
        h5: ({ node, ...props }) => <Typography variant="h5" gutterBottom sx={{mt: 2}} {...props} />,
        h6: ({ node, ...props }) => <Typography variant="h6" gutterBottom sx={{mt: 2}} {...props} />,
        p: ({ node, ...props }) => <Typography variant="body1" component="p" sx={{mb: 2}} {...props} />,
        a: ({ node, ...props }) => <Link {...props} target="_blank" color="inherit" rel="noopener noreferrer" />,
        ul: ({ node, ...props }) => <Typography gutterBottom component="ul" {...props} />,
        ol: ({ node, ...props }) => <Typography gutterBottom component="ol" {...props} />,
        li: ({ node, ...props }) => <Typography component="li" {...props} />,
        div: ({ node, ...props }) => <Box {...props} />,
        table: ({ node, ...props }) => <TableContainer component={Paper} variant="outlined" sx={{my: 2, mb: 4}}><Table stickyHeader {...props} /></TableContainer>,
        thead: ({ node, ...props }) => <TableHead {...props} />,
        tbody: ({ node, ...props }) => <TableBody {...props} />,
        tfoot: ({ node, ...props }) => <TableFooter {...props} />,
        tr: ({ node, ...props }) => <TableRow {...props} />,
        th: ({ node, ...props }) => <TableCell {...props} />,
        td: ({ node, ...props }) => <TableCell {...props} />,
        hr: ({ node, ...props }) => <Divider sx={{my: 4}} {...props} />,
        code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ?
                <Paper 
                    variant="outlined"
                    component="pre"
                    sx={{ 
                        backgroundColor: theme => lighten(theme.palette.background.paper, 0.05), 
                        py: 2, 
                        px: 4, 
                        my: 2,
                        overflow: 'auto', 
                    }}
                    {...props}
                >
                    {children}
                </Paper>
            : 
                <Typography
                    component="code"
                    sx={{
                        backgroundColor: theme => theme.palette.action.disabledBackground,
                        px: '4px',
                        py: '2px',
                        borderRadius: '4px',
                        fontSize: 'inherit',
                        fontWeight: 'inherit',
                    }}
                    {...props}
                >
                    {children}
                </Typography>
        },
    };

    return (
        <div className={styles["markdown-body"]}>
            <Markdown 
                breaks
                components={components} 
                remarkPlugins={[remarkBreaks, remarkGfm]} 
                rehypePlugins={enableHtml ? [rehypeRaw, rehypeSanitize, rehypeHighlight] : undefined}
            >
                {value}
            </Markdown>
        </div>
    );
}