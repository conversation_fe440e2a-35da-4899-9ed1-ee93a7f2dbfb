import React, { useCallback, useEffect, useState } from 'react';
import clsx from 'clsx';
import { Stack, Container, ButtonBase, Collapse, FormHelperText, Paper, useMediaQuery } from '@mui/material';
import { ChevronRightOutlined as MoreIcon } from '@mui/icons-material';
import styles from './Link.module.scss';

import Modal from '../../../../../components/Modal';

export const Link = ({ item, selected, disabled, onClick, slotProps, children, fullPage, ...props }) => (
    <ButtonBase 
        //component={fullPage ? Paper : undefined}
        key={item.id}
        disabled={disabled} 
        elevation={0} 
        selected={selected ? 1 : 0}
        className={clsx(styles.link, (selected && fullPage) ? styles.active : null)} 
        onClick={onClick}
        value={item.id}
        disableRipple
        {...slotProps}
        {...props}
    >
        {children}
    </ButtonBase>
);

export const LinkWrapper = ({ items, selected, disabled, onClick, slotProps, slots, children, ...props }) => {
    if (!Array.isArray(selected)) selected = [selected];
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [openModalId, setOpenModalId] = useState(null);

    const clickHandler = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        const id = e.currentTarget.value;
        if (id){
            const found = selected.find(a => +a?.id === +id);
            const item = items.find(a => +a?.id === +id);

            if (onClick) onClick(item);

            // replaces the modal content with whatever component is passed in the extraInfo slot
            if (slots?.extraInfo && slotProps?.extraInfo?.show && item && !found) {
                if (slotProps?.fullPage) setOpenModalId(item.id);
                else if (slotProps?.changeView) slotProps.changeView(slots?.extraInfo({item}));
            }
        }
    }, [onClick, items, slotProps, slots, selected]);

    return (
        <Stack 
            component={Container} 
            direction="row" 
            spacing={1}
            useFlexGap 
            flexWrap="wrap"
            maxWidth
            disableGutters
            {...slotProps.stack} 
            sx={{
                py: 1,
                overflow: 'hidden', 
                overflowY: 'auto', 
                justifyContent: 'center',
                ...slotProps?.sx
            }}
        >
            {items.map((item, i) => {
                let checked = Boolean(selected?.find(a => +a?.id === +item.id)), _disabled = disabled, helperText;
                if (slots?.validate && !disabled) {
                    let valid = slots.validate(item);
                    if (typeof valid === 'string') {
                        helperText = valid;
                        valid = false;
                    }
                    valid = Boolean(valid);
                    checked = valid ? checked : false;
                    _disabled = !valid;
                }
                if (slotProps?.fullPage) checked = true;
                return (
                    <React.Fragment key={item.id}>
                        <Link 
                            item={item}
                            disabled={_disabled} 
                            selected={checked} 
                            onClick={clickHandler}
                            slotProps={slotProps?.button}
                            fullPage={slotProps?.fullPage}
                            {...props}
                        >
                            <Stack direction="row" spacing={1} useFlexGap sx={{width: '100%', justifyContent: "space-between"}}>
                                {/*!slotProps?.fullPage &&*/ children?.[i]}
                                {!_disabled && slots?.extraInfo && slotProps?.extraInfo?.show && !slotProps?.fullPage && <MoreIcon fontSize='small'/> }
                                {helperText && <FormHelperText sx={{textAlign: "center"}}>{helperText}</FormHelperText>}
                            </Stack>
                        </Link>
                        {!_disabled && slots?.extraInfo && slotProps?.extraInfo?.show && slotProps?.fullPage &&
                            <Modal 
                                open={openModalId === item.id} 
                                onClose={() => setOpenModalId(null)}
                                maxWidth={"sm"}
                                fullScreen={isMobile}
                            >
                                {slots?.extraInfo({item, onClose: () => setOpenModalId(null)})}
                            </Modal>
                        }
                    </React.Fragment>
                );
            })}
        </Stack>
    );
}