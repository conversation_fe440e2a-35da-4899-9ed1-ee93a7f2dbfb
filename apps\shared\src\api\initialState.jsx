export const initialState = {
    data: null,
    loading: false,
    errors: null,
    httpCode: null,
};

export const emptyApiResponse = {
    data: null,
    loading: false,
    errors: null,
    httpCode: 200,
    LoadingBar: () => null,
    ErrorBar: () => null,
    fetchData: () => {},
    cancelFetch: () => {},
    setState: () => {},    
};


export const mapErrorMessage = status => {
    let errorMessage = 'error:default';

    switch (status) {
        case 401:
            errorMessage = 'error:unauthorized';
            break;
        case 403:
            errorMessage = 'error:forbidden';
            break;
        case 404:
            errorMessage = 'error:notFound';
            break;
        case 409:
            errorMessage = 'error:conflict';
            break;
        case 422:
        case 405:
            errorMessage = 'error:default';
            break;
        case 500:
            errorMessage = 'error:serverError';
            break;
        default:
            if (status >= 400 && status < 500) errorMessage = 'error:default';
            if (status >= 500) errorMessage = 'error:serverError';
            break;
    }
    return errorMessage;
};