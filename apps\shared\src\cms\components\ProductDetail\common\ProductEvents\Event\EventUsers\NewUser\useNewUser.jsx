import { useState, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { useApi } from '../../../../../../../../api/useApi';

const apiParams = [
    {params: {endpoint: `/user/register`, method: 'POST'}},
    {params: {endpoint: `/group/invite`, method: 'POST'}},
    {enableCache: true, params: {endpoint: `/group_type/4`, method: 'GET', params: { include_member_roles: 1 }}},
];

export const useNewUser = ({ user, goToPreviousView }) => {
    const { t } = useTranslation();

    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);
    const [success, setSuccess] = useState(false);
    const [fields, setFields] = useState({dob: new Date()});

    const {fetchData:saveUser, ErrorBar} = useApi(apiParams[0]);
    const {fetchData:addToGroup} = useApi(apiParams[1]);
    const {fetchData:fetchRelationShips, data:relationships, loading:relationshipsLoading, errors: relationshipErrors} = useApi(apiParams[2]);

    const formFields = useMemo(() => {
        const _relationships = relationships?.group_member_roles?.map(r => ({id: r.id, slug: t(`user:relationships.${r.name.toLowerCase()}`, r.name)}));
        return [
            {name: 'first_name', type: 'text', label: 'user:firstName', required: true, value: "", component: "TextField", margin: "normal", rowId: 1},
            {name: 'last_name', type: 'text', label: 'user:lastName', required: true, value: "", component: "TextField", margin: "normal", rowId: 1},
            {name: 'email', type: 'text', label: 'user:email', required: true, value: "", component: "TextField", margin: "normal", rowId: 2},
            {name: 'dob', type: 'text', label: 'user:dateOfBirth', required: true, value: new Date(), component: "DatePicker", margin: "normal", disableFuture: true, rowId: 3},
            {name: 'relationship', type: 'select', label: 'user:relationship', required: true, value: "", component: "Select", margin: "normal", options: _relationships || [], rowId: 3},
            {name: 'username', type: 'text', label: 'user:username', required: true, value: "", component: "TextField", margin: "normal", rowId: 4},
            {name: 'password', type: 'text', label: 'user:password', required: true, value: "", component: "PasswordField", margin: "normal", rowId: 4},
        ];
    }, [relationships, t]);

    const handleAddUser = useCallback(async values => {
        setLoading(true);
        try {
            // save the user
            const _values = {...values, dob: format(new Date(values.dob), 'yyyy-MM-dd')};
            const res = await saveUser({..._values, "password_confirmation": values?.password});
            if (res?.errors){
                if (res.errors?.validation) setErrors(res.errors.validation);
                else setErrors(res.errors);
                return false;
            } else if (res?.data){
                const res2 = await addToGroup({
                    group_id: user.family_groups?.[0]?.id,
                    user_ids: res.data,
                    status_id: 2,
                    member_role_id: _values?.member_role_id || 11,
                });
                if (res2?.errors){
                    if (res2.errors?.validation) setErrors(res2.errors.validation);
                    else setErrors(res2.errors);
                    return false;
                } else if (res2?.data){
                    setSuccess(true);
                    return true;
                }
            }
        } catch(e){
            setErrors({error: e});
            return false;
        } finally {
            setLoading(false);
        }
    }, [saveUser, addToGroup, user]);

    // validate required fields
    const validateFields = useCallback(() => {
        let valid = true;
        const requiredFields = formFields?.filter(field => field.required);
        if (!requiredFields || requiredFields.length === 0) return valid;

        for (let i = 0; i < requiredFields.length; i++) {
            if (!fields[requiredFields[i].name]){
                setErrors(prev => ({
                    ...(prev || {}),
                    [requiredFields[i].name]: t("error:required"),
                }));
                valid = false;
            }
        }
        return valid;
    }, [fields, formFields]);

    const handleSubmit = useCallback(async (getOut = true) => {
        setErrors(null);
        const valid = validateFields();
        if (valid) {
            const res = await handleAddUser(fields);
            if (res && getOut) goToPreviousView();
            return valid;
        }
        return valid;
    }, [validateFields, goToPreviousView, user, handleAddUser, fields]);

    const handleChange = useCallback(e => {
        if (e.target.name === "dob") e.target.value = format(new Date(e.target.value), 'yyyy-MM-dd');
        setFields(prev => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    }, []);


    useEffect(() => {
        fetchRelationShips();
    }, [fetchRelationShips]);

    useEffect(() => {
        setLoading(relationshipsLoading);
        setErrors(relationshipErrors);
    }, [relationshipsLoading, relationshipErrors]);

    return {
        relationships: relationships?.group_member_roles || [],
        loading,
        errors,
        ErrorBar,
        success,
        fields,
        formFields,
        handleAddUser,
        validateFields,
        handleSubmit,
        handleChange,
    };
}