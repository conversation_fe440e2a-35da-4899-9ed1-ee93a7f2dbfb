
const typeTitle = [
    { id: 'h1', slug: 'builder:component.heading.types.h1' },
    { id: 'h2', slug: 'builder:component.heading.types.h2' },
    { id: 'h3', slug: 'builder:component.heading.types.h3' },
    { id: 'h4', slug: 'builder:component.heading.types.h4' },
    { id: 'h5', slug: 'builder:component.heading.types.h5' },
    { id: 'h6', slug: 'builder:component.heading.types.h6' },
];

const typeText = [
    { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
    { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
    { id: 'body1', slug: 'builder:component.heading.types.body1' },
    { id: 'body2', slug: 'builder:component.heading.types.body2' },
    { id: 'caption', slug: 'builder:component.heading.types.caption' },
    { id: 'overline', slug: 'builder:component.heading.types.overline' },
    { id: 'p', slug: 'builder:component.heading.types.p' },
    { id: 'code', slug: 'builder:component.heading.types.code' },
];

export const properties = [
    {
        name: 'shopId',
        label: 'builder:component.events.shopId',
        component: "local.ShopId",//"GeneralItemSelector",
        value: 0,
        /*fetchParams: {params: {endpoint: "/cms/site/page", data: {website_id: websiteId, page_type_id: 14}, method: 'POST'}},
        valueFormatter: value => {
            if (!value) return null;
            if (!Array.isArray(value)) value = [value];
            return value.map(v => ({id: v?.id, slug: v?.title}));
        },*/
        size: "small",
        margin: "normal",
    },
    {
        name: 'type',
        label: 'builder:component.events.type',
        component: "Select",
        value: 'upcoming',
        options: [
            {id: 'upcoming', slug: 'event:upcomingEvents'},
            {id: 'past', slug: 'event:pastEvents'},
            {id: 'all', slug: 'general:all'},
        ],
        size: "small",
        margin: "normal",
    },
    /*{
        name: 'title',
        label: 'builder:component.heading.title',
        component: "TextField",
        value: '',
        size: "small",
        margin: "normal",
    },
    {
        name: 'titleVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'h1',
        size: "small",
        margin: "normal",
        options: [...typeTitle, ...typeText]
    },
    {
        name: 'subtitle',
        label: 'builder:component.heading.subtitle',
        component: "TextField",
        value: '',
        size: "small",
        margin: "normal",
    },
    {
        name: 'subtitleVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'subtitle2',
        size: "small",
        margin: "normal",
        options: [...typeText]
    },
    {
        name: 'body',
        label: 'builder:component.heading.body',
        component: "TextField",
        minRows: 4,
        value: '',
        size: "small",
        margin: "normal",
    },
    {
        name: 'bodyVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'body1',
        size: "small",
        margin: "normal",
        options: [...typeText]
    },*/
];
