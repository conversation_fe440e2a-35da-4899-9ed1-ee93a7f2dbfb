import { useCallback } from 'react';
import { useSelector, shallowEqual } from 'react-redux';

const getProperty = (obj, path) => {
    return path.split('.').reduce((acc, part) => (acc ? acc[part] : undefined), obj);
};

/* 
This hook is used combine values from multiple slices in the store. Commonly used in cms components to evaluate conditions.
- It uses an array of fields paths: ['product.id', 'user.id:profile.userId']
- fields can have an alias, for example 'user.id:profile.userId' will return the value of 'user.id' as 'profile.userId'
*/
export const useFieldsFromStore = fieldPaths => {

    const selector = useCallback(state => {
        const values = {};
        fieldPaths.forEach(path => {
            let _path = path, _alias = path;            
            if (_path.includes(":")) { // if it has an alias
                const [field, alias] = _path.split(":");
                _alias = alias;
                _path = field;
            }
            values[_alias] = getProperty(state, _path);
        });
        return values;
    }, [fieldPaths]);

    return useSelector(selector, shallowEqual);
};