import { useState, useCallback, useMemo, useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { PosContext } from '../../hooks/PosContext';
import { formatSlug } from '../../../utils';

const payCheapestFirst = (items, total) => {
    if (!items.length) return null;

    const sortedItems = [...items].sort((a, b) => {
        const p1 = (+a?.metadata?.price || 0) * (+a?.qty || 1);
        const p2 = (+b?.metadata?.price || 0) * (+a?.qty || 1);
        return p1 - p2;
    });
    
    let remainingPayment = +total || 0;
    
    return sortedItems.map(item => {
        const itemTotal = (+item?.metadata?.price || 0) * (+item?.qty || 1) - (+item?.metadata?.total_paid || 0);
        let itemPaymentAmount = 0;
        
        if (remainingPayment > 0 && itemTotal > 0) {
            itemPaymentAmount = Math.min(remainingPayment, itemTotal);
            remainingPayment -= itemPaymentAmount;
        }

        return {
            order_item_id: item?.metadata?.order_item_id || null,
            amount: Number(itemPaymentAmount.toFixed(2))
        };
    }).filter(a => a.amount > 0);
}

const payDistributed = (items, total) => {
    if (!items.length) return null;

    const totalOrderAmount = items.reduce((sum, item) => sum + ((+item?.metadata?.price || 0) * (+item?.qty || 1)) - (+item?.metadata?.total_paid || 0), 0);
    let paymentAmount = +total || 0;
    if (paymentAmount > totalOrderAmount) paymentAmount = totalOrderAmount;

    return [...items].map(a => {
        const itemTotal = (+a?.metadata?.price || 0) * (+a?.qty || 1);
        let itemPaymentAmount = totalOrderAmount > 0 ? (itemTotal / totalOrderAmount) * paymentAmount : 0;
        if (itemPaymentAmount > itemTotal) itemPaymentAmount = itemTotal;

        return {
            order_item_id: a?.metadata?.order_item_id || null,
            amount: Number(itemPaymentAmount.toFixed(2))
        };
    }).filter(a => a.amount > 0);
}

export const usePosCheckout = props => {
    const { t } = useTranslation();
    const { 
        setUpOrder = () => {}, 
        updateOrder = () => {}, 
        handleOrderPay = () => {}, 
        loadLatestOrder = () => {}, 
        resetOrder = () => {}, 
        updatePosInfo = () => {}, 
        reduxStore, 
        selectedUser, 
        registerId, 
        router, 
        cart, 
        errorBars
    } = useContext(PosContext) || {};

    const [extraFields, setExtraFields] = useState([]);
    const [loading, setLoading] = useState(false);
    const [orderFullfilled, setOrderFullfilled] = useState(false);

    const routes = useMemo(() => {
        let cartRoute = null, successRoute = null;
        if (router) {
            if (router?.confirmation) successRoute=`/${formatSlug(`${router?.slug?.value}/${router?.confirmation?.value}/${orderFullfilled?.order?.id}`)}`;
            if (router?.cart) cartRoute=`/${formatSlug(`${router?.slug?.value}/${router?.cart?.value}`)}`;
        }
        return [successRoute, cartRoute];
    }, [router, orderFullfilled]);    

    const handlePaymentChange = useCallback((fields, totals) => {
        setExtraFields(prev => {
            let _fields = [];
            let adminFee = 0, change = 0;
            let amount = +fields?.values?.amount || 0;

            if (!totals) totals = cart?.totals;
    
            const _prev = [...prev].filter(a => a.type !== "change" && a.type !== "adminFee" && a.type !== fields?.paymentMethod);
            if (amount > 0) _fields = [{type: fields?.paymentMethod, amount, label: t(`pos:paymentMethods.${fields?.paymentMethod}`, "Payment"), metadata: fields?.values}];
    
            let allMethods = [..._prev, ..._fields];
            let balance = +totals?.total - +totals?.payments || 0;
    
            // check if cash discount should be applied if the only payment method is cash and the amount paid covers the total
            if (amount > 0){
                const hasCash = allMethods.findIndex(a => a.type === "cash");
                const hasOther = allMethods.findIndex(a => a.type !== "cash" && a.type !== "adminFee");
                const shouldRemoveDiscount = allMethods.findIndex(a => a?.metadata?.removeCashDiscount === true) > -1;
                if ((hasCash > -1 && hasOther === -1 && +amount >= +totals?.total) || shouldRemoveDiscount) {
                    adminFee = +(totals?.calculatedCashDiscount || cart?.totals?.calculatedCashDiscount || 0) * (totals?.priceAdjustments?.length > 0 ? (shouldRemoveDiscount ? 1 : 0) : 1); // if there are price adjustments, don't apply the admin fee because it should be there already
                    if (adminFee > 0) {
                        allMethods = [{
                            type: "adminFee", amount: -adminFee, label: t("order:cashDiscount"), 
                            slotProps: {sx: {textDecoration: shouldRemoveDiscount ? "line-through" : undefined}}
                        }, ...allMethods];
                        balance -= adminFee;
                    }
                }
            }
    
            // calculate change
            change = allMethods.reduce((acc, curr) => acc + curr?.type !== "adminFee" ? curr.amount : 0, 0) - balance;
            allMethods.push({type: "change", amount: Math.abs(change), label: change >= 0 ? t("order:change") : t("status:unpaid"), variant: "h6", slotProps: {sx: {color: change >= 0 ? "success.main" : "error.main"}}})

            return allMethods;
        });
    }, [t, cart?.totals]);

    const handlePaymentProcess = useCallback(async ({values, paymentMethodId}, callback) => {
        setLoading(true);
        const orderItems = payCheapestFirst(cart?.cart, values?.amount) || undefined;
        let payments = [{paymentMethodId, ...values, order_items: orderItems}];
        switch (paymentMethodId){
            case 0: // terminal
                break;
            case 1: // credit card
                payments = [{
                    paymentMethodId,
                    ...values,
                    order_items: orderItems,
                    type: 'sale',
                    description: "Online Credit Card Payment",
                }];
                break;
            default:
                break;
        }

        /**/
        try {
            await handleOrderPay({payments}, res2 => {
                let isFullfilled = false;
                if (res2.payments) setExtraFields([]);

                if (res2?.order){
                    updatePosInfo(res2.order);
                    handlePaymentChange({}, {
                        total: res2.order.total_price,
                        payments: res2.order.payment_total,
                        priceAdjustments: res2.order.price_adjustments,
                        calculatedCashDiscount: res2.order.calculated_cash_discount,
                    });

                    if (+res2.order.total_price <= res2.order.payment_total){
                        isFullfilled = true;
                    }
                }
                const orderUpdate = {...res2, fullfilled: isFullfilled};
                setOrderFullfilled(orderUpdate);
                if (isFullfilled) resetOrder();
                if (callback) callback(orderUpdate);
                setLoading(false);
            });
        } catch (error){
            console.log(error);
        }
        /**/
    }, [handleOrderPay, resetOrder, handlePaymentChange, updatePosInfo, cart]);

    const handleTipChange = useCallback(async fields => {
        if (!fields) return;

        const tipAmount = fields?.amount || 0;
        if (tipAmount < 0) return;
        
        setLoading(true);
        try{
            await updateOrder({fields: {tip: tipAmount}}, order => {
                if (order){
                    handlePaymentChange({}, {
                        total: order.total_price,
                        payments: order.payment_total,
                        priceAdjustments: order.price_adjustments,
                        calculatedCashDiscount: order.calculated_cash_discount,
                    });
                }
                setLoading(false);
            });
        } catch (error){
            console.log(error);
        }

    }, [updateOrder, handlePaymentChange]);

    useEffect(() => {
        loadLatestOrder(selectedUser?.id, registerId);
    }, [loadLatestOrder, selectedUser, registerId, orderFullfilled]);

    return {
        extraFields,
        setExtraFields,
        loading,
        setLoading,
        handlePaymentChange,
        handleTipChange,
        handlePaymentProcess,
        cart,
        setUpOrder,
        orderFullfilled,
        reduxStore,
        successRoute: routes[0],
        cartRoute: routes[1],
        errorBars,
    }
}