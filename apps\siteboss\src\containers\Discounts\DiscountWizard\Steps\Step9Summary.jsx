import React, { useEffect, useState, useCallback } from 'react';
import {
    Typography,
    Paper,
    Box,
    Divider,
    List,
    ListItem,
    ListItemText,
    styled,
    Tooltip
} from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step9 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import EditIcon from '@mui/icons-material/Edit';

// Styled components for clickable section headers
const SectionHeader = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
    '&:hover': {
        backgroundColor: theme.palette.action.hover,
        borderRadius: theme.shape.borderRadius,
    },
    padding: theme.spacing(1),
    marginLeft: theme.spacing(-1),
    marginRight: theme.spacing(-1),
    transition: 'background-color 0.3s'
}));

const Step9Summary = ({ data, onStepChange }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);

    // Function to navigate to a specific step
    const navigateToStep = useCallback((stepIndex) => {
        if (onStepChange) {
            onStepChange(stepIndex);
        }
    }, [onStepChange]);

    // Get all form values for summary from form data
    const getFieldValue = (fieldName) => {
        return formData?.[fieldName];
    };

    const name = getFieldValue('name');
    const description = getFieldValue('description');
    const autoApply = getFieldValue('auto_apply');
    const couponCode = getFieldValue('coupon_code');
    const unlimited = getFieldValue('unlimited');
    const maxUses = getFieldValue('max_uses');
    const validFrom = getFieldValue('valid_from');
    const noEndDate = getFieldValue('no_end_date');
    const validUntil = getFieldValue('valid_until');
    const discountType = getFieldValue('discount_type');
    const discountAmount = getFieldValue('discount_amount');
    const applyToAll = getFieldValue('apply_to_all');
    const combinable = getFieldValue('combinable');
    const [localStatus, setLocalStatus] = useState(1);

    // Get status value from form data
    useEffect(() => {
        const statusValue = getFieldValue('status');
        if (statusValue !== undefined) {
            setLocalStatus(statusValue);
        } else if (data?.status !== undefined) {
            setLocalStatus(data.status);
        }
    }, [formData, data, getFieldValue]);

    // Handle status change
    const handleStatusChange = useCallback((e) => {
        const newValue = parseInt(e.target.value);
        setLocalStatus(newValue);
        dispatch(updateFormData({
            ...formData,
            status: newValue
        }));
    }, [dispatch, formData]);
    const params = getFieldValue('params') || {};

    // Save fields to the form context - only run once when data changes
    useEffect(() => {
        // Save the step data to the form context
        if (data && !formData?.initialized) {
            const fields = step9(data);
            if (fields && fields.length > 0) {
                const updatedData = { ...formData, initialized: true };
                fields.forEach(field => {
                    updatedData[field.name] = field.value;
                });
                dispatch(updateFormData(updatedData));
            }
        }
    }, [data, dispatch]);

    // Format date for display
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    };

    // Format discount type and amount for display
    const formatDiscountValue = () => {
        if (discountType === undefined || discountAmount === undefined) return 'N/A';

        return discountType === 0
            ? `${discountAmount}%`
            : `$${parseFloat(discountAmount).toFixed(2)}`;
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom>
                Review your discount details
            </Typography>

            <Paper sx={{ p: 3, mb: 3 }}>
                <Tooltip title="Click to edit Basic Information" arrow placement="top">
                    <SectionHeader onClick={() => navigateToStep(0)}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Basic Information</Typography>
                        <EditIcon fontSize="small" color="primary" />
                    </SectionHeader>
                </Tooltip>
                <List>
                    <ListItem>
                        <ListItemText primary="Name" secondary={name || 'N/A'} />
                    </ListItem>
                    <ListItem>
                        <ListItemText primary="Description" secondary={description || 'N/A'} />
                    </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Tooltip title="Click to edit Application Method" arrow placement="top">
                    <SectionHeader onClick={() => navigateToStep(1)}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Application Method</Typography>
                        <EditIcon fontSize="small" color="primary" />
                    </SectionHeader>
                </Tooltip>
                <List>
                    <ListItem>
                        <ListItemText
                            primary="Method"
                            secondary={autoApply === 1 ? 'Auto Apply' : 'Coupon Code'}
                        />
                    </ListItem>
                    {autoApply === 0 && (
                        <ListItem>
                            <ListItemText primary="Coupon Code" secondary={couponCode || 'N/A'} />
                        </ListItem>
                    )}
                </List>

                <Divider sx={{ my: 2 }} />

                <Tooltip title="Click to edit Usage Limits" arrow placement="top">
                    <SectionHeader onClick={() => navigateToStep(2)}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Usage Limits</Typography>
                        <EditIcon fontSize="small" color="primary" />
                    </SectionHeader>
                </Tooltip>
                <List>
                    <ListItem>
                        <ListItemText
                            primary="Usage Limit"
                            secondary={unlimited === 1 ? 'Unlimited' : `Limited to ${maxUses} uses`}
                        />
                    </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Tooltip title="Click to edit Valid Dates" arrow placement="top">
                    <SectionHeader onClick={() => navigateToStep(3)}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Valid Dates</Typography>
                        <EditIcon fontSize="small" color="primary" />
                    </SectionHeader>
                </Tooltip>
                <List>
                    <ListItem>
                        <ListItemText primary="Valid From" secondary={formatDate(validFrom)} />
                    </ListItem>
                    <ListItem>
                        <ListItemText
                            primary="Valid Until"
                            secondary={noEndDate === 1 ? 'No end date' : formatDate(validUntil)}
                        />
                    </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Tooltip title="Click to edit Discount Details" arrow placement="top">
                    <SectionHeader onClick={() => navigateToStep(4)}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Discount Details</Typography>
                        <EditIcon fontSize="small" color="primary" />
                    </SectionHeader>
                </Tooltip>
                <List>
                    <ListItem>
                        <ListItemText
                            primary="Discount Type"
                            secondary={discountType === 0 ? 'Percentage' : 'Fixed Amount'}
                        />
                    </ListItem>
                    <ListItem>
                        <ListItemText primary="Discount Value" secondary={formatDiscountValue()} />
                    </ListItem>
                    <ListItem>
                        <ListItemText
                            primary="Applies To"
                            secondary={applyToAll === 1 ? 'Entire Order' : 'Specific Items'}
                        />
                    </ListItem>
                    <ListItem>
                        <ListItemText
                            primary="Combinable with other discounts"
                            secondary={combinable === 1 ? 'Yes' : 'No'}
                        />
                    </ListItem>
                </List>

                {Object.keys(params).length > 0 && (
                    <>
                        <Divider sx={{ my: 2 }} />
                        <Tooltip title="Click to edit Conditions" arrow placement="top">
                            <SectionHeader onClick={() => navigateToStep(7)}>
                                <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>Conditions</Typography>
                                <EditIcon fontSize="small" color="primary" />
                            </SectionHeader>
                        </Tooltip>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                            This discount has {Object.keys(params).length} condition(s) that must be met.
                        </Typography>
                    </>
                )}
                </Paper>

            <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                    Set discount status
                </Typography>

                <StyledRadioGroup
                    name="status"
                    label="Discount Status"
                    required
                    options={[
                        {
                            id: 1,
                            value: 1,
                            label: 'Active',
                            icon: <CheckCircleIcon />
                        },
                        {
                            id: 0,
                            value: 0,
                            label: 'Inactive',
                            icon: <CancelIcon />
                        }
                    ]}
                    value={localStatus}
                    onChange={handleStatusChange}
                    helperText="Set the initial status of this discount"
                />

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Set to "Active" to make this discount available immediately (within the valid date range).
                    Set to "Inactive" if you want to save this discount but not make it available yet.
                </Typography>
            </Box>
        </Box>
    );
};

export default Step9Summary;
