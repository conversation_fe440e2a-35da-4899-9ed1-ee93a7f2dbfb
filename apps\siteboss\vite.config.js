import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  // Use empty string as base path for development to avoid WebSocket issues
  const BASE_PATH = mode === 'development' ? '/' : `${env.VITE_BASE_PATH ?? ''}/`;

  return {
    base: BASE_PATH,
    plugins: [react()],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern'
        }
      }
    },
    server: {
      // Force Vite to use port 5173 for consistency
      port: 5173,
      strictPort: false, // Allow fallback to another port if 5173 is in use

      // Configure WebSocket for HMR
      hmr: {
        protocol: 'ws',
        host: 'localhost',
      },

      // Proxy API requests to port 8000
      proxy: {
        '/api': {
          target: env.VITE_API_URL ? env.VITE_API_URL.replace('/api', '') : 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
        }
      }
    }
  }
});