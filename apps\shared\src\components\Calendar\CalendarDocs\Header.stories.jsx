import { Header } from '../Header/Header'
import { fn } from 'storybook/test';

export default {
    title: "Siteboss/Component Groups/Calendar/Header",
    component: Header,
    tags: ["autodocs"],
    argTypes: {
        onPrev: {
            description: "Function to handle previous button click",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
                type: { summary: "() => void" },
                defaultValue: { summary: "undefined" },
            }
        },
        onNext: {
            description: "Function to handle next button click",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
                type: { summary: "(value: type) => void" },
                defaultValue: { summary: "undefined" },
            }
        },
        onToday: {
            description: "Function to handle today button click",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
                type: { summary: "(value: type) => void" },
                defaultValue: { summary: "undefined" },
            }
        },
        onCalendarTypeChange: {
            description: "Function to handle calendar type change",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
                type: { summary: "(type: string) => void" },
                defaultValue: { summary: "undefined" },
            }
        },
        onSearchTextChange: {
            description: "Function to handle search change",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
                type: { summary: "(value: string) => void" },
                defaultValue: { summary: "undefined" },
            }
        },
        showSearchBar: {
            description: "Whether to show the search bar",
            control: { type: 'boolean' },
            type: { required: false },
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: "true" },
            }
        },
        showCalendarType: {
            description: "Whether to show the calendar type selector",
            control: { type: 'boolean' },
            type: { required: false },
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: "true" },
            }
        },
        calendarType: {
            description: "The current calendar type",
            control: { type: 'select', options: ['month', 'week', 'day', 'year', 'schedule'] },
            type: { required: true },
            table: {
                type: { summary: "string" },
                defaultValue: { summary: "undefined" },
            }
        },
        currentDate: {
            description: "The current date, is passed into new Date() on the component side",
            control: { type: 'date' },
            type: { required: true },
            table: {
                type: { summary: "Date" },
                defaultValue: { summary: "undefined" },
            }
        },
        loading: {
            description: "Whether the data is loading",
            control: { type: 'boolean' },
            type: { required: false },
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: "false" },
            }
        },
    },
};

export const Playground={
    args:{
        onPrev: fn(),
        onNext: fn(),
        onToday: fn(),
        onCalendarTypeChange: fn(),
        onSearchTextChange: fn(),
        showSearchBar: true,
        showCalendarType: true,
        calendarType: "month",
        currentDate: new Date(),
        loading: false,
    }
}