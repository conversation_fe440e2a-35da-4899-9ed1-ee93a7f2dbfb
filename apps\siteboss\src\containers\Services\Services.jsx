import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useParams, useOutletContext } from 'react-router-dom';
import { Container, Button, Box } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { Title, SuccessBar, ErrorBar, LoadingBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import ServicesList from './ServicesList';
import ServiceDetails from './ServiceDetails';

export const Services = () => {
    const { t } = useOutletContext();
    const navigate = useNavigate();
    const { id } = useParams();
    const [success, setSuccess] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [error, setError] = useState(null);
    const [services, setServices] = useState([]);
    const [service, setService] = useState(null);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalCount, setTotalCount] = useState(0);

    // Track if this is the first render
    const isFirstRender = useRef(true);

    // API calls
    const { fetchData: fetchServices, loading: servicesLoading, errors: servicesError } = useApi({
        params: {
            endpoint: '/service',
            method: 'POST',
            data: {
                page_no: 1,
                max_records: 50,
                sort_col: 'name',
                sort_direction: 'asc'
            }
        }
    });

    // Store fetchServices in a ref to prevent infinite loop
    const fetchServicesRef = useRef();

    // Update the ref when fetchServices changes
    useEffect(() => {
        fetchServicesRef.current = fetchServices;
    }, [fetchServices]);

    // Track render count for debugging
    const renderCount = useRef(0);
    renderCount.current += 1;
    console.log(`Render count: ${renderCount.current}`);

    // Load services on component mount and when pagination changes
    useEffect(() => {
        console.log('Component mounted or pagination changed');

        // Skip if fetchServicesRef is not set yet
        if (!fetchServicesRef.current) return;

        const loadServices = async () => {
            try {
                // Log different messages based on whether it's the initial fetch or pagination change
                if (isFirstRender.current) {
                    console.log('Initial services fetch...');
                } else {
                    console.log('Pagination changed, fetching services...');
                }

                const response = await fetchServicesRef.current({
                    page_no: page + 1,
                    max_records: rowsPerPage,
                    sort_col: 'name',
                    sort_direction: 'ASC'
                });

                if (response?.data) {
                    setServices(response.data.services || []);
                    setTotalCount(response.data.services?.length || 0);
                }
            } catch (error) {
                console.error('Error loading services:', error);
                setError(error);
            } finally {
                // After first render, mark it as completed
                if (isFirstRender.current) {
                    isFirstRender.current = false;
                }
            }
        };

        loadServices();

        return () => {
            console.log('Component unmounted');
        };
    }, [page, rowsPerPage]); // Don't include fetchServices here

    // Add service API
    const { fetchData: fetchService, loading: serviceLoading, errors: serviceError } = useApi({
        params: {
            endpoint: `/service/${id}`,
            method: 'GET'
        }
    });

    // Store fetchService in a ref to prevent infinite loop
    const fetchServiceRef = useRef();

    // Update the ref when fetchService changes
    useEffect(() => {
        fetchServiceRef.current = fetchService;
    }, [fetchService]);

    // Delete service API
    const { fetchData: deleteService, loading: deleteLoading, errors: deleteError } = useApi({
        params: {
            endpoint: '/service/delete',
            method: 'DELETE'
        }
    });

    // Load service details when ID changes
    useEffect(() => {
        let isMounted = true;

        const loadServiceDetails = async () => {
            if (!isMounted || !id || !fetchServiceRef.current) return;

            try {
                const response = await fetchServiceRef.current();
                if (isMounted && response?.data) {
                    setService(response.data.service);
                }
            } catch (error) {
                if (isMounted) {
                    console.error('Error loading service details:', error);
                    setError(error);
                }
            }
        };

        if (id) {
            loadServiceDetails();
        } else {
            setService(null);
        }

        return () => {
            isMounted = false;
        };
    }, [id]); // Don't include fetchService here

    // Handle service deletion
    const handleDelete = useCallback(async (serviceId) => {
        try {
            const result = await deleteService({
                id: serviceId
            });
            if (result && !result.error) {
                setSuccessMessage(t('service:deleteSuccess'));
                setSuccess(true);

                // Use the ref to avoid dependency issues
                fetchServicesRef.current({
                    page_no: page + 1,
                    max_records: rowsPerPage,
                    sort_col: 'name',
                    sort_direction: 'ASC'
                });

                navigate('/services');
            }
        } catch (err) {
            console.error('Error deleting service:', err);
            setError(err);
        }
    }, [deleteService, navigate, t, page, rowsPerPage]);

    // Create new service
    const handleCreateNew = useCallback(() => {
        navigate('/services/wizard');
    }, [navigate]);

    // Edit service
    const handleEdit = useCallback((serviceId) => {
        navigate(`/services/wizard/${serviceId}`);
    }, [navigate]);

    // Handle page change
    const handleChangePage = (_, newPage) => {
        setPage(newPage);
    };

    // Handle rows per page change
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    if (servicesLoading || serviceLoading || deleteLoading) {
        return <LoadingBar />;
    }

    return (
        <Container>
            {success && <SuccessBar message={successMessage} onClose={() => setSuccess(false)} />}
            {error && <ErrorBar error={error} onClose={() => setError(null)} />}
            {servicesError && <ErrorBar error={servicesError} />}
            {serviceError && <ErrorBar error={serviceError} />}
            {deleteError && <ErrorBar error={deleteError} />}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Title>{id ? t('service:serviceDetails') : t('service:services')}</Title>
                <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleCreateNew}
                >
                    {t('service:newService')}
                </Button>
            </Box>

            {id ? (
                <ServiceDetails
                    service={service}
                    loading={serviceLoading}
                    onEdit={() => handleEdit(id)}
                    onDelete={() => handleDelete(id)}
                />
            ) : (
                <ServicesList
                    services={services}
                    loading={servicesLoading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    page={page}
                    rowsPerPage={rowsPerPage}
                    totalCount={totalCount}
                    onChangePage={handleChangePage}
                    onChangeRowsPerPage={handleChangeRowsPerPage}
                />
            )}
        </Container>
    );
};

export default Services;
