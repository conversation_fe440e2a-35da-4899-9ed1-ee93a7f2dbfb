export const properties = [
    /*{
        /*name: 'shopId',
        label: 'builder:component.eventDetail.shopId',
        component: "TextField",//"GeneralItemSelector",
        value: 1,
        /*fetchParams: {params: {endpoint: "/cms/site/page", data: {website_id: websiteId, page_type_id: 14}, method: 'POST'}},
        valueFormatter: value => {
            if (!value) return null;
            if (!Array.isArray(value)) value = [value];
            return value.map(v => ({id: v?.id, slug: v?.title}));
        },
        size: "small",
        margin: "normal",
    },*/
    {
        name: 'eventType',
        label: 'builder:component.eventDetail.type',
        component: "RadioGroup",
        value: '1',
        options: [{ id: '1', slug: 'builder:component.eventDetail.types.automatic' }, { id: '2', slug: 'builder:component.eventDetail.types.manual' }],
        size: "small",
        margin: "normal",
        helperText: 'builder:component.eventDetail.types.helperText',
    },
    {
        name: 'eventId',
        label: 'builder:component.eventDetail.id',
        component: 'NumberField',
        value: 0,
        size: 'small',
        margin: 'normal',
        condition: { field: 'eventType', value: '2' }
    },
];