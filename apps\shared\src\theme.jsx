import { createTheme, alpha } from '@mui/material/styles';
//import WebFont from 'webfontloader';
import logo from './assets/images/logo.svg';
import icon from './assets/images/favicon.svg';
import { common } from '@mui/material/colors';

import { deepObjectMerge } from './utils';

const menuWidth = 220;
const headerHeight = 65;
const primaryFont = 'Roboto, Arial, sans-serif';
const secondaryFont = 'Montserrat, Arial, sans-serif';
/*
WebFont.load({
    google: {
        families: ['Roboto:300,400,500,600,700', 'Montserrat:300,400,500,600,700']
    }
});
*/
export const theme = (colorMode = 'light', overrides = null) => {
    const baseTheme = {
        /*cssVariables: { cssVariables: { cssVarPrefix: 'sb' }},
        colorSchemes: { light: true, dark: true},
        cssVariables: { colorSchemeSelector: 'class' },*/
        logo: {
            url: logo,
            iconUrl: icon,
            root: {
                width: 'auto',
                height: '65px',
                objectFit: 'contain',
            },
            sm: {
                height: '35px',
            },
            icon: {
                height: '25px'
            }
        },
        sizes:{
            headerHeight: `${headerHeight}px`,
            menuWidth: `${menuWidth}px`,
            menuDockedWidth: '90px'
        },
        breakpoints: {
            values: {
                xs: 170,
                sm: 600,
                md: 900,
                lg: 1200,
                xl: 1536,
                xxl: 1920,
            }
        },
        palette: {
            mode: colorMode || 'light',
            primary: {
                main: '#662d91',
            },
            secondary: {
                main: '#f7931e',
            },
            black: {
                main: common.black,
                contrastText: common.white,
            },
            white: {
                main: common.white,
                contrastText: common.black,
            },
            //divider: colorMode === 'dark' ? 'rgba(255, 255, 255, 0.075)' : 'rgba(0, 0, 0, 0.075)',
        },
        typography: {
            fontFamily: primaryFont,
            fontSize: 16,
            h1: {
                fontFamily: secondaryFont,
                fontSize: '2.5rem',
                fontWeight: 700,
            },
            h2: {
                fontFamily: secondaryFont,
                fontSize: '2rem',
                fontWeight: 700,
            },
            h3: {
                fontFamily: secondaryFont,
                fontSize: '1.75rem',
                fontWeight: 700,
            },
            h4: {
                fontFamily: secondaryFont,
                fontSize: '1.5rem',
                fontWeight: 700,
            },
            h5: {
                fontFamily: secondaryFont,
                fontSize: '1.25rem',
                fontWeight: 700,
            },
            h6: {
                fontFamily: secondaryFont,
                fontSize: '1rem',
                fontWeight: 700,
            },
            subtitle1: {
                fontFamily: secondaryFont,
                fontSize: '1rem',
                fontWeight: 400,
            },
            subtitle2: {
                fontFamily: secondaryFont,
                fontSize: '0.875rem',
                fontWeight: 400,
            },
            subtitle3:{
                fontFamily: secondaryFont,
                fontSize: '0.75rem',
                fontWeight: 400,
                lineHeight: 1.25,
            },
            body1: {
                fontSize: '1rem',
                fontWeight: 400,
            },
            body2: {
                fontSize: '0.875rem',
                fontWeight: 400,
            },
            body3: {
                fontSize: '0.75rem',
            },
            button: {
                fontSize: '1rem',
                fontWeight: 500,
            },
            caption: {
                fontFamily: secondaryFont,
                fontSize: '0.75rem',
                fontWeight: 400,
            },
            overline: {
                fontSize: '0.625rem',
                fontWeight: 400,
            },
            calendarEvent: {
                fontFamily: secondaryFont,
                fontSize: '0.65rem',
                fontWeight: 400,
            },
            bold:{
                fontWeight: 700,
            },
            uppercase:{
                textTransform: 'uppercase',
            },
            lowercase:{
                textTransform: 'lowercase',
            },
            code: {
                fontFamily: 'monospace',
                fontSize: '0.875rem',
            },
        },
        components: {
            MuiCssBaseline: {
                styleOverrides: {
                    body: {
                    },
                },
            },
            MuiSnackbar: {
                styleOverrides: {
                    root: {
                        zIndex: 9999,
                    },
                },
            },
            MuiAppBar: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        backgroundColor: theme.palette.mode === 'light' ? theme.palette.background.paper : theme.palette.background.default,
                        boxShadow: 'none',
                        color: 'inherit',
                        //backgroundImage: 'none',
                        //width: ownerState.mobile ? '100%' : `calc(100% - ${menuWidth}px)`,
                        //marginLeft: ownerState.mobile ? 0 : `${menuWidth}px`,
                    }),
                },
            },
            MuiBottomNavigation: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 400,
                        fontSize: '0.8rem',
                        alignItems: 'flex-start',
                        '& .MuiBottomNavigationAction-root': {
                            fontFamily: secondaryFont,
                            fontWeight: 400,
                            fontSize: '0.8rem',
                            height: '100%',
                            width: '100%',
                            '& .MuiSvgIcon-root': {
                                width: '0.85em',
                                height: '0.85em',
                                marginBottom: '0.15em',
                            },
                            '& .MuiBottomNavigationAction-label':{
                                fontFamily: secondaryFont,
                                fontWeight: 400,
                                fontSize: '0.8rem',
                            }
                        },
                    },
                },
            },
            MuiBottomNavigationAction: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        '&.Mui-selected': {
                            backgroundColor: theme.palette.action.selected,
                            color: 'inherit',
                        },
                    }),
                },
            },            
            MuiBreadcrumbs: {
                styleOverrides: {
                    root: {
                        fontSize: 14,
                        fontWeight: 400,
                    },
                    separator: {
                        color: 'inherit',
                        paddingLeft: '0.5rem',
                        paddingRight: '0.5rem',
                    },
                },
            },
            MuiDrawer: {
                styleOverrides: {
                    root: ({ ownerState }) => ({
                        width: ownerState.menu ? menuWidth : undefined,
                        flexShrink: 0,
                    }),
                    paper: ({ ownerState, theme }) => ({
                        marginTop: 0, //ownerState.variant==='temporary' ? '0' : `calc(${headerHeight}px + 16px)`,
                        height: '100%', //ownerState.variant==='temporary' ? '100%' : `calc(100% - ${headerHeight}px)`,
                        width: ownerState.menu ? `${menuWidth}px` : ownerState.details ? '100%' : undefined,
                        maxWidth: ownerState.details ? theme.breakpoints.values.md : undefined,                        
                        boxSizing: 'border-box',
                        backgroundColor: 'background.paper',
                        //border: ownerState.menu ? 'none' : undefined,
                        overflowX: 'hidden',
                    }),
                },
            },
            MuiContainer: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        // style the scrollbar
                        '&::-webkit-scrollbar': {
                            width: '0.35em',
                            height: '0.35em',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: theme.palette.action.selected,
                            borderRadius: '0.5em',
                        },
                        '&::-webkit-scrollbar-thumb:hover': {
                            backgroundColor: theme.palette.action.focus,
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: theme.palette.action.disabledBackground,
                        },
                        /*'&scrollbar-width': 'thin',
                        '&scrollbar-color': 'auto',*/
                    }),
                },
            },
            MuiStack: {
                defaultProps: {
                    useFlexGap: true,
                },
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        // style the scrollbar
                        '&::-webkit-scrollbar': {
                            width: '0.35em',
                            height: '0.35em',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: theme.palette.action.selected,
                            borderRadius: '0.5em',
                        },
                        '&::-webkit-scrollbar-thumb:hover': {
                            backgroundColor: theme.palette.action.focus,
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: theme.palette.action.disabledBackground,
                        },
                        /*'&scrollbar-width': 'thin',
                        '&scrollbar-color': 'auto',*/
                    }),
                },
            },
            MuiPaper: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        // style the scrollbar
                        '&::-webkit-scrollbar': {
                            width: '0.35em',
                            height: '0.35em',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: theme.palette.action.selected,
                            borderRadius: '0.5em',
                        },
                        '&::-webkit-scrollbar-thumb:hover': {
                            backgroundColor: theme.palette.action.focus,
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: theme.palette.action.disabledBackground,
                        },
                        /*'&scrollbar-width': 'thin',
                        '&scrollbar-color': 'auto',*/
                    }),
                },
                variants: [
                    {
                        props: { variant: 'outlined' },
                        style: ({ theme }) => ({
                            backgroundImage: theme.palette.mode === 'dark' ? `linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05));` : 'none' 
                        }),
                    },
                    {
                        props: { variant: 'tileButton' },
                        style: ({ theme, ownerState }) => ({
                            backgroundColor: 'transparent', //theme.palette.background.paper,
                            boxShadow: theme.shadows[0],
                            borderRadius: theme.shape.borderRadius,
                            border: `1px solid ${theme.palette.divider}`,
                            margin: ownerState.selected ? 0 : '1px',
                            borderWidth: ownerState.selected ? 2 : 1,
                            borderColor: ownerState.selected ? theme.palette.text.primary : theme.palette.divider,
                            padding: (!ownerState.size || ownerState.size === "md") ? theme.spacing(4) : ownerState.size === "sm" ? theme.spacing(2) : theme.spacing(6),
                            textTransform: 'capitalize',
                            '& span': {
                                lineHeight: 1.5,
                            },
                            '&:hover': {
                                backgroundColor: theme.palette.action.hover,
                            },
                            '&:active': {
                                backgroundColor: theme.palette.action.selected,
                            },
                            '&:focus': {
                                backgroundColor: theme.palette.action.focus,
                            },
                            '&:selected': {
                                backgroundColor: theme.palette.action.selected,
                            },
                        }),
                    },
                    {
                        props: { variant: 'textField' },
                        style: ({ theme }) => ({
                            backgroundColor: 'transparent', //theme.palette.background.paper,
                            boxShadow: theme.shadows[0],
                            borderRadius: theme.shape.borderRadius,
                            border: `1px solid ${theme.palette.divider}`,
                            padding: theme.spacing(1),
                            transition: theme.transitions.create(['border-color', 'box-shadow'], {
                                duration: theme.transitions.duration.shortest,
                            }),                            
                            '&:hover': {
                                borderColor: theme.palette.text.primary,
                            },
                            '&.Mui-error': {
                                borderColor: theme.palette.error.main,
                                //boxShadow: `0 0 0 1px ${theme.palette.error.main} inset`,
                            },
                            '&.Mui-focused': {
                                borderColor: theme.palette.mode === 'dark' ? theme.palette.text.primary : theme.palette.primary.main,
                                boxShadow: `0 0 0 1px ${theme.palette.mode === 'dark' ? theme.palette.text.primary : theme.palette.primary.main} inset`,
                            },
                        }),
                    }
                ]
            },
            MuiList: {
                variants: [
                    {
                        props: { variant: 'menu' },
                        style: ({ theme }) => ({
                            fontSize: 14,
                            '& .MuiTypography-root': {
                                fontFamily: secondaryFont,
                                fontWeight: 500,
                                fontSize: 'inherit',
                                whiteSpace: 'break-spaces',
                                wordBreak: 'break-word',                                
                            },
                            '& .MuiListItemButton-root': {
                                paddingTop: '0.25rem',
                                paddingBottom: '0.25rem',
                                //borderRadius: '24px',
                                marginLeft: '0.5rem',
                                marginRight: '0.5rem',
                                marginBottom: '0',
                                whiteSpace: 'break-spaces',
                            },
                            '& .MuiListItemIcon-root': {
                                fontSize: 14,
                                minWidth: 24,
                                maxWidth: 32,
                                color: theme.palette.text.primary,
                                '& svg': {
                                    fontSize: 16,
                                },
                            },
                            '& .Mui-selected': {
                                '& span': {
                                    fontWeight: 500,
                                }
                            },
                        }),
                    },
                ],
            },
            MuiMenuItem: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        fontFamily: secondaryFont,
                        fontWeight: ownerState.selected ? 700 : 500,
                        fontSize: 14,
                        '& .MuiTypography-root': {
                            display:'flex',
                            fontFamily: secondaryFont,
                            fontWeight: ownerState.selected ? 700 : 500,
                            fontSize: 14,
                            alignItems: 'center',
                        },
                    }),
                },
            },
            MuiChip: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 400,
                        fontSize: 12,
                    },
                },
                variants: [
                    {
                        props: { size: 'xs' },
                        style: ({ theme }) => ({
                            fontSize: 10,
                            height: 21,
                        }),
                    },
                ],
            },
            MuiTabs: {
                styleOverrides: {
                    root: ({ ownerState, theme }) => ({
                        position: 'sticky',
                        borderRight: ownerState.orientation === 'vertical' ?  `1px solid ${theme.palette.divider}` : undefined, 
                        borderBottom: ownerState.orientation === 'vertical' ? undefined :  `1px solid ${theme.palette.divider}`,
                        minWidth: ownerState["data-ismobile"] ? 100 : undefined,
                        
                        '& .MuiTab-root': {
                            //minWidth: 'auto',
                            minHeight: 'auto',
                            fontWeight: ownerState.profile ? 500 : 500,
                            //alignItems: ownerState.orientation === 'vertical' ? 'flex-end' : undefined,
                            //textAlign: ownerState.orientation === 'vertical' ? 'right' : undefined,
                            '&.Mui-selected': {
                                color: 'inherit',
                                fontWeight: ownerState.profile ? 600 : 700,
                            }
                        },
                    }),
                    vertical: ({ theme }) => ({
                        '& .MuiButtonBase-root': {
                            paddingLeft: theme.spacing(2),
                        },
                    }),
                },
            },
            MuiTab: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 500,
                        fontSize: 14,
                        textTransform: 'none',
                        '&.Mui-selected': {
                            color: 'inherit',
                            fontWeight: 700,
                        }
                    }
                },
            },
            MuiToggleButton: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 500,
                        fontSize: 14,
                        textTransform: 'none',
                    },
                },
            },
            MuiButton: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 500,
                        fontSize: 14,
                        lineHeight: 1.5,
                        textTransform: 'none',
                    },
                    sizeXl: {
                        padding: '16px 32px',
                        fontSize: '1.2rem',
                        //textTransform: 'uppercase',
                        fontWeight: 700,
                        lineHeight: 1,
                    },
                    sizeXs: {
                        padding: '4px 8px',
                        fontSize: '0.8rem',
                        //textTransform: 'uppercase',
                        fontWeight: 700,
                        lineHeight: 1,
                    },
                },
                variants: [
                    {
                        props: { variant: 'text' },
                        style: ({theme}) => ({
                            //fontFamily: primaryFont,
                            fontWeight: 400,
                            color: theme.palette.mode === 'dark' ? 'inherit' : undefined,
                        }),
                    },
                    {
                        props: { variant: 'outlined' },
                        style: ({theme}) => ({
                            color: theme.palette.mode === 'dark' ? 'inherit' : undefined,
                            borderColor: theme.palette.mode === 'dark' ? 'inherit' : undefined,
                        }),
                    },
                ],
            },
            MuiCard: {
                variants: [
                    {
                        props: { variant: 'groups' },
                        style: ({ theme }) => ({
                            backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.default : theme.palette.background.paper,
                            boxShadow: theme.shadows[1],
                            '& .MuiCardHeader-title': {
                                fontFamily: secondaryFont,
                                fontWeight: 700,
                                fontSize: 18,
                            },
                            '& .MuiCardHeader-subheader': {
                                fontFamily: secondaryFont,
                                fontWeight: 500,
                                fontSize: 12,
                            },
                        }),
                    },
                ],
            },
            MuiDivider: {
                styleOverrides: {
                    root: {
                        //borderStyle: 'dashed',
                    },
                },
            },
            MuiTableCell: {
                styleOverrides: {
                    root: ({ theme }) => ({
                        fontWeight: 400,
                        borderBottom: `1px solid ${theme.palette.divider}`,
                    }),
                    head: {
                        fontFamily: secondaryFont,
                        fontWeight: 700,
                        fontSize: 12,
                        textTransform: 'uppercase',
                    },
                },
            },
            MuiDataGrid: {
                styleOverrides: {
                    root: ({ theme }) => ({
                        border: `0px solid ${theme.palette.divider}`,
                        '& .MuiDataGrid-columnHeaders': {
                            borderBottom: `1px solid ${theme.palette.divider}`,
                        },
                        '& .MuiDataGrid-footerContainer': {
                            borderTop: `0px solid ${theme.palette.divider}`,
                        },
                        '& .MuiDataGrid-overlayWrapper': {
                            height: 'auto !important',
                        },
                        '& .MuiDataGrid-overlayWrapperInner': {
                            height: 'auto !important',
                        },
                    }),
                    cell: ({ theme }) => ({
                        borderBottom: `1px solid ${theme.palette.divider}`,
                    }),
                    columnHeaderTitle: {
                        fontFamily: secondaryFont,
                        fontWeight: 700,
                        fontSize: 12,
                        textTransform: 'uppercase',
                    },
                },
            },
            MuiMultiSectionDigitalClockSection: {
                styleOverrides: {
                    root: ({ theme }) => ({
                        '&::-webkit-scrollbar': {
                            width: '0.35em',
                            height: '0.35em',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: theme.palette.action.selected,
                            borderRadius: '0.5em',
                        },
                        '&::-webkit-scrollbar-thumb:hover': {
                            backgroundColor: theme.palette.action.focus,
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: 'none',
                        },
                    }),
                },
            },
            MuiAlert: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontWeight: 500,
                        fontSize: 14,
                    },
                },
            },
            MuiFormControlLabel: {
                styleOverrides: {
                    root: {
                        '& .MuiTypography-root': {
                            //fontSize: 14,
                        }
                    },
                },
            },
            MuiInputBase: {
                styleOverrides: {
                    root: {
                        fontFamily: secondaryFont,
                        fontSize: 14,
                    },
                },
            },
            MuiTextField: {
                styleOverrides: {
                    root: ({theme}) => ({
                        '& label': {
                            fontFamily: secondaryFont,
                            fontSize: 14,
                        },
                        '& label.Mui-focused': {
                            color: theme.palette.mode === 'dark' ? theme.palette.text.primary : theme.palette.primary.main,
                        },
                        '& label.MuiInputLabel-shrink': {
                            top:'1px',
                        },
                        '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                                borderColor: theme.palette.mode === 'dark' ? theme.palette.text.primary : theme.palette.primary.main,
                            }
                        },                        
                        '& .MuiInputBase-root': {
                            fontSize: 14,
                        },
                        '& input': {
                            '&:-webkit-autofill, &:-webkit-autofill:active, &:-webkit-autofill:focus, &:-webkit-autofill:hover': {
                                WebkitBoxShadow: `0 0 0 100px ${theme.palette.background.paper} inset`
                            },
                        },
                        '& legend': {
                            fontFamily: secondaryFont,
                        },
                    }),
                },
            },
        },
        /*customCSS: theme => ({
            global: `
                body {
                    background-color: ${theme.palette.background.default};
                }
            `,
            components: {
                // add styles here that will apply to specific components
            },
        }),*/
    };

    const _theme = deepObjectMerge(baseTheme, overrides, true);
    return createTheme(_theme);
}
