
import CmsIcon from '../../utils/CmsIcon';

/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/

const icons = [
    CmsIcon({
        iconProps: {height: 48, width: 48, direction: 'column', spacing: 2},
        elements: [
            {type: 'rectangle', width: '100%', height: 24, alignSelf: 'center', position: 'relative'}
        ]
    }),
    CmsIcon({elements: [
        {type: 'group', direction: 'row', spacing: 3, m: 'auto', children: [
            {type: 'text', width: '70%',m: 'auto'},
            {type: 'text', width: '25%',m: 'auto'},
        ]},
    ]}),
    CmsIcon({elements: [
        {type: 'circle', size: 32, m: 'auto'},
    ]}),
];

export const widgetIcon = () => icons[0];
export const layouts = [
    {
        id: 1,
        name: 'Button',
        icon: icons[0],
        slotProps: {
            button: {
                width: 'fit-content'
            }
        }
    },
    {
        id: 2,
        name: 'Link',
        icon: icons[1],
        slotProps: {
            button: {
                variant: "text",
            },
        }
    },    
    {
        id: 3,
        name: 'Icon',
        icon: icons[2],
        slotProps: {
        }
    },
];