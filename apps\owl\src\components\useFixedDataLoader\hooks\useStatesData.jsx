import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../KeycloakProvider/tenantConfig";
import { setInfo } from '../../../store/reducers/fixedDataSlice';

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

const apiParams = {
    enableCache: true,
    params: {endpoint: '/states', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}
};

export const useStatesData = () => {
    const dispatch = useDispatch();
    const loaded = useSelector(state => state.fixedData.loaded.states);
    const token = useSelector(state => state.user.token);

    const { fetchData, loading } = useApi(apiParams);

    useEffect(() => {
        if (!loaded && token) {
            fetchData()
                .then(result => {
                    let states = [];
                    if (result?.data) {
                        states = result.data.map(state => ({
                            state_id: state.id || state.state_id || state.code,
                            id: state.code,
                            name: state.name,
                            code: state.code,
                            country_id: state.country_id,
                        }));
                    }
                    dispatch(setInfo({ states }));
                })
                .catch(error => {
                    console.error('Error fetching states:', error);
                });
        }
    }, [loaded, fetchData, dispatch, token]);

    return { loading };
};
