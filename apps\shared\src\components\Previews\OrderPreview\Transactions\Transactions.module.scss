.wrapper{
    width: 100%;
    display: flex;
    flex-direction: column;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

@media print {
    .wrapper{
        order: 2;
        
        table{
            margin-top: 8px;
            margin-bottom: 8px;
    
            td {
                padding-top: 4px;
                padding-bottom: 4px;
                border-color: #f5f5f5 !important;
                word-wrap: break-word;
                white-space: break-word;
            }
    
            thead{
                tr {
                    border: 1px solid #e0e0e0 !important;
    
                    th {
                        padding-top: 4px;
                        padding-bottom: 4px;
                        background-color: #000 !important;
                        color: #fff !important;    
                    }
                }
            }
        }           
    }
}