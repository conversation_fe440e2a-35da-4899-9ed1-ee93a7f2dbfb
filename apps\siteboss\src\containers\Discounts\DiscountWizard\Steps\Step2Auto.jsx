import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON>rid, Typography, FormHelperText, TextField } from '@mui/material';
import { useApi } from '@siteboss-frontend/shared';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import StyledRadioGroup from '../components/StyledRadioGroup';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';

const Step2Auto = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);
    const [couponCodeValid, setCouponCodeValid] = useState(true);
    const [couponCodeError, setCouponCodeError] = useState('');
    const [autoApply, setAutoApply] = useState(0);
    const [couponCode, setCouponCode] = useState('');

    // Initialize local state from Redux store
    useEffect(() => {
        if (formData?.auto_apply !== undefined) {
            setAutoApply(parseInt(formData.auto_apply) || 0);
        }
        if (formData?.coupon_code !== undefined) {
            setCouponCode(formData.coupon_code || '');
        }
    }, [formData]);

    // Initialize form data from the API response - only run once when data changes
    useEffect(() => {

        // Only initialize if we haven't already
        if (data && !formData?.initialized) {
            const updatedData = { ...formData, initialized: true };

            // If we have data from the API, use it
            if (data.auto_apply !== undefined) {
                const autoApplyValue = parseInt(data.auto_apply) || 0;
                setAutoApply(autoApplyValue);
                updatedData.auto_apply = autoApplyValue;
            }

            if (data.coupon_code !== undefined) {
                const couponCodeValue = data.coupon_code || '';
                setCouponCode(couponCodeValue);
                updatedData.coupon_code = couponCodeValue;
            }

            // Dispatch a single update with all changes
            dispatch(updateFormData(updatedData));
        }
    }, [data, dispatch]);

    // Handle auto_apply change
    const handleAutoApplyChange = useCallback((e) => {
        const value = parseInt(e.target.value);
        setAutoApply(value);
        // Clear coupon code if auto apply is selected
        if (value === 1) {
            setCouponCode('');
            dispatch(updateFormData({
                ...formData,
                coupon_code: ''
            }));
        }
        // Save the auto_apply value to the form context
        dispatch(updateFormData({
            ...formData,
            auto_apply: value
        }));
    }, [dispatch, formData]);

    // Handle coupon code change
    const handleCouponCodeChange = useCallback((e) => {
        const value = e.target.value;
        setCouponCode(value);
        dispatch(updateFormData({
            ...formData,
            coupon_code: value
        }));
    }, [dispatch, formData]);

    // API call to check if coupon code is valid
    const { fetchData: checkCouponCode, loading: checkingCode } = useApi({
        params: {
            endpoint: '/coupon/checkname',
            method: 'POST'
        }
    });

    // Handle coupon code validation
    const handleCouponCodeCheck = useCallback(async (code) => {
        try {
            const response = await checkCouponCode({ coupon_code: code });
            if (response.errors) {
                setCouponCodeValid(false);
                setCouponCodeError(response.errors || 'This coupon code is already in use');
            } else {
                setCouponCodeValid(true);
                setCouponCodeError('');
            }
        } catch (error) {
            setCouponCodeValid(false);
            setCouponCodeError(error.message || 'This coupon code is already in use');
        }
    }, [checkCouponCode, setCouponCodeValid, setCouponCodeError]);

    // Handle coupon code validation
    const handleCouponCodeBlur = useCallback((e) => {
        const code = e.target.value;
        if (code && autoApply === 0) {
            handleCouponCodeCheck(code);
        }
    }, [autoApply, handleCouponCodeCheck]);



    // Validate the form when auto_apply or couponCode changes
    useEffect(() => {
        // If auto_apply is 0 (require coupon code) and no coupon code is entered, show error
        if (autoApply === 0 && !couponCode) {
            setCouponCodeError('A coupon code is required');
            setCouponCodeValid(false);
        } else {
            setCouponCodeError('');
            setCouponCodeValid(true);
        }
    }, [autoApply, couponCode]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Choose how this discount will be applied
                </Typography>
                <StyledRadioGroup
                    name="auto_apply"
                    label="Application Method"
                    required
                    options={[
                        {
                            id: 1,
                            value: 1,
                            label: 'Automatically apply to qualifying orders',
                            icon: <AutoAwesomeIcon />
                        },
                        {
                            id: 0,
                            value: 0,
                            label: 'Require a coupon code',
                            icon: <LocalOfferIcon />
                        }
                    ]}
                    value={autoApply}
                    onChange={handleAutoApplyChange}
                    helperText="Choose how customers will receive this discount"
                />

                {autoApply === 0 && (
                    <>
                        <TextField
                            name="coupon_code"
                            label="Coupon Code"
                            fullWidth
                            required
                            margin="normal"
                            helperText="Enter a unique code that customers will use to apply this discount"
                            value={couponCode}
                            onBlur={handleCouponCodeBlur}
                            onChange={handleCouponCodeChange}
                            error={!couponCodeValid}
                            disabled={checkingCode}
                        />
                        {!couponCodeValid && (
                            <FormHelperText error>{couponCodeError}</FormHelperText>
                        )}
                    </>
                )}
            </Grid>
        </Grid>
    );
};

export default Step2Auto;
