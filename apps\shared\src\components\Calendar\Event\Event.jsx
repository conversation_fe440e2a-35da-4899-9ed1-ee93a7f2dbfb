import React, { useState, useRef, useCallback } from 'react';
import { Paper, Typography, ButtonBase, useMediaQuery } from '@mui/material';
import Portal from '../../Portal';
import Modal from '../../Modal';
import Details from './Details';

export const Event = ({
    event, // the event to display
    typographyProps, // the typography properties
    paperProps, // the paper properties
    disabled, 
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided)
    ...props // additional props
}) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('sm'));
    const [open, setOpen] = useState(false);

    const ref = useRef(null);
    
    const handleClose = () => setOpen(false);

    const handleClick = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        if (event){
            if (onEventClick) onEventClick(event);
            else setOpen(true);
        }
    }, [onEventClick, event]);

    if (!event) return null;

    return (
        <>
            <Paper
                ref={ref}
                component={props.component || ButtonBase}
                square={false}
                elevation={0}
                className={props.className}
                sx={{
                    px: 1,
                    zIndex: theme => theme.zIndex.mobileStepper + 1,
                    ...props.sx,
                }}
                onClick={handleClick}
                {...paperProps}
            >
                {event.title && !props.children &&
                    <Typography id="event-title" variant={'calendarEvent'} component='span' {...typographyProps}>
                        {event.title}
                    </Typography>
                }
                {props.children}
            </Paper>
            <Portal>
                <Modal 
                    open={open} 
                    onClose={handleClose}
                    maxWidth="md"
                    fullScreen={isMobile}
                    aria-describedby="event-title"
                    triggerRef={ref}
                >
                    <Details event={event} />
                </Modal>
            </Portal>
        </>
    );
}