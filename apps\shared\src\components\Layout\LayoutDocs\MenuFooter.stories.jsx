import { Footer } from '../Menu/Footer/Footer'

export default {
    title: 'Shared/Layout/Menu/Footer',
    component: Footer,
    tags:['autodocs'],
    argTypes: {
        isDocked: {
            description: "Flag to indicate if the footer is docked",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag indicating whether the footer is docked."
            }
        },
        drawerWidth: {
            description: "Width of the drawer",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 240 },
                detail: "The width of the drawer in pixels."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        isDocked: false,
        drawerWidth: 240
    }
};

// Following stories to illustrate each significant prop
export const DockedFooter = {
    args: {
        isDocked: true,
        drawerWidth: 240
    }
};

export const WithCustomDrawerWidth = {
    args: {
        isDocked: false,
        drawerWidth: 300
    }
};
