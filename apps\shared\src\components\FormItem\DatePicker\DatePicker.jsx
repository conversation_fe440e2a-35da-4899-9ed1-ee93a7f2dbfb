import React, { useCallback, useMemo, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { enUS, es } from 'date-fns/locale';
import { isValid, parseISO, isBefore } from 'date-fns';
import { LocalizationProvider, DatePicker as MuiDatePicker, DateRangePicker } from '@mui/x-date-pickers-pro'; //'@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { EventOutlined as EventOutlinedIcon } from '@mui/icons-material';


// basic date validations
const checkDate = (date, options={}) => {
    let isValidDate = true;
    if (!date) date = new Date(); // if the date is not set, we set it to today
    else date = new Date(date);
    //if (!(date instanceof Date)) date = parseISO(date); // if the date is a string, we parse it to a date

    const now = new Date();
    date.setHours(0, 0, 0, 0);
    now.setHours(0, 0, 0, 0);

    if (date.getFullYear() < 1900) isValidDate = false; // if the year is less than 1900, we consider it invalid
    if (options?.disablePast && isBefore(date, now)) isValidDate = false; // if the date is in the past and we are disabling past dates, we consider it invalid
    if (options?.disableFuture && isBefore(now, date)) isValidDate = false; // if the date is in the future and we are disabling future dates, we consider it invalid
    if (options?.minDate && isBefore(date, new Date(options.minDate).setHours(0, 0, 0, 0))) isValidDate = false; // if the date is before the minimum date, we consider it invalid
    if (options?.maxDate && isBefore(new Date(options.maxDate).setHours(0, 0, 0, 0), date)) isValidDate = false; // if the date is after the maximum date, we consider it invalid
    if (isValidDate) isValidDate = isValid(date);
    return [date, isValidDate];
}

export const DatePicker = ({
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors, // the errors for the field
    loading, // the loading status of the form so we can disable the fields
    value, // the value of the field
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onBlur, // the function to be called when the field is blurred
    isRangePicker = false, // if the date picker is a range picker
    disableFuture, // if the date picker should disable future dates
    disablePast, // if the date picker should disable past dates
    maxDate, // the maximum date allowed
    minDate, // the minimum date allowed
    ...props
}) => {
    // Use a ref to store the language to prevent re-renders
    const languageRef = useRef(null);
    const languageFromStore = useSelector(state => state.language);

    // Only update the ref if it's null or if the language has changed
    useEffect(() => {
        if (!languageRef.current || languageFromStore.code !== languageRef.current.code) {
            languageRef.current = languageFromStore;
        }
    }, [languageFromStore]);

    // Use the ref value instead of the direct selector result
    const language = languageRef.current || { code: 'en' };

    const handleChange = useCallback(date => {
        let isValidDate = true;
        if (Array.isArray(date)) {
            date = date.map((a, i) => {
                const res = checkDate(a, { disablePast, disableFuture, minDate, maxDate});
                a = res[0];
                if (!res[1]) isValidDate = res[1];
                if (i > 0){
                    const current = new Date(a).setHours(0, 0, 0, 0);
                    const previous = new Date(date[i-1]).setHours(0, 0, 0, 0);
                    if (isBefore(current, previous)) isValidDate = false;
                }
                return a;
            });
        } else {
            const res = checkDate(date, { disablePast, disableFuture, minDate, maxDate });
            date = res[0];
            if (!res[1]) isValidDate = res[1];
        }
        if (isValidDate) {
            if (onChange) onChange({ target: { name, value: date } });
            if (onBlur) onBlur({ target: { name, value: date } });
        }
    }, [name, onChange, onBlur, disablePast, disableFuture, minDate, maxDate]);

    const componentValue = useMemo(() => {
        if (isRangePicker) {
            if (!Array.isArray(value) || value.length <= 0) return [new Date(), new Date()];
            else return value.map(a => a ? new Date(a) : new Date());
        } else {
            if (Array.isArray(value) || !value) return new Date();
            else return new Date(value || undefined);
        }
    }, [value, isRangePicker]);

    const Component = isRangePicker ? DateRangePicker : MuiDatePicker;

    return (
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
            <Component
                name={name}
                label={label}
                autoFocus={false}
                value={componentValue}
                disabled={loading}
                disableFuture={disableFuture || undefined}
                disablePast={disablePast || undefined}
                maxDate={maxDate || undefined}
                minDate={minDate || undefined}
                loading={loading}
                onChange={handleChange}
                localeText={props?.localeText || undefined}
                slots={{
                    openPickerIcon: EventOutlinedIcon,
                }}
                slotProps={{
                    ...props?.slotProps,
                    textField: {
                        ...props?.slotProps?.textField,
                        required: required || false,
                        variant: "outlined",
                        fullWidth: true,
                        error: !!errors,
                        helperText: errors || undefined,
                        margin: props.margin || undefined,
                        ...props?.InputProps,
                    },
                    shortcuts: props.shortcuts || undefined,
                }}
                sx={{
                    marginY: 2,
                    ...props.sx,
                }}
            />
        </LocalizationProvider>
    );
}