import React, { useState, useRef, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { Container } from '@mui/material';
import { TabPanel, TabContext } from '@mui/lab';
import { RecentActorsOutlined  as AccountIcon, LocalAtmOutlined as OrderIcon, TodayOutlined as EventIcon, GroupOutlined as GroupIcon, Diversity1Outlined as FamilyIcon, RotateRightOutlined as SubscriptionIcon } from '@mui/icons-material';
import { LoadingBar, WithDetailsToolbar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import Profile from './Profile';
import Groups from './Groups';
import Orders from './Orders';
import Events from './Events';
import Subscriptions from './Subscriptions';
import WaiverNotification from './WaiverNotification';

import UserInfo from './UserInfo';

const tabs = [
    { id: 0, slug: 'user:toolbar.profile', moduleId: 100, icon: <AccountIcon/>},
    { id: 1, slug: 'user:toolbar.orders', moduleId: 76, icon: <OrderIcon/>},
    { id: 2, slug: 'user:toolbar.events', moduleId: 77, icon: <EventIcon/>},
    { id: 3, slug: 'user:toolbar.subscriptions', moduleId: 75, icon: <SubscriptionIcon/>},
    { id: 4, slug: 'user:toolbar.family', moduleId: 207, icon: <FamilyIcon/>},
    { id: 5, slug: 'user:toolbar.groups', moduleId: 106, icon: <GroupIcon/>},
];
/*
this component is used to display the user's information and tabs with user related data
props:
    id: the id of the user to display
    onDelete: function to be called when the delete button is clicked
    onEdit: function to be called when the edit button is clicked
    roles: an array of roles
    loading: loading state for roles
    parentRef: a ref to the parent container
*/
export const UserData = ({onDelete, onEdit, id, roles, loading:rolesLoading, parentRef, selectedUsers, ...props}) => {    
    const { isMobile } = useOutletContext();
    const contentRef = parentRef || useRef(null);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/user/user/${id}`, method: 'GET'}},
    ], [id]);

    const { fetchData, data, ErrorBar, loading } = useApi(apiParams[0]);
    const { permissions } = usePermission({moduleIds: tabs.map(tab => tab.moduleId)});

    const [selectedTab, setSelectedTab] = useState('0');
    const handleToolbarClick = (e, action) => {
        if (!action) return;
        setSelectedTab(action);
    }

    const visibleTabs = useMemo(() => tabs.filter(tab => permissions[tab.moduleId]), [permissions, tabs]);

    useEffect(() => {
        if (id) fetchData();
    }, [id, fetchData]);
    
    if (!id) return null;

    return (
        <Container disableGutters ref={contentRef}>
            <ErrorBar />
            {loading && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: 0}} />}
            {data?.[0] &&
                <TabContext value={selectedTab || '0'}> 
                    {!data?.[0]?.signed_waiver_path &&
                        <WaiverNotification userData={data?.[0]} />
                    }
                    {!isMobile && 
                        <UserInfo userData={data?.[0]} onDelete={onDelete} onEdit={onEdit} loading={loading || rolesLoading} />
                    }
                    <WithDetailsToolbar 
                        tabs={visibleTabs}
                        userData={data?.[0]} 
                        parentRef={contentRef} 
                        onSelection={handleToolbarClick} 
                        selectedTab={selectedTab}
                        setSelectedTab={setSelectedTab}
                        sx={{mt: 2, ml: 'auto'}} 
                        stickyTop={selectedUsers?.length > 1 ? 55 : 38}
                    />
                    <TabPanel value={`${selectedTab}`} sx={{ px: isMobile ? 1 : undefined, pb: isMobile ? theme => theme.sizes.headerHeight : undefined }}>
                        {selectedTab === '0' && <Profile userData={data?.[0]} roles={roles} loading={loading || rolesLoading} onDelete={onDelete} onEdit={onEdit} />}
                        {selectedTab === '1' && <Orders userData={data?.[0]} loading={loading || rolesLoading} />}
                        {selectedTab === '2' && <Events userData={data?.[0]} loading={loading || rolesLoading} />}
                        {selectedTab === '3' && <Subscriptions userData={data?.[0]} loading={loading || rolesLoading} />}
                        {selectedTab === '4' && <Groups userData={data?.[0]} groupTypeId={4} loading={loading || rolesLoading} />}
                        {selectedTab === '5' && <Groups userData={data?.[0]} loading={loading || rolesLoading} />}
                    </TabPanel>
                </TabContext>
            }
        </Container>
    );
}