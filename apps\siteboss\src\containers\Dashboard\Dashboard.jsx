import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Dialog, DialogTitle, DialogActions, DialogContent, Button, Typography, Alert, Snackbar, Box } from '@mui/material';
import { Title, WithSortable } from '@siteboss-frontend/shared/components';
import { Widgets, Toolbar as WidgetToolbar } from './Widgets';

import { RoleBasedLayout } from './RoleBasedLayout';
import Toolbar from './Toolbar';

import { useDashboard } from './useDashboard';

let localWidgets = localStorage.getItem('_siteboss') || '{}';
if (localWidgets) {
	localWidgets = JSON.parse(localWidgets);
}

export const Dashboard = () => {
	const { isMobile, t } = useOutletContext();

	const {
		items,
		setItems,
		isEditing,
		openAddModal,
		openSettingsModal,
		selectedWidget,
		currentSettings,
		gridLayout,
		userProfile, 
		userRole,
		WidgetRenderer,
		handleEditMode,
		openAddWidgetModal,
		openWidgetSettingsModal,
		handleAddWidget,
		handleMinimize,
		handleOpenSettings,
		handleRemoveWidget,
		handleSaveSettings,
		handleLayoutChange,
		handleSetDefaultWidgets,
		handleSetDefaultLayout,
		userPreferences,
		snackbarOpen,
		snackbarMessage,
		handleCloseSnackbar,
	} = useDashboard();

  	return (
		<RoleBasedLayout
			onSetDefaultWidgets={handleSetDefaultWidgets}
			onSetDefaultLayout={handleSetDefaultLayout}
			userPreferences={userPreferences}
		>
			<Container>
				<Title
					title={t('dashboard:welcome', { name: userProfile?.first_name || 'User' })}
					subtitle={t('dashboard:roleBasedMessage', { role: userRole.name })}
				/>
				<Toolbar
					isEditing={isEditing}
					onClose={openAddWidgetModal(false)}
					onEditMode={handleEditMode}
					onAddMode={openAddWidgetModal(true)}
					onLayoutChange={handleLayoutChange}
					layout={gridLayout}
				/>

				{items ? (
					<WithSortable
						items={items}
						setItems={setItems}
						isEditing={isEditing}
						rows={gridLayout.rows}
						cols={gridLayout.cols}
						slots={{
							toolbar: WidgetToolbar,
							item: WidgetRenderer,
						}}
						slotProps={{
							toolbar: {
								isEditing,
								onMinimize: ({id}) => handleMinimize(id),
								onDelete: ({id}) => handleRemoveWidget(id),
								onWidgetSettings: ({id}) => handleOpenSettings(id),
							},
							item: {
								isEditing, // pass isEditing to the renderer
							},
							itemContainer: {
								width: widget => widget?.settings?.width || 4,
								height: widget => widget?.settings?.height || 1,
								sx: {
									minHeight: '40px',
								}
							}
						}}
					/>
				) : (
					<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
						<Typography variant="h6" color="text.secondary">
							{t('dashboard:loading')}
						</Typography>
					</Box>
				)}

				{/* Dialog to add new widgets */}
				<Dialog
					open={openAddModal}
					onClose={openAddWidgetModal(false)}
					maxWidth='md'
					fullWidth
					fullScreen={isMobile}
					closeAfterTransition={false}
					slotProps={{
						paper: {
							sx: {
								overflowY: 'visible',
								overflowX: 'hidden',
								backgroundColor: theme => theme.palette.background.default,
								backdropFilter: 'blur(10px)',
							}
						}
					}}
				>
	                <DialogTitle sx={{ borderBottom: '1px solid', borderColor: 'divider', mb: 2 }}>
						<Typography variant="h5">{t('widget:addWidget')}</Typography>
						<Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
							{t('widget:addWidgetDescription', { defaultValue: 'Select a widget to add to your dashboard' })}
						</Typography>
					</DialogTitle>
	                <DialogContent>
	                    <Widgets onAddWidget={handleAddWidget}/>
	                </DialogContent>
	                <DialogActions sx={{ borderTop: '1px solid', borderColor: 'divider', mt: 2 }}>
	                    <Button variant='contained' onClick={openAddWidgetModal(false)}>{t('general:cancel')}</Button>
	                </DialogActions>
	            </Dialog>

				{/* Dialog to edit widget settings */}
	            <Dialog open={Boolean(isEditing && openSettingsModal)} onClose={openWidgetSettingsModal(false)}>
					<DialogTitle>{t('widget:settings')}</DialogTitle>
					<DialogContent>
						{(selectedWidget && selectedWidget.settings)
							? selectedWidget.renderSettings(selectedWidget)
							:<Typography variant='body2'>{t('widget:noSettings')}</Typography>
						}
					</DialogContent>
					<DialogActions>
						{(selectedWidget && selectedWidget.settings) ?
							<>
								<Button variant='contained' color='primary' onClick={openWidgetSettingsModal(false)}>{t('general:cancel')}</Button>
								<Button variant='contained' color='inherit' onClick={() => handleSaveSettings(selectedWidget.id, currentSettings)}>{t('general:save')}</Button>
							</>
							: <Button variant='contained' color='primary' onClick={openWidgetSettingsModal(false)}>{t('general:close')}</Button>
						}
					</DialogActions>
	            </Dialog>

				{/* Notification Snackbar */}
				<Snackbar
					open={snackbarOpen}
					autoHideDuration={6000}
					onClose={handleCloseSnackbar}
					anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
				>
					<Alert onClose={handleCloseSnackbar} severity="info" sx={{ width: '100%' }}>
						{snackbarMessage}
					</Alert>
				</Snackbar>
			</Container>
		</RoleBasedLayout>
  	);
}