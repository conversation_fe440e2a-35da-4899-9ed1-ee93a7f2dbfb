import React, { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
    Container,
    Grid,
    Card,
    CardContent,
    CardActionArea,
    Typography,
    Box,
    Tab
} from '@mui/material';
import { Tab<PERSON>ontex<PERSON>, <PERSON>b<PERSON><PERSON>, TabPanel } from '@mui/lab';
import {
    PointOfSaleOutlined as RegisterIcon,
    ShowChartOutlined as SalesIcon,
    ConfirmationNumberOutlined as EventsIcon,
    PaymentsOutlined as OutstandingIcon,
    CardMembershipOutlined as MembershipsIcon,
    PeopleOutlined as UsersIcon,
    BarChartOutlined as UsageIcon,
    DashboardOutlined as DashboardIcon,
    SpaOutlined as ServicesIcon
} from '@mui/icons-material';
import { Title, SimpleBar, WithScrollEffect } from '@siteboss-frontend/shared/components';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { MODULES } from '../Dashboard/constants';

// Import report components
import RegisterReportsContent from './RegisterReports/RegisterReportsContent';
import SalesReportsContent from './SalesReports/SalesReportsContent';
import EventReportsContent from './EventReports/EventReportsContent';
import OutstandingReportsContent from './OutstandingReports/OutstandingReportsContent';
import MembershipReportsContent from './MembershipReports/MembershipReportsContent';
import UserReportsContent from './UserReports/UserReportsContent';
import UsageReportsContent from './UsageReports/UsageReportsContent';
import ServicesReportsContent from './ServicesReports/ServicesReportsContent';

const Reports = () => {
    const { t, isMobile } = useOutletContext();

    // Check permissions for reports
    const { permissions } = usePermission({moduleIds: [MODULES.SALES_REPORTS] });

    // Define report categories with their respective tabs
    const reportCategories = useMemo(() => [
        {
            id: 'overview',
            name: t('reports:categories.overview.name') || 'Overview',
            description: t('reports:categories.overview.description') || 'View all available reports',
            icon: <DashboardIcon />,
            path: '/reports',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'register',
            name: t('reports:categories.register.name') || 'Register Reports',
            description: t('reports:categories.register.description') || 'View reports for register transactions, cash flow, and settlements',
            icon: <RegisterIcon />,
            path: '/reports/register',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'sales',
            name: t('reports:categories.sales.name') || 'Sales Reports',
            description: t('reports:categories.sales.description') || 'View reports for sales by product, category, and payment method',
            icon: <SalesIcon />,
            path: '/reports/sales',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'events',
            name: t('reports:categories.events.name') || 'Event Reports',
            description: t('reports:categories.events.description') || 'View reports for event registrations, attendance, and revenue',
            icon: <EventsIcon />,
            path: '/reports/events',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'outstanding',
            name: t('reports:categories.outstanding.name') || 'Outstanding Reports',
            description: t('reports:categories.outstanding.description') || 'View reports for outstanding payments and invoices',
            icon: <OutstandingIcon />,
            path: '/reports/outstanding',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'memberships',
            name: t('reports:categories.memberships.name') || 'Memberships & Subscriptions',
            description: t('reports:categories.memberships.description') || 'View reports for memberships and subscription activity',
            icon: <MembershipsIcon />,
            path: '/reports/memberships',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'users',
            name: t('reports:categories.users.name') || 'User Reports',
            description: t('reports:categories.users.description') || 'View reports for user activity and demographics',
            icon: <UsersIcon />,
            path: '/reports/users',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'usage',
            name: t('reports:categories.usage.name') || 'Usage Reports',
            description: t('reports:categories.usage.description') || 'View reports for system usage and performance',
            icon: <UsageIcon />,
            path: '/reports/usage',
            permission: MODULES.SALES_REPORTS
        },
        {
            id: 'services',
            name: t('reports:categories.services.name') || 'Services Reports',
            description: t('reports:categories.services.description') || 'View reports for service bookings, providers, and performance',
            icon: <ServicesIcon />,
            path: '/reports/services',
            permission: MODULES.SALES_REPORTS
        }
    ], []);

    // Get tab from URL path or default to 'overview'
    const [selectedTab, setSelectedTab] = useState(() => {
        // Extract the report type from the URL path
        const path = location.pathname;
        if (path === '/reports') return 'overview';

        const reportType = path.split('/').pop();
        // Find the matching tab or default to overview
        return reportCategories.find(cat => cat.id === reportType) ? reportType : 'overview';
    });

    // Filter tabs based on permissions
    const visibleTabs = useMemo(() =>
        reportCategories.filter(tab => permissions[tab.permission]),
        [permissions, reportCategories]
    );

    // Handle tab change
    const handleTabChange = (_, newValue) => {
        setSelectedTab(newValue);
    };

    // Handle card click in overview tab
    const handleCardClick = (category) => {
        setSelectedTab(category.id);
    };

    // Set default tab on initial load
    useEffect(() => {
        setSelectedTab('overview');
    }, []);

    // Overview tab content
    const OverviewTab = () => (
        <>
            <SimpleBar sx={{ mb: 4, overflowX: 'auto' }}>
                <Box sx={{ minWidth: { xs: '600px', md: 'auto' } }}>
                    <Box>
                        <Typography variant="h6" component="div">
                            {t('reports:quickAccess')}
                        </Typography>
                    </Box>
                    <Box>
                        <Typography variant="h6" component="div">
                            {t('reports:recentReports')}
                        </Typography>
                    </Box>
                    <Box>
                        <Typography variant="h6" component="div">
                            {t('reports:favorites')}
                        </Typography>
                    </Box>
                </Box>
            </SimpleBar>

            <Typography variant="h5" gutterBottom>
                {t('reports:reportCategories')}
            </Typography>

            <Grid container spacing={3}>
                {reportCategories.filter(cat => cat.id !== 'overview').map((category) => (
                    <Grid item xs={12} sm={6} lg={4} key={category.id}>
                        <Card
                            variant="outlined"
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column'
                            }}
                        >
                            <CardActionArea
                                onClick={() => handleCardClick(category)}
                                sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'flex-start',
                                    justifyContent: 'flex-start'
                                }}
                            >
                                <CardContent sx={{ width: '100%' }}>
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            mb: 2
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                p: 1,
                                                borderRadius: 1,
                                                bgcolor: 'action.hover',
                                                mr: 2
                                            }}
                                        >
                                            {category.icon}
                                        </Box>
                                        <Typography variant="h6" component="div">
                                            {t(`reports:categories.${category.id}.name`)}
                                        </Typography>
                                    </Box>
                                    <Typography variant="body2" color="text.secondary">
                                        {t(`reports:categories.${category.id}.description`)}
                                    </Typography>
                                </CardContent>
                            </CardActionArea>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </>
    );



    return (
        <Container maxWidth="lg" sx={{ overflow: 'hidden' }}>
            <Title
                title={t('reports:reports')}
                breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('reports:reports')}]}
            />

            <TabContext value={selectedTab}>
                <WithScrollEffect threshold={0} targetElement={window} effect={{ elevation: 3 }}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                        <TabList
                            onChange={handleTabChange}
                            variant="scrollable"
                            scrollButtons="auto"
                            aria-label="report categories"
                            sx={{
                                '& .MuiTab-root': {
                                    display: 'flex',
                                    flexDirection: isMobile ? 'column' : 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minHeight: isMobile ? '72px' : '48px',
                                }
                            }}
                        >
                            {visibleTabs.map((tab) => (
                                <Tab
                                    key={tab.id}
                                    label={t(`reports:categories.${tab.id}.name`)}
                                    value={tab.id}
                                    icon={tab.icon}
                                    iconPosition={isMobile ? "top" : "start"}
                                />
                            ))}
                        </TabList>
                    </Box>
                </WithScrollEffect>

                <TabPanel value="overview" sx={{ p: 0 }}>
                    <OverviewTab />
                </TabPanel>

                <TabPanel value="register" sx={{ p: 0 }}>
                    <RegisterReportsContent />
                </TabPanel>

                <TabPanel value="sales" sx={{ p: 0 }}>
                    <SalesReportsContent />
                </TabPanel>

                <TabPanel value="events" sx={{ p: 0 }}>
                    <EventReportsContent />
                </TabPanel>

                <TabPanel value="outstanding" sx={{ p: 0 }}>
                    <OutstandingReportsContent />
                </TabPanel>

                <TabPanel value="memberships" sx={{ p: 0 }}>
                    <MembershipReportsContent />
                </TabPanel>

                <TabPanel value="users" sx={{ p: 0 }}>
                    <UserReportsContent />
                </TabPanel>

                <TabPanel value="usage" sx={{ p: 0 }}>
                    <UsageReportsContent />
                </TabPanel>

                <TabPanel value="services" sx={{ p: 0 }}>
                    <ServicesReportsContent />
                </TabPanel>
            </TabContext>
        </Container>
    );
};

export default Reports;
