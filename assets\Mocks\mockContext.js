import { useTranslation } from "react-i18next";
import { useRef } from 'react'

export const mockContext =()=>{

    const t= useTranslation();
    const ref =useRef(null);
    const isMobile = false;
    const isDesktop = true;
    const isLargeDesktop = true;
    const colorMode = "dark";
    const language = "en";
    const company = {
        name: "Cat Company",
        id: 99,
        location: "123 Somewhere St"
    };

    return{
        t,
        ref,
        isMobile,
        isDesktop,
        isLargeDesktop,
        colorMode,
        language,
        company
    }
}    
