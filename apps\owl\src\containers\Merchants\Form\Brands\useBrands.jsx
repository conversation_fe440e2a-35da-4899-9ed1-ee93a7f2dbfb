import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

export const useBrands = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/brands`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/brands`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/brands`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/brands`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/contacts`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);
    const { fetchData: fetchContacts, data: contactsData, loading: contactsLoading } = useApi(apiParams[4]);

    const fields = useMemo(() => [
        {name: 'brand_name', type: 'text', label: 'merchant:brandName', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'brand_code', type: 'text', label: 'merchant:brandCode', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'brand_phone', type: 'tel', label: 'merchant:brandPhone', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'primary_contact_id', type: 'tel', label: 'merchant:contact', required: false, value: null, component: "Select", 
            options: contactsData?.items?.map(a => ({
                id: a.id, 
                name: a.first_name + ' ' + a.last_name
            })) || [], margin: "normal", rowSize: {xs: 12, md: 6}
        },
        {name: 'brand_from_email', type: 'email', label: 'merchant:brandEmail', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'brand_from_name', type: 'text', label: 'merchant:brandEmailName', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
    ], [contactsData]);

    const [modalOpen, setModalOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    }, []);

    const handleToggleModal = useCallback(open => e => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({page: page, per_page: pageSize});
            if (res?.data) return res.data;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);

    const handleEditItem = useCallback(() => {
        if (!selected?.length) return;

        const _data = {
            id: selected[0]?.id,
            brand_name: selected[0]?.brand_name,
            brand_code: selected[0]?.brand_code,
            brand_phone: selected[0]?.brand_phone,
            primary_contact_id: selected[0]?.primary_contact_id,
            brand_from_email: selected[0]?.brand_from_email,
            brand_from_name: selected[0]?.brand_from_name,
        }
        
        setFormData(_data);
        setModalOpen(true);
    }, [selected]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                const res = await deleteData({endpoint: `/clients/${merchantId}/brands/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData();
        }
    }, [deleteData, fetchData, merchantId]);

    const handleSaveItem = useCallback(async () => {
        if (!formData) return;

        if (merchantId){
            // when a merchantId is defined, save directly
            const apiCall = formData.id ? updateData : createData;
            try {
                const res = await apiCall(formData);
                if (res?.data) {
                    fetchData();
                    setModalOpen(false);
                }
                return true;
            } catch(e){
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            onMainFormChange({
                target: {
                    name: 'brands',
                    value: {...formData}
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [formData, updateData, createData, fetchData, merchantId, onMainFormChange]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        data?.items?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data]);

    const handleResetForm = useCallback(() => {
        setFormData({});
    }, []);

    // DataTable columns
    const columns = useMemo(() => [
        {
            field: 'brand_name',
            headerName: t('brand:name'),
            flex: 1,
        },
        {
            field: 'brand_code',
            headerName: t('brand:code'),
        },
    ], [t]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
        fetchContacts();
    }, [handleLoadItems, fetchContacts]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        setFormData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading || contactsLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: fields || [],
    };  
};