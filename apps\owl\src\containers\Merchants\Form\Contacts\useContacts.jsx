import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';
import { toCamelCase } from '@siteboss-frontend/shared/utils';
import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

export const useContacts = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();

    const fixedData = useSelector(state => state.fixedData);

    const fields = useMemo (() => [
        {name: 'first_name', type: 'text', label: 'merchant:contactFirstName', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'last_name', type: 'text', label: 'merchant:contactLastName', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'title', type: 'text', label: 'merchant:contactTitle', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'contact_type', label: 'merchant:type', required: true, value: '', component: "Select", options: fixedData?.contactTypes?.map(a => ({id: a.id, slug: `merchant:contactTypes.${toCamelCase(a.name)}`})) || [], margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'email', type: 'email', label: 'merchant:email', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'work_phone', type: 'tel', label: 'merchant:phone', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},

        {name: 'address_line1', type: 'text', label: 'merchant:addressLine1', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'address_line2', type: 'text', label: 'merchant:addressLine2', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 6}},
        {name: 'location', label: null, required: false, value: {}, component: "HierarchySelector", margin: "normal", rowSize: {xs: 12},
            levels: [
                { id: 'country', label: 'merchant:country', parent: null, parentField: 'country_id' },
                { id: 'state_province_code', label: 'merchant:state', parent: 'country', parentField: 'country_id', parentIdField: 'country_id' },
                { id: 'city', label: 'merchant:city', parent: 'state_province_code', parentField: 'state_id', parentIdField: 'state_id' }
            ],
            isAsync: false,
            data: {
                country: fixedData?.countries || [],
                state_province_code: fixedData?.states || [],
                city: fixedData?.cities || [],
            },
            getOptionLabel: {
                country: option => option ? (option?.name || option) : '',
                state_province_code: option => option ? (option?.name || option) : '',
                city: option => option ? (option?.name || option) : '',
            },
            renderOption: {
                country: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
                state_province_code: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
                city: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
            },
            slotProps: {
                city: { freeSolo: true }
            },
        },
        {name: 'postal_code', type: 'text', label: 'merchant:zipcode', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, lg: 4}},
        {name: 'is_primary', type: 'text', label: 'merchant:primary', required: false, value: 1, checked: false, component: "Switch", margin: "normal", rowSize: {xs: 12, lg: 8}},
        {name: 'contact_notes', type: 'text', label: 'merchant:notes', required: false, value: '', component: "TextField", margin: "normal", multiline: true, rows: 3, rowSize: {xs: 12}},
    ], [fixedData]);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/contacts`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/contacts`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/contacts`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/contacts`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);

    const [modalOpen, setModalOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        let checked;
        if (e.target?.type === 'checkbox') checked = e.target.checked;
        if (checked !== undefined) {
            if (checked === false) {
                setFormData(prev => ({
                    ...prev,
                    [name]: undefined
                }));
                return;
            }            
        }
        setFormData(prev => ({ ...prev, [name]: value}));
    }, []);

    const handleToggleModal = useCallback(open => e => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({client_id: merchantId, page: page, per_page: pageSize});
            if (res?.data?.items) return res.data.items;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);

    const handleEditItem = useCallback(() => {
        if (!selected?.length) return;

        /*const _data = {
            id: selected[0]?.id,
            issuing_state: {country: "United States", state: selected[0]?.issuing_state_code},
            license_name: selected[0]?.license_name,
            expiration_date: format(new Date(selected[0]?.expiration_date), 'yyyy-MM-dd'),
        }*/

            console.log("SELECTED", selected);
        
        setFormData(selected[0]);
        setModalOpen(true);
    }, [selected]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                const res = await deleteData({endpoint: `/contacts/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData({client_id: merchantId, page: 1, per_page: pageSize});
        }
    }, [deleteData, fetchData, merchantId, pageSize]);

    const handleSaveItem = useCallback(async () => {
        if (!formData) return;

        if (merchantId){
            // when a merchantId is defined, save directly
            const apiCall = formData.id ? updateData : createData;
            try {
                const res = await apiCall({...formData, client_id: merchantId });
                if (res?.data) {
                    fetchData({client_id: merchantId, page: 1, per_page: pageSize});
                    setModalOpen(false);
                }
                return true;
            } catch(e){
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            onMainFormChange({
                target: {
                    name: 'contacts',
                    value: {...formData}
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [formData, updateData, createData, fetchData, merchantId, onMainFormChange, pageSize]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        data?.items?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data]);

    const handleResetForm = useCallback(() => {
        setFormData({});
    }, []);

    // DataTable columns
        const columns = useMemo(() => [
        {
            field: 'first_name',
            headerName: t('merchant:contactFirstName'),
            flex: 1,
        },
        {
            field: 'last_name',
            headerName: t('merchant:contactLastName'),
            flex: 1,
        },
        {
            field: 'email',
            headerName: t('merchant:contactEmail'),
            flex: 1,
        },
        {
            field: 'work_phone',
            headerName: t('merchant:contactPhone'),
            flex: 1,
        },
        {
            field: 'contact_type_info',
            headerName: t('merchant:contactType'),
            flex: 1,
            valueGetter: value => {
                const role = value?.name;
                return role ? t(`merchant:contactTypes.${toCamelCase(role)}`, role) : '-';
            }
        }
    ], [t]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
    }, [handleLoadItems]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        setFormData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: fields,
    };  
};