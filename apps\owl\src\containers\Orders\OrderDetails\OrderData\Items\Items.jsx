import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Table, TableHead, TableBody, TableRow, TableCell } from '@mui/material';
import { createCurrencyFormatter } from '@siteboss-frontend/shared/utils';

export const Items = (props) => {
    const { data } = props;
    const { t, language, currency } = useOutletContext();

    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    if (!data?.items || data.items.length === 0) return null;

    return (
        <Table sx={{ my: 4 }} aria-label="order items">
            <TableHead>
                <TableRow>
                    <TableCell>{t("order:item")}</TableCell>
                    <TableCell align="right">{t("order:qty")}</TableCell>
                    <TableCell align="right">{t("order:price")}</TableCell>
                </TableRow>
            </TableHead>
            <TableBody>
                {data.items.map((item, i) => (
                    <TableRow key={`order-detail-item-${item.id}-${i}`}>
                        <TableCell>{item.product_name || item.name}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell align="right">{currencyFormatter.format(item.final_price || item.price || 0)}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
}
