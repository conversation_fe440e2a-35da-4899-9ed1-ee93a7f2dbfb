export const properties = [
    {
        name: 'label',
        label: 'builder:component.button.label',
        component: "TextField",
        value: 'Will work for clicks!',
        size: "small",
        required: true,
        margin: "normal",
    },
    {
        name: 'url',
        label: 'builder:component.button.url',
        component: "TextField",
        value: '',
        size: "small",
        margin: "normal",
    },
    {
        name: 'target',
        label: 'builder:component.button.target.title',
        component: "Select",
        options: [
            {id: '_self', slug: 'builder:component.button.target.self'},
            {id: '_blank', slug: 'builder:component.button.target.blank'},
            {id: '_popup', slug: 'builder:component.button.target.popup'},
        ],
        value: '_self',
        size: "small",
        margin: "normal",
    },
    {
        name: 'icon',
        label: 'builder:component.button.icon.title',
        component: "IconSelector",
        value: '',
        size: "medium",
        fullWidth: true,
        variant: 'outlined',
        margin: "normal",
    },
    {
        name: 'iconPosition',
        label: 'builder:component.button.iconPosition.title',
        component: "Select",
        options: [
            {id: 'default', slug: 'builder:component.button.iconPosition.default'},
            {id: 'start', slug: 'builder:component.button.iconPosition.start'},
            {id: 'end', slug: 'builder:component.button.iconPosition.end'},
        ],
        value: 'default',
        size: "small",
        margin: "normal",
    },
    /*
    {
        name: 'variant',
        label: 'builder:component.button.variant.title',
        component: "Select",
        options: [
            {id: 'contained', slug: 'builder:component.button.variant.contained'},
            {id: 'outlined', slug: 'builder:component.button.variant.outlined'},
            {id: 'text', slug: 'builder:component.button.variant.text'},
        ],
        value: 'contained',
        size: "small",
        margin: "normal",
    },
    */
    {
        name: 'color',
        label: 'builder:component.button.color.title',
        component: "Select",
        options: [
            {id: 'primary', slug: 'builder:component.button.color.primary'},
            {id: 'secondary', slug: 'builder:component.button.color.secondary'},
            {id: 'success', slug: 'builder:component.button.color.success'},
            {id: 'error', slug: 'builder:component.button.color.error'},
            {id: 'info', slug: 'builder:component.button.color.info'},
            {id: 'warning', slug: 'builder:component.button.color.warning'},
            {id: 'inherit', slug: 'builder:component.button.color.inherit'},
        ],
        value: 'primary',
        size: "small",
        margin: "normal",
    },
    {
        name: 'size',
        label: 'builder:component.button.size.title',
        component: "Select",
        options: [
            {id: 'small', slug: 'builder:component.button.size.small'},
            {id: 'medium', slug: 'builder:component.button.size.medium'},
            {id: 'large', slug: 'builder:component.button.size.large'},
        ],
        value: 'medium',
        size: "small",
        margin: "normal",
    },
];
