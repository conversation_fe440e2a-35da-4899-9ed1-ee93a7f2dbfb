import { useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { uuid } from '../../utils';
import { useLazyDynamicQuery, useFormDataMutation } from '../../store/reducers/apiSlice';
import { initialState, mapErrorMessage } from '../initialState';

export const useRtkQuery = ({ enableCache = false }) => {
    const { t } = useTranslation();
    const query = useRef();

    const [dynamicQuery, queryResult] = useLazyDynamicQuery();
    const [formDataMutate, formDataResult] = useFormDataMutation();

    const returnResult = formDataResult.isUninitialized ? formDataResult : queryResult;

    const call = useCallback(async requests => {
        const { endpoint, method = "GET", data, params, config, enableCache: forceEnableCache = enableCache } = requests;
        const isFormData = data instanceof FormData || params instanceof FormData;

        const queryArgs = {
            url: endpoint,
            method,
            body: method.toUpperCase() !== 'GET' ? (isFormData ? data : (data && Object.keys(data || {}).length > 0 ? {...data} : undefined)) : undefined,
            params: method.toUpperCase() === 'GET' ? (isFormData ? params : (params && Object.keys(params || {}).length > 0 ? {...params} : undefined)) : undefined,
            headers: config?.headers,
        };

        /*if (!forceEnableCache && !isFormData) {
            if (queryArgs.body !== undefined) queryArgs.body.__ = uuid();
            else if (queryArgs.params !== undefined) queryArgs.params.__ = uuid();
        }*/

        const queryParams = {
            preferCacheValue: forceEnableCache,
            fixedCacheKey: forceEnableCache ? (`${method}-${endpoint}-${isFormData ? "FormData" : JSON.stringify(params || data || "")}`) : undefined,
        };

        if (!forceEnableCache && !isFormData) {
            queryParams.forceRefetch = true;
        }

        const triggerQuery = isFormData ? formDataMutate : dynamicQuery;
        query.current = triggerQuery(queryArgs, queryParams);
        const res = await query.current.then(response => ({ response })).catch(error => ({ error }));

        //console.log("forceEnableCache", forceEnableCache, "queryArgs",queryArgs, "queryParams",queryParams, "res",res)

        let result = null, errors = null, httpCode = null;

        if (res.response) {
            result = res.response?.data?.data || null;
            httpCode = res.responses?.data?.status || 200;
            if (res.response?.data?.errors) {
                errors = res.response?.data?.errors;
            }
            if (res.response?.error) {
                httpCode = res.response?.error?.status || res.response?.error?.code;
                errors = t(mapErrorMessage(httpCode));
                result = null;
            }
        } else if (res.error) {
            httpCode = res.error?.status;
            errors = t(mapErrorMessage(res.error?.status));
        }

        return { data: result, errors, httpCode };
    }, [dynamicQuery, formDataMutate, t, enableCache]);

    const refetch = useCallback(() => {
        if (query.current) query.current.refetch();
    }, []);

    const cancel = useCallback(() => {
        if (query.current) query.current.abort();
    }, []);

    return {
        ...initialState,
        hookName: 'useRtkQuery',
        call,
        cancel,
        refetch,
        data: returnResult?.data?.data || null,
        loading: returnResult.isLoading || returnResult.isFetching || false,
        errors: returnResult.isError ? t(mapErrorMessage(returnResult?.error?.status)) : null,
        httpCode: (returnResult.isError ? returnResult?.error?.status : returnResult?.data?.status) || null,
    };
};
