import React, { useRef, useState, useCallback, useMemo } from 'react';
import clsx from 'clsx';
import { Paper, useTheme } from '@mui/material';
import { MDXEditor,
    headingsPlugin,
    listsPlugin,
    quotePlugin,
    thematicBreakPlugin,
    toolbarPlugin,
    linkPlugin,
    linkDialogPlugin,
    imagePlugin,
    tablePlugin,
    frontmatterPlugin,
    codeBlockPlugin,
    codeMirrorPlugin,
    diffSourcePlugin,
    markdownShortcutPlugin,
 } from '@mdxeditor/editor';

import '@mdxeditor/editor/style.css';
import styles from './Editor.module.scss';

import Toolbar from './Toolbar';

export const Editor = ({ language = "markdown", value, errors, onChange, onBlur, ...props }) => {
    const theme = useTheme();
    const editorRef = useRef(null);

    const plugins = useMemo(() =>[
        toolbarPlugin({ toolbarContents: () => <Toolbar /> }),
        listsPlugin(),
        quotePlugin(),
        headingsPlugin({ allowedHeadingLevels: [1, 2, 3] }),
        linkPlugin(),
        linkDialogPlugin(),
        imagePlugin({
          imageAutocompleteSuggestions: [
            "https://placehold.co/150?font=source-sans-pro"
          ],
          imageUploadHandler: async () =>
            Promise.resolve("https://picsum.photos/200/300"),
        }),
        tablePlugin(),
        thematicBreakPlugin(),
        frontmatterPlugin(),
        codeBlockPlugin({ defaultCodeBlockLanguage: "txt" }),
        // sandpackPlugin({ sandpackConfig: virtuosoSampleSandpackConfig }),
        codeMirrorPlugin({
          codeBlockLanguages: {
            js: "JavaScript",
            css: "CSS",
            txt: "text",
            tsx: "TypeScript",
          },
        }),
        // directivesPlugin({
        //   directiveDescriptors: [
        //     YoutubeDirectiveDescriptor,
        //     AdmonitionDirectiveDescriptor,
        //   ],
        // }),
        diffSourcePlugin({ viewMode: "rich-text", diffMarkdown: "" }),
        markdownShortcutPlugin(),
    ], []);


    const [focused, setFocused] = useState(false);

    const handleEditorChange = useCallback(value => {
        try {
            if (editorRef.current && onChange) onChange(null, value);
        } catch(error){
            console.log(error);
        }
    }, [onChange]);

    const handleEditorBlur = useCallback(e => {
        try {
            if (editorRef.current && onChange) {
                setFocused(false);
                if (onBlur) onBlur(e.target.value);
            }
        } catch(error){
            console.log(error);
        }
    }, [onBlur]);


    return (
        <Paper 
            {...props} 
            variant="textField" 
            className={clsx(focused ? 'Mui-focused' : '', errors ? 'Mui-error' : '')} 
            sx={{ bgcolor: 'transparent', width: '100%', height: '100%', overflow: 'auto' }}
            onFocus={() => setFocused(true)}
        >
            <MDXEditor 
                ref={editorRef}
                markdown={value} 
                plugins={plugins}
                onChange={handleEditorChange} 
                onBlur={handleEditorBlur}
                className={`${styles.editor}`}
            />
        </Paper>
    );
}