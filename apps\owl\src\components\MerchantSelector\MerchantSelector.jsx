import { useMemo } from 'react';
import { Box, Typography, Chip, Stack } from '@mui/material';
import { FormItem } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

const apiParams = {
    endpoint: '/user/list',
    method: 'POST',
    data: {
        page_no: 1,
        max_records: 50,
        sort_col: 'id',
        sort_direction: 'DESC',
    }
};

const getSearchParams = searchText => {
    return ({
        filters: {
            search_words: searchText
        }
    });
};

const defaultGetOptionLabel = option => {
    if (!option) return '';
    if (typeof option === 'string') return option;

    const code = option.code || option.first_name || '';
    const name = option.name || `${option.first_name || ''} ${option.last_name || ''}`.trim();

    let label = '';
    if (code) label += code;
    if (name) label += (label ? ' - ' : '') + name;

    return label || `Merchant ${option.id}`;
};

const defaultRenderOption = (props, option) => {
    if (!option) return null;
    const code = option.code || option.first_name || '';
    const name = option.name || `${option.first_name || ''} ${option.last_name || ''}`.trim();
    const email = option.email || '';
    const phone = option.phone || option.mobile_phone || '';
    const type = option.type || option.roles?.[0]?.name || '';

    return (
        <Box component="li" {...props} key={option.id} sx={{ p: 2, width: '100%' }}>
            <Stack useFlexGap spacing={0.5} sx={{ width: '100%' }}>
                <Stack direction="row" useFlexGap alignItems="center" spacing={1}>
                    <Typography variant="body1" sx={{ flexGrow: 1 }}>
                        {name}
                    </Typography>
                    {code &&
                        <Chip
                            label={code}
                            size="small"
                            color="inherit"
                        />
                    }
                </Stack>
                <Stack direction="column" useFlexGap spacing={1}>
                    {type &&
                        <Typography variant="caption" color="text.secondary">
                            {type}
                        </Typography>
                    }
                    <Typography variant="body2" color="text.secondary">
                        {email} {phone && ` / ${phone}`}
                    </Typography>
                </Stack>
            </Stack>
        </Box>
    );
};

export const MerchantSelector = ({
    label = 'merchant:merchant', // the label for the field
    name = 'merchant_id', // the name of the field
    required = false, // if the field is required
    errors: initialErrors, // the errors for the field
    loading: parentLoading, // the loading status of the form so we can disable the fields
    value, // the value of the field
    onChange, // the function to be called when the field value changes
    onBlur, // the function to be called when the field is blurred
    multiple = false, // if the field should allow multiple selections
    placeholder, // placeholder text
    helperText, // helper text to display
    disabled = false, // if the field is disabled
    fullWidth = true, // if the field should take full width
    margin = "normal", // margin for the field
    size = "medium", // size of the field
    variant = "outlined", // variant of the field
    getOptionLabel, // custom function to get option label
    renderOption, // custom function to render options
    filterSelectedOptions = false, // whether to filter out selected options
    freeSolo = false, // whether to allow free text input
    clearOnBlur = false, // whether to clear on blur
    selectOnFocus = true, // whether to select on focus
    ...props
}) => {
    const { fetchData, loading, errors } = useApi({ params: apiParams });

    return (
        <FormItem
            component="Autocomplete"
            label={label}
            name={name}
            required={required}
            errors={initialErrors || errors}
            loading={parentLoading || loading}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            multiple={multiple}
            placeholder={placeholder}
            helperText={helperText}
            disabled={disabled}
            fullWidth={fullWidth}
            margin={margin}
            size={size}
            variant={variant}

            isAsync={true}
            fetchData={fetchData}
            dataField="users"
            getSearchParams={getSearchParams}

            getOptionLabel={getOptionLabel || defaultGetOptionLabel}
            renderOption={renderOption || defaultRenderOption}
            filterSelectedOptions={filterSelectedOptions}
            freeSolo={freeSolo}
            clearOnBlur={clearOnBlur}
            selectOnFocus={selectOnFocus}

            {...props}
        />
    );
};