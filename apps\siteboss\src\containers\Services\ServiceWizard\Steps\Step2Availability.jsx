import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, FormControlLabel, Checkbox, Box } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { enUS, es } from 'date-fns/locale';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step2 } from './stepList';

const Step2Availability = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const [localFormData, setLocalFormData] = useState(formData || {});
    const language = useSelector(state => state.language);

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        setLocalFormData(prev => {
            const updated = { ...prev, [field]: value };

            // Special handling for date checkboxes
            if (field === 'has_start_date' && !value) {
                updated.start_date = null;
            }
            if (field === 'has_end_date' && !value) {
                updated.end_date = null;
            }

            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Handle date changes
    const handleDateChange = useCallback((field, date) => {
        if (date) {
            setLocalFormData(prev => {
                const updated = { ...prev, [field]: date };
                dispatch(updateFormData(updated));
                return updated;
            });
        }
    }, [dispatch]);

    // Get step fields
    const fields = step2(localFormData);

    return (
        <Grid container spacing={3}>
            {fields.map(section => (
                <Grid item xs={12} key={section.id}>
                    <Typography variant="h6" gutterBottom>
                        {section.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                        {section.description}
                    </Typography>
                    <Grid container spacing={2}>
                        {section.fields.map(field => (
                            <Grid item xs={12} md={field.type === 'date' ? 6 : 12} key={field.name}>
                                {field.component === "Checkbox" && (
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={!!localFormData[field.name]}
                                                onChange={(e) => handleChange(field.name, e.target.checked)}
                                                disabled={loading}
                                            />
                                        }
                                        label={field.label}
                                    />
                                )}
                                {field.component === "DatePicker" && (
                                    <Box sx={{ mt: 2 }}>
                                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                                            <DatePicker
                                                label={field.label}
                                                value={localFormData[field.name] || null}
                                                onChange={(date) => handleDateChange(field.name, date)}
                                                disabled={field.disabled || loading}
                                                slotProps={{
                                                    textField: {
                                                        fullWidth: true,
                                                        required: field.required,
                                                        error: field.error,
                                                        helperText: field.helperText,
                                                    }
                                                }}
                                            />
                                        </LocalizationProvider>
                                    </Box>
                                )}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default Step2Availability;
