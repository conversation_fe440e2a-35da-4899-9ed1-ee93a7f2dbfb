import { useLocation, useParams } from 'react-router-dom';

const getRoutePath = (location, params) => {
    const { pathname } = location;
  
    let _params = params;
    if (!Object.keys(params).length) _params = null;
  
    let route = pathname;
    let base = pathname;
    Object.entries(params).forEach(([paramName, paramValue]) => {
        if (paramValue) route = route.replace(paramValue, `:${paramName}`);
    });
    
    return {
        pathname,
        route,
        base: route.replace(/\/:[^/]+/g, ''),
        params: _params
    };
}
  
export const usePath = () => {
    const location = useLocation();
    const params = useParams();
    const path = getRoutePath(location, params);
  
    return path;
};