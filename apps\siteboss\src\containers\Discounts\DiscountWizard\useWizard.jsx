import { useState, useEffect, useCallback, useRef } from 'react';
import { formatISO } from 'date-fns';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useOutletContext, useNavigate } from 'react-router-dom';
import { ErrorBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';
import { uuid } from '@siteboss-frontend/shared/utils';
import { setInfo, resetInfo, setSelectedConditions, updateFormData } from '../../../store/reducers/discountWizardSlice';

export const useWizard = () => {
    const { t } = useOutletContext();
    const { discountId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    // Use a ref to store the discountWizard data to prevent re-renders
    const discountWizardRef = useRef(null);
    const discountWizardFromStore = useSelector(state => state.discountWizard);

    // We're intentionally not cleaning up the Redux store on unmount
    // to preserve form data when navigating away and coming back
    // If you need to reset the form, call resetInfo() explicitly
    // Add a function to reset the form data
    const resetForm = useCallback(() => {
        dispatch(resetInfo());
        console.log('Resetting form data in Redux store');
    }, [dispatch]);

    // Only update the ref if it's null or if the data has changed significantly
    useEffect(() => {
        // if discountWizard formData is null
        if (discountWizardFromStore?.formData !== null && JSON.stringify(discountWizardRef.current?.formData) !== JSON.stringify(discountWizardFromStore?.formData)) {
            discountWizardRef.current = discountWizardFromStore;
            console.log('Updated discountWizardRef with:', discountWizardFromStore);
        } else if (!discountWizardRef.current) {
            console.log('Initializing discountWizardRef with:', discountWizardFromStore);
            discountWizardRef.current = discountWizardFromStore;
        }
    }, [discountWizardFromStore]);

    // Use the ref value instead of the direct selector result
    const discountWizard = discountWizardRef.current;

    const [activeStep, setActiveStep] = useState(0);
    const [success, setSuccess] = useState(false);
    const [hasErrors, setHasErrors] = useState(false);
    const [errorBar, setErrorBar] = useState(null);
    const [loading, setLoading] = useState(false); // Add loading state
    const [completedSteps, setCompletedSteps] = useState([0]); // Track completed steps, starting with step 0

    // API calls

    // Memoize the API configuration to prevent infinite updates
    const discountApiConfig = {
        params: {
            endpoint: `/coupon/${discountId}`,
            method: 'GET',
            data: {id: discountId},
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'If-None-Match': new Date().getTime() // Cache busting
            }
        },
        enableCache: false,
        cacheKey: `discount-${discountId}-${new Date().getTime()}` // Add timestamp to cache key
    };

    const { fetchData: fetchDiscount, data: discountData, loading: discountLoading, errors: discountError } = useApi(discountApiConfig);

    const { fetchData: createDiscount, loading: createLoading, errors: createError } = useApi({
        params: {
            endpoint: '/coupon/create',
            method: 'POST'
        },
        enableCache: false
    });

    const { fetchData: updateDiscount, loading: updateLoading, errors: updateError } = useApi({
        params: {
            endpoint: '/coupon/edit',
            method: 'PUT'
        },
        enableCache: false
    });

    // Error handling
    const DiscountErrorBar = discountError ? () => <ErrorBar message={discountError} /> : null;
    const CreateErrorBar = createError ? () => <ErrorBar message={createError} /> : null;
    const UpdateErrorBar = updateError ? () => <ErrorBar message={updateError} /> : null;

    // Fetch data
    const handleErrors = useCallback((errors) => {
        if (errors) {
            setHasErrors(true);
            // Create a component function instead of a direct element
            setErrorBar(() => () => <ErrorBar message={errors} />);
        } else {
            setHasErrors(false);
            setErrorBar(null);
        }
    }, []);

    // Format data for API
    const formatData = useCallback((data) => {
        console.log('Formatting data for API:', data);
        const formattedData = {};

        console.log('Formatting data for API:', data);

        // Check if data is already in the expected format (not using FormProvider structure)
        if (data.params !== undefined) {
            console.log('Data is already in the expected format');
            // Copy all fields directly
            Object.keys(data).forEach(key => {
                if (key === 'params') {
                    // Process params field
                    if (data.params && typeof data.params === 'object') {
                        // Process multi-select conditions to extract just the IDs
                        const processedParams = {};
                        Object.keys(data.params).forEach(paramKey => {
                            const value = data.params[paramKey];
                            if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].id !== undefined) {
                                // Extract just the IDs from the objects
                                processedParams[paramKey] = value.map(item => item.id);
                                console.log(`Extracted IDs for ${paramKey}:`, processedParams[paramKey]);
                            } else {
                                // Keep other values as is
                                processedParams[paramKey] = value;
                            }
                        });
                        formattedData.params = JSON.stringify(processedParams);
                        console.log('Processed params for API:', formattedData.params);
                    } else if (typeof data.params === 'string') {
                        // If params is already a string, use it directly
                        formattedData.params = data.params;
                    } else {
                        // Default to empty object
                        formattedData.params = JSON.stringify({});
                    }
                } else {
                    // Copy other fields directly
                    formattedData[key] = data[key];
                }
            });
        } else {
            // Check if data is already a flat object (from flat: true in submitForm)
            if (!Array.isArray(data) && typeof data === 'object' && !data[0]) {
                console.log('Data is already a flat object');

                // Get the complete form data from the Redux store
                const reduxState = discountWizardRef.current;
                const reduxFormData = reduxState?.formData || {};
                const reduxParams = reduxFormData?.params;

                console.log('Redux form data:', reduxFormData);
                console.log('Redux params:', reduxParams);

                // Start with the complete Redux form data
                // This ensures we have all fields, including name and description
                for (const key in reduxFormData) {
                    if (key !== 'params') { // Handle params separately
                        formattedData[key] = reduxFormData[key];
                    }
                }

                // Then overlay with any fields from the flattened data
                // This ensures we get any fields that were updated in the current step
                Object.keys(data).forEach(key => {
                    if (key !== 'params') { // Handle params separately
                        formattedData[key] = data[key];
                    }
                });

                console.log('Combined form data:', formattedData);

                // Add params from Redux if they exist
                if (reduxParams && typeof reduxParams === 'object') {
                    // Process multi-select conditions to extract just the IDs
                    const processedParams = {};
                    Object.keys(reduxParams).forEach(paramKey => {
                        const value = reduxParams[paramKey];
                        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].id !== undefined) {
                            // Extract just the IDs from the objects
                            processedParams[paramKey] = value.map(item => item.id);
                            console.log(`Extracted IDs for ${paramKey}:`, processedParams[paramKey]);
                        } else {
                            // Keep other values as is
                            processedParams[paramKey] = value;
                        }
                    });
                    formattedData.params = JSON.stringify(processedParams);
                    console.log('Processed params for API:', formattedData.params);
                } else if (data.params && typeof data.params === 'object') {
                    // Process params field from the flattened data if it exists
                    const processedParams = {};
                    Object.keys(data.params).forEach(paramKey => {
                        const value = data.params[paramKey];
                        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].id !== undefined) {
                            // Extract just the IDs from the objects
                            processedParams[paramKey] = value.map(item => item.id);
                            console.log(`Extracted IDs for ${paramKey}:`, processedParams[paramKey]);
                        } else {
                            // Keep other values as is
                            processedParams[paramKey] = value;
                        }
                    });
                    formattedData.params = JSON.stringify(processedParams);
                    console.log('Processed params for API:', formattedData.params);
                } else if (typeof data.params === 'string') {
                    // If params is already a string, use it directly
                    formattedData.params = data.params;
                } else {
                    // Default to empty object
                    formattedData.params = JSON.stringify({});
                }
            } else {
                // Convert from FormProvider structure to flat object
                for (const key in data) {
                    if (data[key] && Array.isArray(data[key])) {
                        data[key].forEach(field => {
                            // Handle special cases
                            switch (field.name) {
                                case 'params':
                                    // Format params object for conditions
                                    if (field.value && typeof field.value === 'object') {
                                        // Process multi-select conditions to extract just the IDs
                                        const processedParams = {};
                                        Object.keys(field.value).forEach(paramKey => {
                                            const value = field.value[paramKey];
                                            if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].id !== undefined) {
                                                // Extract just the IDs from the objects
                                                processedParams[paramKey] = value.map(item => item.id);
                                                console.log(`Extracted IDs for ${paramKey}:`, processedParams[paramKey]);
                                            } else {
                                                // Keep other values as is
                                                processedParams[paramKey] = value;
                                            }
                                        });
                                        formattedData.params = JSON.stringify(processedParams);
                                        console.log('Processed params for API:', formattedData.params);
                                    } else {
                                        formattedData.params = field.value;
                                    }
                                    break;
                                default:
                                    // For all other fields, just copy the value
                                    if (field.name && field.checked !== false) {
                                        formattedData[field.name] = field.value;
                                    }
                                    break;
                            }
                        });
                    }
                }
            }
        }

        return formattedData;
    }, []);

    // Custom setActiveStep function that also tracks completed steps
    const setActiveStepWithTracking = useCallback((stepOrFn) => {
        setActiveStep(prevStep => {
            const newStep = typeof stepOrFn === 'function' ? stepOrFn(prevStep) : stepOrFn;

            // Update completed steps
            setCompletedSteps(prev => {
                if (!prev.includes(newStep)) {
                    return [...prev, newStep];
                }
                return prev;
            });

            return newStep;
        });
    }, []);

    // Update Redux store with form data as it's being filled out
    const updateReduxFormData = useCallback((formData) => {
        if (formData) {
            console.log('Updating Redux store with form data:', formData);
            dispatch(updateFormData(formData));
        }
    }, [dispatch]);

    // Handle form submission
    const handleSubmit = useCallback(async (formData) => {
        let mounted = true; // Add mounted flag
        console.log('Handling form submission with data:', formData);
        setLoading(true);
        try {
            const response = await (discountId ? updateDiscount : createDiscount)({
                ...formData,
                id: discountId || undefined
            });

            // Check if component is still mounted
            if (!mounted) return;

            if (!response?.errors) {
                setSuccess(true);
                // Force clear all refs and caches
                apiCallsMadeRef.current = false;
                dataProcessedRef.current = false;
                // Clear any cached API responses
                fetchDiscount.clearCache?.(); // If your useApi hook has this method
                // Reset Redux store
                resetForm();
                
                // Navigate after a brief delay
                const navigationTimeout = new Promise((resolve) => {
                    const timeoutId = setTimeout(() => {
                        if (mounted) {
                            // Force reload the discounts list to show updated data
                            navigate('/discounts', { replace: true });
                            // Optionally, you could add state to force a refresh:
                            // navigate('/discounts', { replace: true, state: { refresh: true } });
                        }
                        resolve();
                    }, 1500);

                    // Store the timeout ID for cleanup
                    return () => {
                        clearTimeout(timeoutId);
                        resolve();
                    };
                });

                await navigationTimeout;
            } else {
                if (mounted) {
                    handleErrors(response.errors);
                }
            }
        } catch (error) {
            if (mounted) {
                handleErrors(error);
            }
        } finally {
            if (mounted) {
                setLoading(false);
            }
        }

        return () => {
            mounted = false;
        };
    }, [discountId, resetForm, updateDiscount, createDiscount, handleErrors, navigate, fetchDiscount]);

    // Fetch data on component mount - use a ref to prevent multiple calls
    const apiCallsMadeRef = useRef(false);
    useEffect(() => {
        let mounted = true;

        // Only fetch if we haven't already and have an ID
        if (discountId && !apiCallsMadeRef.current) {
            console.log('Fetching fresh discount data for ID:', discountId);
            fetchDiscount().then(() => {
                if (!mounted) return;
                apiCallsMadeRef.current = true;
            });
        }

        return () => {
            mounted = false;
        };
    }, [discountId, fetchDiscount]);

    // Update Redux store when discount data changes
    const dataProcessedRef = useRef(false);
    useEffect(() => {
        let mounted = true;

        if (discountData && !dataProcessedRef.current && mounted) {
            // If we're editing an existing discount, mark all steps as completed
            if (discountId) {
                // Create an array with all step indices (0 to 8 for the 9 steps)
                const allSteps = Array.from({ length: 9 }, (_, i) => i);
                setCompletedSteps(allSteps);
            }
            try {
                console.log('Processing discount data:', discountData);

                // The API returns an object with a data property that contains an array with a single object
                // We need to extract that object for the form
                let discountInfo;

                // First check if data has a data property that is an array (as shown in the API response example)
                if (discountData.data && Array.isArray(discountData.data) && discountData.data.length > 0) {
                    // Extract the first/only item from the array
                    discountInfo = { ...discountData.data[0] };
                    console.log('Extracted discount info from array:', discountInfo);
                } else if (discountData.data && typeof discountData.data === 'object') {
                    // If it's not an array but an object, use it directly
                    discountInfo = { ...discountData.data };
                    console.log('Using discount info directly from object:', discountInfo);
                } else if (Array.isArray(discountData) && discountData.length > 0) {
                    // Fallback to direct array access if data property doesn't exist
                    discountInfo = { ...discountData[0] };
                    console.log('Extracted discount info from direct array:', discountInfo);
                } else if (discountData && typeof discountData === 'object') {
                    // Fallback to direct object access if data property doesn't exist
                    discountInfo = { ...discountData };
                    console.log('Using discount info directly:', discountInfo);
                } else {
                    // Fallback to empty object if data structure is unexpected
                    discountInfo = {};
                    console.error('Unexpected discount data structure:', discountData);
                }

                // Parse params if it's a string
                if (discountInfo.params && typeof discountInfo.params === 'string') {
                    try {
                        discountInfo.params = JSON.parse(discountInfo.params);
                        console.log('Parsed params:', discountInfo.params);
                    } catch (error) {
                        // Set to empty object to avoid errors
                        discountInfo.params = {};
                        console.error('Error parsing params:', error);
                    }
                } else if (!discountInfo.params) {
                    // Ensure params exists
                    discountInfo.params = {};
                }

                // Log all fields in discountInfo
                console.log('All fields in discountInfo:');
                Object.keys(discountInfo).forEach(key => {
                    console.log(`${key}:`, discountInfo[key]);
                });

                // Specifically log discount_amount
                console.log('discount_amount in discountInfo:', discountInfo.discount_amount);
                console.log('discount_amount type:', typeof discountInfo.discount_amount);

                // Store the discount info in Redux
                console.log('Dispatching discount info to Redux store:', discountInfo);

                // The formData should mirror the discountInfo object exactly
                // But we need to ensure consistent types for certain fields
                const initialFormData = { ...discountInfo };

                // Ensure max_uses is stored as a string in formData for consistency
                if (initialFormData.max_uses !== undefined) {
                    initialFormData.max_uses = initialFormData.max_uses.toString();
                }

                // If max_uses is 0, ensure unlimited is set to 1
                if (initialFormData.max_uses == 0) {
                    initialFormData.unlimited = 1;
                }

                // Log the discount_amount specifically for debugging
                console.log('discount_amount in initialFormData:', initialFormData.discount_amount);
                console.log('max_uses in initialFormData:', initialFormData.max_uses, 'type:', typeof initialFormData.max_uses);

                if (mounted) {
                    dispatch(setInfo({
                        id: discountId,
                        discountData: discountInfo,
                        formData: initialFormData
                    }));
                    dataProcessedRef.current = true;
                }
            } catch (error) {
                if (mounted) {
                    console.error('Error processing discount data:', error);
                    handleErrors(error.message || 'An error occurred while processing discount data');
                }
            }
        }

        return () => {
            mounted = false;
        };
    }, [discountId, discountData, dispatch, handleErrors]);



    // Check if we have form data in localStorage on mount
    useEffect(() => {
        try {
            const siteConfig = localStorage.getItem('_siteboss');
            if (siteConfig) {
                const parsedConfig = JSON.parse(siteConfig);
                const localStorageData = parsedConfig.discountWizardFormData;
                if (localStorageData && !discountWizardRef.current?.formData) {
                    dispatch(updateFormData(localStorageData));
                }
            }
        } catch (error) {
            console.error('Error loading form data from localStorage:', error);
        }
    }, [dispatch]);

    return {
        handleErrors,
        handleSubmit,
        setActiveStep: setActiveStepWithTracking,
        activeStep,
        loading: discountLoading || createLoading || updateLoading,
        success,
        setSuccess,
        hasErrors,
        errorBars: [DiscountErrorBar, CreateErrorBar, UpdateErrorBar, errorBar],
        discountId,
        discountData,
        resetForm,
        completedSteps,
    };
};
