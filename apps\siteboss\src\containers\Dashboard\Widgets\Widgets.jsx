import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Grid2, Typography, Card, CardActionArea, Box } from '@mui/material';
import {
    AnalyticsOutlined as AnalyticsIcon,
    PeopleOutlined as UsersIcon,
    LanguageOutlined as SitesIcon,
    ShowChartOutlined as SalesIcon,
    Inventory2Outlined as InventoryIcon,
    TaskOutlined as TasksIcon,
    CardMembershipOutlined as SubscriptionsIcon,
    NotificationsOutlined as NotificationsIcon,
    CalendarMonthOutlined as CalendarIcon,
    WbSunnyOutlined as WeatherIcon
} from '@mui/icons-material';

import { widgetList } from './widgetList';

// Helper function to get the appropriate icon for a widget
const getWidgetIcon = (slug, size = 'large') => {
    switch (slug) {
        case 'analytics':
            return <AnalyticsIcon fontSize={size} />;
        case 'users':
            return <UsersIcon fontSize={size} />;
        case 'sites':
            return <SitesIcon fontSize={size} />;
        case 'sales':
            return <SalesIcon fontSize={size} />;
        case 'inventory':
            return <InventoryIcon fontSize={size} />;
        case 'tasks':
            return <TasksIcon fontSize={size} />;
        case 'subscriptions':
            return <SubscriptionsIcon fontSize={size} />;
        case 'notifications':
            return <NotificationsIcon fontSize={size} />;
        case 'calendar':
            return <CalendarIcon fontSize={size} />;
        case 'weather':
            return <WeatherIcon fontSize={size} />;
        default:
            return <AnalyticsIcon fontSize={size} />;
    }
};

export const Widgets = ({onAddWidget, ...props}) => {
    const { t } = useOutletContext();

    return (
        <Grid2 container spacing={2}>
            {widgetList.map((item, i) => (
                <Grid2 xs={6} sm={4} md={3} key={`widget-add-preview-${item.id}-${i}`}>
                    <Card
                        variant="outlined"
                        sx={{
                            height: '100%',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                                borderColor: theme => theme.palette.primary.main,
                                boxShadow: theme => `0 0 8px ${theme.palette.primary.main}40`
                            }
                        }}
                    >
                        <CardActionArea
                            onClick={() => onAddWidget(item)}
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: 2
                            }}
                        >
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    color: theme => theme.palette.primary.main,
                                    mb: 1,
                                    width: 60,
                                    height: 60,
                                    borderRadius: '50%',
                                    border: `2px solid ${theme.palette.divider}`,
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                        borderColor: theme => theme.palette.primary.main,
                                        backgroundColor: theme => `${theme.palette.primary.main}10`
                                    }
                                }}
                            >
                                {getWidgetIcon(item.slug)}
                            </Box>
                            <Typography
                                variant="subtitle2"
                                align="center"
                                sx={{ mt: 1 }}
                            >
                                {t(`widget:${item.slug}.title`)}
                            </Typography>
                        </CardActionArea>
                    </Card>
                </Grid2>
            ))}
        </Grid2>
    );
}