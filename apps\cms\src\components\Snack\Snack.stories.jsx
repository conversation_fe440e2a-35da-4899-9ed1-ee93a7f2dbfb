import { Snack } from './Snack';
import { fn } from 'storybook/test'

export default{
    title: "CMS/Components/Snack",
    component: Snack,
    tags: ["autodocs"],
    argTypes:{
        onError: {
            description: "Function to be called when the snackbar is closed",
            control: { type: 'function' },
            action: fn(),
            type: { required: true },
            table: {
              type: { summary: "(error: any) => void" },
              defaultValue: { summary: "undefined" },
            }
          },
          message: {
            description: "The message to be displayed in the snackbar",
            control: { type: 'text' },
            type: { required: false },
            table: {
              type: { summary: "string" },
              defaultValue: { summary: "undefined" },
            }
          },
          duration: {
            description: "The duration (in milliseconds) the snackbar stays visible",
            control: { type: 'number' },
            type: { required: false },
            table: {
              type: { summary: "number" },
              defaultValue: { summary: "60000" },
            }
          },
          severity: {
            description: "The severity of the alert",
            control: { type: 'select', options: ['error', 'warning', 'info', 'success'] },
            type: { required: false },
            table: {
              type: { summary: "string" },
              defaultValue: { summary: "'error'" },
            }
          },
          absolute: {
            description: "Whether the snackbar should have absolute positioning",
            control: { type: 'boolean' },
            type: { required: false },
            table: {
              type: { summary: "boolean" },
              defaultValue: { summary: "false" },
            }
          },
          className: {
            description: "Additional CSS class for the snackbar",
            control: { type: 'text' },
            type: { required: false },
            table: {
              type: { summary: "string" },
              defaultValue: { summary: "undefined" },
            }
        },
    }
}
  
  // Playground story with minimal props
  export const Playground = {
    args: {
      onError: () => {},
    }
  };
  
  // Story with a message to show the snackbar
  export const WithMessage = {
    args: {
      ...Playground.args,
      message: "This is an error message",
    }
  };
  
  // Story with different severity
  export const InfoSeverity = {
    args: {
      ...WithMessage.args,
      severity: "info",
    }
  };
  
  // Story with custom duration
  export const CustomDuration = {
    args: {
      ...WithMessage.args,
      duration: 3000,
    }
  };
  
  // Story with absolute positioning
  export const AbsolutePosition = {
    args: {
      ...WithMessage.args,
      absolute: true,
    }
  };