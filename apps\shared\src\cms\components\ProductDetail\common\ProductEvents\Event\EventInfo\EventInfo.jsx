import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { format, isSameDay } from 'date-fns';
import { Stack, Typography, Alert } from '@mui/material';
import { InfoOutlined as InfoIcon, TodayOutlined as TodayIcon, MonetizationOnOutlined as PriceIcon, SentimentSatisfiedAltOutlined as AgeIcon, LocationOnOutlined as LocationIcon }  from '@mui/icons-material';
import { formatDate, formatDateTime, createCurrencyFormatter, eventFlatten } from '../../../../../../../utils';

export const EventInfo = ({ event, events, slotProps, ...props }) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    // if the events is sent without an event object, we will try to get the first event from the events array
    if (events && !event) {
        const flatEvents = eventFlatten(events);
        event = flatEvents?.metaEvents?.[0] || flatEvents?.events?.[0];
    }

    const sameDay = isSameDay(event?.startDate, event?.endDate);

    const lines = [
        {
            caption: t(sameDay ? 'general:date' : 'calendar:startDate'),
            value: sameDay ? `${formatDate(event?.startDate, language.code, "extended")} <br/>${format(event.startDate, "h:mmaa")} - ${format(event.endDate, "h:mmaa")}` :  formatDateTime(event.startDate, language.code),
            icon: <TodayIcon />,
        },
        {
            caption: t(`calendar:endDate`),
            value: sameDay ? null : formatDateTime(event.endDate, language.code),
            icon: <TodayIcon />,
        },
        {
            caption: t(`general:location`),
            value: event.metadata?.location?.name || null,
            icon: <LocationIcon />,
        },
        {
            caption: t(`calendar:ageRequirement`),
            value: (event.metadata.min_age || event.metadata.max_age) 
                ? (event.metadata.min_age > 0 && !event.metadata.max_age ? `${event.metadata.min_age} ${t('calendar:yearsOld')} ${t('calendar:andUp')}` : '') +
                    (!event.metadata.min_age && event.metadata.max_age > 0 ? `${t('calendar:upTo')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '') + 
                    (event.metadata.min_age > 0 && event.metadata.max_age > 0 ? `${event.metadata.min_age} ${t('calendar:to')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '')
                : null,
            icon: <AgeIcon />,
        },
        {
            caption: t(`calendar:eventFee`),
            value: event.metadata.product_price > 0 ? currencyFormatter.format(event.metadata.product_price) : null,
            icon: <PriceIcon />,
        },
        {
            caption: t(`general:additionalInfo`),
            value: !event.metadata?.requires_registration && !event.metadata?.children?.length ? t(`calendar:noRegistration`) : null,
            icon: <InfoIcon />,
        },
    ];

    if (!event) return null;
    return (
        <Stack direction="row" flexWrap="wrap" useFlexGap spacing={0}>
            {lines.filter(a=>a.value)?.map((line, i) => (
                <Alert key={i} variant="outlined" color="inherit" icon={line.icon || <InfoIcon/>} sx={{width: 250}}>
                    <Typography variant="caption" sx={{...(slotProps?.caption || {})}}>
                        {line.caption}
                    </Typography>
                    <Typography 
                        variant="body2"
                        component="div" 
                        sx={{...(slotProps?.text || {})}} 
                        dangerouslySetInnerHTML={{__html: line.value}} 
                    />
                </Alert>
            ))}
        </Stack>
    );
};