// Generates a random paragraph with a specified number of sentences
// generateParagraph(5);


// Subjects
const singularSubjects = [
    "The cat", "A student", "My neighbor", "An engineer", "A musician", "The scientist", "The artist", "A programmer", 
    "The detective", "The chef", "A traveler", "The writer", "The doctor", "A photographer", "The explorer", "The researcher", 
    "A professor", "The entrepreneur", "The architect", "A designer", "The astronaut", "The archaeologist", "A philosopher", 
    "The mathematician", "The biologist", "A physicist", "The inventor", "The analyst", "A developer", "The pioneer", "The baker", 
    "A sous chef", "The pastry chef", "The food critic", "A culinary student", "The master chef", "The bookworm", "A librarian", 
    "The storyteller", "The book reviewer", "A literature professor", "The gamer", "The athlete", "A chess player", "The coach", 
    "The team captain", "A mentor", "The tutor", "The study group leader", "A research assistant", "The carpenter", "A craftsman", 
    "The woodworker", "The DIY enthusiast", "A maker", "The hobbyist", "The toy maker", "A puzzle solver", "The party planner", 
    "The entertainer", "The artisan", "The barista", "The mixologist", "The vinyl collector", "The urban farmer", 
    "The coffee roaster", "The food blogger", "The craft brewer", "The vintage curator", "The indie developer", 
    "The kombucha brewer", "The typewriter enthusiast", "The record collector", "The sustainable chef", 
    "The minimalist designer", "The organic farmer", "The zero-waste advocate", "The vintage photographer", "The macrame artist", 
    "The terrarium designer", "The dog", "A pet", "A baldie", "The dreamer", "An alien", 
];
const pluralSubjects = [
    "The cats", "Several students", "My neighbors", "Those engineers", "The musicians", "The scientists", "The artists", 
    "The programmers", "The detectives", "The chefs", "Many travelers", "The writers", "The doctors", "The photographers", 
    "The explorers", "The researchers", "The professors", "The entrepreneurs", "The architects", "The designers", 
    "The astronauts", "The archaeologists", "The philosophers", "The mathematicians", "The biologists", "The physicists", 
    "The inventors", "The analysts", "The developers", "The pioneers", "The bakers", "The sous chefs", "The pastry chefs", 
    "The food critics", "The culinary students", "The master chefs", "The bookworms", "The librarians", "The storytellers", 
    "The book reviewers", "The literature professors", "The gamers", "The athletes", "The chess players", "The coaches", 
    "The team captains", "The mentors", "The tutors", "The study group leaders", "The research assistants", "The carpenters", 
    "The craftsmen", "The woodworkers", "The DIY enthusiasts", "The makers", "The hobbyists", "The toy makers", 
    "The puzzle solvers", "The party planners", "The entertainers", "The artisans", "The baristas", "The mixologists", 
    "The vinyl collectors", "The urban farmers", "The coffee roasters", "The food bloggers", "The craft brewers", 
    "The vintage curators", "The indie developers", "The kombucha brewers", "The typewriter enthusiasts", 
    "The record collectors", "The sustainable chefs", "The minimalist designers", "The organic farmers", 
    "The zero-waste advocates", "The vintage photographers", "The macrame artists", "The terrarium designers", "The dogs",
    "The pets", "The baldies", "The dreamers", "The aliens",
];

// Verbs (3rd-person singular vs. plural/base form)
const singularVerbs = [ 
    "admires", "investigates", "discovers", "builds", "studies", "observes", "creates", "explores", "examines", "develops", 
    "designs", "analyzes", "presents", "transforms", "implements", "researches", "contemplates", "innovates", "hypothesizes", 
    "synthesizes", "validates", "optimizes", "enhances", "revolutionizes", "visualizes", "integrates", "orchestrates", "pioneers", 
    "accelerates", "cultivates", "formulates", "generates", "iterates", "navigates", "bakes", "cooks", "seasons", "tastes", 
    "garnishes", "reads", "writes", "bookmarks", "annotates", "reviews", "plays", "competes", "practices", "trains", "exercises", 
    "learns", "teaches", "tutors", "mentors", "crafts", "constructs", "assembles", "creates", "designs", 
    "decorates", "celebrates", "entertains", "enjoys",
];
const pluralVerbs = [ 
    "admire", "investigate", "discover", "build", "study", "observe", "create", "explore", "examine", "develop", "design", 
    "analyze", "present", "transform", "implement", "research", "contemplate", "innovate", "hypothesize", "synthesize", 
    "validate", "optimize", "enhance", "revolutionize", "visualize", "integrate", "orchestrate", "pioneer", "accelerate", 
    "cultivate", "formulate", "generate", "iterate", "navigate", "bake", "cook", "season", "taste", "garnish", "read", "write", 
    "bookmark", "annotate", "review", "play", "compete", "practice", "train", "exercise", "learn", "teach", "tutor", 
    "mentor", "craft", "construct", "assemble", "create", "design", "decorate", "celebrate", "entertain", "enjoy",
];

// Possible objects
const objects = [ 
    "a mysterious artifact", "the ancient library", "a new technology", "the elaborate design", "the hidden garden", 
    "a groundbreaking solution", "the quantum computer", "a sustainable system", "the digital landscape", "the innovative approach", 
    "a revolutionary concept", "the artificial intelligence", "the renewable energy source", "a virtual reality world", 
    "the blockchain network", "the neural network", "a machine learning model", "the autonomous vehicle", "the quantum algorithm", 
    "a parallel universe", "the genetic code", "a sustainable ecosystem", "the cosmic phenomenon", "a mathematical proof", 
    "the biological system", "a quantum entanglement", "the neural interface", "a holographic display", "the molecular structure", 
    "a quantum sensor", "the cybernetic system", "an algorithmic pattern", "the quantum encryption", "a neural synthesis", 
    "a delicious meal", "the perfect recipe", "a gourmet dish", "the secret ingredient", "a cooking technique", 
    "the favorite cookbook", "an engaging novel", "the classic story", "a fascinating chapter", "the book collection", 
    "a board game", "the video game console", "a challenging puzzle", "the sports equipment", "a team strategy", 
    "the study materials", "a research paper", "the homework assignment", "a learning plan", "the study guide", 
    "a wooden masterpiece", "the DIY project", "a handmade creation", "the building blocks", "a toy collection", 
    "the craft supplies", "a party decoration", "the fun activity", "a group game", "the entertainment system", "artisanal coffee", 
    "vintage typewriter", "vinyl record", "craft beer", "sourdough starter", "fixed-gear bicycle", "succulent garden", 
    "kombucha brew", "mason jar collection", "macrame hanging", "cold brew setup", "polaroid camera", "thrifted flannel", 
    "minimalist workspace", "sustainable ecosystem", "quinoa bowl", "bespoke furniture", "microbrew selection", "organic latte", 
    "retro turntable", "vintage polaroid", "artisanal scrunchie", "matcha latte", "terrarium garden", "wire-wrapped crystal", 
    "avocado toast", "sourdough loaf", "kimchi ferment", "indie zine", "fixed-gear bike", "vintage cassette", "upcycled planter", 
    "ethical coffee", "vegan leather", "sustainable bamboo", "organic cotton", "recycled denim", "vintage vinyl", 
    "handcrafted pottery", "local honey", "fair-trade chocolate", "zero-waste kit", "sustainable packaging", "vintage film camera", 
    "handbound journal",
];

// Optional adjectives or adverbs to add variety
const adjectives = [
    "remarkable", "fascinating", "complex", "impressive", "innovative", "groundbreaking", "sophisticated", "revolutionary", 
    "cutting-edge", "sustainable", "efficient", "advanced", "intelligent", "dynamic", "versatile", "robust", "scalable", "intuitive", 
    "quantum", "exponential", "algorithmic", "autonomous", "neural", "synthetic", "holographic", "cybernetic", "biometric", 
    "quantum-entangled", "self-evolving", "biomechanical", "hyper-connected", "nano-scale", "cognitive", "adaptive", "delicious", 
    "tasty", "savory", "sweet", "spicy", "engaging", "fascinating", "captivating", "entertaining", "educational", "playful", 
    "competitive", "exciting", "energetic", "strategic", "studious", "focused", "dedicated", "hardworking", "motivated", "creative", 
    "skillful", "handcrafted", "homemade", "artistic", "enjoyable", "festive", "amusing", "recreational", "entertaining", 
    "artisanal", "sustainable", "organic", "vintage", "locally-sourced", "craft", "minimalist", "authentic", "bespoke", "upcycled", 
    "small-batch", "ethically-made", "hand-crafted", "farm-to-table", "eco-conscious", "fair-trade", "zero-waste", "biodegradable", 
    "vegan", "indie", "retro", "analog", "handbound", "slow-fashion", "micro-roasted", "single-origin", "cold-pressed", 
    "naturally-dyed", "recycled", "thrifted", "curated", "conscious", "mindful", "sustainable", "ethical", "artisanal", "handmade", 
    "local", "political", "social", "environmental", 
];

const adverbs = [
    "quickly", "carefully", "quietly", "boldly", "occasionally", "efficiently", "systematically", "thoroughly", "precisely", 
    "effectively", "continuously", "gradually", "consistently", "innovatively", "strategically", "seamlessly", "reliably", 
    "intelligently", "exponentially", "quantum-mechanically", "algorithmically", "autonomously", "synthetically", "holographically", 
    "cybernetically", "biomechanically", "adaptively", "recursively", "dynamically", "iteratively", "predictively", "heuristically", 
    "probabilistically", "deterministically", "deliciously", "skillfully", "carefully", "enthusiastically", "patiently", 
    "attentively", "eagerly", "passionately", "playfully", "energetically", "competitively", "strategically", "diligently", 
    "studiously", "thoroughly", "creatively", "artistically", "expertly", "joyfully", "happily", "methodically", "precisely", 
    "carefully", "imaginatively", "cheerfully", "excitedly", "entertainingly", "amusingly", "pleasantly", "enjoyably", 
    "festively", "unapologetically", "authentically", "sustainably", "ethically", "responsibly", "mindfully", "delightfully", 
    "deliciously", "tastefully", "savoryly", "sweetly", "spicily", "engagingly", "fascinatingly", "captivatingly", 
    "fearlessly", "unconventionally", "fearfully",
];

// Prepositional phrases
const prepositions = [
    "in the city", "with great skill", "under the bright sun", "beyond the horizon", "during the festival", "in the digital age", 
    "with advanced technology", "through innovative methods", "across multiple platforms", "within the ecosystem", 
    "among industry leaders", "throughout the process", "behind the scenes", "despite the challenges", "alongside the team", 
    "through machine learning", "in real-time", "at the cutting edge", "in the kitchen", "at the dining table", 
    "with fresh ingredients", "in the library", "at the bookstore", "with great enthusiasm", "on the playing field", 
    "at the game table", "with team spirit", "in the study room", "at the desk", "with full concentration", "in the workshop", 
    "at the crafting table", "with careful attention", "during the party", "at the celebration", "with friends and family", 
    "in the playroom", "at the entertainment center", "in the urban garden", "at the indie cafe", "in the vinyl shop", 
    "at the craft brewery", "in the co-working space", "at the farmers market", "in the vintage store", "at the pop-up shop", 
    "in the art collective", "at the food co-op", "in the zero-waste store", "at the record shop", "in the plant nursery", 
    "at the kombucha bar", "in the thrift store", "at the artisan market", "in the maker space", "at the sustainable cafe", 
    "in the organic farm", "at the flea market", "in the indie bookstore", "at the craft fair", "in the vintage boutique",
    "at the artisanal brewery", "in the eco-conscious store", "at the local farmers market", "in the sustainable living center",
    "in the office", "at the conference", "in the lab", "at the meeting", "in the factory", "at the construction site", 
    "in the warehouse", "at the distribution center", "in the supply chain", "at the logistics center", "in the shipping dock", 
    "at the retail store", "in the customer service department", "at the call center", "in the marketing team", "at the sales floor", 
    "in the customer support team", "at the product development department", "in deep space", "at the edge of the universe", 
    "in the heart of the galaxy", "on the surface of Mars", "in the depths of the ocean", "at the summit of Mount Everest", 
    "in the heart of the Amazon rainforest", "in the Sahara Desert", "in the Arctic Circle", "in the heart of the city", 
    "in the countryside", "in the suburbs", "with a team of experts", "with cutting-edge technology", "in the digital realm", 
    "in the physical world", "in the virtual world", "in the metaverse", "in the future", "in the past", "in the present", 
    "in the morning", "in the afternoon", "in the evening", "in the night", "in the early hours", "in the late hours", 
];

// Add new conjunctions for compound sentences
const conjunctions = [
    "and", "but", "while", "although", "because", "when", "if", "unless", "since", "until", "as", "though", "even though", "whereas",
];

// Pronouns
const pronouns = {
  singular: {
    masculine: ["he", "him", "his"],
    feminine: ["she", "her", "her"],
    neutral: ["it", "its", "its"]
  },
  plural: ["they", "them", "their"]
};

// Continuation phrases
const continuationPhrases = [
  "In response",
  "As a result",
  "Subsequently",
  "Following this",
  "Consequently",
  "Meanwhile",
  "Additionally",
  "Furthermore",
  "Moreover",
  "Building on this",
  "With this in mind"
];

// Add more variety in sentence structures
const transitionPhrases = [
  "For instance,",
  "To illustrate,",
  "Specifically,",
  "In particular,",
  "Notably,",
  "Interestingly,",
  "Remarkably,",
  "Surprisingly,"
];

const timeTransitions = [
  "Eventually,",
  "Later,",
  "Soon after,",
  "Moments later,",
  "Shortly thereafter,",
  "After some time,",
  "Within minutes,"
];

// Add contrast transitions
const contrastTransitions = [
  "However,",
  "In contrast,",
  "On the other hand,",
  "Conversely,",
  "Nevertheless,",
  "Despite this,",
  "Yet,"
];

// --- 2. Helpers to pick random array elements ---  
const randItem = array => array[Math.floor(Math.random() * array.length)];

// Capitalizes first letter of a string
const capitalize = str => str.charAt(0).toUpperCase() + str.slice(1);

// Helper function to get pronouns for a subject
const getPronouns = (subject) => {
  if (subject.toLowerCase().includes("she") || subject.toLowerCase().includes("her")) {
    return pronouns.singular.feminine;
  } else if (subject.toLowerCase().includes("he") || subject.toLowerCase().includes("him")) {
    return pronouns.singular.masculine;
  } else if (pluralSubjects.includes(subject)) {
    return pronouns.plural;
  } else if (subject.toLowerCase().includes("it")) {
    return pronouns.singular.neutral;  // Now using neutral pronouns
  }
  return pronouns.singular.neutral;  // Default to neutral for unspecified
};

// --- 3. Build a sentence with correct subject-verb agreement and optional add-ons ---
  
const generateSentence = () => {
  const isSingular = Math.random() < 0.5;
  const subject = isSingular ? randItem(singularSubjects) : randItem(pluralSubjects);
  const verb = isSingular ? randItem(singularVerbs) : randItem(pluralVerbs);
  const object = randItem(objects);
  
  // Optional expansions
  const useAdjective = Math.random() < 0.4;   // 40% chance
  const useAdverb = Math.random() < 0.4;      // 40% chance
  const usePreposition = Math.random() < 0.4;  // 40% chance
  
  let sentenceCore = `${subject} ${verb}`;
  
  // Only modify the object handling to prevent article issues
  if (useAdjective) {
    const adj = randItem(adjectives);
    // Check if object already has an article
    if (object.startsWith('a ') || object.startsWith('the ')) {
      const [article, ...rest] = object.split(' ');
      sentenceCore += ` ${article} ${adj} ${rest.join(' ')}`;
    } else {
      sentenceCore += ` a ${adj} ${object}`;
    }
  } else {
    sentenceCore += ` ${object}`;
  }
  
  if (useAdverb) {
    sentenceCore += ` ${randItem(adverbs)}`;
  }
  
  if (usePreposition) {
    sentenceCore += ` ${randItem(prepositions)}`;
  }
  
  return capitalize(sentenceCore.trim()) + ".";
};
  
  // --- 4. Generate a paragraph of multiple sentences ---
  
  const generateSimpleSentence = () => {
    const isSingular = Math.random() < 0.5;
    const subject = isSingular ? randItem(singularSubjects) : randItem(pluralSubjects);
    const verb = isSingular ? randItem(singularVerbs) : randItem(pluralVerbs);
    const object = randItem(objects);
    
    return `${subject} ${verb} ${object}`;
  };

  const generateMediumSentence = () => {
    // This is the current generateSentence function
    return generateSentence();
  };

  const generateComplexSentence = () => {
    const complexity = Math.random();
    
    if (complexity < 0.4) {
      // Current complex sentence with two clauses
      const firstClause = generateSentence().slice(0, -1);
      const conjunction = randItem(conjunctions);
      const secondClause = generateSentence().toLowerCase();
      const needsComma = ["but", "although", "while", "unless", "and", "therefore", "moreover", "furthermore"].includes(conjunction);
      return `${firstClause}${needsComma ? "," : ""} ${conjunction} ${secondClause}`;
    } else if (complexity < 0.7) {
      // Add transition-based complex sentence
      const transition = randItem([...transitionPhrases, ...timeTransitions, ...contrastTransitions]);
      const mainClause = generateSentence().slice(0, -1);
      const secondClause = generateSentence().toLowerCase();
      return `${mainClause}. ${transition} ${secondClause}`;
    } else {
      // Add relative clause
      const mainClause = generateSentence().slice(0, -1);
      const relativePronouns = ["which", "that", "who", "whom", "whose"];
      const relativePronoun = randItem(relativePronouns);
      const verb = randItem(pluralVerbs);
      const object = randItem(objects);
      return `${mainClause}, ${relativePronoun} ${verb} ${object}.`;
    }
  };

  const generateVeryComplexSentence = () => {
    const mainClause = generateSentence().slice(0, -1);
    const conjunction1 = randItem(conjunctions);
    const secondClause = generateMediumSentence().toLowerCase().slice(0, -1);
    const conjunction2 = randItem(conjunctions);
    const thirdClause = generateSimpleSentence().toLowerCase();
    
    // Add commas before appropriate conjunctions
    const needsComma1 = ["but", "although", "while", "unless", "and", "therefore", "moreover", "furthermore"].includes(conjunction1);
    const needsComma2 = ["but", "although", "while", "unless", "and", "therefore", "moreover", "furthermore"].includes(conjunction2);
    
    return `${mainClause}${needsComma1 ? "," : ""} ${conjunction1} ${secondClause}${needsComma2 ? "," : ""} ${conjunction2} ${thirdClause}.`;
  };

  // Enhanced paragraph generator with varying complexity
  export const generateParagraph = (sentenceCount = 5) => {
    const sentences = [];
    let previousSubject = null;
    let previousVerb = null;
    let wasPlural = false;
    let usedTransition = false;
    
    for (let i = 0; i < sentenceCount; i++) {
      const complexity = Math.random();
      let sentence;

      // Avoid too many consecutive transitions
      if (i > 0 && !usedTransition && Math.random() < 0.4) {
        // Now using transitionPhrases directly instead of just spreading
        const transition = Math.random() < 0.5 ? 
          randItem(transitionPhrases) : 
          randItem([...contrastTransitions, ...timeTransitions]);
        const newSentence = generateMediumSentence().toLowerCase();
        sentence = `${transition} ${newSentence}`;
        usedTransition = true;
      } else {
        usedTransition = false;
        
        if (i > 0 && Math.random() < 0.5 && previousSubject) {
          const [subjPronoun, objPronoun, possPronoun] = getPronouns(previousSubject);
          wasPlural = pluralSubjects.includes(previousSubject) || subjPronoun === "they";
          
          // Now using objPronoun in sentence construction
          const continuationType = Math.random();
          if (continuationType < 0.33) {
            const verb = wasPlural ? randItem(pluralVerbs) : randItem(singularVerbs);
            sentence = `${capitalize(subjPronoun)} ${verb} ${randItem(objects)} with ${objPronoun}.`;
          } else if (continuationType < 0.66) {
            const verb = wasPlural ? randItem(pluralVerbs) : randItem(singularVerbs);
            sentence = `${randItem(continuationPhrases)}, someone ${verb} ${objPronoun} ${randItem(objects)}.`;
          } else {
            const verb = wasPlural ? randItem(pluralVerbs) : randItem(singularVerbs);
            sentence = `${capitalize(possPronoun)} ${previousVerb.toLowerCase()}, which ${verb} ${objPronoun} of ${randItem(objects)}.`;
          }
        } else {
          // Vary sentence complexity based on position in paragraph
          if (i === 0) {
            // Start with medium complexity
            sentence = generateMediumSentence();
          } else if (i === sentenceCount - 1) {
            // End with either simple or medium sentence
            sentence = Math.random() < 0.5 ? generateSimpleSentence() + "." : generateMediumSentence();
          } else {
            // Mix complexity in the middle
            if (complexity < 0.2) {
              sentence = capitalize(generateSimpleSentence()) + ".";
            } else if (complexity < 0.5) {
              sentence = generateMediumSentence();
            } else if (complexity < 0.8) {
              sentence = generateComplexSentence();
            } else {
              sentence = generateVeryComplexSentence();
            }
          }
        }
      }

      // Update previous subject and verb
      const matches = sentence.match(/^(.*?)\s(.*?)\s/);
      if (matches) {
        previousSubject = matches[1];
        previousVerb = matches[2];
        wasPlural = pluralSubjects.includes(previousSubject);
      }

      // remove double spaces, double commas, and double periods
      sentence = sentence.replace(/(\s|,|\.){2,}/g, "$1");
      
      sentences.push(sentence);
    }
    
    return sentences.join(" ");
};


export const generateTitle = (words = 1) => {
    const title = [];
    for (let i = 0; i < words; i++) {
        title.push(randItem(singularSubjects));
    }
    return title.join(" ");
}

export const generateSubTitle = (words = 1) => {
    const subTitle = [];
    for (let i = 0; i < words; i++) {
        subTitle.push(capitalize(randItem(prepositions)));
    }
    return subTitle.join(" ");
}