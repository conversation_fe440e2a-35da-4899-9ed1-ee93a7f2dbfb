import { 
    EventOutlined as EventIcon, 
    CalendarViewMonthOutlined as MonthIcon, 
    CalendarViewWeekOutlined as WeekIcon, 
    CalendarViewDayOutlined as DayIcon
} from '@mui/icons-material';

export const layouts = [
    {
        id: 1,
        name: 'Schedule',
        icon: <EventIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
    {
        id: 2,
        name: 'Month',
        icon: <MonthIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
    {
        id: 3,
        name: 'Week',
        icon: <WeekIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
    {
        id: 4,
        name: 'Day',
        icon: <DayIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
];

export const widgetIcon = () =>layouts[0].icon;