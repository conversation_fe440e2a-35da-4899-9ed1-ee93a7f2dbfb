import React, { useCallback, useState } from 'react';
import { TextField, InputAdornment, CircularProgress } from '@mui/material';
import { YouTube as YouTubeIcon, PlayCircleOutlineOutlined as VideoLibraryIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { ErrorBar } from "../../Snacks";

const isValidVideoURL = (url) => {
    const videoPattern = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|vimeo\.com)\/.+$/;
    return videoPattern.test(url);
};

const VideoIcon = value => {
    if (value.includes('youtube.com') || value.includes('youtu.be')) {
        return <YouTubeIcon />;
    }
    return <VideoLibraryIcon />;
};

export const VideoURL = ({
    label,
    name,
    value,
    onChange,
    onBlur,
    error: initialError,
    helperText,
    disabled,
    loading,
    variant,
    ...props
}) => {
    const { t } = useTranslation();

    const [error, setError] = useState(initialError);

    const handleChange = useCallback(event => {
        const newValue = event.target.value;
        onChange({ target: { name, value: newValue } });
        if (isValidVideoURL(newValue) || newValue === '') {
            setError(null);
        } else {
            setError(t('error:invalid'));
        }
    }, [name, onChange, t]);

    return (
        <TextField
            name={name || undefined}
            fullWidth={props.fullWidth === false ? false : true}
            label={label || undefined}
            error={!!error}
            variant={variant || "outlined"}
            placeholder={props.placeholder || undefined}
            value={value}
            disabled={disabled || loading}
            onChange={handleChange}
            onBlur={onBlur}
            helperText={helperText || error || undefined}
            margin={props.margin || undefined}
            sx={props.sx || {}}
            {...props}
            slotProps={{...props?.slotProps, input: { ...props?.slotProps?.input,
                startAdornment: (
                    <InputAdornment position="start">
                        {VideoIcon(value)}
                    </InputAdornment>
                ),
                endAdornment: loading ? (
                    <InputAdornment position="end">
                        <CircularProgress size={20} />
                    </InputAdornment>
                ) : null,
            }}}
        />
    );
};

export default VideoURL;
