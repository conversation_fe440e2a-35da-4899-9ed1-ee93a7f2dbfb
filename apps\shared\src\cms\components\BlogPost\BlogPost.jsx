import React from 'react';
import { Stack, Typography } from '@mui/material';
import { Title, MarkDownPreview, LoadingBar, Gallery } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';

import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';
import { useBlogPost } from './useBlogPost';

export const BlogPost = ({
    id,
    postId,
    postType,
    /*title,
    content,
    publishDate,
    authors,
    images,*/
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        title: {},          // MUI typography props
        content: {},        // MUI typography props
        images: {},          // Image wrapper props
        authors: {},        // MUI typography props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    websiteId,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, customCss, canRender, noContent, t, contentWindow } = prepareComponent({
        name: "blog-post", 
        layoutId, 
        layouts, 
        slotProps, 
        isBuilder, 
        condition, 
        widgetIcon
    });
    slotProps = updatedSlotProps;

    const { post, loading, ErrorBar } = useBlogPost({ isBuilder, postId, postType, websiteId });

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            {(!post?.title && !post?.content && !loading) ? noContent : 
                <Stack {...slotProps?.cmsStack}>
                    <ErrorBar />
                    {loading && <LoadingBar color="inherit" />}
                    {post &&
                        <>
                            {post.images && (
                                <Gallery 
                                    images={post.images.map(image => ({preview_url: image, description: null}))} 
                                    disabled={isBuilder} 
                                    contentWindow={contentWindow} 
                                    type="carousel"
                                    size={{xs: 1}}
                                    {...slotProps?.images} 
                                />
                            )}
                            <Title 
                                title={post.title} 
                                subtitle={post.publish_date} 
                                //breadcrumbs={[{title: "Home", to: "/"}, {title: "Blog", to: "/blog"}]}
                                {...slotProps?.title}
                            />
                            {post?.authors &&
                                <Typography variant="subtitle2" {...slotProps?.authors}>
                                    {t("general:by")}: {post.authors.join(', ')}
                                </Typography>
                            }
                            {post?.content && 
                                <MarkDownPreview value={post.content} />
                            }
                        </>
                    }
                </Stack>
            }
            {children}
        </CmsContainer>
    );
};