import React, { useRef, useState, useCallback, useEffect, useContext, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { isBefore } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { Container, Stack, Paper, Box, Button, Grid2, Typography, Chip, useMediaQuery, Collapse, Grow } from '@mui/material';

import { Gallery, Modal, WithScrollEffect } from '../../../../../components';
import Lines from '../common/Lines';
import Line from '../../../common/pos/Line';
import { Default } from '../../../ProductDetail/Layouts/Default';
import { PosProductDetailProvider, PosContext } from '../../../../hooks';

import styles from './Extended.module.scss';

export const Extended = ({ 
    event, 
    slotProps, 
    isBuilder,
    contentWindow,
    onUserSelect,
    onAddToCart,
    selectedUser,
    cartRoute,
    cartItemId,
    ...props
 }) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const navigate = useNavigate();

    const { LoginForm } = useContext(PosContext) || {};

    const user = useSelector(state => state.user);
    const headerRef = useRef(null);
    const ctaRef = useRef(null);
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [headerHeight, setHeaderHeight] = useState(300);

    const handleToggleDrawer = useCallback(_ => {
        setDrawerOpen(prev => !prev);
    }, []);

    const handleAddToCart = useCallback((product, reduxState) => {
        if (onAddToCart) onAddToCart(product, reduxState);
        setDrawerOpen(false);
    }, [onAddToCart, navigate, cartRoute]);

    const disableSignUp = useMemo(() => {
        return !isBefore(new Date(), event.endDate);
    }, [event.endDate]);

    useEffect(() => {
        if (cartItemId && !disableSignUp) setDrawerOpen(true);
    }, [cartItemId, disableSignUp]);

    useEffect(() => {
        if (headerRef?.current?.offsetHeight) setHeaderHeight(headerRef?.current?.offsetHeight);
    }, [headerRef?.current?.offsetHeight]);

    return (
        <Container disableGutters maxWidth={false} sx={{ flexGrow: 1, position: 'relative' }} className={isMobile ? styles.mobile : undefined}>
            <Box ref={headerRef}>
                {event.metadata?.images?.length > 0 &&
                    <Gallery 
                        images={event.metadata.images} 
                        disabled={isBuilder} 
                        type="carousel"
                        size={{xs: 1}}
                        imageSize={isMobile ? undefined : {width: '100%', height: 300}}
                        contentWindow={contentWindow}
                        {...slotProps?.images} 
                    />
                }
            </Box>
            <Grid2 container spacing={2} className={styles["wrapper"]} sx={{ mt: event.metadata?.images?.length > 0 && !isMobile ? -5 : undefined }}>
                {isMobile &&
                    <Grid2 size={{xs: 12}}>
                        <Typography variant={isMobile ? "h3" : "h1"} className={styles.title} {...slotProps?.title}>{event.title}</Typography>
                    </Grid2>
                }
                <Grid2 size={{xs: 12, md: "grow"}} className={styles["content-wrapper"]}>
                    <Box sx={{px: 2}}>
                        {!isMobile && 
                            <>
                                <Typography variant={isMobile ? "h3" : "h1"} className={styles.title} {...slotProps?.title}>{event.title}</Typography>
                                <Line text={event.metadata.short_description} slotProps={slotProps} />
                            </>
                        }
                        <Typography variant='body1' component='div' className={styles.description} dangerouslySetInnerHTML={{__html: event.metadata.description}} {...slotProps?.text}/>
                    </Box>
                </Grid2>
                <Grid2 size={{xs: 12, md: "auto"}} ref={ctaRef} className={styles["cta-wrapper"]}>
                    <Paper 
                        elevation={isMobile ? 0 : 2} 
                        square={isMobile} 
                        sx={{ p: isMobile ? undefined : 2, height: 'fit-content', width: theme => isMobile ? undefined : `calc( 1.5 * ${theme.sizes.menuWidth})` }} 
                        {...slotProps?.ctaContainer}
                    >
                        {!isMobile &&
                            <WithScrollEffect targetElement={ctaRef.current} threshold={headerHeight} effect = {{in: true}}>
                                <Collapse in={false}>
                                    <Typography variant={"bold"} component="div" sx={{my: 2}}>{event.title}</Typography>
                                </Collapse>
                            </WithScrollEffect>
                        }
                        <Lines event={event} slotProps={slotProps} />
                        {event.metadata?.requires_registration === 1 &&
                            <Box className={styles.box} sx={{backgroundColor: 'background.paper', borderColor: 'divider'}}>
                                <Button variant='contained' color='primary' size="xl" fullWidth {...slotProps?.button} disabled={disableSignUp} onClick={handleToggleDrawer}>
                                    {t('calendar:signUp')}
                                </Button>
                            </Box>
                        }
                        {disableSignUp && <Line text={t('event:eventNotAvailable')} color="error" sx={{mt: 1, mb: 0}} slotProps={slotProps} />}
                    </Paper>
                    <Stack direction='row' spacing={1/2}  sx={{maxWidth: '100%', mt: 2}} flexWrap='wrap' useFlexGap>
                        {event.metadata?.tags?.map(cat => <Grow key={cat.id} in><Chip label={cat.name} /></Grow>)}
                    </Stack>
                    <Line text={event.metadata?.registration_msg} sx={{mt: 1, mb: 0}} slotProps={slotProps} />
                </Grid2>
            </Grid2>

            <Modal
                open={drawerOpen}
                onClose={handleToggleDrawer}
                maxWidth={slotProps.modalSize || "sm"}
                fullScreen={isMobile}
                aria-describedby={t(`product:productDetails`)}
            >
                {selectedUser ?
                    <PosProductDetailProvider 
                        productId={event?.metadata?.product_id} 
                        variantId={event?.metadata?.variants?.[0]?.id} 
                        cartRoute={cartRoute} 
                        modalSize="sm"
                        cartItemId={cartItemId}
                    >
                        <Default 
                            slotProps={{
                                ...slotProps,
                                gallery: {
                                    type: 'carousel', // carousel, list, masonry, 
                                    hide: true,
                                },
                                variants: {
                                    layoutType: 'radio', // radio, button
                                },
                                addons: {
                                    layoutType: 'checkbox', // radio, checkbox, button
                                },
                                events: {
                                    hide: true,
                                },
                                eventUsers: {
                                    layoutType: 'checkbox', // button, radio, checkbox
                                },
                                memo: {
                                    layoutType: 'link', // link, button
                                },
                                cartButton: {
                                    size: isMobile ? 'xl' : 'large',
                                },
                            }}
                            onAddToCart={handleAddToCart}
                            isEdit={Boolean(cartItemId)}
                        />
                    </PosProductDetailProvider>
                : !user?.profile?.id &&
                    <Container sx={{my: 2}}>
                        <LoginForm />
                    </Container>
                }
            </Modal>
        </Container>
    );
};