import React, { useCallback, useEffect, useReducer, useMemo } from 'react';
import { useApiContext } from './ApiContext';
import { emptyApiResponse, initialState} from './initialState';
import { apiReducer} from './apiReducer';
import { ErrorBar, LoadingBar } from '../components/Snacks';

/* 
Hook to handle api requests. It uses the ApiContext to create an api instance (using useAxios)
props:
	shouldValidateParams: if true, validates the params before sending the request
	params:
		endpoint: the api endpoint (required)
		method: GET, POST, PUT, DELETE (optional)
		data: data to send with the request (optional)
		config: axios config object (optional)
		result: initial data to return, in case we need to return data along with the api context methods (optional)
		withCancel: if true, shows the cancel button on the loading bar (optional)
	withRetry: if true, shows the retry button on the error bar (optional)
	enableCache: if true, enables caching of the data. If the endpoint and params match the cache, it will return the cached data instead of fetching it again (optional)
	keepCache: if true, the cache will persist even after the component unmounts (optional)
*/
export const useApi = ({
	params = null,
	shouldValidateParams = false,
	withRetry = false,
	enableCache = false,
	keepCache = false,
}) => {
	const { useRtkQuery, getCachedData, setCachedData, updateCacheCounter, updateCacheSubscriptions, getCacheKey } = useApiContext();
	
	const { call, cancel, hookName } = useRtkQuery({ enableCache });
	//const { call, cancel, hookName } = createApi();
  
	const [state, dispatch] = useReducer(apiReducer, initialState);

	if (params?.result) {
		dispatch({ type: 'FETCH_SUCCESS', payload: { data: params.result, httpCode: 200 } });
	}

	const setState = newState => dispatch({ type: 'SET_STATE', payload: newState });
	
	const isValid = useMemo(() => {
		if (!params?.endpoint) return false;
		if (!shouldValidateParams) return true;
		return params.data && Object.keys(params.data).length > 0;
	}, [params, shouldValidateParams]);

	const updateCacheSubscriptionKey = useMemo(() => {
		if (params?.endpoint && enableCache && hookName === 'useAxios' && !keepCache) {
			const cacheKey = getCacheKey(params?.endpoint, params?.data);
			return cacheKey;
		}
		return false;
  	}, [params?.endpoint, params?.data, enableCache, hookName, keepCache, getCacheKey]);	
  
	const fetchData = useCallback(async (fetchParams = {}) => {		
		if (!isValid) {
		  	dispatch({ type: 'FETCH_FAILURE', payload: { errors: 'Invalid parameters', httpCode: null } });
		  	return { data: null, errors: 'Invalid parameters', httpCode: null };
		}

		//dispatch({ type: 'FETCH_INIT' });

		// Only dispatch FETCH_INIT if we're actually going to make an API call
		const cacheKey = enableCache && hookName === 'useAxios' ? getCacheKey(params.endpoint, params.data) : null;
		const cachedData = cacheKey ? getCachedData(cacheKey) : null;
		
		if (!enableCache || !cachedData || cachedData.errors) {
			dispatch({ type: 'FETCH_INIT' });
		}		

		let requestParams = {...params};
		
		//console.log(enableCache ? "CACHED" : "UNCACHED", requestParams)
		requestParams.enableCache = enableCache;

		if (fetchParams) {
			if (fetchParams?.endpoint){ // if the endpoint is included in the fetch params, set it as the new endpoint and remove it from the fetch params
				requestParams.endpoint = fetchParams.endpoint;
			}
			if (fetchParams?.method){ // if the method is included in the fetch params, set it as the new method and remove it from the fetch params
				requestParams.method = fetchParams.method;
			}
			if (fetchParams?.config){ // if the config is included in the fetch params, set it as the new config and remove it from the fetch params
				requestParams.config = fetchParams.config;
			}
			if (fetchParams.enableCache !== null) { // force cache on or off
				requestParams.enableCache = fetchParams.enableCache;
			}
			if (fetchParams instanceof FormData) { // check if fetchParams is a form data object, if so, set it as the data
				if (requestParams.data){
					for (const key in requestParams.data) {
						fetchParams.append(key, requestParams.data[key]);
					}
				}
				requestParams.data = fetchParams;
			} else {
				requestParams.data = {...requestParams.data, ...fetchParams}; 
			}
		}

		// Handle caching
		if (enableCache && hookName === 'useAxios') {
			const cacheKey = getCacheKey(requestParams.endpoint, requestParams.data);
		  	const cachedData = getCachedData(cacheKey);
		  	if (cachedData && !cachedData.errors) {
				dispatch({ type: 'FETCH_SUCCESS', payload: { data: cachedData.data, httpCode: cachedData.httpCode || 200 } });
				return cachedData;
		  	}
		}
  
		try {
			//console.log("CALLING API", requestParams);
		  	const response = await call({...requestParams, [requestParams.method === 'GET' ? 'params' : 'data']: requestParams.data});
		  	if (enableCache && hookName === 'useAxios' && response) {
				const cacheKey = getCacheKey(requestParams.endpoint, requestParams.data);
				setCachedData(cacheKey, response);
		  	}
			const _errors = (response?.errors === null || response?.errors?.length === 0) ? null : response.errors;
			const dispatchData = {
				type: _errors ? 'FETCH_FAILURE' : 'FETCH_SUCCESS',
				payload: {
					httpCode: response?.httpCode ?? null,
					data: response?.data ?? null,
					errors: _errors,
				},
			};
			//Object.freeze(dispatchData);
			dispatch(dispatchData);
		  	return dispatchData.payload;
		} catch (error) {
		  	const errorMessage = error.message || 'An error occurred';
		  	dispatch({ type: 'FETCH_FAILURE', payload: { errors: errorMessage, httpCode: error.httpCode || null } });
		  	return {
				data: null,
				errors: errorMessage,
				httpCode: error.httpCode || null,
		  	};
		}
	}, [call, enableCache, hookName, isValid, params, getCachedData, getCacheKey, setCachedData, dispatch]);
  
	const handleRetry = useCallback(() => {
		updateCacheCounter();
	  	fetchData({ enableCache: false });
	}, [fetchData, updateCacheCounter]);
    
	// Clean up subscriptions on unmount
	useEffect(() => {
		return () => {
		  	if (updateCacheSubscriptionKey) {
				updateCacheSubscriptions({ key: updateCacheSubscriptionKey, value: -1 });
		  	}
		};
  	}, [updateCacheSubscriptions, updateCacheSubscriptionKey]);
  
	return {
		...emptyApiResponse,
		data: state.data,
		loading: state.loading,
		cancelFetch: cancel,
		errors: state.errors,
		httpCode: state.httpCode,
		fetchData,
		setState,
		LoadingBar: () => state.loading 
			? <LoadingBar cancel={params?.withCancel ? cancel : undefined} /> 
			: null,
		ErrorBar: () => state.errors 
			? <ErrorBar
				open={!!state.errors}
				message={state.errors}
				httpCode={state.httpCode}
				onClose={() => setState({ errors: null })}
				retry={withRetry ? handleRetry : undefined}
			  /> 
			: null,
	};
};
