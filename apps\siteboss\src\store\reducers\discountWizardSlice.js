import { createSlice } from '@reduxjs/toolkit';

export const discountWizardSlice = createSlice({
    name: 'discountWizard',
    initialState: {
        id: null,
        discountData: null,
        selectedConditions: [],
        formData: null
    },
    reducers: {
        setInfo: (state, action) => {
            if (action.payload.hasOwnProperty('id')) state.id = action.payload.id;
            if (action.payload.hasOwnProperty('discountData')) state.discountData = action.payload.discountData;
            if (action.payload.hasOwnProperty('formData')) state.formData = action.payload.formData;
        },
        setSelectedConditions: (state, action) => {
            state.selectedConditions = action.payload;
        },
        addCondition: (state, action) => {
            const exists = state.selectedConditions.some(condition => condition.id === action.payload.id);
            if (!exists) {
                state.selectedConditions.push(action.payload);
            }
        },
        removeCondition: (state, action) => {
            state.selectedConditions = state.selectedConditions.filter(
                condition => condition.id !== action.payload
            );
        },
        updateFormData: (state, action) => {
            state.formData = action.payload;
        },
        resetInfo: (state) => {
            state.id = null;
            state.discountData = null;
            state.selectedConditions = [];
            state.formData = null;
        }
    }
});

export const {
    setInfo,
    setSelectedConditions,
    addCondition,
    removeCondition,
    updateFormData,
    resetInfo
} = discountWizardSlice.actions;

export default discountWizardSlice.reducer;
