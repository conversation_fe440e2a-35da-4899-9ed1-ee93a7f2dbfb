import React from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { Box, Paper, IconButton, Typography, Tooltip, useTheme, Collapse } from "@mui/material";
import { AddBoxOutlined as MoreI<PERSON>, IndeterminateCheckBoxOutlined as LessI<PERSON>, ClearOutlined as RemoveIcon, ExpandMoreOutlined as ExpandIcon, ExpandLessOutlined as CollapseIcon } from "@mui/icons-material";

import styles from "./Item.module.scss";

export const Item = React.forwardRef(({
    childCount,
    clone,
    depth,
    disableSelection,
    disableInteraction,
    ghost,
    handleProps,
    indentationWidth,
    indicator,
    collapsed,
    showDetails,
    onToggleDetails,
    onCollapse,
    onRemove,
    style,
    value,
    details,
    wrapperRef,
    ...props
}, ref) => {
    const { t } = useTranslation();
    const theme = useTheme();

    return (
        <Box
            className={classNames(
                styles.wrapper,
                clone && styles.clone,
                ghost && styles.ghost,
                indicator && styles.indicator,
                disableSelection && styles.disableSelection,
                disableInteraction && styles.disableInteraction
            )}
            ref={wrapperRef}
            style={{'--spacing': `${indentationWidth * depth}px`, '--background-color': theme.palette.secondary.main}}
            {...props}
        >
            <Box component={Paper} className={styles.treeItem} ref={ref} style={style} variant="outlined">
                {onCollapse &&
                    <Tooltip title={t(`general:${collapsed ? 'expand' : 'collapse'}`)}>
                        <IconButton onClick={onCollapse} size="small">
                            {collapsed ? <LessIcon fontSize="inherit"/> : <MoreIcon fontSize="inherit"/>}
                        </IconButton>
                    </Tooltip>
                }
                <Typography variant="body2" component="div" className={styles.text} {...handleProps}>{value}</Typography>
                {details && onToggleDetails &&
                    <Tooltip title={t(`general:${showDetails ? 'expand' : 'collapse'}`)}>
                        <IconButton onClick={onToggleDetails} size="small">
                            {showDetails ? <CollapseIcon fontSize="inherit"/> : <ExpandIcon fontSize="inherit"/>}
                        </IconButton>
                    </Tooltip>
                }
                {!clone && onRemove && 
                    <Tooltip title={t("general:remove")}>
                        <IconButton onClick={onRemove} size="small">
                            <RemoveIcon fontSize="inherit"/>
                        </IconButton>
                    </Tooltip>
                }
            </Box>
            {details && 
                <Collapse in={showDetails} timeout="auto">
                    <Box component={Paper} className={styles.details} square variant="outlined">{details}</Box>
                </Collapse>
            }
        </Box>
    );
});