import { useState } from 'react';
import { LinearProgress } from '@mui/material';

export const useUploadProgress = () => {
    const [uploadProgress, setUploadProgress] = useState(0);

    return {
        uploadProgress,
        updateUploadProgress: setUploadProgress,
        LoadingBar: props => <LinearProgress color="secondary" variant="determinate" value={uploadProgress} sx={{width: "100%"}} {...props} />,
    };
}
