export const widgetList = [
    // Analytics widgets
    {
        id: 'analytics',
        slug: "analytics",
        icon: "analytics",
        preview_img_url: "https://placehold.co/300",
        settings: {
            dataRange: "week",
            chartType: "linear",
            width: 6,
            height: 2,
        },
        roles: [1, 2, 3, 4] // Master Admin, Super Admin, Company Owner, Company Admin
    },
    {
        id: 'users',
        slug: "users",
        icon: "users",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showActive: true,
            showInactive: false,
            width: 6,
            height: 2,
        },
        roles: [1, 2, 3, 4] // Master Admin, Super Admin, Company Owner, Company Admin
    },
    {
        id: 'sites',
        slug: "sites",
        icon: "sites",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showStatus: true,
            width: 6,
            height: 2,
        },
        roles: [1, 2] // Master Admin, Super Admin
    },
    {
        id: 'sales',
        slug: "sales",
        icon: "sales",
        preview_img_url: "https://placehold.co/300",
        settings: {
            period: "month",
            showComparison: true,
            width: 6,
            height: 2,
        },
        roles: [1, 2, 3, 4] // Master Admin, Super Admin, Company Owner, Company Admin
    },
    {
        id: 'inventory',
        slug: "inventory",
        icon: "inventory",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showLowStock: true,
            showOutOfStock: true,
            width: 6,
            height: 2,
        },
        roles: [3, 4] // Company Owner, Company Admin
    },
    {
        id: 'tasks',
        slug: "tasks",
        icon: "tasks",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showCompleted: false,
            showUpcoming: true,
            width: 4,
            height: 2,
        },
        roles: [1, 2, 3, 4, 5, 6] // All except Patron
    },
    {
        id: 'subscriptions',
        slug: "subscriptions",
        icon: "subscriptions",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showExpiring: true,
            width: 6,
            height: 1,
        },
        roles: [7] // Patron
    },
    {
        id: 'notifications',
        slug: "notifications",
        icon: "notifications",
        preview_img_url: "https://placehold.co/300",
        settings: {
            showUnread: true,
            width: 3,
            height: 2,
        },
        roles: [1, 2, 3, 4, 5, 6, 7] // All roles
    },
    // Original widgets
    {
        id: 'calendar',
        slug: "calendar",
        icon: "calendar",
        preview_img_url: "https://placehold.co/300",
        settings: {
            view: "schedule",
            width: 6,
            height: 2,
        },
        roles: [1, 2, 3, 4, 5, 6, 7] // All roles
    },
    {
        id: 'weather',
        slug: "weather",
        icon: "weather",
        preview_img_url: "https://placehold.co/300",
        settings: {
            location: "New York, NY",
            units: "Fahrenheit",
            width: 3,
            height: 1,
        },
        roles: [1, 2, 3, 4, 5, 6, 7] // All roles
    },
];
