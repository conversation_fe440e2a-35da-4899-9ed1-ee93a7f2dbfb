/*eslint-disable*/
/// <reference types="cypress" />

import React from 'react';
import { usePrograms } from '../../apps/siteboss/src/containers/Programs/usePrograms';
import { CustomHookSetup } from '../support/CutomHookSetup';

import events from '../../assets/Mocks/fixtures/events.json';

describe("It will test the 'useProgram' custom hook",()=>{


    beforeEach(()=>{
        cy.mount(<CustomHookSetup useCustomHook={usePrograms} /> )
        cy.intercept('POST', '/api/event', JSON.parse(events.test_program_event.data.events) );
        // cy.intercept('POST', '/api/event/type', events.program_type_is_program);
        // cy.intercept('GET', '/api/group_type', events.group_type);
        // cy.intercept('POST', '/api/tag', events.tag)

        cy.mockStore(true, "siteboss" )
        expect(usePrograms).to.be.a('function')
        cy.log(usePrograms)
    })
    
    // it("will log something",()=>{
    //     cy.window().its('store').invoke('getState')
    // });

    it("will check the default data",()=>{
        cy.get('[data-cy="hook-open"]').should('contain', false);
        cy.get('[data-cy="hook-isNew"]').should('contain', false);
        cy.get('[data-cy="hook-isEdit"]').should('contain', false);
        cy.get('[data-cy="hook-showConfirm"]').should('contain', false);
        cy.get('[data-cy="hook-selectedPrograms"]').should('contain', []);
        cy.get('[data-cy="hook-deleteParams"]').should('contain', []);
        cy.get('[data-cy="hook-fetchCounter"]').should('contain', 0);
        cy.get('[data-cy="hook-loading"]').should('contain', false);
        cy.get('[data-cy="hook-errorBar"]').should('not.exist')
    });

    //this is where we call the functions that effect state.  
    it("will call the functions that effect state",()=>{
        cy.get('[data-cy="hook-setOpen-button]').click();
        cy.get('[data-cy="hook-open"]').should('contain', true);
    })

    //this is where we mock the apis and their responses

})