import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Alert, Button, Container } from '@mui/material';

export const WaiverNotification = ({userData, color='warning', ...props}) => {
    const { t, company, isMobile } = useOutletContext();
    return (
        <Alert variant='filled' severity={color} sx={{mb: 2}} action={
            <Button 
                color='inherit' 
                variant="outlined" 
                size='small' 
                sx={{
                    whiteSpace: 'nowrap', 
                    textTransform: 'uppercase', 
                    fontWeight: 600,
                    m: 1,
                }}
            >
                {t('waiver:signNow')}
            </Button>
        }>
            {`${t('waiver:notSigned',{first_name: userData?.first_name, last_name: userData?.last_name})}`}.<br/>
            {isMobile && <br/>}
            {`${t('waiver:requiredMessage',{company_name: company?.name || "Our Company"})}`}.
        </Alert>
    );
}