export const en = {
    dashboard: {
        welcome: "Welcome, {{name}}!",
        roleBasedMessage: "You are logged in as {{role}}. Your dashboard has been customized for your role.",
        loading: "Loading your personalized dashboard...",
        defaultWidgetsLoaded: "Default widgets have been loaded based on your role.",
        dashboard: "Dashboard",
        totalOrders: "Total Orders",
    },
    general: {
        loading: "Loading...",
        send: "Send",
        save: "Save",
        saveAndPublish: "Save & Publish",
        saveChanges: "Save Changes",
        saveAsDraft: "Save as Draft",
        saveAs: "Save as",
        cancel: "Cancel",
        delete: "Delete",
        edit: "Edit",
        add: "Add",
        addItems: "Add Items",
        addItem: "Add Item",
        addNew: "Add New",
        addField: "Add Field",
        exit: "Exit",
        close: "Close",
        open: "Open",
        create: "Create",
        update: "Update",
        remove: "Remove",
        duplicate: "Duplicate",
        move: "Move",
        moveUp: "Move Up",
        moveDown: "Move Down",
        moveLeft: "Move Left",
        moveRight: "Move Right",
        clear: "Clear",
        confirm: "Confirm",
        undo: "Undo",
        redo: "Redo",
        maximize: "Maximize",
        minimize: "Minimize",
        restore: "Restore",
        settings: "Settings",
        properties: "Properties",
        crop: "Crop",
        rotate: "Rotate",
        zoom: "Zoom",
        reset: "Reset",
        upload: "Upload",
        browse: "Browse",
        download: "Download",
        preview: "Preview",
        device: "Device",
        previewMode: "Preview Mode",
        yes: "Yes",
        no: "No",
        ok: "OK",
        continue: "Continue",
        next: "Next",
        back: "Back",
        done: "Done",
        search: "Search",
        filter: "Filter",
        select: "Select",
        actions: "Actions",
        noResults: "No results",
        noContent: "No content to display",
        changeLanguage: "Change language",
        lightMode: "Light mode",
        darkMode: "Dark mode",
        print: "Print",
        printedOn: "Printed on",
        phone: "Phone",
        email: "Email",
        address: "Address",
        name: "Name",
        description: "Description",
        date: "Date",
        joined: "Joined",
        id: "ID",
        hide: "Hide",
        show: "Show",
        more: "More",
        less: "Less",
        showLess: "Show Less",
        showMore: "Show More",
        location: "Location",
        confirmMessage: "Are you sure you want to perform this action?",
        selectAll: "Select All",
        unselectAll: "Unselect All",
        checkAll: "Check All",
        uncheckAll: "Uncheck All",
        clearAll: "Clear All",
        none: "None",
        all: "All",
        option: "Option",
        expand: "Expand",
        collapse: "Collapse",
        expandAll: "Expand All",
        collapseAll: "Collapse All",
        lastUpdated: "Last Updated",
        default: "Default",
        other: "Other",
        extra: "Extra",
        additionalInfo: "Additional Information",
        showing: "Showing",
        of: "of",
        perPage: "Per Page",
        time: {
            second: "Second",
            seconds: "Seconds",
            secondsShort: "sec",
            secondsShortest: "s",
            minute: "Minute",
            minutes: "Minutes",
            minutesShort: "min",
            minutesShortest: "m",
            hour: "Hour",
            hours: "Hours",
            hoursShort: "hr",
            hoursShortest: "h",
            day: "Day",
            days: "Days",
            daysShort: "day",
            daysShortest: "d",
            week: "Week",
            weeks: "Weeks",
            weeksShort: "wk",
            weeksShortest: "w",
            month: "Month",
            months: "Months",
            monthsShort: "mon",
            monthsShortest: "m",
            year: "Year",
            years: "Years",
            yearsShort: "yr",
            yearsShortest: "y",
        },
        timeUp: "Time's up!",
        learnMore: "Learn more",
        records: "Records",
        slug: "Slug",
        editor: "Editor",
        list: "List",
        by: "By",
        cart: "Cart",
    },

    module: {
        module: "Module",
        modules: "Modules",
        siteBoss: {
            name: "SiteBoss",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                orders: "Orders",
                users: "Users",
                events: "Events",
                programs: "Programs",
                discounts: "Discounts",
                services: "Services",
                reports: "Reports",
                chat: "Chat",
            },
        },
        owl: {
            name: "OWL",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                products: "Products",
                merchants: "Merchants",
                orders: "Orders",
                reports: "Reports",
                billingInvoices: "Billing & Invoices",
                customerManagement: "Customer Management",
                carrierConfigurations: "Carrier Configurations",
                marketingTools: "Marketing & Communication Tools",
                serviceStatus: "Service Status",
            },
        },
        siteBossAdmin: {
            name: "SiteBoss Admin",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                chat: "Chat",
            },
        },
        pos: {
            name: "Registers",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                chat: "Chat",
            },
        },
        wms: {
            name: "Warehouse",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                chat: "Chat",
            },
        },
        cms: {
            name: "CMS",
            menu: {
                home: "Home",
                dashboard: "Dashboard",
                websites: "Websites",
                builder: "Builder",
                themes: "Themes",
                chat: "Chat",
            },
        },
    },

    datePicker: {
        today: "Today",
        clear: "Clear",
        close: "Close",
        reset: "Reset",
        thisWeek: "This Week",
        nextWeek: "Next Week",
        lastWeek: "Last Week",
        thisMonth: "This Month",
        nextMonth: "Next Month",
        lastMonth: "Last Month",
        thisYear: "This Year",
        nextYear: "Next Year",
        lastYear: "Last Year",
        tomorrow: "Tomorrow",
        yesterday: "Yesterday",
        thisWeekend: "This Weekend",
        nextWeekend: "Next Weekend",
        lastWeekend: "Last Weekend",
        start: "Start",
        end: "End",
        time: "Time",
        date: "Date",
    },

    menu: {
        documentation: "Documentation",
        help: "Help",
    },

    /*dashboard: {
        dashboard: "Dashboard",
        totalOrders: "Total Orders",
        totalCustomers: "Total Customers",
        totalSales: "Total Sales",
        totalRevenue: "Total Revenue",
    },*/

    login: {
        title: "Portal Log In",
        login: "Sign In",
        username: "Username",
        password: "Password",
        rememberMe: "Remember me",
        forgotPassword: "Forgot password",
        loginWith: "Login with",
        loginError: "Invalid email or password",
        or: "or",
        logout: "Sign Out",
        logBackIn: "Log back in",
    },

    order: {
        order: "Order",
        orders: "Orders",
        customer: "Customer",
        date: "Date",
        total: "Total",
        status: "Status",
        price: "Price",
        expected: "Expected",
        qty: "Qty",
        item: "Item",
        items: "Items",
        details: "Details",
        product: "Product",
        payment: "Payment",
        payments: "Payments",
        balance: "Balance",
        paymentMethod: "Payment Method",
        endingIn: "ending in",
        shipping: "Shipping",
        amount: "Amount",
        tax: "Tax",
        subtotal: "Subtotal",
        tip: "Tip",
        discount: "Discount",
        cashDiscount: "Cash Discount",
        creditCard: "Credit Card",
        cash: "Cash",
        check: "Check",
        giftCard: "Gift Card",
        adminAuthorized: "Admin Authorized",
        tendered: "Tendered",
        change: "Change",
        adminFee: "Admin Fee",
        viewCart: "View Cart",
        cartEmpty: "Your cart is empty",
        continueShopping: "Continue Shopping",
        orderSummary: "Order Summary",
        payNow: "Pay Now",
        sendEmail: "Send Email",
        sendEmailText: "Enter the email address you would like to send the order to.",
        formats: {
            fullPage: "Full Page",
            ticket: "Ticket",
            kitchenTicket: "Kitchen Ticket",
        },
        empty: "No orders found",
        emptyPendingCharges: "No pending charges found",
        transactionId: "Transaction ID",
        ordersDashboard: "Orders Dashboard",
        ordersByGroup: "Orders by Group",
        groups: "groups",
        group: "group",
        merchant: "Merchant",
        orderNumber: "Order #",
        noOrdersFound: "No orders found",
        noOrdersForStatus: "No orders found for {{status}}",
        statuses: {
            invalid: "Invalid",
            new: "New",
            fulfilling: "Fulfilling",
            shipped: "Shipped",
            return: "Return",
        },
    },

    status: {
        all: "All",
        total: "Total",
        pending: "Pending",
        completed: "Completed",
        paid: "Paid",
        overdue: "Overdue",
        outstanding: "Outstanding",
        unpaid: "Unpaid",
        shipped: "Shipped",
        canceled: "Canceled",
        refunded: "Refunded",
        returned: "Returned",
        allOrders: "All Orders",
        others: "Rest",
        failed: "Failed",
        inFlight: "In flight",
        expired: "Expired",
        voided: "Voided",
        captured: "Captured",
        uncaptured: "Uncaptured",
        adminAuthorized: "Admin Authorized",
        completePendingSettlement: "Complete / Pending Settlement",
    },

    user: {
        user: "User",
        users: "Users",
        newUser: "New User",
        newFamilyMember: "New Family Member",
        newGroupMember: "New Group Member",
        familyMember: "Family Member",
        groupMember: "Group Member",
        editUser: "Edit User",
        search: "Search Users",
        profile: "User Profile",
        myProfile: "My Profile",
        myTransactions: "My Transactions",
        personalSettings: "Personal Settings",
        logout: "Sign Out",
        fullName: "Full Name",
        firstName: "First Name",
        lastName: "Last Name",
        middleName: "Middle Name",
        mobilePhone: "Mobile Phone",
        homePhone: "Home Phone",
        email: "Email",
        address: "Address",
        address2: "Address Line #2",
        city: "City",
        state: "State",
        zip: "Zip Code",
        country: "Country",
        username: "Username",
        password: "Password",
        confirmPassword: "Confirm Password",
        dateOfBirth: "Date of Birth",
        role: "Role",
        relationship: "Relationship",
        deleteTitle: "Delete User(s)",
        deleteMessage: "Are you sure you want to delete the selected user(s)?",
        saved: "User information saved successfully",
        deleted: "User deleted successfully",
        updated: "User information updated successfully",
        created: "User created successfully",
        lastLogin: "Last Login",
        toolbar: {
            profile: "Profile",
            orders: "Orders",
            events: "Events",
            subscriptions: "Subscriptions",
            family: "Family",
            groups: "Groups",
            pendingCharges: "Pending Charges",
            wallet: "Wallet",
        },
        notes: "Notes",
        addNote: "Add a note...",
        noteStatus: {
            public: "Public",
            adminOnly: "Admins Only",
            author: "Just me",
        },
        roles:{
            siteBossMasterAdmin: "SiteBoss Master Admin",
            siteBossSuperAdmin: "SiteBoss Super Admin",
            companyOwner: "Company Owner",
            companyAdmin: "Company Admin",
            staff: "Staff",
            nonStaffManager: "Non-Staff Manager",
            patron: "Patron",
        },
        relationships: {
            admin: "Admin",
            parent: "Parent",
            child: "Child",
            guardian: "Guardian",
            friend: "Friend",
            spouse: "Spouse",
            partner: "Partner",
            sibling: "Sibling",
            other: "Other",
        },
    },

    wallet: {
        wallet: "Wallet",
        wallets: "Wallets",
        balance: "Balance",
        transactions: "Transactions",
        empty: "No wallet info found",
        tokens: "Tokens",
        token: "Token",
        emptyTokens: "No tokens found",
        expirationDate: "Expiration Date",
        noExpirationDate: "No expiration date",
        useDate: "Use Date",
    },

    reports: {
        reports: "Reports",
        quickAccess: "Quick Access",
        recentReports: "Recent Reports",
        favorites: "Favorites",
        reportCategories: "Report Categories",
        filters: "Filters",
        startDate: "Start Date",
        endDate: "End Date",
        cashier: "Cashier",
        apply: "Apply",
        totalSales: "Total Sales",
        transactions: "Transactions",
        averageSale: "Average Sale",
        salesByPaymentMethod: "Sales by Payment Method",
        dailyRegisterSummary: "Daily Register Summary",
        export: "Export",
        print: "Print",
        all: "All",
        categories: {
            overview: {
                name: "Overview",
                description: "View all available reports"
            },
            register: {
                name: "Registers",
                description: "View reports for register transactions, cash flow, and settlements"
            },
            sales: {
                name: "Sales",
                description: "View reports for sales by product, category, and payment method"
            },
            events: {
                name: "Events",
                description: "View reports for event registrations, attendance, and revenue"
            },
            outstanding: {
                name: "Outstanding",
                description: "View reports for outstanding payments and invoices"
            },
            memberships: {
                name: "Subscriptions",
                description: "View reports for memberships and subscription activity"
            },
            users: {
                name: "Users",
                description: "View reports for user activity and demographics"
            },
            usage: {
                name: "Usage",
                description: "View reports for system usage and performance"
            },
            services: {
                name: "Services",
                description: "View reports for service bookings, providers, and performance"
            }
        },
        register: {
            name: "Register",
            dailySales: "Daily Sales",
            settlements: "Settlements",
            cashFlow: "Cash Flow",
            transactions: "Transactions"
        },
        registerReports: "Register Reports",
        salesReports: "Sales Reports",
        eventReports: "Event Reports",
        outstandingReports: "Outstanding Reports",
        membershipReports: "Membership & Subscription Reports",
        userReports: "User Reports",
        usageReports: "Usage Reports",
        servicesReports: "Services Reports",
        services: {
            bookings: "Bookings",
            providers: "Providers",
            performance: "Performance",
            utilization: "Utilization"
        },
        sales: {
            byProduct: "By Product",
            byCategory: "By Category",
            byPaymentMethod: "By Payment Method",
            trends: "Trends",
            topProducts: "Top Products",
            revenue: "Revenue",
            profit: "Profit",
            margin: "Margin",
            quantity: "Quantity",
            productPerformance: "Product Performance",
            categoryPerformance: "Category Performance",
            salesForecast: "Sales Forecast",
            comparePeriods: "Compare Periods",
            productType: "Product Type",
            timeFrame: "Time Frame",
            daily: "Daily",
            weekly: "Weekly",
            monthly: "Monthly",
            quarterly: "Quarterly",
            yearly: "Yearly",
            compareWith: "Compare With",
            previousPeriod: "Previous Period",
            samePeroidLastYear: "Same Period Last Year",
            custom: "Custom",
            salesBreakdown: "Sales Breakdown",
            salesTrend: "Sales Trend",
            salesGrowth: "Sales Growth",
            salesVelocity: "Sales Velocity",
            averageOrderValue: "Average Order Value",
            conversionRate: "Conversion Rate",
            customerAcquisitionCost: "Customer Acquisition Cost",
            customerLifetimeValue: "Customer Lifetime Value",
            returnOnInvestment: "Return on Investment",
            netProfit: "Net Profit",
            grossProfit: "Gross Profit"
        }
    },

    discount: {
        discounts: "Discounts",
        discount: "Discount",
        discountManager: "Discount Manager",
        addDiscount: "Add Discount",
        editDiscount: "Edit Discount",
        newDiscount: "New Discount",
        name: "Name",
        code: "Code",
        type: "Type",
        validFrom: "Valid From",
        validUntil: "Valid Until",
        status: "Status",
        active: "Active",
        inactive: "Inactive",
        autoApply: "Auto Apply",
        noEndDate: "No End Date",
        noDiscounts: "No discounts found",
        deleteConfirmTitle: "Delete Discount",
        deleteConfirmMessage: "Are you sure you want to delete the discount '{{name}}'?",
        success: "Discount saved successfully!",
        wizard: {
            name: {
                title: "Name & Description",
                subtitle: "Basic information about the discount",
                description: "Enter a name and description for your discount",
                label: "Discount Name"
            },
            description: {
                label: "Description"
            },
            auto: {
                title: "Application Method",
                subtitle: "How the discount will be applied",
                description: "Choose whether this discount will be automatically applied or requires a coupon code",
                label: "Application Method",
                autoApply: "Automatically apply to qualifying orders",
                couponCode: "Require a coupon code",
                codeLabel: "Coupon Code",
                codeHelper: "Enter a unique code that customers will use to apply this discount"
            },
            maxUses: {
                title: "Usage Limits",
                subtitle: "How many times this discount can be used",
                description: "Set whether this discount has unlimited uses or a specific limit",
                label: "Usage Limit",
                unlimited: "Unlimited uses",
                limited: "Limited number of uses",
                maxLabel: "Maximum Number of Uses",
                maxHelper: "Enter the maximum number of times this discount can be used"
            },
            dates: {
                title: "Valid Dates",
                subtitle: "When this discount is valid",
                description: "Set the date range when this discount can be used",
                fromLabel: "Valid From",
                untilLabel: "Valid Until",
                noEndDate: "No end date"
            },
            type: {
                title: "Discount Type",
                subtitle: "How the discount amount is calculated",
                description: "Choose whether this is a percentage or fixed amount discount",
                label: "Discount Type",
                percentage: "Percentage discount",
                fixed: "Fixed amount discount",
                percentLabel: "Percentage",
                amountLabel: "Amount"
            },
            applyTo: {
                title: "Application Scope",
                subtitle: "What the discount applies to",
                description: "Choose whether this discount applies to the entire order or specific items",
                label: "Apply To",
                entireOrder: "Entire Order",
                specificItems: "Specific Items"
            },
            combo: {
                title: "Combination Rules",
                subtitle: "Whether this discount can be combined with others",
                description: "Choose whether this discount can be used in combination with other discounts",
                label: "Can this discount be combined with others?",
                yes: "Yes",
                no: "No"
            },
            conditions: {
                title: "Conditions",
                subtitle: "Requirements for this discount to apply",
                description: "Set conditions that must be met for this discount to be applied",
                label: "Conditions"
            },
            summary: {
                title: "Summary",
                subtitle: "Review and activate your discount",
                description: "Review the details of your discount and set its status",
                statusLabel: "Discount Status",
                active: "Active",
                inactive: "Inactive"
            }
        }
    },

    waiver: {
        waiver: "Waiver",
        waivers: "Waivers",
        notSigned: "{{first_name}} has not signed their waiver",
        signed: "{{first_name}} has signed their waiver",
        requiredMessage: "{{company_name}} requires a signed waiver to participate in activities and services",
        signNow: "Sign now",
        signLater: "Sign later",
    },

    subscription: {
        subscription: "Subscription",
        subscriptions: "Subscriptions",
        intervals: "Intervals",
        interval: "Interval",
        nextBillDate: "Next Bill Date",
        lastBillDate: "Last Bill Date",
        firstBillDate: "First Bill Date",
        finalBillDate: "Final Bill Date",
        restartBillDate: "Restart Bill Date",
        intervalQuantity: "Interval Quantity",
        every: "Every",
        days: "Days",
        day: "Day",
        weeks: "Weeks",
        week: "Week",
        months: "Months",
        month: "Month",
        years: "Years",
        year: "Year",
        starting: "Starting",
        ending: "Ending",
        on: "on",
        of: "of",
        for: "for",
        this: "this",
        billed: "Billed",
        billedEvery: "Billed every",
        intervalTypes: {
            daily: "Daily",
            weekly: "Weekly",
            monthly: "Monthly",
            yearly: "Yearly",
        },
        status:{
            active: "Active",
            suspended: "Suspended",
            cancelled: "Cancelled",
            expired: "Expired",
        },
        empty: "No subscriptions found",
        cycle: "Cycle",
        cycles: "Cycles",
    },

    program: {
        program: "Program",
        programs: "Programs",
        newProgram: "New Program",
        editProgram: "Edit Program",
        search: "Search Programs",
        empty: "No programs found",
        name: "Name",
        type: "Type",
        groupTypeName: "Group Types",
        shortDescription: "Short Description",
        description: "Description",
        tags: "Tags",
        images: "Program Images",
        selectImages: "Select Images",
        deleteTitle: "Delete Program(s)",
        deleteMessage: "Are you sure you want to delete the selected program(s)?",
        success: "Program saved successfully",
        deleted: "Program deleted successfully",
        childEvents: "{{count}} child events",
        toolbar: {
            basic: "Program Info",
            groupTypes: "Groups Types",
            images: "Images",
        },
        groupTypes: {
            list: "Available Groups Types",
            assigned: "Assigned to the program",
            add: "Add Group Type",
            remove: "Remove Group Type",
            create: "New Group Type",
            addAll: "Add All",
            removeAll: "Remove All",
            emptyList: "No group types found",
            emptyAssigned: "No group types assigned",
        },
    },

    event: {
        event: "Event",
        events: "Events",
        upcomingEvents: "Upcoming Events",
        pastEvents: "Past Events",
        newEvent: "New Event",
        editEvent: "Edit Event",
        search: "Search Events",
        empty: "No events found",
        name: "Name",
        type: "Type",
        status: "Status",
        startDate: "Start Date",
        endDate: "End Date",
        dateTime: "Date & Time",
        location: "Location",
        attendeesColumn: "Attendees",
        pricing: "Pricing",
        media: "Media",
        defaultPrice: "Default",
        additionalVariants: "more options",
        to: "to",
        deleteTitle: "Delete Event(s)",
        deleteMessage: "Are you sure you want to delete the selected event(s)?",
        success: "Event saved successfully",
        deleted: "Event deleted successfully",
        belowAge: "Too young for this event",
        aboveAge: "Too old for this event",
        eventCapacityFull: "This event is full",
        eventCapacityUnder: "This event is under the minimum capacity",
        eventNotAvailable: "This event ended or is not available",
        types: {
            class: "Class",
            practice: "Practice",
            reservation: "Reservation",
            game: "Game",
            series: "Series",
            clubSignUps: "Club Sign-Ups",
            camp: "Camp",
            tournament: "Tournament",
            serviceBooking: "Service Booking",
        },
        statuses: {
            pending: "Pending",
            confirmed: "Confirmed",
            postponed: "Postponed",
            cancelled: "Cancelled",
            reservedPrivate: "Reserved / Private",
            archived: "Archived",
            inCart: "In Cart",
            expired: "Expired",
        },
        roles:{
            owner: "Owner",
            manager: "Manager",
            attendee: "Attendee",
        },
        toolbar: {
            basic: "Event Info",
            groups: "Groups",
            tree: "Event Tree",
            attendees: "Attendees",
            statistics: "Statistics",
            images: "Images",
            customFields: "Custom Fields",
        },
        groups: {
            list: "Available Groups",
            assigned: "Assigned to the event",
            add: "Add Group",
            remove: "Remove Group",
            create: "New Group",
            addAll: "Add All",
            removeAll: "Remove All",
            emptyList: "No groups found",
            emptyAssigned: "No groups assigned",
            showTags: "Show Tags",
            hideTags: "Hide Tags",
            members: "Members",
            memberType: {
                allUsers: "All Users",
                registeredUsers: "Registered Users",
            },
        },
        attendees: {
            list: {
                all: "All",
                pending: "Pending",
                attending: "Attending",
                notAttending: "Not Attending",
                tentative: "Tentative",
                invite: "Invite",
            },
            empty: "No attendees found",
            invite: "Invite people or groups",
            notRequired: "Attendees are not required for this event",
        },
        wizard: {
            details: {
                title: "Event Details",
                subtitle: "Basic information",
                description: "Add basic information about your event, like the name and description."
            },
            parentEvent: {
                title: "Parent Event / Program",
                subtitle: "Parent event or program",
                description: "If the event is part of another event, for example a tournament or a series, you can select a parent event.\n\nOr select a program if this is the main event in a program.",
            },
            eventTags: {
                title: "Event Tags",
                subtitle: "Add tags",
                description: "Add tags that describe your event. This will help attendees find it easier.",
            },
            eventImages: {
                title: "Event Images",
                subtitle: "Add images",
                description: "Add images to showcase your event. You can add multiple images. The format should be JPG, PNG or GIF.",
            },
            eventType: {
                title: "Event Type",
                subtitle: "Select the type of event",
                description: "Select the type of event you are creating. This will help categorize your event and make it easier for attendees to find."
            },
            eventDate: {
                title: "Event Date & Time",
                subtitle: "Select the date and time",
                description: "Select the date and time for your event. If your event has multiple dates, the event will be created on each of these dates on the same time.",
            },
            timeSheet: {
                title: "Available Time Slots",
                subtitle: "Select time slots for your event",
                description: "Select the time slots when your event will be available. You can select a slot per day of the week if the slot is available.",
                error: "Please select a location, date and time range for your event."
            },
            ageRequirement: {
                title: "Age Requirement",
                subtitle: "Set the age requirement",
                description: "Select the age requirement for your event. If the event has no age requirement, leave it blank.",
                stepLabel: "year(s) old",
            },
            registrationRequirement: {
                title: "Registration Requirement",
                subtitle: "Set the registration requirement",
                description: "Select if attendees need to register for your event, and if so, if they need to register for each event or just once.",
            },
            registrationMessages: {
                title: "Registration Messages",
                subtitle: "Customize the registration messages",
                description: "Customize the messages that attendees will see when they register for your event. You can customize the registration message, the confirmation message, and the messages for attendees below and above the age requirement.",
            },
            customFields:{
                title: "Custom Fields",
                subtitle: "Add custom fields",
                description: "Define any custom fields attendees will need to fill out when registering for your event. You can add text fields, dropdown lists, and more.",
                empty: "This event has no custom fields",
            },
            paymentOptions: {
                title: "Pricing",
                subtitle: "Pricing & plans",
                description: "If your event requires a payment, you can add payment plans that attendees can choose from. You create different plans, like one-time payments, installments, etc.",
            },
            locations: {
                title: "Locations & Dates",
                subtitle: "Where and when",
                description: "Add locations and the dates / times for your event. You can add multiple locations and dates if they are available.",
            },
            requirements: {
                title: "Requirements & Restrictions",
                subtitle: "What the attendee needs",
                description: "Set requirements and restrictions for your event, like if it requires registration, a ticket, an age limit, etc.",
            },
            review: {
                title: "Review & Publish",
                subtitle: "Final review",
                description: "Review your event details and publish it. You can always edit it later.",
            },
            name: "Event Name",
            shortDescription: "Short Description",
            description: "Description",
            tags: "Tags",
            images: "Images",
            type: "Type",
            parent: "Parent Event",
            program: "Program",
            location: "Location",
            timeSheets: "Time Slots",
            time: "Time",
            date: "Date",
            startDate: "Start Date",
            endDate: "End Date",
            recurring: "This is a recurring event",
            ageRange: "Age Range",
            registration: "Requires Registration",
            registrationType: "Registration Type",
            registrationPerEvent: "Per Event",
            registrationAllEvents: "All Events",
            registrationMessage: "Registration Message",
            confirmationMessage: "Confirmation Message",
            belowAgeMessage: "Below Age Message",
            aboveAgeMessage: "Above Age Message",
            customFieldLabel: "Label / Place holder",
            customFieldType: "Field Type",
            customFieldTypeText: "Text",
            customFieldTypeSelect: "Dropdown List",
            customFieldRequired: "Required",
            customFieldOptions: "Options",
            customFieldOptionValue: "Option #{{number}} Value",
            customFieldOptionLabel: "Option #{{number}} Label",
            pricingFullFee: "Full Fee",
            pricingRecurring: "Recurring Charge",
            pricingName: "Plan Name",
            pricingUpfront: "Upfront Fee",
            pricingPrice: "Charge Fee",
            addCustomField: "Add Custom Field",
            addPaymentOption: "Add Payment Plan",
        },
        charts:{
            signUps: {
                title: "Sign-ups",
                subtitle: "Event sign-ups per day",
            },
            paymentDistribution: {
                title: "Payment Distribution",
                subtitle: "Payment distribution for the event",
            },
        },
    },

    eventType: {
        eventType: "Event Type",
        eventTypes: "Event Types",
        newEventType: "New Event Type",
        editEventType: "Edit Event Type",
        search: "Search Event Types",
        empty: "No event types found",
        name: "Name",
        description: "Description",
        isMeta: "Meta Event",
        isProgram: "Program",
        deleteTitle: "Delete Event Type(s)",
        deleteMessage: "Are you sure you want to delete the selected event type(s)?",
        saved: "Event type saved successfully",
        deleted: "Event type deleted successfully",
        updated: "Event type updated successfully",
        created: "Event type created successfully",
    },

    group: {
        group: "Group",
        groups: "Groups",
        newGroup: "New Group",
        editGroup: "Edit Group",
        search: "Search Groups",
        addMember: "Add Member",
        addMembers: "Add Members",
        memberList: "Member List",
        inviteMember: "Invite Member",
        relationship: "Relationship",
        relationships: "Relationships",
        member: "Member",
        members: "Members",
        join: "Join",
        leave: "Leave",
        accept: "Accept",
        reject: "Reject",
        empty: "No groups found",
        emptyFamily: "No family group found",
        type: {
            department: "Department",
            team: "Team",
            friends: "Friends",
            family: "Family",
            league: "League",
            club: "Club",
            class: "Class",
            employee: "Employee",
            organization: "Organization",
            business: "Business",
            other: "Other",
        },
        status: {
            active: "Active",
            pending: "Pending",
            inactive: "Inactive",
            suspended: "Suspended",
            banned: "Banned",
        },
        name: "Name",
        groupType: "Group Type",
        tags: "Tags",
        deleteTitle: "Delete Group(s)",
        deleteMessage: "Are you sure you want to delete the selected group(s)?",
        saved: "Group information saved successfully",
        deleted: "Group deleted successfully",
        updated: "Group information updated successfully",
        created: "Group created successfully",
    },

    groupType: {
        groupType: "Group Type",
        groupTypes: "Group Types",
        newGroupType: "New Group Type",
        editGroupType: "Edit Group Type",
        search: "Search Group Types",
        empty: "No group types found",
        name: "Name",
        description: "Description",
        deleteTitle: "Delete Group Type(s)",
        deleteMessage: "Are you sure you want to delete the selected group type(s)?",
        saved: "Group type saved successfully",
        deleted: "Group type deleted successfully",
        updated: "Group type updated successfully",
        created: "Group type created successfully",
        customFields: "Custom Fields",
    },

    website: {
        website: "Website",
        websites: "Websites",
        newWebsite: "New Website",
        editWebsite: "Edit Website",
        search: "Search Websites",
        empty: "No websites found",
        name: "Name",
        description: "Description",
        keywords: "Keywords",
        theme: "Theme",
        deleteTitle: "Delete Website(s)",
        deleteMessage: "Are you sure you want to delete the selected website(s)?",
        success: "Website saved successfully",
        deleted: "Website deleted successfully",
        page: {
            empty: "No pages found",
            newPage: "New Page",
            subtitle: "Add a new page to the selected website",
            batchCreate: "Batch Create",
            batchCreateSubtitle: "Create all the pages you need at once!",
            generatePages: "Generate Pages",
            type: {
                category: {
                    new: "New Category",
                    name: "Category Name",
                    parentId: "Parent Category",
                    slug: "Slug",
                    toolbar: {
                        general: "Category",
                    },
                    deleteTitle: "Delete Categories",
                    deleteMessage: "Are you sure you want to delete the selected categories?",
                },
                template: {
                    new: "New Template",
                    newSubtitle: "Add a new template that you can use for your pages",
                },
                blog: {
                    new: "New Blog",
                    newSubtitle: "Add a new blog to share your thoughts, ideas, and more",
                    editSubtitle: "Manage your blog entries, categories, pages and settings",
                    newPage: "New Blog Page",
                    newPageSubtitle: "Add a new page with blog capabilities",
                    form: {
                        name: "Blog Name",
                        nameHelper: "The name of the blog",
                        slug: "Blog Slug",
                        slugHelper: "The slug used to access the blog",
                        customFields: "Custom Fields",
                        customFieldsHelper: "Add custom fields to your blog entries",
                        index: "Index Page Slug",
                        indexHelper: "The slug used to access the blog's index page",
                        post: "Post Page Slug",
                        postHelper: "The slug used to access a blog post",
                        posts: "Post Listing Slug",
                        postsHelper: "The slug used to access the list of blog posts",
                        search: "Search Page Slug",
                        searchHelper: "The slug used to access the search page",
                        toolbar: {
                            general: "Blog Settings",
                            slugs: "Pages",
                            customFields: "Custom Fields",
                        },
                    },
                    entries: {
                        title: "Blog Post Title",
                        slug: "Blog Post Slug",
                        slugHelper: "The slug used to access the blog post",
                        content: "Content",
                        images: "Images",
                        attachments: "Attachments",
                        categories: "Categories",
                        tags: "Tags",
                        authors: "Authors",
                        status: "Status",
                        publishDate: "Publish Date",
                        endDate: "End Date",
                        relatedPosts: "Related Posts",
                        metaTitle: "Meta Title",
                        metaDescription: "Meta Description",
                        metaKeywords: "Meta Keywords",
                        toolbar: {
                            general: "Blog Post",
                            media: "Media",
                            meta: "Meta Data",
                            customFields: "Custom Fields",
                            properties: "Properties",
                            advanced: "Advanced",
                        },
                        deleteTitle: "Delete Blog Post(s)",
                        deleteMessage: "Are you sure you want to delete the selected blog post(s)?",
                    },
                },
                ecommerce: {
                    new: "New Shop",
                    newSubtitle: "Add a new shop to sell products, services, register for events, etc. Each shop has its own pages",
                    editSubtitle: "Manage your store pages and settings",
                    newPage: "New Ecommerce Page",
                    newPageSubtitle: "Add a new page with ecommerce capabilities",
                    form: {
                        name: "Shop Name",
                        nameHelper: "The name of the shop",
                        slug: "Shop Slug",
                        slugHelper: "The slug used to access the shop",
                        index: "Index Page Slug",
                        indexHelper: "The slug used to access the shop's index page",
                        cart: "Cart Page Slug",
                        cartHelper: "The slug used to access the cart page",
                        checkout: "Checkout Page Slug",
                        checkoutHelper: "The slug used to access the checkout page",
                        confirmation: "Payment Confirmation Slug",
                        confirmationHelper: "The slug used to access the payment confirmation page",
                        product: "Product Page Slug",
                        productHelper: "The slug used to access product details",
                        products: "Product Listing Page Slug",
                        productsHelper: "The slug used to access the product listing page",
                        category: "Category Page Slug",
                        categoryHelper: "The slug used to access the category page",
                        event: "Event Page Slug",
                        eventHelper: "The slug used to access the event page",
                        events: "Event Listing Page Slug",
                        eventsHelper: "The slug used to access the event listing page",
                        registerId: "Register ID",
                        registerIdHelper: "The ID of the register to use for this shop",
                        toolbar: {
                            general: "Shop  Settings",
                            slugs: "Pages",
                        },
                    },
                },
            },
            entries: "Entries",
            categories: "Categories",
            tags: "Tags",
            statuses: {
                draft: "Draft",
                published: "Published",
                archived: "Archived",
                deleted: "Deleted",
            },
        },
        toolbar: {
            basic: "Website Info",
            urls: "URLs",
            pages: "Pages",
            formSubmissions: "Form Submissions",
            templates: "Templates",
            storedWidgets: "Stored Widgets",
            ecommerce: "Ecommerce",
            blog: "Blog",
            wiki: "Wiki",
            articles: "Articles",
            contracts: "Contracts",
            sitemap: "Sitemap",
        },
        urls: {
            url: "URL",
            urls: "URLs",
            domain: "Domain",
            subdomain: "Subdomain",
            indexPage: "Index Page",
            remove: "Remove URL",
            add: "Add URL",
        },
    },

    builder: {
        name: "Builder",
        addSection: "Add New Section",
        mobileWarning: "The website builder is not compatible with your mobile device.",
        actions: "Actions",
        empty: "A canvas of endless possibilities awaits!\nSelect a block to start building.",
        emptyTemplate: "Let’s get started!\nAdjust the template to fit your website.",
        loadHistory: "Are you sure you want to load the selected page version?",
        closeBuilder: "Any unsaved changes will be lost. Are you sure you want to close the builder?",
        contentGoesHere: "This space will be filled with awesomeness!",
        toolbar: {
            chat: "Chat",
            widgets: "Widgets",
            blocks: "Blocks",
            settings: "Settings",
            themes: "Themes",
            sourceCode: "Source Code",
            history: "History",
            boxModel: "Box Model",
        },
        settings:{
            showHeader: "Show Header",
            showFooter: "Show Footer",
            addToMenu: "Add to Menu",
            redirectTo: "Redirect to",
        },
        storedWidget: {
            save: "Save as stored widget",
            load: "Load stored widget",
        },
        page: {
            title: "Page Title",
            slug: "Page Slug",
            keywords: "Keywords",
            template: "Template",
        },
        component: {
            layouts: "Layouts",
            properties: "Properties",
            margin: "Margin",
            padding: "Padding",
            maxWidth: "Max Width",
            width: "Width",
            background: "Background color",
            center: "Center",
            inner: "inner",
            outer: "outer",
            boxModel: "Box Model",
            types: {
                general: "General",
                ecommerce: "Ecommerce",
                pos: "Ecommerce",
                event: "Events",
                blog: "Blog",
                cms: "General",
            },
            header: {
                name: "Header",
                layouts: {
                    basic: "Basic",
                    centered: "Centered",
                    logo: "Logo Only",
                },
                title: 'Title',
                logo: 'Logo',
                showMenu: 'Show Menu',
                sticky: 'Sticky',
            },
            footer: {
                name: "Footer",
                layouts: {
                    basic: "Basic",
                },
                title: 'Title',
                logo: 'Logo',
                showMenu: 'Show Menu',
                copyRight: 'Copy Right',
                socialMedia: 'Social Media',
            },
            body: {
                name: "Body",
                layouts: {
                    default: "Default",
                    centered: "Centered",
                },
                cols: "Columns",
                colsHelper: "The number of columns to use for this section",
            },
            core: {
                name: "Core",
                layouts: {
                    centered: "Centered",
                    mediaFirst: "Media First",
                    mediaRight: "Media Right",
                    mediaLeft: "Media Left",
                    mediaOverlay: "Media Overlay",
                },
                mediaType: "Media Type",
                mediaTypes: {
                    image: "Image",
                    video: "Video",
                },
                videoUrl: "Video URL",
                autoplay: "Autoplay",
                muted: "Muted",
                controls: "Show Controls",
                images: "Images",
                buttons: "Buttons",
            },
            heading: {
                name: "Text",
                layouts: {
                    default: "Default",
                    caption: "Caption",
                },
                title: "Title",
                subtitle: "Subtitle",
                body: "Text",
                type: "Type",
                types: {
                    h1: "Heading 1",
                    h2: "Heading 2",
                    h3: "Heading 3",
                    h4: "Heading 4",
                    h5: "Heading 5",
                    h6: "Heading 6",
                    subtitle1: "Subtitle 1",
                    subtitle2: "Subtitle 2",
                    subtitle3: "Subtitle 3",
                    body1: "Body 1",
                    body2: "Body 2",
                    body3: "Body 3",
                    caption: "Caption",
                    overline: "Overline",
                    code: "Code",
                    p: "Paragraph",
                    span: "Span",
                    div: "Div",
                }
            },
            button: {
                name: "Button",
                layouts: {
                    button: "Button",
                    link: "Link",
                    icon: "Icon",
                },
                url: "URL",
                label: "Label",
                icon: {
                    title: "Icon",
                    helperText: "Icon name from Material Icons\nhttps://mui.com/material-ui/material-icons/",
                },
                iconPosition: {
                    title: "Icon Position",
                    start: "Start",
                    end: "End",
                    default: "Default",
                },
                variant: {
                    title: "Variant",
                    contained: "Contained",
                    outlined: "Outlined",
                    text: "Text",
                },
                color: {
                    title: "Color",
                    primary: "Primary",
                    secondary: "Secondary",
                    success: "Success",
                    error: "Error",
                    info: "Info",
                    warning: "Warning",
                    inherit: "Inherit",
                },
                size: {
                    title: "Size",
                    small: "Small",
                    medium: "Medium",
                    large: "Large",
                },
                target: {
                    title: "Target",
                    blank: "New Tab",
                    self: "Same Tab",
                    popup: "Popup",
                },
            },
            divider: {
                name: "Divider",
                layouts: {
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                orientation: "Orientation",
                orientations: {
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                flexItem: "Flex Item",
                flexItemHelper: "If checked, the divider will have the correct height when used in a flex container.",
                variant: "Variant",
                variants: {
                    fullWidth: "Full Width",
                    inset: "Inset",
                    middle: "Middle",
                },
                textAlign: "Text Alignment",
                aligns: {
                    left: "Left",
                    center: "Center",
                    right: "Right",
                },
            },
            countDown: {
                name: "Count Down",
                layouts: {
                    default: "Default",
                },
                dateTime: "Finish Date Time",
                variant: "Variant",
                timeUpMessage: "Time's up message",
                interval: "Interval",
                intervalType: "Interval Type",
                format: "Format",
                formats: {
                    long: "Long",
                    short: "Short",
                    shortest: "Shortest",
                },
            },
            list: {
                name: "List",
                layouts: {
                    default: "Default",
                },
                type: "Type",
                dense: "Dense",
                disablePadding: "Disable Padding",
                types: {
                    none: "None",
                    icon: "Icon",
                    avatar: "Avatar",
                    bullet: "● Bullet",
                    circle: "○ Circle",
                    square: "■ Square",
                    decimal: "1. Numbers",
                    decimalLeadingZero: "01. Numbers with Leading Zero",
                    lowerAlpha: "a. Lowercase Letters",
                    upperAlpha: "A. Uppercase Letters",
                    lowerRoman: "i. Lowercase Roman numbers",
                    upperRoman: "I. Uppercase Roman numbers",
                }
            },
            hero: {
                name: "Hero Block",
                layouts: {
                    centered: "Centered",
                    left: "Left",
                    right: "Right",
                },
                image: "Background Image",
                imageHeight: "Image Height",
                title: "Catchy Phrase",
                body: "Short Description",
                callToActionUrl: "Call to Action URL",
                callToActionText: "Call to Action Label",
            },
            gallery: {
                name: "Gallery",
                layouts: {
                    carousel: "Carousel",
                    list: "List",
                    masonry: "Masonry",
                },
                images: "Images",
                imageSize: "Image Size",
                objectPosition: "Image Position",
                objectFit: "Image Fit",
            },
            breadcrumb:{
                name: "Breadcrumbs",
                layouts: {
                    default: "Default",
                },
                separator: "Separator",
                maxItems: "Max Items",
                underline: "Underline",
                variant: "Variant",
                alignItems: "Align Items",
                underlines: {
                    none: "None",
                    hover: "Hover",
                    always: "Always",
                },
                aligns: {
                    center: "Center",
                    left: "Left",
                    right: "Right",
                },
            },
            events: {
                name: "Events",
                layouts: {
                    schedule: "Schedule",
                    month: "Month",
                    week: "Week",
                    day: "Day",
                },
                shopId: "Ecommerce Site",
                type: "Event type",
                types: {
                    upcoming: "Upcoming Events",
                    past: "Past Events",
                    all: "All Events",
                },
            },
            eventDetail: {
                name: "Event",
                layouts: {
                    default: "Default",
                },
                id: "Event ID",
                type: "Event Load",
                types: {
                    automatic: "Auto",
                    manual: "Manual",
                    helperText: "Auto loads the event from the URL slug. Manual loads it from the event selector.",
                },
            },
            products: {
                shopId: "Ecommerce Site",
                itemsToLoad: "Items to load at a time",
                layoutType: "Layout Type",
                details: "Details",
                fullPage: "Redirect to product page when an item is clicked",
                modalSize: "Modal Size",
                galleryLayoutType: "Gallery Layout",
                variantsLayoutType: "Variants Layout",
                addonsLayoutType: "Add-ons Layout",
                eventUsersLayoutType: "Event Users Layout",
                memoLayoutType: "Memo Layout",
                showCategories: "Show Categories",
                categories: "Categories",
            },
            productDetail: {
                name: "Product",
                layouts: {
                    default: "Default",
                },
                id: "Product ID",
                variantId: "Variant ID",
                type: "Product Load",
                types: {
                    automatic: "Auto",
                    manual: "Manual",
                    helperText: "Auto loads the product from the URL slug. Manual loads it from the product selector.",
                },
            },
            cart: {
                name: "Cart",
                layouts: {
                    list: "List",
                    table: "Table",
                },
                redirectToProductPage: "Redirect to product page when an item is clicked",
            },
            checkout: {
                name: "Checkout",
                layouts: {
                    default: "Default",
                },
                showTips: "Show Tips",
                showTotals: "Show Totals",
                totalItems: "Items in totals",
                paymentMethods: "Payment Methods",
                allowMultiplePayments: "Allow multiple payments",
                redirectToSuccess: "Redirect to success page after checkout",
            },
            posTotals: {
                name: "Totals",
                layouts: {
                    default: "Default",
                    sticky: "Sticky",
                },
                showTotals: "Show Totals",
                showButton: "Show Button",
                showCheckoutInModal: "Show Checkout in Modal",
            },
            posSuccess: {
                name: "Purchase Success",
                layouts: {
                    default: "Default",
                },
                showOrderPreview: "Show Order Preview",
                showTransactions: "Show Transactions",
                showPrintButton: "Show Print Button",
            },
            blogPost:{
                name: "Blog Post",
                layouts: {
                    default: "Default",
                },
                id: "Blog Post ID",
                type: "Blog Post Load",
                types: {
                    automatic: "Auto",
                    manual: "Manual",
                    helperText: "Auto loads the blog post from the URL slug. Manual loads it from the blog post selector.",
                },
            },
            pos: {
                sizes: {
                    extraSmall: "Extra Small",
                    small: "Small",
                    medium: "Medium",
                    large: "Large",
                    extraLarge: "Extra Large",
                },
                layoutTypes: {
                    card: "Cards",
                    list: "Lists",
                    grid: "Grid",
                    masonry: "Masonry",
                    button: "Buttons",
                    icon: "Icons",
                    link: "Links",
                    checkbox: "Checkboxes",
                    radio: "Radio Buttons",
                },
                orderNumber: {
                    buttonType: "Button Type",
                },
                product: {
                    items: "Items",
                    itemsToLoad: "Items to Load",
                    layoutType: "Layout Type",
                    details: "Details",
                    fullPage: "Full Page",
                    modalSize: "Modal Size",
                    gallery: "Gallery",
                    variants: "Variants",
                    addons: "Add-ons",
                    eventUsers: "Event Users",
                    memo: "Memo",
                },
                productFilter: {
                    ids: "IDs",
                    filterType: "Filter Type",
                    types: {
                        category: "Category",
                        type: "Product Type",
                    }
                },


            },
            customHtml: {
                name: "HTML",
                content: "HTML Content",
                layouts: {
                    default: "Default",
                },
            },
            menu: {
                name: "Menu",
                layouts: {
                    responsive: "Responsive",
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                fetchPages: "Add website pages to menu",
                title: "Title",
                url: "URL",
                showCart: "Show Cart",
                showProfile: "Show Profile",
                optionType: "Option Type",
                optionTypes: {
                    icon: "Icon",
                    menu: "Menu",
                },
                shopId: "Ecommerce Site",
                shopIdHelperText: "The ID of the shop site to use for this menu",
                customItems: "Custom Items",
            },
            video: {
                name: "Video",
                layouts: {
                    default: "Default",
                    videoOnly: "Video Only",
                },
                placeholderImage: "Placeholder Image",
                title: "Title",
                subtitle: "Subtitle",
                body: "Description",
                videoUrl: "Video URL",
                autoplay: "Autoplay",
                muted: "Muted",
                controls: "Show Controls",
                ctas: "Calls to Action",
                button: "Button",
            },
        },
    },

    breakpoints: {
        xs: "Extra Small",
        sm: "Small",
        md: "Medium",
        lg: "Large",
        xl: "Extra Large",
    },

    file: {
        dropFileOrClick: "Drop a file or click here...",
        dropImageOrClick: "Drop an image or click here...",
        dropVideoOrClick: "Drop a video or click here...",
        dropAudioOrClick: "Drop an audio file or click here...",
        dropDocumentOrClick: "Drop a document or click here...",
        dropFile: "Drop a file here...",
        dropImage: "Drop an image here...",
        dropVideo: "Drop a video here...",
        dropAudio: "Drop an audio file here...",
        dropDocument: "Drop a document here...",
        uploadFile: "Upload file",
        uploadFiles: "Upload files",
        upload: "Upload",
        browse: "Browse",
        download: "Download",
        preview: "Preview",
        file: "File",
        files: "Files",
        size: "Size",
        type: "Type",
        allowed: "Allowed",
        maxSize: "Max size",
        photo: "Photo",
        photos: "Photos",
        image: "Image",
        images: "Images",
        video: "Video",
        videos: "Videos",
        audio: "Audio",
        audios: "Audios",
        document: "Document",
        documents: "Documents",
        pdf: "PDF",
        logo: "Logo",
    },

    icon: {
        iconStyle: "Icon Style",
        styles: {
            filled: "Filled",
            outlined: "Outlined",
            rounded: "Rounded",
            twoTone: "Two Tone",
            sharp: "Sharp",
        },
        icon: "Icon",
        icons: "Icons",
        empty: "No icons found",
        changeIcon: "Change Icon",
        addIcon: "Add Icon",
        removeIcon: "Remove Icon",
    },

    calendar: {
        calendar: "Calendar",
        today: "Today",
        month: "Month",
        week: "Week",
        day: "Day",
        year: "Year",
        schedule: "Schedule",
        event: "Event",
        events: "Events",
        newEvent: "New Event",
        editEvent: "Edit Event",
        title: "Title",
        startDate: "Start Date",
        endDate: "End Date",
        startTime: "Start Time",
        endTime: "End Time",
        allDay: "All Day",
        repeat: "Repeat",
        to: "to",
        upTo: "Up to",
        andUp: "and up",
        ageRequirement: "Age Requirement",
        age: "Age",
        yearsOld: "years old",
        requiresRegistration: "Requires Registration",
        requiresMembership: "Requires Membership",
        noRegistration: "No registration required",
        noMembership: "No membership required",
        signUp: "Sign Up",
        eventFee: "Event Fee",
        startingFrom: "Starting from",
        where: "Where",
        when: "When",
        noEvents: "No events found",
    },

    pos: {
        preferences: "Preferences",
        item: "Item",
        price: "Price",
        quantity: "Quantity",
        add: "Add",
        addToCart: "Add to Cart",
        saveCart: "Save Cart",
        addons: "Add-ons",
        variants: "Variants",
        relatedProducts: "Related Products",
        priceStartingFrom: "Starting from",
        total: "Total",
        tax: "Tax",
        subtotal: "Subtotal",
        priceAdjustments: "Price Adjustments",
        tip: "Tip",
        noTip: "No Tip",
        discount: "Discount",
        selectDiscounts: "Select Discounts",
        noDiscounts: "No discounts available",
        payment: "Payment",
        payments: "Payments",
        paymentType: "Payment Type",
        balance: "Balance",
        paymentMethod: "Payment Method",
        endingIn: "ending in",
        shipping: "Shipping",
        amount: "Amount",
        customAmount: "Custom Amount",
        tendered: "Tendered",
        change: "Change",
        memo: "Memo",
        processPartialPayment: "Process Partial Payment",
        processPayment: "Process Payment",
        processDiscount: "Process Discount",
        void: "Void",
        refund: "Refund",
        return: "Return",
        closeOrder: "Close Order",
        clearUser: "Clear User",
        changeUser: "Change User",
        newUser: "New User",
        selectUser: "You must select a user fist",
        checkout: "Checkout",
        viewCart: "View Cart",
        payNow: "Pay Now",
        searchOrders: "Search Orders",
        registerReports: "Register Reports",
        register: "Register",
        openOrders: "Open Orders",
        print: "Print",
        printReceipt: "Print Receipt",
        printKitchenTicket: "Print Kitchen Ticket",
        printTicket: "Print Ticket",
        printFullPage: "Print Full Page",
        forUser: "For User",
        plusTax: "Plus Tax",
        whoWillRegisterForEvent: "Who will register for the event?",
        whoWillReceiveGiftCard: "Who will receive the gift card?",
        fullPrice: "Full Price",
        itemMemo: "Special Instructions",
        checkIn: "Check In",
        lastCheckIn: "Last Check In",
        editProfile: "Edit Profile",
        order: {
            temporary: "Temporary number until an item is added",
            toolbar: {
                openOrders: "Open Orders",
                printOrder: "Print Order",
                closeOrder: "Close Order",
            },
        },
        paymentMethods: {
            creditCard: "Credit Card",
            scanCard: "Scan Card",
            cash: "Cash",
            check: "Check",
            giftCard: "Gift Card",
            managerDiscount: "Manager Discount",
            token: "Token",
            walletBalance: "Wallet Balance",
        },
        warnings: {
            removeCashDiscount: "Processing this payment will remove the cash discount of {{amount}}",
            forUserAlert: "Items in the cart that are linked to the selected user will be removed.\n\nDo you want to continue?",
        },
        success: {
            thankYou: "Thank you!",
            continueShopping: "Continue Shopping",
            description: "Your order has been placed successfully.\nYou will receive an email confirmation shortly.",
        },
    },

    giftCard: {
        addRecipient: "Add Recipient",
        recipient: "Gift Card Recipient",
        recipientFullName: "Recipient Name",
        recipientEmail: "Recipient Email",
        deliveryDate: "Delivery Date",
        message: "Message",
        customAmount: "Custom Amount",
        code: "Gift Card Code",
        invalidCode: "Invalid Gift Card Code",
        balance: "Balance",
    },

    check: {
        number: "Check Number",
        name: "Name on Check",
        bank: "Bank",
        routing: "Routing Number",
        account: "Account Number",
        accountType: "Account Type",
        types: {
            checking: "Checking",
            savings: "Savings",
        },
        front: "Front of Check",
        back: "Back of Check",
    },

    creditCard: {
        cardNumber: "Card Number",
        expiration: "Expiration",
        cvv: "CVV",
        zip: "Zip Code",
        cardHolder: "Name on Card",
        cardType: "Card Type",
        types: {
            visa: "Visa",
            mastercard: "Mastercard",
            amex: "American Express",
            discover: "Discover",
        },
        gatewayTransaction: "Gateway Transaction ID",
        gatewayNotConfigured: "Credit card gateway is not configured",
        invalidCard: "Invalid credit card",
        invalidField: "Invalid value",
    },

    tag: {
        tag: "Tag",
        tags: "Tags",
        newTag: "New Tag",
        editTag: "Edit Tag",
        search: "Search Tags",
        empty: "No tags found",
        name: "Tag Name",
        description: "Description",
        deleteTitle: "Delete Tag(s)",
        deleteMessage: "Are you sure you want to delete the selected tag(s)?",
        saved: "Tag saved successfully",
        deleted: "Tag deleted successfully",
        updated: "Tag updated successfully",
        created: "Tag created successfully",
        cloud: "Tag Cloud",
    },

    category: {
        category: "Category",
        categories: "Categories",
        newCategory: "New Category",
        editCategory: "Edit Category",
        search: "Search Categories",
        empty: "No categories found",
        name: "Category Name",
        description: "Description",
        slug: "Slug",
        sortOrder: "Sort Order",
        parentId: "Parent Category",
        deleteTitle: "Delete Categories",
        deleteMessage: "Are you sure you want to delete the selected categories?",
        saved: "Category saved successfully",
        deleted: "Category deleted successfully",
        updated: "Category updated successfully",
        created: "Category created successfully",
    },

    error: {
        default: "An error occurred. Please try again later",
        simple: "An error occurred",
        required: "This field is required",
        conflict: "There is a conflict with the data you entered",
        codeError: "There is a code error",
        invalid: "The value entered is invalid",
        tryAgain: "Try again",
        retry: "Retry",
        noConnection: "No connection",
        connectionError: "Connection error",
        accessDenied: "Access denied",
        notFound: "The resource was not found",
        serverError: "The server encountered an error",
        unauthorized: "You are not authorized to access this resource",
        forbidden: "You don't have permission to access this resource",
        invalidFileType: "Invalid file type",
        invalidFileSize: "Invalid file size",
        unknownError: "Unknown error",
        invalidDateRange: "The date range is not valid",
        invalidTimeRange: "The time range is not valid",
        invalidEmail: "Invalid email address",
        invalidPhone: "Invalid phone number",
        invalidPassword: "Invalid password",
        weakPassword: "Password is too weak",
        invalidUsername: "Invalid username",
        userNameTaken: "Username is not available",
        oops: "Oops!",
        backHome: "Back to Home",
        exists: "The value entered already exists",
        viewErrorDetails: "View Error Details",
        contactUs: "An error occurred. Please contact us for assistance",
    },

    success: {
        default: "Success",
        saved: "Data saved successfully",
        deleted: "Deleted successfully",
        updated: "Data updated successfully",
        created: "Created successfully",
        sent: "Sent successfully",
    },

    aiChat: {
        you: "You",
        send: "Send",
        message: "Message",
        scrollBottom: "Scroll to bottom",
        isTyping: "is typing...",
    },

    widget: {
        addWidget: "Add Widget",
        settings: "Widget Settings",
        noSettings: "No settings available for this widget",
        gridConfig: "Grid Configuration",
        exitEditMode: "Exit Edit Mode",

        analytics: {
            title: "Analytics",
            day: "Day",
            week: "Week",
            month: "Month",
            visitors: "Visitors",
            pageViews: "Page Views"
        },
        users: {
            title: "Users",
            showActive: "Active",
            showInactive: "Inactive",
            roleDistribution: "Role Distribution"
        },
        sites: {
            title: "Websites",
            status: "Status",
            active: "Active",
            inactive: "Inactive"
        },
        inventory: {
            title: "Inventory",
            showLowStock: "Low Stock",
            showOutOfStock: "Out of Stock",
            items: "Items"
        },
        subscriptions: {
            title: "Subscriptions",
            showExpiring: "Show Expiring",
            active: "Active",
            expired: "Expired",
            expiringSoon: "Expiring Soon"
        },
        tasks: {
            title: "Tasks",
            all: "All",
            highPriority: "High Priority",
            today: "Today",
            complete: "Complete",
            noTasks: "No tasks found",
            noTasksDescription: "There are no tasks matching your filters"
        },
        notifications: {
            title: "Notifications",
            noNotifications: "No notifications"
        },
        editWidget: "Edit Widget",
        enterEditMode: "Enter Edit Mode",
        width: "Width",
        height: "Height",
        size: "Size",
        weather:{
            title: "Weather",
            weatherIn: "Weather in",
            location: "Location",
            temperature: "Temperature",
            units: "Units",
        },
        sales: {
            title: "Sales",
            total: "Total",
            date: "Date",
            type: "Type",
            chartTypes: {
                line: "Line",
                bar: "Bar",
                pie: "Pie",
                doughnut: "Doughnut",
                radar: "Radar",
                polar: "Polar",
                bubble: "Bubble",
                scatter: "Scatter",
                area: "Area",
            }
        },
        calendar: {
            title: "Calendar",
            type: "Type",
            calendarTypes: {
                schedule: "Schedule",
                day: "Day",
                week: "Week",
                month: "Month",
                year: "Year",
            },
        },
    },

    media: {
        empty: "No files found for this media type",
        types: {
            image: "Image",
            video: "Video",
            audio: "Audio",
            document: "Document",
            logo: "Logo",
            waiver: "Waiver",
            theme: "Theme Media",
            other: "Other"
        },
    },

    measurement: {
        width: "Width",
        height: "Height",
        length: "Length",
        depth: "Depth",
        weight: "Weight",
        volume: "Volume",
        area: "Area",
        radius: "Radius",
        diameter: "Diameter",
        kg: "kg",
        mg: "mg",
        mm: "mm",
        cm: "cm",
        m: "m",
        km: "km",
        g: "g",
        gr: "gr",
        oz: "oz",
        ml: "ml",
        l: "l",
        lb: "lb",
        ft: "ft",
        yd: "yd",
        in: "in",
        mi: "mi",
        ac: "ac",
    },

    position: {
        top: "Top",
        bottom: "Bottom",
        left: "Left",
        right: "Right",
        center: "Center",
        start: "Start",
        end: "End",
        default: "Default",
        topCenter: "Top Center",
        topLeft: "Top Left",
        topRight: "Top Right",
        bottomCenter: "Bottom Center",
        bottomLeft: "Bottom Left",
        bottomRight: "Bottom Right",
        centerLeft: "Center Left",
        centerRight: "Center Right",
    },

    fit: {
        cover: "Cover",
        contain: "Contain",
        fill: "Fill",
        scaleDown: "Scale Down",
        none: "None",
    },

    markdown: {
        type: "Type",
        toGet: "To Get",
        useSample: "Use a sample",
        backticks: "with backticks",
        codeBlock: "3 backticks (and optionally the language to highlight)",
        h1: "Latest Sports News",
        h2: "Football",
        h3: "Match Highlights",
        h4: "Top Goals",
        italic: "Offside Rule",
        bold: "Training Schedule",
        link: "Click here!",
        image: "Image description",
        list1: "Banana",
        list2: "Strawberry",
        list3: "Dairy",
        list4: "Yogurt",
        list5: "Milk",
        divider: "Horizontal divider",
        code: "Inline code",
        header1: "Item",
        header2: "Description",
        headerHelp: "Use a : at the beginning or end of the cell to align the content",
        row1: "Row 1",
        row2: "Row 2",
        check1: "Eat Breakfast",
        check2: "Workout"
    },

    billing: {
        billingAndInvoices: "Billing & Invoices",
        myInvoices: "My Invoices",
        customerInvoices: "Customer Invoices",
        paymentMethods: "Payment Methods",
        downloadStatement: "Download Statement",
        createInvoice: "Create Invoice",
        addPaymentMethod: "Add Payment Method",
        invoiceNumber: "Invoice #",
        date: "Date",
        amount: "Amount",
        status: "Status",
        actions: "Actions",
        view: "View",
        pay: "Pay",
        edit: "Edit",
        customer: "Customer",
    },

    customers: {
        customerManagement: "Customer Management",
        customers: "Customers",
        addCustomer: "Add Customer",
        customerName: "Customer Name",
        contactPerson: "Contact Person",
        email: "Email",
        phone: "Phone",
        status: "Status",
        actions: "Actions",
        view: "View",
        edit: "Edit",
        customerDetails: "Customer Details",
        contactInformation: "Contact Information",
        billingInformation: "Billing Information",
        recentActivity: "Recent Activity",
        editCustomer: "Edit Customer",
        createInvoice: "Create Invoice",
    },

    carriers: {
        carrierConfigurations: "Carrier Configurations",
        carriers: "Carriers",
        addCarrier: "Add Carrier",
        rateTables: "Rate Tables",
        addRateTable: "Add Rate Table",
        serviceOptions: "Service Options",
        configureOptions: "Configure Options",
        carrier: "Carrier",
        services: "Services",
        status: "Status",
        lastUpdated: "Last Updated",
        actions: "Actions",
        configure: "Configure",
        active: "Active",
        inactive: "Inactive",
        name: "Name",
        effectiveDate: "Effective Date",
        expirationDate: "Expiration Date",
        globalSettings: "Global Settings",
    },

    marketing: {
        marketingTools: "Marketing & Communication Tools",
        emailCampaigns: "Email Campaigns",
        smsNotifications: "SMS Notifications",
        templates: "Templates",
        analytics: "Analytics",
        createCampaign: "Create Campaign",
        configureSMS: "Configure SMS",
        createTemplate: "Create Template",
        exportReport: "Export Report",
        campaignName: "Campaign Name",
        status: "Status",
        sentDate: "Sent Date",
        performance: "Performance",
        actions: "Actions",
        view: "View",
        edit: "Edit",
        duplicate: "Duplicate",
        smsUsage: "SMS Usage",
        cost: "Cost",
        messagesSent: "Messages sent this month",
        currentMonthCharges: "Current month charges",
        activeSMSCampaigns: "Active SMS Campaigns",
        messageTemplates: "Message Templates",
        templateName: "Template Name",
        type: "Type",
        lastUsed: "Last Used",
        marketingAnalytics: "Marketing Analytics",
        performanceOverview: "Performance Overview",
    },

    service: {
        services: "Services",
        service: "Service",
        serviceDetails: "Service Details",
        newService: "New Service",
        editService: "Edit Service",
        search: "Search Services",
        name: "Name",
        description: "Description",
        shortDescription: "Short Description",
        price: "Price",
        duration: "Duration",
        status: "Status",
        active: "Active",
        inactive: "Inactive",
        locations: "Locations",
        noLocations: "No locations selected",
        managers: "Managers",
        noManagers: "No managers selected",
        bookingNotice: "Booking Notice",
        minutes: "minutes",
        hours: "hours",
        days: "days",
        startDate: "Start Date",
        endDate: "End Date",
        availability: "Availability",
        id: "ID",
        deleteConfirmTitle: "Delete Service",
        deleteConfirmMessage: "Are you sure you want to delete the service '{name}'?",
        deleteSuccess: "Service deleted successfully",
        success: "Service saved successfully",

        wizard: {
            name: {
                title: "Name & Description",
                subtitle: "Basic service information",
                description: "Enter the basic information about the service",
                label: "Service Name"
            },
            shortDescription: {
                label: "Short Description"
            },
            description: {
                label: "Full Description"
            },
            availability: {
                title: "Availability",
                subtitle: "Service availability period",
                description: "Set the time period when this service is available",
                hasStartDate: "Set a start date",
                startDate: "Start Date",
                hasEndDate: "Set an end date",
                endDate: "End Date"
            },
            increments: {
                title: "Time Increments",
                subtitle: "Service booking time settings",
                description: "Configure how time slots work for this service",
                blockMinutes: "Block Minutes",
                minTimeslots: "Minimum Timeslots",
                maxTimeslots: "Maximum Timeslots",
                timeslotsForToken: "Timeslots Per Token",
                minBookingNotice: "Minimum Booking Notice (minutes)"
            },
            location: {
                title: "Locations",
                subtitle: "Where the service is available",
                description: "Select the locations where this service is offered",
                locations: "Service Locations"
            },
            manager: {
                title: "Managers",
                subtitle: "Service managers",
                description: "Select the staff who manage this service",
                managers: "Service Managers"
            },
            payment: {
                title: "Payment Options",
                subtitle: "Service pricing and tokens",
                description: "Configure payment options for this service",
                defaultPrice: "Default Price",
                defaultTokenName: "Token Name"
            },
            cancellation: {
                title: "Cancellation Policy",
                subtitle: "Service cancellation rules",
                description: "Set the cancellation policy for this service",
                policy: "Cancellation Policy",
                hours: "Cancellation Notice (hours)"
            },
            summary: {
                title: "Summary",
                subtitle: "Review service details",
                description: "Review all service details before saving",
                statusLabel: "Service Status",
                active: "Active",
                inactive: "Inactive"
            }
        }
    },

    services: {
        serviceStatus: "Service Status",
        systemStatus: "System Status",
        activeIncidents: "Active Incidents",
        allSystemsOperational: "All Systems Operational",
        operationalServices: "Operational Services",
        degradedServices: "Degraded Services",
        servicesWithIncidents: "Services with Incidents",
        investigating: "Investigating",
        monitoring: "Monitoring",
        operational: "Operational",
        degradedPerformance: "Degraded Performance",
        incident: "Incident",
        service: "Service",
        uptime: "Uptime",
        lastIncident: "Last Incident",
        updates: "Updates",
        started: "Started",
    },

    product: {
        product: "Product",
        products: "Products",
        productDetails: "Product Details",
        newProduct: "New Product",
        editProduct: "Edit Product",
        search: "Search Products",
        name: "Name",
        description: "Description",
        shortDescription: "Short Description",
        longDescription: "Long Description",
        labelDescription: "Label Description",
        price: "Price",
        retailPrice: "Retail Price",
        cost: "Cost",
        sku: "SKU",
        altSku: "Alt SKU",
        changeSku: "Change SKU",
        addSku: "Add SKU",
        changeSkuWarning: "Changing a SKU is a significant action. This should only be done when absolutely necessary.",
        confirmChangeSku: "I understand and confirm this change",
        barcode: "Barcode",
        category: "Category",
        productType: "Type",
        variants: "Variants",
        addOns: "Add-ons",
        notFound: "Product not found",
        brand: "Brand",
        model: "Model",
        color: "Color",
        size: "Size",
        weight: "Weight",
        dimensions: "Dimensions",
        images: "Images",
        selectImages: "Select Images",
        deleteTitle: "Delete Product(s)",
        deleteMessage: "Are you sure you want to delete the selected product(s)?",
        success: "Product saved successfully",
        deleted: "Product deleted successfully",
        slotCount: "Slot Count",
        slotEquivalent: "Slot Equivalent",
        skuList: "SKU List",
        inventory: "Inventory",
        stockQuantity: "Stock Quantity",
        reservedQuantity: "Reserved Quantity",
        availableQuantity: "Available Quantity",
        lowStockThreshold: "Low Stock Threshold",

        toolbar: {
            basic: "Product Info",
            descriptions: "Detailed descriptions",
            properties: "Properties",
            handling: "Handling",
        },

        productTypes: {
            title: "Product Types",
            all: "All product types",
            subscription: "Subscription",
            physical: "Physical",
            digital: "Digital",
            service: "Service",
            event: "Event",
            bundle: "Bundle",
            rental: "Rental",
            foodDrink: "Food & Drink",
            token: "Token",
            cancellationFee: "Cancellation Fee",
            featureProduct	: "Feature Product",
            giftCard: "Gift Card",
            wine: "Wine",
            kit: "Kit",
            merchandise: "Merchandise",
            collateral: "Collateral",
            food: "Food",
            speciality: "Speciality",
        },

        productCategories: {
            title: "Categories",
            all: "All categories",
        },

        wine: {
            type: "Type",
            vintage: "Vintage",
            origin: "Origin",
            vineyard: "Vineyard",
            cola: "COLA #",
            slotCount: "Slot Count",
            slotEquivalent: "Slot Equivalent",
            alcoholByVolume: "Alcohol by Volume",
            varietal: "Varietal",
            bottleSize: "Bottle Size",
            registrationNumber: "Registration Number",
            printOnPacking: "Print on Packing",
            types: {
                still: "Still",
                nonAlcoholic: "Non-Alcoholic",
                sparkling: "Sparkling",
                fortified: "Fortified",
            },
        },
        merchandise: {
            shipAlone: "Ship Alone",
        },
        collateral: {
            type: "Type",
        },
        food: {
            type: "Type",
            origin: "Origin",
            perishable: "Perishable",
            refrigeration: "Refrigeration",
        },
        speciality: {
            type: "Type",
        },
        kit: {
            type: "Type",
        },

        handling: {
            title: "Handling Parameters",
            container: "Container",
            backorderWarning: "Backorder Warning",
            serviceFee: "Service Fee",
            amountPerContainer: "Amount per Container",
            handlingFee: "Handling Fee",
            fulfillmentFee: "Fulfillment Fee",
        },
    },

    merchant: {
        merchant: "Merchant",
        merchants: "Merchants",
        merchantDetails: "Merchant Details",
        newMerchant: "New Merchant",
        editMerchant: "Edit Merchant",
        createMerchant: "Create Merchant",
        updateMerchant: "Update Merchant",
        search: "Search Merchants",
        code: "Code",
        codeHelperText: "5-character merchant code",
        name: "Name",
        email: "Email",
        phone: "Phone",
        contact: "Contact",
        type: "Type",
        addressLine1: "Address Line 1",
        addressLine2: "Address Line 2",
        location: "Location",
        city: "City",
        state: "State",
        zipcode: "Zip Code",
        country: "Country",
        notes: "Notes",
        primary: "Make Primary",
        deleteTitle: "Delete Merchant(s)",
        deleteMessage: "Are you sure you want to delete the selected merchant(s)?",
        success: "Merchant saved successfully",
        updated: "Merchant updated successfully",
        saved: "Merchant saved successfully",
        deleted: "Merchant deleted successfully",
        toolbar: {
            basic: "Basic Information",
        },
        types: {
            producer: "Producer",
            retailer: "Retailer",
            marketer: "Marketer",
            club: "Club",
            ecommerce: "E-commerce",
        },
        tabs: {
            info: "Info",
            setup: "Setup",
            contacts: "Contacts",
            licenses: "Licenses",
            billing: "Billing",
            brands: "Brands",
            shipping: "Shipping",
            packaging: "Packaging",
            email: "Email",
        },
        // License-related translations
        addLicense: "Add License",
        editLicense: "Edit License",
        licenseState: "License State",
        licenseBrand: "License Brand",
        licenseName: "License Number",
        licenseExpirationDate: "Expiration Date",
        allowShipperManualUpload: "Allow Shipper Manual Upload",
        enableWeightBasedQA: "Enable Weight Based QA",
        renameOrderOnCancel: "Rename Order On Cancel",
        rejectUnknownSKUs: "Reject Unknown SKUs",
        orderFileFormat: "Order File Format",
        // Contact-related translations
        addContact: "Add Contact",
        editContact: "Edit Contact",
        contactFirstName: "First Name",
        contactLastName: "Last Name",
        contactTitle: "Title",
        contactDepartment: "Department",
        contactEmail: "Email",
        contactPhone: "Phone",
        contactMobile: "Mobile",
        contactFax: "Fax",
        contactRole: "Role",
        contactPreferredMethod: "Preferred Contact Method",
        contactNotes: "Notes",
        contactRoles: {
            primary: "Primary",
            billing: "Billing",
            shipping: "Shipping",
            technical: "Technical",
            sales: "Sales",
            support: "Support",
        },
        contactMethods: {
            email: "Email",
            phone: "Phone",
            mobile: "Mobile",
            fax: "Fax",
        },
        contactType: "Contact Type",
        contactTypes: {
            billing: "Billing",
            inventory: "Inventory",
            club: "Club",
            business: "Business",
            other: "Other",
        },        
        billingMethod: "Billing Method",
        billingFrequency: "Billing Frequency",
        sendTo: "Send To",
        groupBy: "Group By",
        creditLimit: "Credit Limit",
        billingMethods: {
            dropInMail: "Drop In Mail",
            email: "Email",
            fax: "Fax",
            print: "Print",
            webPortal: "Web Portal",
        },
        billingFrequencies: {
            weekly: "Weekly",
            monthly: "Monthly",
            biMonthly: "Bi-monthly",
            never: "Never",
        },
        billingTerms: {
            title: "Billing Terms",
            net7: "NET 7",
            net14: "NET 14",
            net15: "NET 15",
            net30: "NET 30",
            net60: "NET 60",
            net90: "NET 90",
        },
        sendToOptions: {
            business: "Business",
            billing: "Billing",
            other: "Other",
        },
        groupByOptions: {
            manifestLocation: "Manifest Location",
            brandName: "Brand Name",
            fulfillmentCenter: "Fulfillment Center",
            orderType: "Order Type",
        },
        addBrand: "Add Brand",
        editBrand: "Edit Brand",
        brandName: "Brand Name",
        brandCode: "Brand Code",
        brandNotes: "Brand Notes",
        brandPhone: "Brand Phone",
        brandEmail: "Name for Branded Emails",
        brandEmailName: "Email for Branded Emails",
    },

    emailTemplates: {
        title: "Email Templates",
        configuration: "Configuration",
        addEmailTemplate: "Add Email Template",
        editEmailTemplate: "Edit Email Template",
        noEmailTemplates: "No email templates found",
        orderType: "Order Type",
        emailSubject: "Email Subject",
        bcc: "BCC",
        template: "Template",
        fromEmailAddress: "From Email Address",
        fromName: "From Name",
        ccRecipient: "CC Recipient",
        bccRecipient: "BCC Recipient",
        templateName: "Template Name",
        templateType: "Template Type",
        status: "Status",
        smtpConfiguration: "SMTP Configuration",
        smtpServer: "SMTP Server",
        smtpPort: "SMTP Port",
        smtpUsername: "SMTP Username",
        smtpPassword: "SMTP Password",
        emailTemplateTypes: {
            processed: "Processed",
            shipped: "Shipped",
            outForDelivery: "Out for Delivery",
            deliveryAttempt: "Delivery Attempt",
            exception: "Exception",
            returned: "Returned",
            delivered: "Delivered",
        },        
    },

    geographic: {
        country: "Country",
        state: "State",
        city: "City",
        county: "County",
        area: "Area",
        neighborhood: "Neighborhood",
        latitude: "Latitude",
        longitude: "Longitude",
    },
};