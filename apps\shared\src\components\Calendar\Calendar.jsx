import React, { useEffect } from 'react';
import { Container } from '@mui/material';

import MetaEvents from './MetaEvents';
import Header from './Header';
import Month from './Month';
import Week from './Week'; // this is also used for day view, or 3-day view and the like
import Year from './Year';
import Schedule from './Schedule';
import { useCalendar } from './useCalendar';

export const Calendar = ({
    events, // an object that holds events and metaEvents ({events: [], metaEvents: []})
    type = 'schedule', // the type of calendar 'week', 'day', 'month', 'year', or 'schedule'
    selectedDate, // the selected date (Date object)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onDateChange = () => {}, // function to handle date changes
    onSearchTextChange = () => {}, // function to handle search text
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided)
    hideSearchBar, // whether to hide the search bar
    hideCalendarType, // whether to hide the calendar type selection
    ...props // additional props
}) => {

    const {
        currentDate,
        calendarType,
        setCalendarType,
        showSearch,
        handlePrev,
        handleNext,
        handleToday,
        handleSearchTextChange,
        calendarProps,
    } = useCalendar({
        events,
        type,
        selectedDate,
        loading,
        disabled,
        onDateChange,
        onSearchTextChange,
        onEventClick,
        hideSearchBar,
        hideCalendarType,
    });

    if (!events || !currentDate) return null;

    return (
        <Container disableGutters>
            <Header 
                currentDate={currentDate} 
                onPrev={handlePrev} 
                onNext={handleNext} 
                onToday={handleToday}
                calendarType={calendarType}
                onCalendarTypeChange={setCalendarType}
                onSearchTextChange={handleSearchTextChange}
                loading={loading}
                disabled={disabled}
                showSearchBar={showSearch}
                showCalendarType={!hideCalendarType}
            />

            <MetaEvents {...calendarProps} calendarType={calendarType} metaEvents={events?.metaEvents || []} />

            {calendarType === 'month' && <Month {...calendarProps} />}
            {(calendarType === 'week' || calendarType === 'day') && <Week type={calendarType} {...calendarProps} />}
            {calendarType === 'year' && <Year {...calendarProps} />}
            {calendarType === 'schedule' && <Schedule {...calendarProps}/>}
        </Container>
    );
};