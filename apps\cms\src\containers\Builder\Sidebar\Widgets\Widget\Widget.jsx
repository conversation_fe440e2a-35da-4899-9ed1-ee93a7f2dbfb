import React, { Suspense, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { toCamelCase } from '@siteboss-frontend/shared/utils';
import { Stack, Box, Button, ToggleButton, Typography } from '@mui/material';
import { HeartBrokenOutlined as EmptyIcon } from '@mui/icons-material';

export const Widget = ({component, onAddWidget, disabled}) => {
    const { t } = useOutletContext();    

    const WidgetIcon = useMemo(() => {
        return component?.widgetIcon || EmptyIcon;
    }, [component]);

    return (
        <ToggleButton component={Button} value={component.id} onClick={e => onAddWidget({component})} sx={{py: 0.5}} disabled={disabled}>
            <Box sx={{color: theme => theme.palette.text.primary, width: 80, lineHeight: 1.2, pb: 1}}>
                <Stack alignItems="center" justifyContent="center" sx={{minHeight: 48, minWidth: 48}}>
                    <Suspense fallback="...">
                        {WidgetIcon 
                            ? <WidgetIcon sx={{color: theme => theme.palette.text.secondary}}/>
                            : <EmptyIcon sx={{color: theme => theme.palette.text.secondary}}/>
                        }
                    </Suspense>
                </Stack>
                <Typography variant="subtitle3">
                    {t(`builder:component.${toCamelCase(component.name)}.name`, component.name)}
                </Typography>
            </Box>
        </ToggleButton>
    );
}