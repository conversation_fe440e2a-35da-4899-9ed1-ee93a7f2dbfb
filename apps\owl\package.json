{"name": "@siteboss-frontend/owl", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_76ef7f5ea8e78bb"}, "dependencies": {"@siteboss-frontend/shared": "*", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@chromatic-com/storybook": "^1.6.1", "@storybook/addon-designs": "^8.2.0", "@storybook/addon-essentials": "^8.5.7", "@storybook/addon-interactions": "^8.5.7", "@storybook/addon-links": "^8.5.7", "@storybook/addon-onboarding": "^8.5.7", "@storybook/blocks": "^8.5.7", "@storybook/react": "^8.5.7", "@storybook/react-vite": "^8.5.7", "@storybook/test": "^8.5.7", "@storybook/addon-a11y": "^8.5.7", "storybook": "^8.5.7", "eslint-plugin-storybook": "^0.8.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@vitejs/plugin-react": "^4.3.4", "@welldone-software/why-did-you-render": "^8.0.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prop-types": "^15.8.1", "vite": "^5.4.11"}}