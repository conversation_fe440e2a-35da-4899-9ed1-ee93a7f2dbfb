import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MenuList, MenuItem, Box, Drawer } from '@mui/material';
import { flattenTree } from '../.././../../../utils';

export const List = ({ 
    variant = "selectedMenu",
    dense = true,
    indented = true,
    options = [], 
    loading,
    disabled,
    option = {},  // MUI MenuItem props
    children,
    allSlug = "product:productCategories.all", 
    onSelect,
    reset = false,
    isBuilder,
    selectedId,
    ...props 
}) => {
    const {t} = useTranslation();

    const allOptions = useMemo(() => {
        if (!options || reset) return [];
        return flattenTree(JSON.parse(JSON.stringify(options)));
    }, [options, reset]);

    const [value, setValue] = useState(selectedId || "");

    const handleClick = useCallback(e => {
        setValue(e.target.value);
        if (onSelect) onSelect(e.target.value);
    }, [onSelect]);

    useEffect(() => {
        if (reset) setValue("");
    }, [reset]);

    return (
        <Drawer
            sx={{
                width: theme => theme.sizes.menuWidth,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: theme => theme.sizes.menuWidth,
                    boxSizing: 'border-box',
                },
            }}
            variant={isBuilder ? "temporary" : "persistent"}
            anchor="left"
            open
        >
            <MenuList {...props} dense={dense} variant={variant}>
                {allOptions.length > 1 && 
                    <MenuItem 
                        value={""} 
                        selected={!value} 
                        onClick={handleClick}
                        disabled={disabled || loading}
                        {...option}
                    >
                        {t(allSlug)}
                    </MenuItem>
                }
                {allOptions.map((option, i) => (
                    <MenuItem 
                        key={option.id || i} 
                        value={option.id} 
                        selected={value === option.id} 
                        onClick={handleClick} 
                        disabled={disabled || loading}
                    >
                        <Box component="span" sx={{ml: (indented ? option.depth : 0) * 2, fontWeight: option?.children?.length > 0 ? theme => theme.typography.fontWeightBold : undefined}}>
                            {t(option?.slug) || option?.label || option?.name || option}
                        </Box>
                    </MenuItem>
                ))}
            </MenuList>
            {children}
        </Drawer>
    );
}