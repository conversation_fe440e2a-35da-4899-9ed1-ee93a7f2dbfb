import React, { useState, useEffect, useRef } from 'react';
import { intervalToDuration, isPast } from 'date-fns';
import { Stack, Typography, useMediaQuery } from '@mui/material';

import { usePortalIframe } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { toCamelCase } from '../../../utils';

import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

const intervalMilliseconds = {
    seconds: 1000,
    minutes: 60 * 1000,
    hours: 60 * 60 * 1000,
    days: 24 * 60 * 60 * 1000,
    weeks: 7 * 24 * 60 * 60 * 1000,
    months: 30 * 24 * 60 * 60 * 1000,
    years: 365 * 24 * 60 * 60 * 1000,
};

const isAfter = (intervalType1, intervalType2) => {
    switch(intervalType1){
        case "seconds":
            return intervalType2 !== "seconds";
        case "minutes":
            return intervalType2 === "hours" || intervalType2 === "days" || intervalType2 === "weeks" || intervalType2 === "months" || intervalType2 === "years";
        case "hours":
            return intervalType2 === "days" || intervalType2 === "weeks" || intervalType2 === "months" || intervalType2 === "years";
        case "days":
            return intervalType2 === "weeks" || intervalType2 === "months" || intervalType2 === "years";
        case "weeks":
            return intervalType2 === "months" || intervalType2 === "years";
        case "months":
            return intervalType2 === "years";
        default:
            return false;
    }
}

const getSlug = ({id, count, format = "shortest"}) => {
    let slug = "general:time.";
    if (format === "long") slug += count === 1 ? id.substr(0, id.length - 1) : id;
    else slug += toCamelCase(`${id} ${format}`);
    return slug;
};

const computeTimeLeft = ({dateTime, minutes, seconds}) => {
    let past = false;
    let time = { days: 0, hours: 0, minutes: 0, seconds: 0 };
    if (dateTime) {
        const target = new Date(dateTime);
        if (minutes !== undefined) target.setMinutes(minutes);
        if (seconds !== undefined) target.setSeconds(seconds);
        if (isPast(target)) past = true;
        else {
            const now = new Date();
            if (minutes !== undefined) now.setMinutes(minutes);
            if (seconds !== undefined) now.setSeconds(seconds);
            time = intervalToDuration({ start: now, end: target });
        }
    }
    return {time, past};
};


export const CountDown = ({
    id,
    variant,
    dateTime,
    interval,
    intervalType = 'seconds',
    format,
    timeUpMessage,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        countDown: {},      // MUI typography props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const timerIdRef = useRef(null);
    const initialTimeLeft = computeTimeLeft({dateTime});
    const [timeLeft, setTimeLeft] = useState(isBuilder ? initialTimeLeft.time : { days: 0, hours: 0, minutes: 0, seconds: 0 });
    const [isTimeUp, setIsTimeUp] = useState(isBuilder ? initialTimeLeft.past: false);
    
    const contentWindow = isBuilder ? usePortalIframe() : null;
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('sm'), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined});

    const { slotProps: updatedSlotProps, canRender, t, customCss, noContent } = prepareComponent({name: "count-down", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon, localState: {
        timeLeft,
        isTimeUp,
    }});
    slotProps = updatedSlotProps;

    useEffect(() => {
        if (!dateTime) return;

        const updateTimer = () => {
            const time = computeTimeLeft({dateTime});
            setTimeLeft(time.time);
            setIsTimeUp(time.past);
            if (time.past && timerIdRef.current) {
                clearInterval(timerIdRef.current);
                timerIdRef.current = null;
            }
        };

        updateTimer();
        timerIdRef.current = setInterval(updateTimer, (intervalMilliseconds?.[intervalType] || 1000) * +interval);

        return () => {
            if (timerIdRef.current) clearInterval(timerIdRef.current);
        }
    }, [dateTime, interval, intervalType]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={2} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {!dateTime ? noContent :
                    <>
                        <Stack direction={isMobile ? "column" : "row"} spacing={0} useFlexGap flexWrap="wrap" sx={{alignItems: "center"}}>
                            {Object.keys(timeLeft).map(key => ((timeLeft[key] === undefined || isAfter(key, intervalType)) ? null : 
                                <Typography key={key} component="div" variant={variant || "h1"} {...slotProps?.countDown}>
                                    {timeLeft[key]} 
                                    {format === "shortest" ? "" : " "}
                                    {t(getSlug({id: key, count: timeLeft[key], format}), key.substring(0, 1))}&nbsp;
                                </Typography>
                            ))}
                        </Stack>
                        {isTimeUp && <Typography variant="caption">{timeUpMessage || t("general:timeUp", "Time is up!")}</Typography>}
                    </>
                }
                {children}
            </Stack>
        </CmsContainer>
    );
};