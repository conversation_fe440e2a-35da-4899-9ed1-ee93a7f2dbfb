import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Button, Paper, Alert, Checkbox, FormControlLabel, Stack, useTheme } from '@mui/material';
import { FormItem } from '@siteboss-frontend/shared/components';

export const ChangeSku = ({field, parentRef, loading, onChange, onClose, ...props}) => {
    const { t } = useOutletContext();
    const theme = useTheme();

    return (
        <Paper variant="outlined" sx={{p: 2, bgcolor: 'transparent'}}>
            <FormItem
                {...field}
                name="new_sku"
                component="TextField"
                parentRef={parentRef}
                onChange={onChange}
                loading={loading}
            />
            <Alert variant="outlined" severity="warning">
                {t('product:changeSkuWarning')}
            </Alert>
            <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center" useFlexGap  sx={{mt: 2}}>
            <FormControlLabel control={<Checkbox color={theme.palette.mode === "light" ? "primary" : "inherit"} size="small" />} label={t('product:confirmChangeSku')} slotProps={{typography: {variant: "body2"}}} />
            <div>
                <Button variant="outlined" size="small" color="primary" onClick={onClose}>
                    {t('general:cancel')}
                </Button>
            </div>
            </Stack>
        </Paper>
    );
};