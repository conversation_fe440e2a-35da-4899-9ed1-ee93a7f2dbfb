import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON>rid, Typography, FormControlLabel, Checkbox } from '@mui/material';
import { DatePicker } from '@siteboss-frontend/shared/components';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step4 } from './stepList';

const Step4Dates = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);

    // Get valid_from value for min date validation
    const validFrom = formData?.valid_from;


    // Initialize state for date values
    const [validFromValue, setValidFromValue] = useState(null);
    const [validUntilValue, setValidUntilValue] = useState(null);
    const [noEndDateValue, setNoEndDateValue] = useState(0);

    // Initialize form data from API response - only run once when data changes
    useEffect(() => {

        // Check if we have data from the API and we need to initialize
        if (data && !formData?.initialized) {
            const updatedData = { ...formData, initialized: true };

            // Handle valid_from date
            if (data.valid_from) {
                const validFromDate = new Date(data.valid_from);
                setValidFromValue(validFromDate);
                updatedData.valid_from = validFromDate;
            }

            // Handle valid_until date and no_end_date checkbox
            if (data.valid_until === null) {
                // If valid_until is null, set no_end_date to true
                setNoEndDateValue(1);
                updatedData.no_end_date = 1;
                updatedData.valid_until = null;
            } else if (data.valid_until) {
                const validUntilDate = new Date(data.valid_until);
                setValidUntilValue(validUntilDate);
                setNoEndDateValue(0);
                updatedData.no_end_date = 0;
                updatedData.valid_until = validUntilDate;
            }

            // Save the step data to the form context
            const fields = step4(data);
            if (fields && fields.length > 0) {
                fields.forEach(field => {
                    updatedData[field.name] = field.value;
                });
            }

            // Dispatch a single update with all changes
            dispatch(updateFormData(updatedData));
        }
    }, [data, dispatch]);

    // Update state when form data changes
    useEffect(() => {
        if (formData?.no_end_date !== undefined) {
            setNoEndDateValue(formData.no_end_date);
        }

        if (formData?.valid_from !== undefined) {
            // Make sure we have a Date object
            if (formData.valid_from instanceof Date) {
                setValidFromValue(formData.valid_from);
            } else if (formData.valid_from) {
                try {
                    const date = new Date(formData.valid_from);
                    setValidFromValue(date);
                } catch (e) {
                    console.error('Error parsing valid_from date:', e);
                }
            }
        }

        if (formData?.valid_until === null) {
            // If valid_until is null, set no_end_date to true and clear valid_until
            setNoEndDateValue(1);
            setValidUntilValue(null);
        } else if (formData?.valid_until !== undefined) {
            // Make sure we have a Date object
            if (formData.valid_until instanceof Date) {
                setValidUntilValue(formData.valid_until);
            } else if (formData.valid_until) {
                try {
                    const date = new Date(formData.valid_until);
                    setValidUntilValue(date);
                } catch (e) {
                    console.error('Error parsing valid_until date:', e);
                }
            }
        }
    }, [formData]);

    // Handle checkbox change
    const handleNoEndDateChange = (event) => {
        const isChecked = event.target.checked;
        const newValue = isChecked ? 1 : 0;
        setNoEndDateValue(newValue);
        
        if (isChecked) {
            // If no end date is checked, clear the valid_until field
            setValidUntilValue(null);
            dispatch(updateFormData({
                ...formData,
                no_end_date: newValue,
                valid_until: null
            }));
        } else {
            // If unchecked, set valid_until to one month from now
            const oneMonthFromNow = new Date();
            oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
            setValidUntilValue(oneMonthFromNow);
            dispatch(updateFormData({
                ...formData,
                no_end_date: newValue,
                valid_until: oneMonthFromNow
            }));
        }
    };

    // Handle date changes
    const handleValidFromChange = (event) => {
        // The DatePicker component passes an event-like object with target.value containing the date
        const date = event.target.value;

        // Make sure we're working with a Date object
        const dateObj = date instanceof Date ? date : new Date(date);

        // Update local state
        setValidFromValue(dateObj);

        // Update Redux store
        dispatch(updateFormData({
            ...formData,
            valid_from: dateObj
        }));
    };

    const handleValidUntilChange = (event) => {
        // The DatePicker component passes an event-like object with target.value containing the date
        const date = event.target.value;

        // Make sure we're working with a Date object
        const dateObj = date instanceof Date ? date : new Date(date);

        // Update local state
        setValidUntilValue(dateObj);

        // Update Redux store
        dispatch(updateFormData({
            ...formData,
            valid_until: dateObj
        }));
    };

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Set the date range when this discount is valid
                </Typography>

                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <DatePicker
                            name="valid_from"
                            label="Valid From"
                            fullWidth
                            required
                            margin="normal"
                            value={validFromValue}
                            onChange={handleValidFromChange}
                        />
                    </Grid>

                    <Grid item xs={12}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={noEndDateValue === 1}
                                    onChange={handleNoEndDateChange}
                                    name="no_end_date"
                                />
                            }
                            label="No end date"
                        />
                    </Grid>

                    {noEndDateValue !== 1 && (
                        <Grid item xs={12} md={6}>
                            <DatePicker
                                name="valid_until"
                                label="Valid Until"
                                fullWidth
                                required
                                margin="normal"
                                minDate={validFrom}
                                value={validUntilValue}
                                onChange={handleValidUntilChange}
                            />
                        </Grid>
                    )}
                </Grid>
            </Grid>
        </Grid>
    );
};

export default Step4Dates;
