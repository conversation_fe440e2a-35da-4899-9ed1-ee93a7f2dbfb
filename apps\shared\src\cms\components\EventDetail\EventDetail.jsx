import React, { useMemo } from 'react';
import { Stack } from '@mui/material';

import { LoadingBar, ErrorBox, Title } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils';

import { layouts, widgetIcon } from './Layouts';
import { properties } from './properties';
import { useEventDetail } from './useEventDetail';

export const EventDetail = ({
    id,
    //shopId, // this will tell us the routes of the other pages we need to see event datails, cart page, etc
    eventId,
    eventType, // automatic or manual
    redirectToCart,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        title: {},          // MUI box props
        caption: {},        // MUI typography props
        text: {},           // MUI typography props
        images: {},         // MUI stack props
        button: {},         // MUI button props
        ctaContainer: {},   // MUI paper props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const {
        eventProductId,
        formattedEvent,
        cartRoute,
        handleAddToCart,
        loading,
        reduxStore,
        selectedUser,
        ErrorBar,  
        handleSelectUser,
        cartItemId,
    } = useEventDetail({ ...props, eventId, eventType, redirectToCart, isBuilder });

    const { slotProps: updatedSlotProps, customCss, canRender, noContent, contentWindow, isMobile, t } = prepareComponent({name: "event-detail", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon, localState: {
        ...reduxStore,
    }});    
    slotProps = updatedSlotProps;

    const Layout = useMemo(() => layouts.find(layout => layout.id === layoutId)?.component, [layoutId]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...updatedSlotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...updatedSlotProps?.cmsStack}>
                {(!formattedEvent && !loading && isBuilder) ? noContent :
                    <>
                        <ErrorBar />
                        {loading && <LoadingBar color="inherit" />}
                        {(formattedEvent?.length > 0) &&
                            <Layout 
                                event={formattedEvent?.[0]} 
                                slotProps={updatedSlotProps} 
                                isBuilder={isBuilder} 
                                contentWindow={contentWindow} 
                                cartRoute={cartRoute} 
                                onAddToCart={handleAddToCart}
                                onUserSelect={handleSelectUser}
                                selectedUser={selectedUser}
                                cartItemId={cartItemId}
                            /> 
                        }
                    </>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};