import React, { useState, useCallback, useMemo, useId, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, But<PERSON> } from '@mui/material';

import FormItem from '../../../../../components/FormItem';

const fields = [
    {
        name: 'username',
        label: 'login:username',
        required: true,
        component: 'TextField',
        margin: 'dense'
    },
    {
        name: 'password',
        label: 'login:password',
        required: true,
        component: 'PasswordField',
        margin: 'dense'
    },
    {
        name: 'amount',
        label: 'pos:discount',
        required: true,
        component: 'MoneyField',
        margin: 'normal'
    },
];

export const ManagerDiscount = ({paymentMethod, paymentMethodId, amount, loading: parentLoading, onPaymentProcess, onPaymentChange, slotProps, ...props}) => {
    const { t } = useTranslation();
    const id = useId();

    const [values, setValues] = useState({});
    const [errors, setErrors] = useState({});

    const isValid = useMemo(() => {
        let valid = true;
        for (let i = 0; i < fields.length; i++) {
            let _valid = values?.[fields[i].name] && !errors?.[fields[i].name];
            if (fields[i].required && !_valid) {
                valid = false;
                break;
            }
        }
        return valid;
    }, [errors, values, t]);

    const checkErrors = useCallback(_values => {
        let _errors = {};
        const field = fields.find(f => f.name === _values.name);
        if (!field) _errors = {[_values.name]: t('error:invalid')};
        else if (field.required){
            if (_values.name === "amount" && (+_values?.value > +amount || _values?.value < 0)) {
                _errors = {[field.name]: t('error:invalid')};
            }
            if (!_values?.value) {
                _errors = {[field.name]: t('error:required')};
            }
        }
        return _errors;
    }, [t, amount]);

    const handleChange = useCallback(e => {
        let _errors = checkErrors(e.target);
        setErrors(prev => ({...prev, [e.target.name]: null, ..._errors}));
        setValues(prev => ({...prev, [e.target.name]: e.target.value}));
    }, [checkErrors]);

    const handlePay = useCallback(async () => {
        if (onPaymentProcess && isValid) await onPaymentProcess({id, paymentMethod, paymentMethodId, values: values});
    }, [onPaymentProcess, id, values, isValid, paymentMethod, paymentMethodId]);

    useEffect(() => {
        if (onPaymentChange && isValid) onPaymentChange({id, values, paymentMethod, paymentMethodId});
    }, [id, values, isValid, onPaymentChange, paymentMethod, paymentMethodId]);    

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            {fields.map(field => (
                <FormItem 
                    key={field.name}
                    {...field}
                    label={t(field.label)}
                    value={values?.[field.name] || ""}
                    onChange={handleChange}
                    errors={errors?.[field.name]}
                    {...slotProps?.input}
                />
            ))}
            <Button 
                loading={parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl" 
                fullWidth 
                disabled={parentLoading || !isValid || +values?.amount === 0} 
                onClick={handlePay} 
                {...slotProps?.button}
            >
                {t(`pos:processDiscount`)}
            </Button>
        </Stack>
    );
}