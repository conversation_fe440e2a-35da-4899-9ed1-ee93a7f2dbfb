import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Grid2, TextField, InputAdornment, Typography } from '@mui/material';

const marginSx = {
    'dense': {mt: 0.5, mb: 0.5},
    'normal': {mt: 1, mb: 0.5},
    'none': {m: 0},
};

export const Measurement = ({
    label,
    name,
    required,
    errors,
    disabled,
    loading,
    value = {},
    onChange,
    onBlur,
    measurements = ['width', 'height'],
    unit,
    size,
    gridSize,
    ...props
}) => {
    const { t } = useTranslation();
    const [values, setValues] = useState(value || {});

    const maxGridSize = gridSize || ((12 / measurements.length) < 4 ? 4 : (12 / measurements.length));

    const handleChange = useCallback(measurement => e => {
        const newValue = e.target.value;
        //const numericValue = newValue === '' ? '' : Number(newValue);

        setValues(prev => ({
            ...prev,
            [measurement]: newValue
        }));

        if (onChange) {
            const syntheticEvent = {
                target: {
                    name,
                    value: {
                        ...values,
                        [measurement]: newValue
                    }
                }
            };
            onChange(syntheticEvent);
        }
    }, [name, onChange, values]);

    const handleBlur = useCallback(e => {
        if (onBlur) {
            const syntheticEvent = {
                target: {
                    name,
                    value: values
                }
            };
            onBlur(syntheticEvent);
        }
    }, [name, onBlur, values]);

    useEffect(() => {
        setValues(value || {});
    }, [value]);

    return (
        <Box sx={marginSx[props?.margin || 'normal']}>
            {label && measurements.length > 1 && <Typography variant="caption" sx={{mt: 1}}>{label}</Typography>}
            <Grid2 container spacing={props?.margin === "dense" ? 1 : 2}>
                {measurements.map((measurement, i) => (
                    <Grid2 size={{xs: 12, md: maxGridSize}} key={`${name}-${measurement}`}>
                        <TextField
                            label={t(`measurement:${measurement}`, measurement)}
                            name={`${name}-${measurement}`}
                            required={required}
                            error={!!errors?.[measurement]}
                            helperText={errors?.[measurement]}
                            disabled={disabled || loading}
                            value={values[measurement] || ''}
                            onChange={handleChange(measurement)}
                            onBlur={handleBlur}
                            size={size || 'medium'}
                            {...props}
                            slotProps={{...props?.slotProps, input: {...props?.slotProps?.input, ...(unit && {
                                endAdornment:
                                    <InputAdornment position="end">
                                        {Array.isArray(unit) ? unit[i] : (typeof unit === 'object') ? unit?.[measurement] : unit}
                                    </InputAdornment>
                            })}}}
                            sx={{
                                flexGrow: 1,
                                '& .MuiInputBase-input': {
                                    textAlign: 'right'
                                },
                                ...props?.sx
                            }}
                        />
                    </Grid2>
                ))}
            </Grid2>
        </Box>
    );
};
