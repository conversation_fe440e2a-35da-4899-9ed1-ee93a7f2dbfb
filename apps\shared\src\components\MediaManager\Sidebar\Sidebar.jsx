import React, { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Stack, Drawer, Button, useTheme, useMediaQuery } from "@mui/material";
import { CloudUploadOutlined as UploadIcon} from '@mui/icons-material';

import MediaTypes from "./MediaTypes";

export const Sidebar = ({mediaType, setMediaType, setShowUpload, ...props}) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const drawerWidth = useMemo(() => isMobile ? '100%' : theme.sizes.menuWidth, [theme, isMobile]);

    const handleSelectMediaType = useCallback((e, newValue) => {
        if (newValue !== null) setMediaType(newValue);
        setShowUpload(false);
    }, [setMediaType]);

    const handleShowUpload = useCallback(() => {
        setShowUpload(true);
    }, [setShowUpload]);

    return (
        <Drawer
            sx={{
                width: drawerWidth,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: drawerWidth,
                    boxSizing: 'border-box',
                },
            }}
            PaperProps={{
                style: {
                    position: "absolute",
                    background: "transparent",
                    border: "none",
                }
            }}                
            variant="permanent"
            anchor="left"
        >
            <Stack direction="column" spacing={2} useFlexGap sx={{zIndex: theme => theme.zIndex.drawer - 1}}>
                <Button variant="contained" size="large" fullWidth startIcon={<UploadIcon />} onClick={handleShowUpload}>{t(("file:upload"))}</Button>
                <MediaTypes value={mediaType} onChange={handleSelectMediaType} />
            </Stack>
        </Drawer>
    );
}