import React, { useState, useCallback } from 'react';
import { useNavigate, useOutletContext } from 'react-router-dom';
import {
    Paper,
    Box,
    Typography,
    Skeleton,
    Stack,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from '@mui/material';
import { DataTableNoRows, DataTableSearchInput, Confirm } from '@siteboss-frontend/shared/components';
import ServiceCard from './ServiceCard';

const ServicesList = ({
    services = [],
    loading,
    onDelete,
    page = 0,
    rowsPerPage = 10,
    totalCount = 0,
    onChangePage,
    onChangeRowsPerPage
}) => {
    const { t } = useOutletContext();
    const navigate = useNavigate();
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
    const [serviceToDelete, setServiceToDelete] = useState(null);
    const [selectedServices, setSelectedServices] = useState([]);

    // Define page size options
    const pageSizeOptions = [5, 10, 25, 50];

    // Handle view service details
    const handleView = (serviceId) => {
        navigate(`/services/${serviceId}`);
    };

    // Handle delete confirmation
    const handleDeleteClick = (service) => {
        setServiceToDelete(service);
        setDeleteConfirmOpen(true);
    };

    // Handle delete confirmation
    const handleDeleteConfirm = () => {
        if (serviceToDelete) {
            onDelete(serviceToDelete.id);
            setDeleteConfirmOpen(false);
            setServiceToDelete(null);
        }
    };

    // Handle card selection
    const handleCardSelect = useCallback((_, service) => {
        const isSelected = selectedServices.some(s => s.id === service.id);
        if (isSelected) {
            setSelectedServices(selectedServices.filter(s => s.id !== service.id));
        } else {
            setSelectedServices([...selectedServices, service]);
        }
    }, [selectedServices]);

    // Handle card expansion (view details)
    const handleCardExpand = useCallback((_, service) => {
        handleView(service.id);
    }, []);

    // Loading skeleton
    if (loading) {
        return (
            <Paper sx={{ p: 2 }}>
                <Skeleton variant="rectangular" height={400} />
            </Paper>
        );
    }

    // Card view of services
    return (
        <>
            <Paper sx={{ p: 2, mb: 2 }}>
                <Stack spacing={3}>
                    <Box sx={{ mb: 2 }}>
                        <DataTableSearchInput onSearchChange={() => {}} />
                    </Box>

                    {services.length === 0 ? (
                        <DataTableNoRows title={t('service:noServices')} />
                    ) : (
                        <>
                            <Stack direction="row" flexWrap="wrap" sx={{ mx: -1.5 }}>
                                {services.map((service) => (
                                    <Box
                                        key={service.id}
                                        sx={{
                                            width: {
                                                xs: '100%',
                                                sm: '50%',
                                                md: '33.33%',
                                                lg: '25%'
                                            },
                                            p: 1.5
                                        }}
                                    >
                                        <ServiceCard
                                            service={service}
                                            selected={selectedServices.some(s => s.id === service.id)}
                                            onSelect={handleCardSelect}
                                            onExpand={handleCardExpand}
                                            onDelete={handleDeleteClick}
                                        />
                                    </Box>
                                ))}
                            </Stack>

                            <Stack
                                direction={{ xs: "column", sm: "row" }}
                                spacing={2}
                                justifyContent="space-between"
                                alignItems={{ xs: "center", sm: "flex-end" }}
                            >
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Typography variant="body2" color="text.secondary">
                                        {t('general:showing')} {services.length} {t('general:of')} {totalCount} {t('service:services')}
                                    </Typography>
                                    <FormControl variant="outlined" size="small" sx={{ minWidth: 80 }}>
                                        <InputLabel id="page-size-select-label">{t('general:perPage')}</InputLabel>
                                        <Select
                                            labelId="page-size-select-label"
                                            value={rowsPerPage}
                                            onChange={onChangeRowsPerPage}
                                            label={t('general:perPage')}
                                        >
                                            {pageSizeOptions.map(option => (
                                                <MenuItem key={option} value={option}>{option}</MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Stack>

                                <Pagination
                                    count={Math.ceil(totalCount / rowsPerPage)}
                                    page={page + 1}
                                    onChange={(e, value) => onChangePage(e, value - 1)}
                                    color="primary"
                                    disabled={loading}
                                    siblingCount={{ xs: 0, sm: 1 }}
                                />
                            </Stack>
                        </>
                    )}
                </Stack>
            </Paper>

            {serviceToDelete && (
                <Confirm
                    open={deleteConfirmOpen}
                    onClose={() => setDeleteConfirmOpen(false)}
                    onConfirm={handleDeleteConfirm}
                    title={t('service:deleteConfirmTitle')}
                    content={t('service:deleteConfirmMessage', { name: serviceToDelete?.name || '' })}
                />
            )}
        </>
    );
};

export default ServicesList;
