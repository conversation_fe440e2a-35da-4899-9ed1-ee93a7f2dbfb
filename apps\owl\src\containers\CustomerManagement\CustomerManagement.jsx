import React, { useState } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Drawer,
  IconButton,
  Grid
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { Title, WithDetails } from '@siteboss-frontend/shared/components';

// Sample data for customers
const customers = [
  { id: 1, name: 'Acme Corp', contact: '<PERSON>', email: '<EMAIL>', phone: '************', status: 'Active' },
  { id: 2, name: 'Globex Inc', contact: '<PERSON>', email: '<EMAIL>', phone: '************', status: 'Active' },
  { id: 3, name: 'Initech', contact: '<PERSON>', email: '<EMAIL>', phone: '************', status: 'Inactive' },
];

export const CustomerManagement = (props) => {
  const { t } = useOutletContext();
  const { id } = useParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [drawerOpen, setDrawerOpen] = useState(Boolean(id));

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedCustomer(null);
  };

  return (
    <Container>
      <Title 
        title={t('customers:customerManagement')}
        breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('customers:customerManagement')}]}
      />
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Customers</Typography>
          <Box>
            <TextField 
              size="small" 
              placeholder="Search customers..." 
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ mr: 2 }}
            />
            <Button variant="contained" color="primary">Add Customer</Button>
          </Box>
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Customer Name</TableCell>
                <TableCell>Contact Person</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>{customer.name}</TableCell>
                  <TableCell>{customer.contact}</TableCell>
                  <TableCell>{customer.email}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell>{customer.status}</TableCell>
                  <TableCell>
                    <Button size="small" onClick={() => handleCustomerSelect(customer)}>View</Button>
                    <Button size="small" color="primary" sx={{ ml: 1 }}>Edit</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Customer Details Drawer */}
      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={handleCloseDrawer}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: '50%', md: '40%' },
            p: 2
          }
        }}
      >
        {selectedCustomer && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">Customer Details</Typography>
              <IconButton onClick={handleCloseDrawer}>
                <CloseIcon />
              </IconButton>
            </Box>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h5">{selectedCustomer.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Status: {selectedCustomer.status}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Contact Information</Typography>
                <Typography variant="body1">{selectedCustomer.contact}</Typography>
                <Typography variant="body1">{selectedCustomer.email}</Typography>
                <Typography variant="body1">{selectedCustomer.phone}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Billing Information</Typography>
                <Typography variant="body1">Payment Terms: Net 30</Typography>
                <Typography variant="body1">Billing Cycle: Monthly</Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2">Recent Activity</Typography>
                <Typography variant="body1">• Last invoice sent: 05/01/2023</Typography>
                <Typography variant="body1">• Last payment received: 04/15/2023</Typography>
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Button variant="contained" color="primary" sx={{ mr: 1 }}>Edit Customer</Button>
                <Button variant="outlined">Create Invoice</Button>
              </Grid>
            </Grid>
          </Box>
        )}
      </Drawer>
    </Container>
  );
}
