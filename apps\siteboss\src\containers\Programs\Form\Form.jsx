import React, { useRef, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Grid2, Button, Box, Typography } from '@mui/material';
import { FormItem, MediaManager, Modal } from '@siteboss-frontend/shared/components';
import { CloudUploadOutlined as UploadIcon } from '@mui/icons-material';
import Thumbnails from './Thumbnails';

import { useFormLogic } from './useFormLogic';

export const Form = ({groupTypes = [], eventTypes = [], loading:parentLoading, typeId, id, onSuccess, ...props}) => {
    const { t } = useOutletContext();
    const ref = useRef(null);
    const [mediaManagerOpen, setMediaManagerOpen] = useState(false);

    const {
        values,
        errors,
        success,
        loading,
        handleChange,
        handleSubmit,
        handleFileDrop,
        handleFileRemove,
        programErrors,
        ErrorBar,
        LoadingBar,
        ProgramErrorBar,
        UploadErrorBar,
    } = useFormLogic({groupTypes, eventTypes, parentLoading, typeId, id, onSuccess});

    const handleMediaSelection = (selected) => {
        // Get the current images
        const currentImages = values.find(v => v.name === 'images')?.value || [];

        // Format the selected URLs to match what the API expects
        const formattedUrls = selected.map(url => ({
            url: url,
            preview_url: url
        }));

        // Combine with existing images
        const combinedImages = [...currentImages, ...formattedUrls];

        // Update the form state with the selected images
        handleChange({
            target: {
                name: 'images',
                value: combinedImages
            }
        });

        // Close the media manager modal
        setMediaManagerOpen(false);
    };

    if (programErrors) {
        return (
            <Container>
                <ProgramErrorBar />
            </Container>
        );
    }

    return (
        <Container>
            <Grid2 container spacing={2} ref={ref}>
                <Grid2 size={{xs: 12}}>
                    {values.filter(a => a.component && a.name !== 'images').map(field => (
                        <FormItem
                            key={`form-${field.name}`}
                            {...field}
                            parentRef={ref}
                            errors={errors?.[field.name]}
                            onChange={handleChange}
                            loading={loading}
                        />
                    ))}
                </Grid2>
                <Grid2 size={{xs: 12}}>
                    {values.filter(a => a.name === 'images').map(field => (
                        <Box key={`form-${field.name}`} sx={{ mt: 2 }}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <Typography variant="subtitle1">{t('program:images')}</Typography>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    startIcon={<UploadIcon />}
                                    onClick={() => setMediaManagerOpen(true)}
                                    disabled={loading}
                                >
                                    {t('program:selectImages')}
                                </Button>
                                <Thumbnails
                                    urls={field.value}
                                    fieldName={field.name}
                                    onChange={handleChange}
                                />
                            </Box>

                            <Modal
                                open={mediaManagerOpen}
                                onClose={() => setMediaManagerOpen(false)}
                                title={t('program:selectImages')}
                                maxWidth="lg"
                                slotProps={{
                                    paperProps: {
                                        style: {
                                            height: `calc(100% - 64px)`
                                        }
                                    }
                                }}
                            >
                                <MediaManager
                                    onSelection={handleMediaSelection}
                                    multiple={true}
                                    accept="image/*"
                                    parentLoading={loading}
                                    mediaType={1}
                                />
                            </Modal>
                        </Box>
                    ))}
                </Grid2>
                <Grid2 size={{xs: 12}}>
                    <Button variant='contained' color='primary' fullWidth size='large' sx={{mt:1}} onClick={handleSubmit}>
                        {t('general:save')}
                    </Button>
                </Grid2>
            </Grid2>
            <LoadingBar />
            <ErrorBar />
            <UploadErrorBar />
        </Container>
    );
}