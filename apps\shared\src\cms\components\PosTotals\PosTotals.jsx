import React, { useState, useMemo, useCallback, useContext } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { Stack } from '@mui/material';

import { Modal } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';

import { PosContext } from '../../hooks';
import { layouts } from './layouts';
import { properties } from './properties';
import Checkout from './Checkout';
import Totals from '../common/pos/Totals';
import { formatSlug } from '../../../utils';

import PosCheckout from '../Checkout';

export const PosTotals = ({
    id,
    layoutId,
    showTotals = true,
    showButton = true,
    showCheckoutInModal = true,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // <PERSON><PERSON> stack props
        stack: {},          // MUI stack props (for totals)
        label: {},          // <PERSON>UI typography props
        total: {},          // MUI typography props
        button: {},         // MUI button props
        checkout: {         // MUI modal props
            modalSize: "md"
        },
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    builderProps,
    children,
    ...props
}) => {
    const { orderLoading, reduxStore, router } = useContext(PosContext) || {};
    const cart = useSelector(state => state.cart, shallowEqual);
    const balance = (+cart?.totals?.total - +cart?.totals?.payments) || 0;
    
    const [openCheckout, setOpenCheckout] = useState(false);

    const { slotProps: updatedSlotProps, isMobile, t, canRender, customCss } = prepareComponent({name: "totals", layoutId, layouts, slotProps, isBuilder, condition, localState: {
        openCheckout,
        balance,
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;
    
    const checkoutRoute = useMemo(() => {
        let route = null;
        if (router) route = `/${formatSlug(`${router?.slug?.value}/${router?.checkout?.value}`)}`;            
        return route;
    }, [router]);

    const toggleCheckout = useCallback(() => {
        if (isBuilder) return;
        setOpenCheckout(!openCheckout);
    }, [openCheckout, isBuilder]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack direction="column" spacing={1} useFlexGap {...slotProps?.cmsStack}>
                {showTotals && <Totals slotProps={slotProps} />}
                {showButton && <Checkout amount={balance} onCheckout={toggleCheckout} slotProps={slotProps?.button} disabled={orderLoading || !cart?.cart || cart?.cart.length <= 0} />}
            </Stack>

            {children}

            {!isBuilder && 
                <>
                    {showCheckoutInModal ?
                        <Modal 
                            open={openCheckout} 
                            onClose={toggleCheckout}
                            maxWidth={slotProps?.checkout?.modalSize || "md"}
                            fullScreen={isMobile}
                            aria-describedby={t(`pos:checkout`)}
                        >
                            {<PosCheckout slotProps={slotProps?.checkout} />}
                        </Modal>
                    : openCheckout && checkoutRoute && <Navigate to={checkoutRoute} />}
                </>
            }
        </CmsContainer>
    );
}