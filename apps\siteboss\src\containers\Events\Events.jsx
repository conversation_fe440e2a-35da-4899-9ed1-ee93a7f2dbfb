import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Drawer, Stack, Box, Typography } from '@mui/material';
import { Title, Confirm, ErrorBar, WithDetails } from '@siteboss-frontend/shared/components';

import List from './List';
import NewButton from './NewButton';
import EventData from './EventData';

import { useEvents } from './useEvents';

export const Events = (props) => {
    const { t } = useOutletContext();
    const ref = useRef(null);
    const [loading, setLoading] = useState(false);

    const {
        open,
        setOpen,
        isNew,
        isEdit,
        showConfirm,
        setShowConfirm,
        selectedEvents,
        setSelectedEvents,
        deleteParams,
        setDeleteParams,
        fetchCounter,
        setFetchCounter,
        errors,
        setErrors,
        eventTypes,
        eventTypesErrors,
        EventTypesErrorBar,
        toggleDrawer,
        handleDelete,
        handleUpdate,
        processDelete,
        eventTypesLoading,
        updateLoading,
        handleEdit,
        handleEventTypeSelect,
    } = useEvents({setLoading});

    const handleConfirmDelete = useCallback(async () => {
        if (deleteParams.length > 0) {
            const _ids = deleteParams.map(s => s.id);
            setLoading(true);
            try {
                const result = await processDelete({ id: _ids });
                if (result?.data) {
                    setDeleteParams([]);
                    setFetchCounter(prev => prev + 1);
                    setShowConfirm(false);
                }
            } catch (error) {
                setErrors(error);
            } finally {
                setLoading(false);
            }
        }
    }, [deleteParams, processDelete, setDeleteParams, setFetchCounter, setShowConfirm, setErrors]);

    const handleDeclineDelete = useCallback(() => {
        setShowConfirm(false);
        setDeleteParams([]);
    }, [setDeleteParams, setShowConfirm]);

    useEffect(() => {
        if (eventTypesLoading || updateLoading) setLoading(true);
        else setLoading(false);
    }, [eventTypesLoading, updateLoading]);

    return (
        <Container>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title
                    title={t('event:events')}
                    breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('event:events')}]}
                />
                <Box>
                    {eventTypesErrors
                        ? <EventTypesErrorBar />
                        : <NewButton data={eventTypes} loading={loading} onSelect={handleEventTypeSelect} />
                    }
                </Box>
            </Stack>

            <ErrorBar open={Boolean(errors)} message={errors} onClose={()=>setErrors(null)} />
            {showConfirm && deleteParams.length > 0 &&
                <Confirm
                    title={t('event:deleteTitle')}
                    message={t('event:deleteMessage')}
                    onConfirm={handleConfirmDelete}
                    onDecline={handleDeclineDelete}
                >
                    <ul>
                        {deleteParams?.map(event => (
                            <Typography key={`delete-event-${event.id}`} variant="body2" component="li">
                                {event?.type_name || event?.event_type_name} / {event.name}
                            </Typography>
                        ))}
                    </ul>
                </Confirm>
            }

            <List
                setSelected={setSelectedEvents}
                selected={selectedEvents}
                onExpand={toggleDrawer(true)}
                onDelete={handleDelete}
                fetchCounter={fetchCounter}
                loading={loading}
            />

            <Drawer
                details={1} // this is to flag the drawer as a details drawer in the theme
                anchor='right'
                open={open}
                onClose={toggleDrawer(false)}
                keepMounted={false} // Don't keep the drawer mounted when closed
            >
                <WithDetails
                    ref={ref}
                    itemIds={selectedEvents.map(a => ({id: a.id, label: (
                        <>
                            <span>{a.name}</span> {/* we use a span so that it inherits the default typography variant defined in WithDetails */}
                            <Typography variant="subtitle3">{a.type_name}</Typography>
                        </>
                    )}))}
                    onClose={(e) => {
                        console.log('WithDetails onClose called');
                        toggleDrawer(false)(e);
                    }}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    isNew={isNew}
                    isEdit={isEdit}
                    titles={{
                        new: t('event:newEvent'),
                        edit: t('event:editEvent'),
                        view: t('event:events'),
                    }}
                    slots={{
                        details: props => (
                            <EventData
                                parentRef={ref}
                                loading={loading}
                                onDelete={handleDelete}
                                onEdit={handleEdit}
                                onUpdate={handleUpdate}
                                fetchCounter={fetchCounter}
                                setFetchCounter={setFetchCounter}
                                selectedEvents={selectedEvents}
                                {...props}
                            />
                        ),
                    }}
                />
            </Drawer>
        </Container>
    );
}