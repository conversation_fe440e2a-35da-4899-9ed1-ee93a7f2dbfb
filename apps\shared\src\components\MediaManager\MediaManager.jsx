import React, { useRef } from "react";
import { useTranslation } from 'react-i18next';
import { DndContext, useDroppable } from '@dnd-kit/core';
import { Box } from "@mui/material";

import { ErrorBar } from "../Snacks";
import { useMediaManager } from "./useMediaManager";
import Sidebar from "./Sidebar";
import Preview from "./Preview";
import Dropzone from "./Dropzone";

export const MediaManager = ({
    size, // number with the maximum file size in bytes
    multiple = true, // boolean to allow multiple files to be uploaded
    message, // string with the message to show on the dropzone
    parentLoading, // the loading state of the parent component
    onSelection, // function to be called when a file is selected
    mediaType: selectedMediaType, // the media type to show
    ...props
}) => {
    const { t } = useTranslation();
    const fileInputRef = useRef(null);
    const { setNodeRef } = useDroppable({ id: 'mediaManager' });

    const {
        isDragging,
        errors,
        setErrors,
        imageToCrop,
        handleDragOver,
        handleDrop,
        handleDragLeave,
        handleChange,
        handleFileCrop,
        handleFileRemove,
        handleCrop,
        handleHideImageCropper,
        handleButtonClick,
        handleFileClick,
        acceptedMimeType,
        loading,
        mediaType,
        setMediaType,
        showUpload,
        setShowUpload,
        selected,
        uploadProgress,
        uploadBuffer,
        LoadingBar,
        fetchCounter,
    } = useMediaManager({ size, multiple, message, parentLoading, onSelection, selectedMediaType, fileInputRef });

    return (
        <DndContext onDragOver={handleDragOver} onDragEnd={handleDragLeave}>
            <Box
                ref={setNodeRef}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                sx={{
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    height: '100%',
                    minHeight: 200,
                    flexGrow: 1,
                    flex: props?.sx?.height ? undefined : 1,
                    ...(props.sx || {}),
                    '&:hover:after': isDragging ? {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        content: `"${t('file:dropFile')}"`,
                        backgroundColor: theme => theme.palette.success.main,
                        fontFamily: theme => theme.typography.h6.fontFamily,
                        fontSize: theme => theme.typography.h6.fontSize,
                        fontWeight: theme => theme.typography.fontWeightBold,
                        //color: theme => theme.palette.text.primary,
                        zIndex: theme => theme.zIndex.drawer + 1,
                        borderRadius: 1,
                        opacity: 0.85,
                        position: 'absolute',
                        width: '100%',
                        height: '100%',
                        maxHeight: '100vh',
                        top: 0,
                        left: 0,
                    } : undefined,
                }}
            >
                <input
                    type='file'
                    ref={fileInputRef}
                    onChange={handleChange}
                    style={{ display: 'none' }}
                    multiple={multiple || false}
                    accept={acceptedMimeType}
                    disabled={(loading || parentLoading)}
                />

                <Box sx={{ display: 'flex', position: 'relative', flexGrow: 1 }}>
                    <Sidebar 
                        mediaType={mediaType} 
                        setMediaType={setMediaType} 
                        setShowUpload={setShowUpload}
                    />
                    <Box sx={{ display:'flex', flexDirection: 'column', flexGrow: 1, pl: 4, width: '100%', minWidth: theme => theme.breakpoints.values.sm }}>
                        <ErrorBar 
                            open={Boolean(errors)}
                            message={errors} 
                            onClose={() => setErrors(null)}
                        />
                        {(uploadProgress > 0 || uploadBuffer > 0) && <LoadingBar color="success" variant="buffer" value={uploadProgress} valueBuffer={uploadBuffer} sx={{mb: 2}} />}
                        {showUpload ? 
                            <Dropzone mediaType={mediaType} message={message} isDragging={isDragging} onClick={handleButtonClick} />
                        : 
                            <Preview 
                                mediaType={mediaType}
                                imageToCrop={imageToCrop} 
                                onCrop={handleCrop} 
                                onHideImageCropper={handleHideImageCropper} 
                                onError={setErrors} 
                                onFileRemove={handleFileRemove}
                                onFileClick={handleFileClick}
                                selected={selected}
                                fetchCounter={fetchCounter}
                                loading={loading || parentLoading}
                            />
                        }
                    </Box>
                </Box>
            </Box>
        </DndContext>
    );
}