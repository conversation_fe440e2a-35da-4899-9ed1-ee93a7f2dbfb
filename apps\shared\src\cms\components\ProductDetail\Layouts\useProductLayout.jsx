import React, { useCallback, useContext, useRef, useMemo, useLayoutEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from '@mui/material';
import { Gallery, Title } from '../../../../components';
import { toCamelCase } from '../../../../utils/string';

import { PosProductDetailContext } from '../../../hooks/PosProductDetailContext';
import ProductVariants from '../common/ProductVariants';
import ProductAddons from '../common/ProductAddons';
import ProductEvents from '../common/ProductEvents';
import ProductGiftCards from '../common/ProductGiftCards';
import ProductMemo from '../common/ProductMemo';
import AddToCart from '../common/AddToCart';
import EventInfo from '../common/ProductEvents/Event/EventInfo';

/*
This is the product detail view, which should show a picture, prices, etc. 
In the POS, it should load in a modal, but it could also be used in a regular page like for an e-commerce shop. 
The layout is controlled via slotProps, which is passed to the children components. For example you may want to load the product variants as buttons (on the POS) or as radio button (for an ecommerse site).
*/
export const useProductLayout = ({
    slotProps = {
        header: {},
        body: {},
        footer: {},
        title: {},
        cartButton: {},
        memo: {},
        events: {},
        eventUsers: {},
        giftCards: {},
        addons: {},
        variants: {},
        gallery: {},
    },
    onAddToCart,
    isEdit = false,
    replacePageTitle,               // the page title to be replaced with the item's name
    ...props 
    // everything else comes from the context
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    //const { handleAddToCart } = useContext(PosContext);
    const { 
        product,
        media,
        variant,
        forUserIds,
        giftCardRecipient,
        addons,
        modalSize,
        fullPage,
        setErrors, 
        errors,
        loading,
        currentShopItem,
        handleVariantChange = () => {},
        handleForUserChange = () => {},
        handleGiftCardChange = () => {},
        handleAddonChange = () => {},
        handleMemoSave = () => {},
    } = useContext(PosProductDetailContext) || {};

    const headerRef = useRef(null);
    const footerRef = useRef(null);
    const hideQuantity = useMemo(() => 
        Boolean((product?.events && product?.events?.length > 0) || product?.product_type_id === 12),
        [product?.events, product?.product_type_id]
    );

    const allProps = useMemo(() => {
        const processSlotProps = (slotKey) => {
            const { hide, ...rest } = slotProps?.[slotKey] || {};
            return { [slotKey]: { hide: hide === true, props: rest } };
        };

        const slotKeys = ['gallery', 'variants', 'addons', 'events', 'eventUsers', 'giftCards', 'memo'];
        return slotKeys.reduce((acc, key) => ({ ...acc, ...processSlotProps(key) }), {});
    }, [slotProps]);

    const bodySx = useMemo(() => ({
        ...slotProps?.body?.sx, 
        mb: isMobile ? 10 : undefined,
    }), [slotProps?.body?.sx, isMobile]);

    const handleAddToCartClick = useCallback(() => {
        if (!variant || !product) return;
        if (onAddToCart) onAddToCart(product, currentShopItem);
    }, [product, onAddToCart, currentShopItem, variant]);

    const renderTitle = useCallback(({children, ...props}) => (
        <Title 
            title={`${product?.name}`} 
            subtitle={product?.events?.length > 0 ? undefined : (
                product?.description || 
                t(`product:productTypes.${toCamelCase(product?.product_type_name)}`, product?.product_type_name)
            )}
            {...allProps.props?.title}
            {...props}
        >
            {children}
        </Title>
    ), [product?.name, product?.events?.length, product?.description, product?.product_type_name, t, allProps.props?.title]);

    const renderGallery = useCallback(props => (
        <Gallery 
            images={media} 
            {...allProps.props?.gallery} 
            {...props} 
        />
    ), [media, allProps.props?.gallery]);

    const renderVariants = useCallback(props => (
        <ProductVariants
            product={product}
            variants={product?.product_variants || null} 
            selectedVariantId={variant?.id || null} 
            onSelect={handleVariantChange} 
            {...allProps.props?.variants}
            {...props}
        />
    ), [product, variant, handleVariantChange, allProps.props?.variants]);

    const renderAddons = useCallback(props => (
        <ProductAddons
            product={product}
            selectedAddons={addons} 
            onSelect={handleAddonChange} 
            {...allProps.props?.addons}
            {...props}
        />
    ), [product, addons, handleAddonChange, allProps.props?.addons]);

    const renderEvents = useCallback(props => (
        <ProductEvents
            events={product?.events || null} 
            selectedUsers={forUserIds}
            onSelect={handleForUserChange}
            {...allProps.props?.events}
            {...props}
        />
    ), [product?.events, forUserIds, handleForUserChange, allProps.props?.events]);

    const renderEventInfo = useCallback(props => (
        <EventInfo
            events={product?.events || null} 
            {...allProps.props?.eventInfo}
            {...props}
        />
    ), [product?.events, forUserIds, handleForUserChange, allProps.props?.events]);

    const renderGiftCards = useCallback(props => (
        <ProductGiftCards
            recipient={giftCardRecipient}
            product={product}
            onSelect={handleGiftCardChange}
            {...allProps.props?.giftCards}
            {...props}
        />
    ), [product, giftCardRecipient, handleGiftCardChange, allProps.props?.giftCards]);

    const renderMemo = useCallback(props => (
        <ProductMemo 
            product={product}
            onSave={handleMemoSave}
            {...allProps.props?.memo}
            {...props}
        />
    ), [product, handleMemoSave, allProps.props?.memo]);

    const renderCartButton = useCallback(props => (
        <AddToCart 
            variantId={variant?.id || null} 
            amount={(+currentShopItem?.productCustomPrice || +variant?.price || 0) + (+currentShopItem?.addons?.reduce((acc, a) => +acc + +a.price, 0) || 0)}
            showQuantity={!hideQuantity} 
            onAddToCart={handleAddToCartClick} 
            isEdit={isEdit}
            loading={loading}
            {...allProps.props?.cartButton}
            {...props}
        />
    ), [product, onAddToCart, currentShopItem, variant, isEdit, loading, handleAddToCartClick, allProps.props?.cartButton]);

    /*useEffect(() => {
        if (product?.id) handleSelectProduct(product);
    }, [product, handleSelectProduct]);*/

    useLayoutEffect(() => {
        if (replacePageTitle && product?.name) {
            document.title = replacePageTitle?.replace(/\{([^}]+)\}/g, product.name || '');
        }
    }, [product?.name, replacePageTitle]);

    return {
        product,
        media,
        errors,
        setErrors,
        loading,
        hideQuantity,
        modalSize,
        fullPage,
        hides: Object.fromEntries(Object.entries(allProps).map(([k, v]) => [k, v.hide])),
        slotProps: {
            header: {...slotProps?.header, ref: headerRef},
            body: {...slotProps?.body, sx: bodySx},
            footer: {...slotProps?.footer, ref: footerRef},
            ...Object.fromEntries(Object.entries(allProps).map(([k, v]) => [k, v.props])),
        },
        slots: {
            title: renderTitle,
            gallery: renderGallery,
            variants: renderVariants,
            addons: renderAddons,
            events: renderEvents,
            eventInfo: renderEventInfo,
            giftCards: renderGiftCards,
            memo: renderMemo,
            cartButton: renderCartButton,
        }
    };
};

