import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useOutletContext, useParams, useNavigate } from 'react-router-dom';
import { ErrorBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';
import { setInfo } from '../../store/reducers/eventWizardSlice';

const apiParams = [
    {enableCache: true, params: {endpoint: '/event/type', method: 'POST', data: {is_program: 1}}},
    {enableCache: true, params: {endpoint: '/group_type', method: 'GET'}},
    {params: {endpoint: '/event/delete', method: 'DELETE'}},
    {params: {endpoint: '/event/edit_wiz', method: 'POST'}},
];

const typeId = 25; // this is the type id for programs

export const usePrograms = () => {
    const { t } = useOutletContext();
    const { typeId: urlTypeId, ...params } = useParams();

    const dispatch = useDispatch();
    const navigate = useNavigate();

    const [open, setOpen] = useState(false);
    const [isNew, setIsNew] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedPrograms, setSelectedPrograms] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [fetchCounter, setFetchCounter] = useState(0);
    const [loading, setLoading] = useState(false);
    const [errorBar, setErrorBar] = useState(null);
    const [success, setSuccess] = useState(false);

    // api calls
    const { fetchData: fetchEventTypes, data: eventTypes, loading: eventTypesLoading, ErrorBar: EventTypesErrorBar } = useApi(apiParams[0]); // event types api call
    const { fetchData: fetchGroupTypes, data: groupTypes, loading: groupTypesLoading, ErrorBar: GroupsTypesErrorBar } = useApi(apiParams[1]); // group types api call
    const { fetchData: processDelete } = useApi(apiParams[2]); // event delete api call
    const { fetchData: updateProgram, loading: updateLoading } = useApi(apiParams[3]); // event update api call

    /*const { permissions } = usePermission({moduleIds: [74, 204]});*/

    const toggleDrawer = useCallback(open => e => {
        if (e?.type === 'keydown' && (e?.key === 'Tab' || e?.key === 'Shift')) return;
        if (!open) {
            navigate(`/programs`);
            setSuccess(false);
        }
        if (!open && (isNew || isEdit)) setFetchCounter(prev => prev + 1); // resets the page and order to trigger a refresh
        if (!open && params.id) {
            setSelectedPrograms([]);
            params.id=null;
        }
        setIsNew(false);
        setIsEdit(false);
        setOpen(open);
    }, [isNew, isEdit, params.id]);

    const handleDelete = useCallback((e, program) => {
        if (program) setDeleteParams([program]);
        else setDeleteParams(selectedPrograms);
        if (program || selectedPrograms.length > 0) setShowConfirm(true);
    }, [selectedPrograms]);

    const addErrorBar = ({message, httpCode, retry}) => {
        setErrorBar(_ => (()=> (
            <ErrorBar
                open
                message={message}
                httpCode={httpCode || undefined}
                onClose={() => setErrorBar(null)}
                retry={retry || undefined}
            />
        )));
    }

    // update the program: when the status is changed, or an image is added
    const handleUpdate = useCallback(async (data, callback) => {
        setLoading(true);
        try {
            setFetchCounter(prev => prev + 1);
            const result = await updateProgram(data);
            if (result){
                if (result?.errors) {
                    addErrorBar({message: result.errors, httpCode: result?.httpCode || undefined, retry: () => handleUpdate});
                } else if (result?.data) {
                    if (callback) await callback();
                }
            } else {
                addErrorBar({message: t("error:default"), retry: () => handleUpdate});
            }
        } catch (error) {
            addErrorBar({message: t("error:default"), retry: () => handleUpdate});
        } finally {
            setLoading(false);
        }
    }, [updateProgram, addErrorBar]);

    const handleEdit = useCallback((e, program) => {
        if (program) {
            setIsEdit(true);
            setIsNew(false);
            setOpen(true);
            setSelectedPrograms([program]);
            //navigate(`/programs/${program.id}`);
        }
    }, []);

    // when the event type is selected, navigate to the wizard
    const handleEventTypeSelect = useCallback(id => {
        dispatch(setInfo({eventTypeId: id}));
        navigate(`/programs/t/${id}`); // we send the id to the wizard so it doesn't lose the selected event type when the user refreshes
    }, [dispatch, navigate]);

    // directly open the drawer for creating a new program without waiting for event types
    const handleNewProgram = useCallback(() => {
        // Navigate directly to the program creation URL with type ID 25
        navigate(`/programs/t/25`);
    }, [navigate]);

    // when the user confirms a delete
    const handleConfirmDelete = useCallback(async () => {
        if (deleteParams.length > 0) {
            const _ids = deleteParams.map(s => s.id);
            setLoading(true);
            try {
                const result = await processDelete({ id: _ids });
                if (result?.errors){
                    addErrorBar({message: result.errors, httpCode: result?.httpCode || undefined, retry: () => handleConfirmDelete});
                } else if (result?.data) {
                    setDeleteParams([]);
                    setFetchCounter(prev => prev + 1);
                    setShowConfirm(false);
                }
            } catch (error) {
                addErrorBar({message: t("error:default"), retry: () => handleConfirmDelete});
            } finally {
                setLoading(false);
            }
        }
    }, [deleteParams, processDelete, addErrorBar]);

    // when the user declines to delete
    const handleDeclineDelete = useCallback(() => {
        setShowConfirm(false);
        setDeleteParams([]);
    }, [setDeleteParams, setShowConfirm]);

    const handleSuccess = useCallback(e => {
        // First close the drawer
        toggleDrawer(false)(e);
        // Show success message
        setSuccess(true);
        // Force a refresh by incrementing the fetchCounter
        // This will trigger the useEffect in useProgramList to fetch fresh data
        setTimeout(() => {
            setFetchCounter(prev => prev + 1);
        }, 100); // Small delay to ensure state updates have completed
    }, [toggleDrawer, setFetchCounter]);

    // set the delete params when the selected events change
    useEffect(() => {
        if (selectedPrograms.length <= 0) {
            setOpen(false);
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedPrograms);
            setShowConfirm(false);
        }
    }, [selectedPrograms]);

    // open the drawer if the type id is passed in URL parameters
    useEffect(() => {
        if (urlTypeId) {
            setIsNew(true);
            setIsEdit(false);
            setOpen(true);
        }
    }, [urlTypeId]);

    // get the event types and group types on load
    useEffect(() => {
        fetchEventTypes();
        fetchGroupTypes();
    }, [fetchEventTypes, fetchGroupTypes]);

    return {
        open,
        isNew,
        isEdit,
        showConfirm,
        selectedPrograms,
        deleteParams,
        fetchCounter,
        typeId: urlTypeId || typeId,
        params,
        eventTypes,
        groupTypes,
        loading: loading || eventTypesLoading || updateLoading || groupTypesLoading,
        success,
        errorBars: [EventTypesErrorBar, GroupsTypesErrorBar, errorBar],
        setOpen,
        setIsNew,
        setIsEdit,
        setShowConfirm,
        setSelectedPrograms,
        setDeleteParams,
        setFetchCounter,
        toggleDrawer,
        handleDelete,
        handleUpdate,
        processDelete,
        handleEdit,
        handleEventTypeSelect,
        handleNewProgram,
        handleConfirmDelete,
        handleDeclineDelete,
        handleSuccess,
    };
}