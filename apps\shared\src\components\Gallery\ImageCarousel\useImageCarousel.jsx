import { useState, useMemo, useEffect, useCallback, useRef, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Slide, Fade, useMediaQuery } from '@mui/material';

const inverseDirection = direction => {
    switch (direction) {
        case "left": return "right";
        case "right": return "left";
        case "up": return "down";
        case "down": return "up";
        default: return "left";
    }
}

const DummyAnimation = memo(({children}) => <Box sx={{height:'100%', width:'100%'}}>{children}</Box>);

/*
Params:
    - images: an array with the following structure:
    {
        preview_url: "https://example.com/image.jpg",
        description: "Image description",
    }
    - imageSize: an object with the following structure: {width: "100%", height: 200}
    - objectFit: a string with the value "cover", "contain", "fill", "scale-down", "none", "initial", or "inherit". This will determine how the image will be displayed
    - objectPosition: a string with the value "center", "top", "bottom", "left", "right", "top left", "top right", "bottom left", "bottom right". This will determine the position of the image
    - aspectRatio: a string with the format "width:height". For square images use "1:1". For better looking images use a common aspect ratio like "16:9" or "4:3".
    - orientation: a string with the value "horizontal" or "vertical". This will determine the direction of the carousel, and the buttons to show
    - animation: a string with the value "slide" or "fade". This will determine the animation to use when changing images
    - timeout: the duration of the animation in milliseconds
    - disabled: a boolean to disable the carousel

This component also binds the following events to the window object:
    - ArrowLeft: go to the previous image
    - ArrowRight: go to the next image
*/
export const useImageCarousel = ({
    images = [], 
    imageSize, 
    objectFit = "cover", 
    objectPosition = "center", 
    aspectRatio = "16:9", 
    orientation = "horizontal", 
    animation = "slide", 
    timeout = 500, 
    disabled, 
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('sm'));
    if (isMobile) orientation = "horizontal"; // Force horizontal orientation on mobile devices

    const [activeStep, setActiveStep] = useState(0);
    const [slideDirection, setSlideDirection] = useState(orientation === "horizontal" ? "left" : "up");
    const [isAnimating, setIsAnimating] = useState(false);
    
    const ref = useRef(null);
    const touchStartX = useRef(0);
    const touchEndX = useRef(0);

    const ratio = aspectRatio.split(':').map(a => parseInt(a));
    const maxSteps = images.length;

    const items = useMemo(() => images.map((image, index) => (
        <img 
            key={image.preview_url} 
            src={`${image.preview_url}`} //?v=${new Date().getTime()}
            alt={image.description || `${t("file:image")} ${index + 1}`} 
            loading='lazy' 
            style={{
                width: imageSize?.width || "100%", 
                height: imageSize?.height || "100%", 
                maxHeight: "100%", 
                objectFit: objectFit || "cover",
                objectPosition: objectPosition ||"center",
                aspectRatio: "auto",
            }}
        />
    )), [images, imageSize]);

    const Animation = disabled ? DummyAnimation : (animation === "slide" ? Slide : Fade);

    const handleNext = useCallback(() => {
        if (isAnimating || disabled) return;
        setActiveStep(prev => prev < (maxSteps - 1) ? prev + 1 : 0);
        setSlideDirection(orientation === "horizontal" ? "left" : "up");
    }, [maxSteps, orientation, isAnimating, disabled]);

    const handleBack = useCallback(() => {
        if (isAnimating || disabled) return;
        setActiveStep(prev => prev > 0 ? prev - 1 : maxSteps -1);
        setSlideDirection(orientation === "horizontal" ? "right" : "down");
    }, [maxSteps, orientation, isAnimating, disabled]);

    const handleKeyDown = useCallback(e => {
        if (isAnimating || images.length <= 1 || disabled) return;
        if (orientation === "horizontal") {
            if (e.key === 'ArrowLeft') handleBack();
            if (e.key === 'ArrowRight') handleNext();
        } else if (orientation === "vertical") {
            if (e.key === 'ArrowUp') handleBack();
            if (e.key === 'ArrowDown') handleNext();
        }
    }, [handleBack, handleNext, isAnimating, images, disabled]);

    const handleTouchStart = useCallback(e => {
        if (isAnimating || images.length <= 1 || disabled) return;
        if (orientation === "horizontal") touchStartX.current = e.touches[0].clientX;
        if (orientation === "vertical") touchStartX.current = e.touches[0].clientY;
    }, [orientation, isAnimating, disabled, images]);

    const handleTouchMove = useCallback(e => {
        if (isAnimating || images.length <= 1 || disabled) return;
        if (orientation === "horizontal") touchEndX.current = e.touches[0].clientX;
        if (orientation === "vertical")touchEndX.current = e.touches[0].clientY;
    }, [orientation, isAnimating, disabled, images]);

    const handleTouchEnd = useCallback(() => {
        if (isAnimating || images.length <= 1 || disabled) return;
        if (touchStartX.current - touchEndX.current > 50) handleNext();
        if (touchStartX.current - touchEndX.current < -50) handleBack();            
    }, [handleNext, handleBack, isAnimating, images, disabled]);    

    useEffect(() => {
        if (disabled) return;
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [handleKeyDown, disabled]);

    return {
        items,
        Animation,
        activeStep,
        slideDirection,
        slideInverseDirection: inverseDirection(slideDirection),
        isAnimating,
        setIsAnimating,
        handleNext,
        handleBack,
        handleTouchStart,
        handleTouchMove,
        handleTouchEnd,
        ratio,
    }
}