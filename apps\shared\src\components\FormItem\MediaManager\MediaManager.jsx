import React, { useState, useCallback, Suspense, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Button, useMediaQuery } from '@mui/material';
import { CloudUploadOutlined as UploadIcon, CloseOutlined as CloseIcon } from '@mui/icons-material';

import Modal from '../../Modal';

const DynamicIcon = lazy(() => import('./Icon'));


import SbMediaManager from '../../MediaManager';

export const  MediaManager = ({
    label, // the label for the button
    name, // the name of the button
    icon, // the icon for the button
    variant, // the variant of the button
    color, // the color of the button
    fullWidth, // if the button should be full width
    accept, // the file types to accept
    mediaType, // the media type to show
    fileSize = 209715200, // the file size
    errors, // the errors for the button
    loading, // the loading status of the form so we can disable the button
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onBlur, // the function to be called when the field is blurred
    multiple = false, // if the button should accept multiple files
    returnMultiple = false, // if the button should return multiple files
    size, // the size of the button
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [open, setOpen] = useState(false);
    const [returnedFiles, setReturnedFiles] = useState([]);

    const handleClickOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    const handleSelection = useCallback(selected => {
        if (onChange) onChange({ preventDefault: () => {}, stopPropagation: () => {}, target: { name, value: selected } });
        if (onBlur) onBlur({ preventDefault: () => {}, stopPropagation: () => {}, target: { name, value: selected } });
        if (!returnMultiple) handleClose();        
    }, [onChange, onBlur, name, returnMultiple, handleClose]);

    if (typeof icon === 'string') {
        icon = <Suspense fallback={<UploadIcon />}><DynamicIcon name={icon} /></Suspense>;
    }

    const marginSx = {
        'dense': {mt: 0.5, mb: 0.5},
        'normal': {mt: 1, mb: 0.5},
        'none': {m: 0},
    };

    return (
        <Box sx={marginSx[props?.margin || 'normal']}>
            <Button 
                variant={variant || 'contained' }
                color={color || 'primary'}
                fullWidth={fullWidth}
                startIcon={icon || <UploadIcon />}
                disabled={loading}
                size={size || undefined}            
                onClick={handleClickOpen}
            >
                {t(label || 'file:upload')}
            </Button>

            <Modal 
                open={open} 
                onClose={handleClose} 
                title={t(label || 'file:upload')} 
                maxWidth='lg'
                slotProps={{paperProps: isMobile ? undefined : {
                    style: {
                        height: `calc(100% - 64px)`
                    }
                }}}
            >
                <SbMediaManager 
                    onSelection={handleSelection} 
                    multiple={multiple} 
                    accept={accept} 
                    size={fileSize} 
                    parentLoading={loading} 
                    mediaType={mediaType}
                />
            </Modal>
        </Box>
    );
}