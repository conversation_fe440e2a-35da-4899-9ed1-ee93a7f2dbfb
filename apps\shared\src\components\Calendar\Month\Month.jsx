import React from 'react';
import { useSelector } from 'react-redux';
import { Grid2, Paper, Typography } from '@mui/material';
import { format, eachDayOfInterval, startOfMonth, endOfMonth, startOfWeek, endOfWeek, isSameMonth, isToday } from 'date-fns';
import { formatDate } from '../../../utils';

import Event from './Event';

export const Month = ({
    currentDate, // the current date (Date object)
    events, // an array of events (optional)
    metaEvents, // an array of meta events (optional)
    colors, // an array of colors (optional)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props // additional props
}) => {
    const language = useSelector(state => state.language);

    const startDay = startOfWeek(startOfMonth(currentDate));
    const endDay = endOfWeek(endOfMonth(currentDate));
    const daysArray = eachDayOfInterval({ start: startDay, end: endDay });

    if (!currentDate) return null;

    return (
        <Grid2 container spacing={0} sx={{border: theme => `1px solid ${theme.palette.divider}`, borderRight: 0}}>
            {daysArray.map((day, i) => (
                <Grid2 size={{xs: 12 / 7}} key={`calendar-month-${day}-${i}`} sx={{minHeight: '100px' }}>
                    <Paper elevation={0} square variant="outlined" disabled={!isSameMonth(day, currentDate)}
                        sx={{
                            position: 'relative',
                            height: '100%',
                            py: 1,
                            backgroundColor: isSameMonth(day, currentDate) ? 'transparent' : theme=> theme.palette.mode === "dark" ? 'transparent' : theme.palette.action.disabledBackground,
                            borderColor: isToday(day) ? theme => theme.palette.primary.light : undefined,
                            borderWidth: isToday(day) ? 3 : undefined,
                            borderLeft: isToday(day) ? undefined : 0,
                            borderBottom: isToday(day) ? undefined : 0,
                            borderTop: (isToday(day) || i >= 7) ? undefined : 0,
                        }}
                    >
                        {i < 7 && 
                            <Typography align="center" variant="subtitle3" component="h6" 
                                sx={{
                                    color: theme => isSameMonth(day, currentDate) ? undefined : theme.palette.action.disabled,
                                }}
                            >
                                {formatDate(day, language.code, 'E')}
                            </Typography>
                        }
                        <Typography align="center" variant="subtitle3" component="h5"
                            sx={{
                                color: theme => isSameMonth(day, currentDate) ? undefined : theme.palette.action.disabled,
                            }}
                        >
                            {+format(day, 'd') === 1 ? formatDate(day, language.code, 'MMM d') : formatDate(day, language.code, 'd')}
                        </Typography>

                        {events?.length > 0 && events.map((event, i) => (
                            <Event 
                                currentDate={day} 
                                event={event} 
                                disabled={disabled} 
                                color={colors ? colors?.[i % colors.length] : null} 
                                key={`event-${event.id}-${i}`} 
                                onEventClick={onEventClick}
                            />
                        ))}
                    </Paper>
                </Grid2>
            ))}
        </Grid2>
    );
}