import React from 'react';
import { useSelector } from 'react-redux';
import { createCurrencyFormatter } from '../../../../../../../utils/currency';
import { ButtonWrapper, Caption } from '../../../../../common/pos';

export const Button = ({ items, selected, product_type_id, disabled, onSelect, slots, slotProps, ...props }) => {
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    return (
        <ButtonWrapper items={items} selected={selected} disabled={disabled} onClick={onSelect} slots={slots} slotProps={slotProps}>
            {items.map(item => {
                let _price = +item?.price || 0;
                if (_price > 0 && selected.find(a => a.id === item.id)) _price = -_price;
                return (
                    <React.Fragment key={item.id}>
                        {item?.name && <Caption variant="subtitle1" component="span" text={item.name} bold />}
                        {item?.price > 0 && <Caption variant="subtitle2" component="span" text={`${_price < 0 ? "-" : "+"}${currencyFormatter.format(item.price, currency)}`} bold />}
                    </React.Fragment>
                );
            })}
        </ButtonWrapper>
    );
};