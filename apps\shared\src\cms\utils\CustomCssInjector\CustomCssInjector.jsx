import React from 'react';
import { useTheme } from '@mui/material/styles';

export const CustomCssInjector = ({ component, ignoreGlobal = false }) => {
    const theme = useTheme();
    const customCSS = typeof theme.customCSS === 'function' ? theme.customCSS(theme) : theme.customCSS;

    if (!customCSS) return null;

    return (
        <>
            {!ignoreGlobal && <style>{customCSS.global}</style>}
            {component && <style>{customCSS.components[component]}</style>}
        </>
    );
};

export const useCustomCss = ({ component }) => {
    const theme = useTheme();
    let customCSS = typeof theme.customCSS === 'function' ? theme.customCSS(theme) : theme.customCSS;

    if (component) customCSS = customCSS?.components?.[component];

    return customCSS;
};