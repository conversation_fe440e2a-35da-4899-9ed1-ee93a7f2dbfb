import { useEffect, useMemo, useCallback, useState, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { useApi } from '../../../api/useApi';
import { buildTree } from '../../../utils';

const apiParams = [
    {enableCache: true, params: {endpoint: `/category`, method: 'POST'}},
    {enableCache: true, params: {endpoint: `/product/type`, method: 'GET'}},
];

/*
type: "category" or "type"
options: (optional) can be an array on parent-child objects, like [{id: 1, name: "<PERSON><PERSON>", children: [{id: 2, name: "Child"}]}]
ids: (optional) can be an array of ids to filter the results
*/
export const useProductFilter = ({ isBuilder = false, type = "category", options, ids = [] }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const loaded = useRef(false);

    const { fetchData:fetchCategories } = useApi(apiParams[0]);
    const { fetchData:fetchTypes } = useApi(apiParams[1]);

    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);
    const [filterData, setFilterData] = useState(null);

    const handleSelection = useCallback(value => {
        if (!isBuilder) navigate(`#${value || ""}`);
    }, [navigate, isBuilder]);

    const itemIds = useMemo(() => ids?.reduce((acc, id) => {
        if (typeof id === "object") acc.push(id.id);
        else acc.push(id);
        return acc;
    }, []) || [], [ids]);

    const fetchFilters = useCallback(async params => {
        try {
            setLoading(true);
            let _data = [];
            if (type === "category") {
                const res = await fetchCategories({ max_records: 999, id: itemIds || undefined, ...params });
                if (res.errors) setErrors(res.errors);
                else if (res?.data?.categories) _data = res.data.categories;
            } else {
                const res = await fetchTypes({endpoint: `/product/type${itemIds ? `/${itemIds.join(",")}` : ""}`, ...params});
                if (res.errors) setErrors(res.errors);
                else if (res?.data) _data = res.data;
            }
            if (_data.length) {
                setFilterData(buildTree(_data));
            }
        } catch (error) {
            setErrors(error);
        } finally {
            setLoading(false);
        }
    }, [type, itemIds, location.hash, fetchCategories, fetchTypes]);

    useEffect(() => {
        if (!loaded.current){
            if (options && options.length) setFilterData(options);
            else fetchFilters();
            loaded.current = true;
        }
    }, [options, fetchFilters]);

    return {
        itemIds,
        data: filterData || [],
        loading,
        errors,
        handleSelection,
        selectedId: location.hash.replace("#", "")
    };
}