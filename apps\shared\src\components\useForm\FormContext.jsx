import React, { createContext, useContext, useState } from 'react';

const FormContext = createContext();
export const FormProvider = ({ children, values, onSubmit = () => {} }) => {
    // output other props
    const [formData, setFormData] = useState(values || {});

    const resetForm = () => {
        console.log('resetting form');
        setFormData({});
    };

    // Save data to the form state. Keys should be unique, and can be used to group different forms, eg in a wizard form.
    // data must be an object with this format { name: "field_name", value: "field_value", checked: true/false/undefined, ... }
    const saveData = (key = 0, data) => {
        console.log('saving data', key, data);
        /*let _data = data;
        if (!Array.isArray(data)) _data = [data];
        setFormData(prev => {
            const _prev = { ...prev };
            _data.forEach(field => {
                if (field.name) {
                    if (!_prev[key]) _prev[key] = [];
                    const idx = _prev[key].findIndex(a => a.name === field.name);
                    if (idx === -1) _prev[key].push(field);
                    else _prev[key][idx] = { ...field };
                }
            });
            return _prev;
        });*/
        //({ ...prev, [key]: [...(prev?.[key] || []), ..._data ] });
    }

    // Submit the form data. If using the UseForm Hook, this would replace the hook's submit fuction. 
    // UseForm's submit could be used instead to update the general form state managed by this context.
    // flat: if false, the form data will be grouped by the key, otherwise it will be flattened.
    const submitForm = async ({flat = true, ...props }) => {
        console.log('submitting form', props);
        // log the formData object from redux
        
        let usesKeys = false;
        if (Object.keys(formData).every(a => !isNaN(a))) usesKeys = true;

        let fullFormData = {};
        if (!flat || !usesKeys) {
            // format form data to be key: [{name: value}, ...]
            for (const key in formData) {
                fullFormData[key] = formData[key].reduce((acc, data) => {
                    if (data.name && data?.checked !== false) acc.push({ name: data.name, value: data.value});
                    else {
                        for (const field in data) {
                            acc.push({ name: field, value: data[field] });
                        }
                    }
                    return acc;
                }, []);
            }
        } else {
            // flatten the form data so its just {name: value}
            for (const key in formData) {
                formData[key].forEach(field => {
                    fullFormData[field.name] = field.value;
                });
            }
        }        
        onSubmit(fullFormData, resetForm);
    }

    return (
        <FormContext.Provider value={{ formData, saveData, submitForm, resetForm }}>
            {children}
        </FormContext.Provider>
    );
};

export const useFormContext = () => useContext(FormContext);