import { useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useApi } from '../../api/useApi';
import { setInfo, resetProduct } from '../../store/reducers/currentShopItemSlice';

const apiParams = {params: {endpoint: `/product`, method: 'POST', data: {product_status_id: 1, max_records: 999, include_child_categories: 1}}, enableCache: true};

export const usePosProducts = ({containerRef, thresholdRef, itemsToLoad = null, skipItemLoad = false, onAddToCart, ...props}) => {
    const dispatch = useDispatch();
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);

    const [allProducts, setAllProducts] = useState([]);
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);
    const [hasMore, setHasMore] = useState(true);
    const [openProductDetail, setOpenProductDetail] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);

    const { fetchData: fetchProducts, loading: productLoading} = useApi(apiParams);

    const handleSelectProduct = useCallback(value => {
        dispatch(setInfo({productId: value?.id || value, qty: 1}));
        setSelectedProduct({productId: value?.id || value, qty: 1});
        setOpenProductDetail(true);
    }, [dispatch]);

    const handleDetailsClose = useCallback(() => {
        dispatch(resetProduct());
        setOpenProductDetail(false);
    }, [dispatch]);

    const handleAddToCart = useCallback((product, reduxState) => {
        const res = onAddToCart(product, reduxState);
        switch (res){
            case 0:
                return;
            case 1:
                handleDetailsClose();
                break;
            case 2:
                handleSelectProduct(product);
                break;
            default:
                break;
        }
    }, [handleDetailsClose, handleSelectProduct, onAddToCart]);

    const fetchData = useCallback(async () => {
        const params = {};
        if (currentShopItem?.productCategoryId) params.categories = [currentShopItem.productCategoryId];
        if (currentShopItem?.productTypeId) params.product_types = [currentShopItem.productTypeId];
        if (currentShopItem?.search) params.search = currentShopItem.search;
        //if (currentShopItem?.dateAvailable) params.date_available_after = formatISO(currentShopItem.dateAvailable);

        setLoading(true);
        try{
            const res = await fetchProducts(params);
            if (res.errors) setErrors(res.errors);
            else if (res.data?.products){
                /*const prods = res.data.products.map(p => ({
                    id: p.id,
                    name: p.name,
                    events: p.events,
                    is_taxable: p.is_taxable,
                    product_variants: p.product_variants,           
                }));
                console.log(res.data?.products)*/
                setAllProducts(res.data?.products);
                if (itemsToLoad) {
                    setFilteredProducts(res.data?.products.slice(0, itemsToLoad));
                    if (res.data?.products.length <= itemsToLoad) setHasMore(false);
                    else setHasMore(true);
                }
            }
        } catch (error){
            setErrors(error);
        } finally {
            setLoading(false);
        }
    }, [currentShopItem?.productCategoryId, currentShopItem?.productTypeId, currentShopItem?.search, currentShopItem?.dateAvailable, itemsToLoad, fetchProducts]);

    // infinite scroll (TODO: make this better so it loads products from the server based on itemsToLoad to make rendering faster)
    useEffect(() => {
        if (!itemsToLoad || loading || !hasMore) return;

        const handleObserver = entities => {
            const target = entities[0];
            if (target.isIntersecting && !loading && hasMore) {
                setLoading(true);        
                setFilteredProducts(prev => {
                    const nextBatch = allProducts.slice(prev.length, prev.length + itemsToLoad);
                    if (nextBatch.length === 0) {
                        setHasMore(false); // Stop loading if no more products are available
                    }
                    setLoading(false);
                    return [...prev, ...nextBatch];
                });
            }
        };

        const observer = new IntersectionObserver(handleObserver, {
            root: containerRef?.current || null,
            rootMargin: '20px',
            threshold: 1,
        });
        
        if (thresholdRef?.current) observer.observe(thresholdRef.current);

        return () => {
            if (thresholdRef.current) {
                observer.unobserve(thresholdRef.current);
                observer.disconnect();
            }            
        }
    }, [loading, hasMore, allProducts, thresholdRef?.current, containerRef?.current, itemsToLoad]);

    // fetch data on load
    useEffect(() => {
        if (!skipItemLoad) fetchData();
        return () => {
            setAllProducts([]);
            setFilteredProducts([]);
            setOpenProductDetail(false);
        }
    }, [fetchData, skipItemLoad]);

    return {
        products: itemsToLoad ? filteredProducts : allProducts,
        loading: loading || productLoading,
        errors,
        setErrors,
        fetchProducts,
        handleSelectProduct,
        handleAddToCart,
        handleDetailsClose,
        openProductDetail,
        setOpenProductDetail,
        selectedProduct,
    };
};
