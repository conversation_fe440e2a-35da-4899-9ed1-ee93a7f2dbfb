import { Module } from '../Menu/Module/Module';

export default {
    title: 'Shared/Layout/Menu/Module',
    component: Module,
    tags: ['autodocs'],
    argTypes: {
        modules: {
            description: "Array of module objects",
            control: 'array',
            type: { required: true },
            table: {
                type: { summary: "array" },
                defaultValue: { summary: "[]" },
                detail: "An array of module objects, each containing an id and slug."
            }
        },
        selectedModuleId: {
            description: "ID of the currently selected module",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 0 },
                detail: "The ID of the module that is currently selected."
            }
        },
        onModuleChange: {
            description: "Function to call when the module is changed",
            control: 'action',
            table: {
                type: { summary: "function" },
                defaultValue: { summary: "undefined" },
                detail: "A callback function that is called when the selected module changes."
            }
        },
        isDocked: {
            description: "Flag to indicate if the module is docked",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: "false" },
                detail: "A boolean flag indicating whether the module is docked."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        modules: [
            { id: 1, slug: 'module1' },
            { id: 2, slug: 'module2' }
        ],
        selectedModuleId: 1
    }
};

// Following stories to illustrate each significant prop
export const WithSelectedModule = {
    args: {
        modules: [
            { id: 1, slug: 'module1' },
            { id: 2, slug: 'module2' }
        ],
        selectedModuleId: 2
    }
};

export const WithModuleChangeAction = {
    args: {
        modules: [
            { id: 1, slug: 'module1' },
            { id: 2, slug: 'module2' }
        ],
        selectedModuleId: 1,
        onModuleChange: module => alert(`Module changed to: ${module.slug}`)
    }
};

export const DockedModule = {
    args: {
        modules: [
            { id: 1, slug: 'module1' },
            { id: 2, slug: 'module2' }
        ],
        selectedModuleId: 1,
        isDocked: true
    }
};