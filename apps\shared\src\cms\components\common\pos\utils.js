export const formatRecurringItem = data => {
    const _intervalNames = {
        adverb: {
            m: 'Monthly',
            w: 'Weekly',
            d: 'Daily',
            y: 'Yearly',
        },
        noun: {
            m: 'Month',
            w: 'Week',
            d: 'Day',
            y: 'Year',
        },
        nounPlural: {
            m: 'Months',
            w: 'Weeks',
            d: 'Days',
            y: 'Years',
        },
    }

    let recurringInfo = [];
    if (data?.bill_interval && data?.interval_quantity){
        if (data?.interval_quantity === 1){
            recurringInfo = [
                "subscription:billed",
                `subscription:${_intervalNames.adverb[data.bill_interval.toLowerCase()]}`,
                "subscription:for",
                data.interval_quantity,
                `subscription:${_intervalNames.noun[data.bill_interval.toLowerCase()]}`,
            ];
        } else {
            recurringInfo = [
                "subscription:billedEvery",
                `subscription:${_intervalNames.noun[data.bill_interval.toLowerCase()]}`,
                'subscription:for',
                data.interval_quantity,
                `subscription:${_intervalNames.nounPlural[data.bill_interval.toLowerCase()]}`,
            ];
        }
    }

    return recurringInfo;
}