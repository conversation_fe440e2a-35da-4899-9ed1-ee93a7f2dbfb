import React, { useContext, useCallback, useState, useEffect } from 'react';
import { differenceInYears } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { Stack, Typography, Button as <PERSON><PERSON><PERSON><PERSON><PERSON>, Collapse, useMediaQuery } from '@mui/material';
import { AddOutlined } from '@mui/icons-material';

import { Modal, LoadingBar } from '../../../../../../../components';
import { PosProductDetailContext, PosContext } from '../../../../../../hooks';
import { Checkbox, Radio, Button } from './Layouts';
import { useEventUsers } from "./useEventUsers";
import CustomFields from "./CustomFields";
import NewUser from "./NewUser";

export const EventUsers = ({layoutType = "checkbox", event, selectedUsers, onSelect, slotProps, ...props}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const { selectedUser, LoginForm } = useContext(PosContext) || {};
    const { fullPage, changeView, loading, setLoading, setErrors } = useContext(PosProductDetailContext) || {};

    const { user, family, loading:eventUserLoading, errors:eventUserErrors } = useEventUsers({event, userId: selectedUser?.id});

    const [openNewModal, setOpenNewModal] = useState(false);

    let Component = Checkbox;
    switch(layoutType) {
        case 'radio':
            Component = Radio;
            break;
        case 'button':
            Component = Button;
            break;
        default:
            break;
    }

    const handleNewClick = useCallback(() => {
        if (fullPage) setOpenNewModal(true);
        else changeView(<NewUser user={user} />);
    }, [fullPage, user, changeView]);

    useEffect(() => {
        setLoading(eventUserLoading);
        setErrors(eventUserErrors);
    }, [eventUserLoading, setLoading, eventUserErrors, setErrors]);

    return (
        <Stack direction="column" spacing={0} useFlexGap {...slotProps?.stack} position="relative">
            <Typography variant="subtitle2" component="div">
                <Typography variant="bold" component="div">{t("pos:whoWillRegisterForEvent")}</Typography>
            </Typography>
            {eventUserLoading && <LoadingBar type="linear" sx={{height: '2px', position: 'absolute', top: 24, width: '100%'}} />}
            {selectedUser ?
                <Collapse in={!eventUserLoading && family?.length > 0}>
                    <Component 
                        items={family} 
                        selected={layoutType === "radio" ? selectedUsers?.[0] : selectedUsers} 
                        disabled={loading} 
                        onSelect={onSelect}
                        slotProps={{
                            fullPage,
                            changeView: fullPage ? null : changeView,
                            extraInfo: {
                                show: Boolean(event?.metadata?.custom_fields?.length > 0),
                            },
                            ...slotProps,
                        }}
                        slots={{
                            extraInfo: ({item, ...props}) => <CustomFields user={item} event={event?.metadata} {...props} />,
                            newInfo:  ({item, ...props}) => <NewUser user={item} {...props} />, // we don't really need this but we'll keep it just in case it is needed in the future
                            validate: item => {
                                //if ((event?.metadata?.max_participants || 0) >= (event?.metadata?.total_participants || 0) + selectedUsers.length) return t("event:eventCapacityFull");
                                if (event?.metadata?.min_age || event?.metadata?.max_age){
                                    const age = differenceInYears(new Date(), new Date(item.dob));
                                    if (event.metadata.min_age && age < event.metadata.min_age) return event?.metadata.below_age_msg || t("event:belowAge");
                                    if (event.metadata.max_age && age > event.metadata.max_age) return event?.metadata.above_age_msg || t("event:aboveAge");
                                }
                                return true;
                            },
                        }}
                    />

                    <MuiButton variant="outlined" fullWidth startIcon={<AddOutlined />} onClick={handleNewClick}>
                        {t("user:newFamilyMember")}
                    </MuiButton>
                </Collapse>
            : <Typography variant="body1" color="error" component="div">{t("pos:selectUser")}</Typography>}

            {fullPage && 
                <Modal 
                    open={openNewModal} 
                    onClose={e => setOpenNewModal(false)}
                    maxWidth={slotProps?.details?.modalSize || "sm"}
                    fullScreen={isMobile}
                    aria-describedby={t(`user:newFamilyMember`)}
                >
                    <NewUser user={user} onClose={e => setOpenNewModal(false)} />
                </Modal>
            }
        </Stack>
    );
}