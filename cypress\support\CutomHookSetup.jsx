import React from 'react';


/**
 * Gives both a visual for hooks to test them and allows us to interact with the data because we have a React component.  This will provide live data manipulation.  If you want to mock data, stub the calls in the Cypress test.  
 * 
 * useCustomHook - the custom hook that needs to be tested
 * initial props - any props passed into the hook
 * other props - as needed
 */
export const CustomHookSetup = ({useCustomHook, initialProps = null, ...props})=>{
    
    const hookResult = useCustomHook(initialProps);

    return(
        <div data-cy="hook-test-top-div">
            {Object.entries(hookResult).map(([key, value]) => (
                <div key={key} data-cy={`hook-${key}`}>
                {typeof value === 'function' ? (
                    <button onClick={() => value()} data-cy={`hook-${key}-button`}>
                        "fn()" - {key}
                    </button>
                ) : (
                    <span>{JSON.stringify(value)}</span>
                )}
                </div>
            ))}
        </div>
    )
}
