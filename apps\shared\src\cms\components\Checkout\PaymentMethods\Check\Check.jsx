import React, { useState, useCallback, useMemo, useEffect, useId } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { <PERSON>ack, Grid2, <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material';

import FormItem from '../../../../../components/FormItem';
import { createCurrencyFormatter } from '../../../../../utils/currency';

const fields = [
    {
        name: 'check_name',
        label: 'check:name',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 1,
    },
    {
        name: 'check_number',
        label: 'check:number',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 2,
    },
    {
        name: 'amount',
        label: 'pos:amount',
        required: true,
        component: 'MoneyField',
        margin: 'dense',
        rowId: 2,
    },
];

export const Check = ({
    paymentMethod, 
    paymentMethodId, 
    amount, 
    loading: parentLoading, 
    removeCashDiscount,
    cashDiscount = 0,
    onPaymentProcess, 
    onPaymentChange, 
    slotProps, 
    ...props
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);       
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const id = useId();

    const [values, setValues] = useState({amount, removeCashDiscount});
    const [errors, setErrors] = useState({});

    // group by row id
    const rows = useMemo(() => (
        [...(fields || [])].reduce((acc, field) => { 
            const group = acc.find(g => g[0]?.rowId === field.rowId);
            if (group) group.push(field);
            else acc.push([field]);
            return acc;
        }, [])
    ), []);    

    const isValid = useMemo(() => {
        let valid = true;
        for (let i = 0; i < fields.length; i++) {
            let _valid = values?.[fields[i].name] && !errors?.[fields[i].name];
            if (fields[i].required && !_valid) {
                valid = false;
                break;
            }
        }
        return valid;
    }, [errors, values, t]);

    const checkErrors = useCallback(_values => {
        let _errors = {};
        const field = fields.find(f => f.name === _values.name);
        if (!field) _errors = {[_values.name]: t('error:invalid')};
        else if (field.required){
            if (_values.name === "amount" && _values?.value < 0) {
                _errors = {[field.name]: t('error:invalid')};
            }
            if (!_values?.value) {
                _errors = {[field.name]: t('error:required')};
            }
        }
        return _errors;
    }, [t]);

    const handleChange = useCallback(e => {
        let _errors = checkErrors(e.target);
        setErrors(prev => ({...prev, [e.target.name]: null, ..._errors}));
        setValues(prev => ({...prev, [e.target.name]: e.target.value}));
    }, [checkErrors]);

    const handlePay = useCallback(async () => {
        if (onPaymentProcess && isValid) await onPaymentProcess({id, values: values, paymentMethod, paymentMethodId});
    }, [id, onPaymentProcess, values, isValid, paymentMethod, paymentMethodId]);

    useEffect(() => {
        if (onPaymentChange && isValid) onPaymentChange({id, values, paymentMethod, paymentMethodId});
    }, [id, values, isValid, onPaymentChange, paymentMethod, paymentMethodId]);

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            {rows.map((_, i) => (
                <Grid2 key={i} container spacing={2}>
                    {fields?.filter(a => a.rowId === i+1).map(field => (
                        <Grid2 key={field.name} size={{xs: 12, lg: "grow"}}>
                            <FormItem 
                                key={field.name}
                                {...field}
                                label={t(field.label)}
                                value={values?.[field.name] || ""}
                                onChange={handleChange}
                                errors={errors?.[field.name]}
                                {...slotProps?.input}
                            />
                        </Grid2>
                    ))}
                </Grid2>
            ))}            
            <Button 
                loading={parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl" 
                fullWidth 
                disabled={parentLoading || !isValid || +values?.amount === 0} 
                onClick={handlePay} 
                {...slotProps?.button}
            >
                {t(`pos:${+values?.amount < +amount ? 'processPartialPayment' : 'processPayment'}`)}
            </Button>
            {removeCashDiscount &&
                <Alert variant="outlined" severity="warning">{t("pos:warnings.removeCashDiscount", {amount: currencyFormatter.format(cashDiscount, currency)})}</Alert>
            }
        </Stack>
    );
}