import { useEffect, useCallback, useRef, useMemo, useReducer } from 'react';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { debounceAsync } from '../../utils';
import { initialState, mapErrorMessage } from '../initialState';
import { axiosReducer } from './axiosReducer';

const baseUrl = import.meta.env.VITE_API_URL;

const loadLocalToken = () => {
    let localuser = localStorage.getItem("user");
    let token = null;
    if (localuser) {
        try {
            localuser = JSON.parse(localuser);
            token = localuser.token;
        } catch(error){}
    }
    return token;
}

export const useAxios = () => {
    const { t } = useTranslation();
    const abortController = useRef();
    const [state, dispatch] = useReducer(axiosReducer, initialState);

    const token = useMemo(() => {
        let _token = loadLocalToken();
        if (_token && !_token.startsWith("Bearer ")) {
            _token = "Bearer " + _token;
        }        
        return _token;
    }, []);

    const setState = useCallback(newState => {
        dispatch({ type: "SET_STATE", payload: newState });
    }, []);

    const call = useCallback(async requests => {
        dispatch({ type: "REQUEST_INIT" });

        if (!Array.isArray(requests)) requests = [requests];
        abortController.current = new AbortController();

        try {
            const responses = await Promise.all(requests.map(r => {
                // Check if params is empty and remove it if it is to avoid trailing question mark
                const requestConfig = {
                    ...r,
                    url: `${baseUrl}${r.endpoint}`,
                    signal: abortController.current.signal,
                    headers: {
                        Authorization: token || undefined,
                        ...r.options?.headers,
                    },
                };

                // If params exists but is empty, remove it to avoid the trailing question mark
                if (requestConfig.params && Object.keys(requestConfig.params).length === 0) {
                    delete requestConfig.params;
                }

                return axios(requestConfig).then(response => ({ response })).catch(error => ({ error }));
            }));

            //setState(prev => ({ ...prev, loading: true }));

            let data = [];
            let errors = [];
            let httpCodes = [];

            responses.forEach(res => {
                if (res.response) {
                    data.push(res.response.data?.data || null);
                    errors.push(null);
                    httpCodes.push(res.response.status);
                } else if (res.error) {
                    if (res.error.code === 'ERR_CANCELED') {
                        // Check if the request was canceled because the component unmounted
                        if (res?.error?.config?.signal?.unMount){
                            //setState(prev => ({ ...prev, loading: true }));
                            //return false;
                        }
                    } else {
                        errors.push(t(mapErrorMessage(res.error.response?.status)));
                        data.push(null);
                        httpCodes.push(res.error.response?.status);
                    }
                }
            });

            // If only one request, return single values
            if (requests.length === 1) {
                data = data[0];
                errors = errors[0];
                httpCodes = httpCodes[0];
            }

            dispatch({
                type: "REQUEST_SUCCESS",
                payload: { data, httpCode: httpCodes },
            });
            return { data, errors, httpCode: httpCodes };
        } catch (error) {
            dispatch({
                type: "REQUEST_FAILURE",
                payload: {
                  errors: t('error:codeError'),
                  httpCode: 500,
                },
            });
        } finally {
            dispatch({type: "REQUEST_END"});
        }
    }, [t, token]);

    //const call = useMemo(() => debounceAsync(doCall, 10), [doCall]);

    const cancel = useCallback(() => {
        dispatch({ type: "REQUEST_CANCEL" });
        abortController.current.abort();
    }, []);

    useEffect(() => {
        return () => {
            if (abortController.current){
                abortController.current.signal.unMount = true;
                abortController.current.abort();
            }
        };
    }, []);

    return { ...state, hookName: 'useAxios', setState, call, cancel };
}