
.wrapper {
    position: relative;

    .title {
        margin-top: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .description {
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .content-wrapper{
        margin-top: 2rem;

    }

    .cta-wrapper{
        position: sticky;
        top: 120px;
        height: fit-content;
        margin-right: 1rem;
    
        span:global(.MuiTypography-root) {
            //font-weight: 600;
        }

        .box {
            position: relative;
        }
    }
}

.mobile {
    .wrapper {
        padding: 1rem;
        margin-top: 0;        
        
        .title {
            margin-top: 0;
            margin-bottom: 0;
        }

        .content-wrapper{
            margin-top: 0;
            padding-bottom: 6rem;
            order: 2;
        }

        .cta-wrapper{
            position: relative;
            top: unset;
            margin-right: unset;
            order: 1;

            .box {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 1rem;
                border-top-width: 1px;
                border-top-style: solid;
            }
        }
    }
}
