# Siteboss Frontend Development Best Practices

## Table of Contents
- [Shared Components](#shared-components)
- [State Management](#state-management)
- [Testing](#testing)
- [Application Development](#application-development)
- [Website (SSR) Development](#website-ssr-development)

## Shared Components

### Available Components
The following components are available from `@siteboss-frontend/shared/components`:

#### Layout Components
- `Layout` - Base layout wrapper
- `Portal` - React portal implementation
- `ErrorBoundary` - Error handling wrapper
- `WithDetails` - Detail view wrapper
- `WithSortable` - Sortable list wrapper
- `WithScrollEffect` - Scroll-based effects wrapper
- `SimpleBar` - Custom scrollbar implementation
- `404` - Not found page handler

#### Form and Input Components
- `FormItem` - Base form item wrapper
- `InlineEditor` - Inline text editing
- `FileUpload` - File upload handler
- `MediaManager` - Media file management
- `ItemSelector` - Item selection interface
- `GroupChips` - Grouped chip selection
- `GridConfig` - Grid configuration interface

#### UI Components
- `ToolbarButtons` - Standard toolbar buttons
- `Chart` - Data visualization
- `Login` - Authentication form
- `Snacks` - Toast notifications
- `Calendar` - Date picker/calendar
- `Gallery` - Image gallery
- `DataTable` - Data grid
- `Previews` - Content preview
- `Modal` - Modal dialog
- `MoreMenu` - Additional options menu
- `Confirm` - Confirmation dialog
- `Logo` - Application logo
- `Title` - Page title component
- `ExpandableText` - Expandable text content
- `SortableTree` - Sortable tree structure

#### Hooks
- `useForm` - Form handling hook
- `useBreakpoint` - Responsive breakpoint detection
- `useCollectJs` - Collection management
- `useEventData` - Event data handling
- `usePath` - Path management
- `usePrint` - Print functionality
- `useTraceUpdate` - Component update tracing
- `useEventData` - Event data management

#### Wizard Components
- `Wizard` - Step-by-step guide implementation

### Component Usage Rules

1. **Provider Requirements**
   ```jsx
   <ApiProvider>
     <ThemeProvider>
       <PermissionProvider>
         {/* Your component */}
       </PermissionProvider>
     </ThemeProvider>
   </ApiProvider>
   ```

2. **Data Attributes**
   - Always include `data-cy` attributes for testing
   - Follow pattern: `data-cy="component-name-element"`

3. **Component Documentation**
   - Create Storybook stories for all shared components
   - Include component API documentation
   - Provide usage examples

4. **Import Pattern**
   ```javascript
   import { ComponentName } from '@siteboss-frontend/shared/components';
   ```

## State Management

### Redux Guidelines

1. **Slice Structure**
   ```jsx
   // slices/mySlice.js
   export const mySlice = createSlice({
     name: 'feature',
     initialState,
     reducers: {
       // Simple mutations
       setData: (state, action) => {
         state.data = action.payload;
       },
     },
     extraReducers: (builder) => {
       // Async operations
       builder.addCase(fetchData.fulfilled, ...);
     }
   });
   ```

2. **API Integration**
   - Use RTK Query for API calls
   - Define endpoints in feature-specific API slices
   - Implement proper error handling

3. **State Access**
   - Use selectors for state access
   - Memoize complex selectors with `createSelector`

## Testing

### Component Testing
1. Write tests alongside components
2. Use Cypress for component testing
3. Include both happy and error paths
4. Test all interactive elements

### Storybook Integration
1. Create stories for all components
2. Include different states and variations
3. Document props and usage
4. Add visual regression tests

## Documentation Maintenance

### Document Updates
- This document MUST be updated when:
  - New shared components are created
  - Existing components are modified or deprecated
  - New best practices are established
  - Development patterns change
  - New tools or libraries are integrated

### Component Documentation Requirements
1. **New Components**
   - Add to relevant section in this document
   - Create Storybook documentation
   - Include Cypress tests
   - Update component index files

2. **Component Modifications**
   - Update all relevant documentation
   - Update affected Storybook stories
   - Update related tests
   - Consider backwards compatibility

## Code Organization

### File Naming Conventions
- React Components: PascalCase (e.g., `MyComponent.jsx`)
- Utilities: camelCase (e.g., `myUtil.js`)
- Test files: `*.cy.jsx` for Cypress
- Story files: `*.stories.jsx`
- Style files: `*.styles.js` for styled-components

### Import/Export Guidelines
- Use named exports for utilities and sub-components
- Use default exports for main components
- Group related components in feature folders
- Maintain index.js files for clean exports

### Code Style
- Use ESLint and Prettier configurations
- Follow project-specific style guide
- Use TypeScript types/interfaces where applicable
- Document complex logic with comments

## Performance Guidelines

### Component Optimization
- Use React.memo() for expensive renders
- Implement proper dependency arrays in hooks
- Avoid inline styles and functions
- Use proper key props in lists

### Bundle Size
- Lazy load routes and large components
- Use code splitting effectively
- Monitor bundle size with available tools
- Optimize imported libraries

## Security Best Practices

### Data Handling
- Never store sensitive data in localStorage
- Sanitize user inputs
- Use proper CORS policies
- Implement proper authentication checks

### API Security
- Use proper error handling
- Implement request timeouts
- Handle token expiration
- Validate API responses

## Accessibility Standards

### Requirements
- Maintain WCAG 2.1 compliance
- Use semantic HTML elements
- Implement proper ARIA attributes
- Ensure keyboard navigation
- Maintain proper color contrast

### Testing
- Use @storybook/addon-a11y
- Test with screen readers
- Verify keyboard navigation
- Check color contrast ratios

## Version Control

### Git Practices
- Use meaningful commit messages
- Follow conventional commits
- Create feature branches
- Submit detailed PR descriptions

### PR Requirements
- Include tests
- Update documentation
- Add Storybook stories
- Pass CI/CD checks

## Troubleshooting

### Common Issues
- Provider wrapping problems
- State management pitfalls
- SSR hydration issues
- Performance bottlenecks

### Debug Tools
- React DevTools
- Redux DevTools
- Storybook debugging
- Cypress debugging

## Additional Resources

### Internal Documentation
- API Documentation
- Component Library
- Design System
- Architecture Diagrams

### External Resources
- React Documentation
- Redux Toolkit Guides
- Storybook Tutorials
- Cypress Documentation

## Application Development

### CMS, POS, and Admin Portal Guidelines

1. **Component Structure**
   ```
   ComponentName/
   ├── ComponentName.jsx
   ├── ComponentName.cy.jsx
   ├── ComponentName.stories.jsx
   └── index.js
   ```

2. **State Management**
   - Use Redux for global state
   - Use local state for UI-only state
   - Implement proper loading states

3. **Error Handling**
   - Use ErrorBoundary for component errors
   - Implement proper API error handling
   - Show user-friendly error messages

4. **Performance**
   - Implement proper memoization
   - Lazy load routes and heavy components
   - Optimize re-renders

## Website (SSR) Development

### SSR-Specific Guidelines

1. **Component Requirements**
   - Components must be SSR-compatible
   - Handle hydration properly
   - Avoid browser-only APIs without proper checks

2. **Data Fetching**
   ```jsx
   // Proper SSR data fetching
   export async function loader({ params }) {
     return fetchData(params.id);
   }
   ```

3. **SEO Optimization**
   - Implement proper meta tags
   - Use semantic HTML
   - Handle dynamic content properly

4. **Performance**
   - Implement proper code splitting
   - Optimize critical rendering path
   - Handle loading states

### Environment Configuration
```bash
# Required environment variables
VITE_APP_PORT=3000
VITE_APP_BASE=/
VITE_API_URL=https://api...
```

## Development Workflow

1. **Starting Development**
   ```bash
   # Start specific app
   npm run dev --workspace=@siteboss-frontend/[app-name]
   
   # Start Storybook
   npm run storybook --workspace=@siteboss-frontend/[app-name]
   ```

2. **Building**
   ```bash
   # Build all apps
   npm run build
   
   # Build specific app
   npm run build --workspace=@siteboss-frontend/[app-name]
   ```

3. **Testing**
   ```bash
   # Run component tests
   npx cypress run --component
   
   # Run Storybook tests
   npm run chromatic
   ```

## Document Maintenance Note

This document should be reviewed and updated quarterly. All developers are responsible for maintaining this documentation. If you notice any outdated information or need to add new best practices, please submit a PR with your changes.

Last Updated: [Current Date]
Version: [Version Number]

## Permissions System

### Permission Provider
- Always wrap your application with `PermissionProvider`:
```jsx
<PermissionProvider>
  <App />
</PermissionProvider>
```

### Using Permissions Hook
1. **Basic Usage**
```jsx
const { permissions } = usePermission({moduleIds: [moduleId]});
```

2. **Multiple Modules**
```jsx
const { permissions } = usePermission({moduleIds: [25, 26, 27]});
```

3. **With User ID**
```jsx
const { permissions } = usePermission({moduleIds, userId});
```

Note: Preferably, params used with this hook should be defined as constants before the component is defined, or in a useMemo to avoid unnecessary re-renders.

```jsx
// constants are preferred
const permisionParams = {moduleIds: [25, 26, 27]};

export const MyComponent = () => {
   const { permissions } = usePermission(permisionParams);
   ...
}
```
-or-
```jsx

export const MyComponent = ({userId, ...props}) => {
   // if params are not constants, useMemo is preferred
   const permissionParams = useMemo(() => ({moduleIds: [25, 26, 27], userId}), [userId]);

   const { permissions } = usePermission(permisionParams);
   ...
}
```

### Permission Caching
- Permissions are automatically cached in Redux store
- Use the cached permissions when available
- Cache is managed by `permissionSlice`
- Avoid unnecessary server calls

### Best Practices

1. **Module IDs**
   - Define module IDs as constants
   - Document the purpose of each module ID
   - Group related permissions logically

2. **Conditional Rendering**
```jsx
const visibleTabs = useMemo(() => 
  tabs.filter(tab => permissions[tab.moduleId]), 
  [permissions, tabs]
);
```

3. **Error Handling**
```jsx
const { permissions } = usePermission({moduleIds});
if (!permissions[requiredModuleId]) {
  return <AccessDenied />;
}
```

4. **Loading States**
- Handle permission loading states appropriately
- Show loading indicators when checking permissions
- Provide fallback UI while permissions load

### Common Patterns

1. **Component Level Protection**
```jsx
const ProtectedComponent = ({ moduleId, children }) => {
  const { permissions } = usePermission({moduleIds: [moduleId]});
  return permissions[moduleId] ? children : null;
};
```

2. **Route Level Protection**
```jsx
const ProtectedRoute = ({ moduleId, element }) => {
  const { permissions } = usePermission({moduleIds: [moduleId]});
  return permissions[moduleId] ? element : <Navigate to="/unauthorized" />;
};
```

3. **Feature Flags**
```jsx
const { permissions } = usePermission({moduleIds: [FEATURE_MODULE_ID]});
const showNewFeature = permissions[FEATURE_MODULE_ID];
```

### Permission Structure

1. **Module Definition**
```javascript
const PERMISSION_MODULES = {
  USER_MANAGEMENT: 1,
  CONTENT_MANAGEMENT: 25,
  REPORTING: 30,
  // Add other modules...
};
```

2. **Permission Levels**
- Define clear permission levels
- Document permission hierarchies
- Consider role-based access control

### Testing Permissions

1. **Component Tests**
```javascript
it('should hide component when permission denied', () => {
  cy.mount(<ProtectedComponent moduleId={25} />);
  cy.get('[data-cy="protected-content"]').should('not.exist');
});
```

2. **Mocking Permissions**
```javascript
const mockPermissions = {
  25: true,
  26: false
};
// Use in tests...
```

### Common Issues

1. **Permission Loading**
- Handle race conditions
- Implement proper loading states
- Cache permissions appropriately

2. **Permission Updates**
- Clear cache when needed
- Handle permission changes
- Update UI accordingly

3. **Error States**
- Handle permission fetch failures
- Provide fallback behaviors
- Log permission errors

## Testing Guidelines

### Cypress Component Testing

1. **Test Structure**
```javascript
describe("Component Name", () => {
    context("specific test scenario", () => {
        beforeEach(() => {
            cy.mount(<ComponentName {...defaultProps} />)
        })

        it("should render correctly", () => {
            cy.get('[data-cy="component"]').should('exist')
        })
    })
})
```

2. **Best Practices**
- Write tests alongside component development
- Test both success and error states
- Use `data-cy` attributes for reliable selectors
- Test all interactive elements
- Mock API calls and Redux state as needed

3. **Redux Testing**
```javascript
// Dispatching actions
cy.get(store.dispatch(someAction(data)))

// Accessing store state
cy.get(store.getState())
```

4. **Running Tests**
```bash
# Open Cypress UI
npx cypress open

# Run component tests headlessly
npx cypress run --component

# Run specific test file
npx cypress run --spec "cypress/component/MyComponent.cy.jsx"
```

## Storybook Development

### Story Creation

1. **Basic Story Structure**
```javascript
export default {
    title: 'Components/MyComponent',
    component: MyComponent,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered'
    }
}

export const Default = {
    args: {
        propName: 'value'
    }
}
```

2. **Component Variants**
- Create stories for different states
- Document all possible props
- Include error states
- Show loading states
- Demonstrate responsive behavior

3. **Testing in Storybook**
- Use Controls addon for prop manipulation
- Test accessibility with a11y addon
- Use Actions addon for event handling
- Implement visual regression tests with Chromatic

### Running Storybook

```bash
# Start Storybook for specific app
npm run storybook --workspace=@siteboss-frontend/[app-name]

# Run visual regression tests
npm run chromatic
```

### Documentation

1. **Component Documentation**
- Include description and usage examples
- Document all props and their types
- Provide code samples
- Add notes about dependencies

2. **Using Addons**
- @storybook/addon-essentials
- @storybook/addon-a11y
- @storybook/addon-designs (Figma integration)
- @chromatic-com/storybook

### Component Development Flow

1. **Initial Setup**
```
ComponentName/
├── ComponentName.jsx
├── ComponentName.cy.jsx
├── ComponentName.stories.jsx
└── index.js
```

2. **Development Steps**
   - Create component structure
   - Write initial Storybook stories
   - Develop component with Storybook
   - Write Cypress component tests
   - Add documentation
   - Implement visual regression tests

3. **Quality Checklist**
   - Component renders in all states
   - All props are documented
   - Accessibility tests pass
   - Component tests written
   - Visual regression tests added
   - Documentation is complete
