import React, { useCallback, useEffect, useState } from 'react';
import { TextField, IconButton, InputAdornment } from '@mui/material';
import { AddOutlined as AddIcon, RemoveOutlined as RemoveIcon } from '@mui/icons-material';

export const NumberField = React.forwardRef(({
    onChange,
    onBlur,
    disabled,
    showControls = false,
    min,
    max,
    step = 1,
    ...props
}, ref) => {
    const [value, setValue] = useState(props.value || 0);
    
    const handleIncrement = useCallback(e => {
        const newValue = parseFloat(value || 0) + (step || 1);
        if (max !== undefined && newValue > max) return;
        if (onChange) onChange({preventDefault: e.preventDefault, stopPropagation: e.stopPropagation, target: { value: newValue, name: props.name }});
    }, [value, onChange, step, max, props.name]);

    const handleDecrement = useCallback(e => {
        const newValue = parseFloat(value || 0) - (step || 1);
        if (min !== undefined && newValue < min) return;
        if (onChange) onChange({preventDefault: e.preventDefault, stopPropagation: e.stopPropagation, target: { value: newValue, name: props.name }});
    }, [value, onChange, step, min, props.name]);

    useEffect(() => {
        setValue(props.value || 0);
    }, [props.value]);    

    return (
        <TextField
            {...props}
            inputRef={ref}
            type="number"
            value={value}
            variant={props?.variant || "outlined"}
            fullWidth={props.fullWidth === false ? false : true}
            error={!!props.error}
            onChange={onChange}
            onBlur={onBlur}
            onFocus={e => e.target.select()}
            disabled={Boolean(disabled)}
            sx={{
                '& .MuiInputBase-root': {
                    px: showControls ? 0 : undefined,
                },
                '& .MuiInputBase-input': {
                    textAlign: showControls ? 'center' : 'right',
                    MozAppearance: showControls ? 'textfield' : undefined,
                    '&::-webkit-outer-spin-button, &::-webkit-inner-spin-button': showControls ? {
                        WebkitAppearance: 'none',
                        margin: 0,
                    } : undefined,
                },
                ...(props.sx || {})
            }}            
            slotProps={{...props?.slotProps, input: { ...props?.slotProps?.input,
                ...(showControls ? {
                        startAdornment: (
                            <InputAdornment position="start">
                                <IconButton
                                    onClick={handleDecrement}
                                    disabled={Boolean(disabled) || (min !== undefined && parseFloat(value) <= min)}
                                    size="small"
                                    sx={{ml: 0.5}}
                                >
                                    <RemoveIcon fontSize="inherit" />
                                </IconButton>
                            </InputAdornment>
                        ),
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    onClick={handleIncrement}
                                    disabled={Boolean(disabled) || (max !== undefined && parseFloat(value) >= max)}
                                    size="small"
                                    sx={{mr: 0.5}}
                                >
                                    <AddIcon fontSize="inherit" />
                                </IconButton>
                            </InputAdornment>
                        )
                } : {})
            }}}
        />
    );
});