import React, { useMemo, useCallback, memo, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { startOfDay, endOfDay } from 'date-fns';
import { Stack } from '@mui/material';

import { LoadingBar, useEventData, Calendar } from '../../../components';
import { prepareComponent, CmsContainer, usePageRouter } from '../../utils';
import { PosContext } from '../../hooks';
import { toKebabCase, formatSlug } from '../../../utils';

import Heading from '../Heading';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

export const Events = memo(({
    id,
    shopId, // this will tell us the routes of the other pages we need to see event datails, cart page, etc
    type, // upcoming, past, all
    eventTypes, // array of event types to filter by (empty for all)
    title,
    titleVariant,
    subtitle,
    subtitleVariant,
    body,
    bodyVariant,
    buttonVariant,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        title: {},          // MUI typography props
        subtitle: {},       // MUI typography props
        body: {},           // MUI typography props
        eventContainer: {}, // MUI stack props
        button: {},         // MUI button props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const navigate = useNavigate();
    const { slotProps: updatedSlotProps, customCss, canRender, noContent } = prepareComponent({name: "events", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const { shopId: contextShopId } = useContext(PosContext) || {};

    //const headingProps = Object.fromEntries(Object.entries(slotProps).filter(([key, value]) => key === 'title' || key === 'subtitle' || key === 'body'));

    const eventFilters = useMemo(() => ({
        event_status_id: [2, 3, 4], 
        event_types: eventTypes || null,
        start_datetime: startOfDay(type === 'upcoming' ? new Date() : new Date('2000-01-01')).toISOString(),
        end_datetime: endOfDay(type === 'past' ? new Date() : new Date('2099-12-31')).toISOString(),
        max_records: 1000, 
        include_media: 1, 
        sort_col: "start_datetime", 
        sort_direction: type === 'upcoming' ? "asc" : "desc", 
    }), [type, eventTypes]);

    const { router } = usePageRouter({pageRouterId: shopId || contextShopId});
    const {
        eventData,
        selectedDate,
        setSelectedDate,
        setSearchText,
        ErrorBar,
        loading,
    } = useEventData({initialDate: isBuilder ? undefined : new Date(), enableCache: true, filters: eventFilters});

    const handleEventClick = useCallback(event => {
        if (event?.id && router?.event) {
            const route = formatSlug(`${router?.slug?.value}/${router?.event?.value}/${event.id}/${toKebabCase(event.title)}`);
            navigate(`/${route}`);
        }
    }, [router, navigate]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {(!shopId && !contextShopId) || !eventData ? noContent :
                    <>
                        {/*
                        <Heading 
                            id={`${id}-heading`} 
                            title={title} 
                            titleVariant={titleVariant} 
                            subtitle={subtitle} 
                            subtitleVariant={subtitleVariant} 
                            body={body && !Array.isArray(body) ? [body] : body} 
                            bodyVariant={bodyVariant} 
                            layoutId={1} 
                            slotProps={headingProps} 
                            isBuilder={false} 
                        />
                        */}
                        <ErrorBar />                        
                        {loading && <LoadingBar color="inherit" />}
                        {(eventData?.events.length > 0 || eventData?.metaEvents.length > 0) &&
                            <Calendar 
                                events={eventData} 
                                onDateChange={setSelectedDate}
                                onSearchTextChange={setSearchText}
                                loading={loading}
                                selectedDate={selectedDate}
                                type={+layoutId === 2 ? "month" : +layoutId === 3 ? "week" : +layoutId === 4 ? "day" : "schedule"}
                                disabled={isBuilder}
                                onEventClick={handleEventClick}
                                {...slotProps.eventContainer}
                            />
                        }
                    </>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
});