import React, { useEffect, useMemo } from 'react';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { ROLES, MODULES, DEFAULT_WIDGETS_BY_ROLE, DEFAULT_GRID_LAYOUTS } from './constants';

const permissionParams = {moduleIds: Object.values(MODULES)};

export const RoleBasedLayout = ({
    children,
    onSetDefaultWidgets,
    onSetDefaultLayout,
    userPreferences
}) => {
    // Get permissions for all dashboard modules
    const { permissions, userRole } = usePermission(permissionParams);

    // Get default widgets for user's role
    const defaultWidgets = useMemo(() => {
        const roleWidgets = DEFAULT_WIDGETS_BY_ROLE[userRole.id];

        // Filter widgets based on permissions
        return roleWidgets.filter(widget => {
            // Required widgets are always shown if they exist for the role
            if (widget.required) return true;

            // Check permissions for optional widgets
            switch (widget.type) {
                case 'analytics':
                    return permissions[MODULES.ANALYTICS];
                case 'users':
                    return permissions[MODULES.USER_MANAGEMENT];
                case 'sites':
                    return permissions[MODULES.SITE_MANAGEMENT];
                case 'sales':
                    return permissions[MODULES.SALES_REPORTS];
                case 'inventory':
                    return permissions[MODULES.INVENTORY_MANAGEMENT];
                case 'calendar':
                    return permissions[MODULES.CALENDAR];
                case 'weather':
                    return permissions[MODULES.WEATHER];
                case 'tasks':
                    return permissions[MODULES.TASKS];
                case 'notifications':
                    return permissions[MODULES.NOTIFICATIONS];
                case 'subscriptions':
                    return true; // Always show subscriptions for patrons
                default:
                    return false;
            }
        });
    }, [userRole, permissions]);

    // Get default grid layout for user's role
    const defaultLayout = useMemo(() => {
        return DEFAULT_GRID_LAYOUTS[userRole] || DEFAULT_GRID_LAYOUTS[ROLES.PATRON];
    }, [userRole]);

    // Set default widgets and layout if no user preferences exist
    useEffect(() => {
        if (!userPreferences?.widgets && defaultWidgets.length > 0) {
            onSetDefaultWidgets(defaultWidgets);
        }

        if (!userPreferences?.layout) {
            onSetDefaultLayout(defaultLayout);
        }
    }, [defaultWidgets, defaultLayout, userPreferences, onSetDefaultWidgets, onSetDefaultLayout]);

    return children;
};