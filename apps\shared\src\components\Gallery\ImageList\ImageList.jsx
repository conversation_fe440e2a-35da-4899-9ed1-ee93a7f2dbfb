import React from 'react';
import { useTranslation } from 'react-i18next';
import { ImageList as MuiImageList, ImageListItem } from '@mui/material';

export const ImageList = ({ 
    images = [], 
    imageSize, 
    objectFit = "cover", 
    objectPosition = "center", 
    cols = 4, 
    gap = 8, 
    variant = "masonry", 
    ...props 
}) => {
    const { t } = useTranslation();
    return (
        <MuiImageList variant={variant} cols={cols} gap={gap} {...props}>
            {images.map((image, index) => (
                <ImageListItem key={`image-${index}`}>
                    <img
                        srcSet={image.preview_url}
                        src={image.preview_url}
                        alt={image.description || `${t('image')} ${index + 1}`}
                        loading='lazy'
                        style={{
                            width: imageSize?.width || "100%", 
                            height: imageSize?.height || "100%", 
                            maxHeight: "100%", 
                            objectFit: objectFit || "cover",
                            objectPosition: objectPosition ||"center",
                        }}
                    />
                </ImageListItem>
            ))}
        </MuiImageList>
    );
}
