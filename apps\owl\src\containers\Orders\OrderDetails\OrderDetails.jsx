import { useOutletContext, usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Container, Box } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

import OrderData from './OrderData';

export const OrderDetails = ({id, ...props}) => {
    const { t } = useOutletContext();
    const navigate = useNavigate();

    if (!id) {
        navigate('/orders');
        return null;
    }

    return (
        <Container>
            <Title
                title={`${t('order:order')} #${id}`}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('order:ordersDashboard'), to: '/orders'},
                    {title: `${t('order:order')} #${id}`}
                ]}
            />

            <Box sx={{ position: 'relative' }}>
                <OrderData id={id} />
            </Box>
        </Container>
    );
}
