Directory Structure:
====================
📄 ApplyTo.js
📄 Auto.js
📄 Combo.js
📄 Conditions.js
📄 Coupon.scss
📄 Create.js
📄 Dashboard.js
📄 Dates.js
📄 Details.js
📄 MaxUses.js
📄 Name.js
📄 Status.js
📄 Summary.js
📄 Type.js
📄 index.js

>>>File: ApplyTo.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const ApplyTo = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Should this be applied to the entire order or only qualifying items?</span>
                <Form.Row>
                    <Form.Check 
                        type="radio"
                        id="apply_to_all-1"
                        label="Entire Order"
                        name="apply_to_all"
                        value={1}
                        checked={coupon.apply_to_all===1}
                        onChange={onChangeInput}
                        isInvalid={!!errors.apply_to_all}
                        className="form-radio"
                    />
                    <Form.Check 
                        type="radio"
                        id="apply_to_all-0"
                        label="Specific Items"
                        name="apply_to_all"
                        value={0}
                        checked={coupon.apply_to_all===0}
                        onChange={onChangeInput}
                        isInvalid={!!errors.apply_to_all}
                        className="form-radio"
                    />
                </Form.Row>
                <div className={`err ${!!errors.apply_to_all ? "" : "hidden"}`}>
                    {errors.apply_to_all}
                </div>
            </Col>
        </Row>
    );
}

export default ApplyTo;

========================================
>>>File: Auto.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import Coupons from '../../api/Coupons';
import * as actions from '../../store/actions';

import './Coupon.scss';

const Auto = ({ onChangeInput=()=>{}, showErrors=()=>{}, onEnterCode=()=>{} }) => {

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Will this be an automatically applied coupon or does it require a coupon code?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="auto_apply-1"
                            label="Auto Apply"
                            name="auto_apply"
                            value={1}
                            checked={coupon.auto_apply===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.auto_apply}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="auto_apply-0"
                            label="Use a Coupon Code"
                            name="auto_apply"
                            value={0}
                            checked={coupon.auto_apply===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.auto_apply}
                            className="form-radio"
                        />
                    </Col>
                    {coupon.auto_apply===0 &&
                        <>
                        <Col className="col-sm-auto">
                            <i className="far fa-arrow-right mt-4"/>
                        </Col>
                        <Col>
                            <Form.Label>Coupon Code</Form.Label>
                            <Form.Control
                                type="text"
                                id="coupon_code"
                                name="coupon_code"
                                value={coupon.coupon_code}
                                onChange={onChangeInput}
                                isInvalid={!!errors.coupon_code}
                                isValid={coupon.coupon_code_valid}
                                onBlur={onEnterCode}
                                style={{width: "250px"}}
                            />
                        </Col>
                        </>
                    }
                </Row>
                <div className={`err ${!!errors.auto_apply || !!errors.coupon_code ? "" : "hidden"}`}>
                    {errors.auto_apply}
                    {errors.coupon_code}
                </div>
            </Col>
        </Row>
    );
}

export default Auto;

========================================
>>>File: Combo.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Combo = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Can this discount be used in combination with others?</span>
                <Form.Row>
                    <Form.Check 
                        type="radio"
                        id="combinable-1"
                        label="Yes"
                        name="combinable"
                        value={1}
                        checked={coupon.combinable===1}
                        onChange={onChangeInput}
                        isInvalid={!!errors.combinable}
                        className="form-radio"
                    />
                    <Form.Check 
                        type="radio"
                        id="combinable-0"
                        label="No"
                        name="combinable"
                        value={0}
                        checked={coupon.combinable===0}
                        onChange={onChangeInput}
                        isInvalid={!!errors.combinable}
                        className="form-radio"
                    />
                </Form.Row>
                <div className={`err ${!!errors.combinable ? "" : "hidden"}`}>
                    {errors.combinable}
                </div>
            </Col>
        </Row>
    );
}

export default Combo;

========================================
>>>File: Conditions.js

import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button } from 'react-bootstrap';
import { AsyncTypeahead, Typeahead, Token } from 'react-bootstrap-typeahead';
import { format } from 'date-fns';

import * as actions from '../../store/actions';

import './Coupon.scss';
import Products from '../../api/Products';
import Groups from '../../api/Groups';
import Events from '../../api/Events';


// sub-component for a Typeahead list - currently used for Events, Groups, Products - ones that have a POST with paginated filters function
const AsyncInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
 
    const [allItems, setAllItems] = useState([]);
    const [isInitialized, setIsInitialized] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (coupon.params[paramItem.id].length>0 && Number.isInteger(coupon.params[0])) {
            setIsInitialized(false);
        } else {
            setIsInitialized(true);
        }
    },[coupon.params,paramItem.id]);

    const maxRecords = 25;

    const onSearch = async (query) => {
        if('method' in paramItem && typeof paramItem.method === "function" && paramItem.isPost) {
            // if (!!errors[paramItem.id]) dispatch(actions.setServiceWizardErrors({managers: false}));

            let mounted = true;
            setIsLoading(true);

            let filters = {  
                max_records: maxRecords,
                page_no: 1,
                sort_col: "name",
                sort_direction: "ASC"
            };
            if (paramItem.id==="groups") {
                // groups POST call structures the filters differently
                filters.filters = { search_words:query || null };
            } else {
                filters.search = query || null;
            }
            
            paramItem.method(filters)
            .then(response => {
                if(mounted && response.status===200) {
                    // items that use Products.get returns an object with an array named products, instead of just an array
                    if (response.data.products) {
                        setAllItems( response.data.products );
                    } else if (response.data.events) {
                        setAllItems( response.data.events );
                    } else if (response.data.groups) {
                        setAllItems( response.data.groups );
                    } else {
                        setAllItems( response.data );
                    }
                }
                setIsLoading(false);
            }).catch(e => console.error(e));

            // cancel stuff when component unmounts
            return () => {
                mounted = false;
                setIsLoading(false);
            }
        }
    }

    const formatForLabel = useCallback((option) => {
        let returnString = `${option?.name}`;
        // events
        if (option.start_datetime && option.end_datetime) {
            let startDate = format(new Date(option.start_datetime), "MM/dd/yyyy");
            let endDate = format(new Date(option.end_datetime), "MM/dd/yyyy");
            returnString += ` (${startDate} - ${endDate})`;
        }
        return returnString;
    },[]);

    const renderInput = useCallback(({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        <>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <div style={{ marginTop: '4px' }} className="tokens-list">
                {coupon.params[paramItem.id].map((option, i) => {
                    return (
                    option &&
                    <Token key={`tkn-${i}`} style={{ marginTop: '2px' }} onRemove={() => {
                        return onRemove(option);
                    }}>
                        {formatForLabel(option)}
                    </Token>
                    )
                })}
            </div>
        </>
    ),[coupon.params, paramItem, formatForLabel]);

    return (
        <>
            <AsyncTypeahead
                isLoading={isLoading && !isInitialized}
                id={paramItem.id}
                labelKey={formatForLabel}
                multiple={true}
                onChange={(e) => onChange(e, paramItem.id) }
                onSearch={onSearch}
                filterBy={() => true}
                minLength={3}
                options={allItems}
                placeholder={`Enter a ${paramItem.name} name...`}
                searchText="Searching..."
                selected={coupon.params[paramItem.id]}
                className="lg-input"
                isInvalid={!!errors[paramItem.id]}
                renderInput={renderInput}
            />
        </>
    )
}


// sub-component for a Typeahead list
const MultiInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const paramMultiInputData = useSelector(state => state.coupon.param_multi_input_data);
    return (
        <>
            <Typeahead
                id={paramItem.id}
                labelKey={option => `${option.name}`}
                multiple
                onChange={(e) => onChange(e, paramItem.id) }
                options={paramMultiInputData[paramItem.id]}
                placeholder={`Enter a ${paramItem.name} name...`}
                selected={coupon.params[paramItem.id]}
                isInvalid={!!errors[paramItem.id]}
            />
        </>
    )
}

// sub-component for a text input
const SingleInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    return (
        <Form.Control
            type="text"
            id={paramItem.id}
            name={paramItem.name}
            value={coupon.params[paramItem.id]}
            onChange={onChange}
            isInvalid={!!errors[paramItem.id]}
            className={`sm-input`}
        />
    )
}

const Conditions = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();
    const ref = React.createRef();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const selectedConditions = useSelector(state => state.coupon.conditions);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const paramMultiInputData = useSelector(state => state.coupon.param_multi_input_data);

    // const [paramData, setParamData] = useState(params || []);
    const [paramFormInputs, setParamFormInputs] = useState([]);
    const [pagePart, setPagePart] = useState();
    const [pagePartSelect, setPagePartSelect] = useState();
    const [loading, setLoading] = useState(false);
    const [pagePartErrors, setPagePartErrors] = useState();
    const [firstPageLoad, setFirstPageLoad] = useState(true);


    const loadData = useCallback(conditionName => {
        if('method' in paramData[conditionName] && typeof paramData[conditionName].method === "function") {

            let mounted = true;
            setLoading(true);

            if (paramData[conditionName].isPost) {

                let query = { };
                if (conditionName==="groups") {
                    query.filters = { group_id: coupon.params[conditionName] };
                } else {
                    query.id = coupon.params[conditionName];
                }
                paramData[conditionName].method(query) 
                .then(response => {
                    if(mounted) {
                        let data = response.data;
                        // items that use Products.get returns an object with an array named products, instead of just an array
                        if (response.data.products) data = response.data.products;
                        if (response.data.events) data = response.data.events;
                        if (response.data.groups) data = response.data.groups;

                        let param = coupon.params[conditionName];
                        if(param && Array.isArray(param) && Number.isInteger(param[0])) {
                            dispatch(actions.setParams({ [conditionName]: data }));
                        }
                    }
                })
                .catch(e => console.error(e));

            } else {

                // get list of all data from the method listed
                paramData[conditionName].method() 
                .then(response => {
                    if(mounted) {
                        // items that use Products.get returns an object with an array named products, instead of just an array
                        if (response.data.products) {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data.products} ));
                        } else if (response.data.events) {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data.events} ));
                        } else {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data} ));
                        }
                    }
                })
                .catch(e => console.error(e));

            }

            // cancel stuff when component unmounts
            return () => {
                mounted = false;
                setLoading(false);
            }
        }  
    },[coupon.params, dispatch, paramData]);

    const onRemove = useCallback(event => {
        if(event.target.id) { // was throwing an error if user clicked on the icon, puttin this in if statement just in case even though it should be fixed
            let tempParams = coupon.params;
            tempParams[event.target.id] = [];
            dispatch(actions.selectedCoupon({ 'params': tempParams }));
            dispatch(actions.setConditions( selectedConditions.filter(c => c !== event.target.id) ));
        }
    },[coupon.params, dispatch, selectedConditions]);

    const onAdd = useCallback(event => {
        if (event.target.value !== "") {
            if (!selectedConditions.includes(event.target.value)) {
                dispatch(actions.setParams({[event.target.value]: paramData[event.target.value].default}));
                dispatch(actions.setConditions( [...selectedConditions, event.target.value] ));
            }
            loadData(event.target.value);
        }
    },[dispatch, paramData, selectedConditions, loadData]);

    const onChangeMulti = useCallback((event, conditionName) => {
        onChangeInput(conditionName, event);
    },[onChangeInput]);

    const onChangeTextbox = useCallback(event => {
        onChangeInput(event.target.id, event.target.value);
    },[onChangeInput]);

    const onChangeCheckbox = useCallback(event => {
        onChangeInput(event.target.id, event.target.checked ? 1 : 0);
    },[onChangeInput]);    

    useEffect(() => {
        // check for param data - if an array of integers is detected, load the appropriate data for the typeahead
        if(coupon.id && firstPageLoad) {
            let conditions = [];
            Object.keys(coupon.params)
                .filter(conditionName => paramData[conditionName]
                    && coupon.params[conditionName]!==paramData[conditionName].default) // don't include items that are populated with the default value
                .forEach((conditionName) => {
                    if (Array.isArray(coupon.params[conditionName]) && coupon.params[conditionName].length>0) {
                        loadData(conditionName);
                    }
                    // add to selected conditions
                    conditions.push(conditionName);
                });
            dispatch(actions.setConditions(conditions));
            setFirstPageLoad(false);    // removing this and setting useEffect to [] does not work right
        }
    },[coupon, firstPageLoad, paramData, dispatch, loadData]);

    useEffect(() => {
        // after new data is loaded, check each paramData for a default array that is not empty
        Object.keys(coupon.params)
            .forEach(conditionName => {
                // check if this is an array of integers, which needs to be converted to an array of data objects - only for regular typeaheads
                if (Array.isArray(paramMultiInputData[conditionName]) && paramMultiInputData[conditionName].length>0 && !paramMultiInputData[conditionName].isPost) {
                    let param = coupon.params[conditionName];
                    if(param && Array.isArray(param) && Number.isInteger(param[0])) {
                        dispatch(actions.setParams({ [conditionName]: paramMultiInputData[conditionName].filter(item => param.includes(item.id)) }));
                    }
                }
            });
    },[paramMultiInputData, coupon.params, dispatch]);

    useEffect(() => {
        let formInputs = {};
        Object.keys(paramData).forEach(conditionName => {
            if (selectedConditions.includes(conditionName)) {
                
                // create the inputs to display for the screen
                if('method' in paramData[conditionName] && paramData[conditionName].isPost) {
                    // if method exists and isPost is true then make an AsyncTypeahead
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <AsyncInput paramItem={paramData[conditionName]} onChange={onChangeMulti} />
                        )
                    }
                }

                else if('default' in paramData[conditionName] && Array.isArray(paramData[conditionName].default)) {
                    // if data is an array, the option needs a Typeahead
                    if ((!coupon.params[conditionName])
                            || !paramMultiInputData[conditionName]
                            || (coupon.params[conditionName] && coupon.params[conditionName][0] && Number.isInteger(coupon.params[conditionName][0]))
                        ) {
                        // data hasn't finished loading yet
                        formInputs = {
                            ...formInputs,
                            [conditionName]: (
                                <span>loading...</span>
                            )
                        }
                    } else {
                        formInputs = {
                            ...formInputs,
                            [conditionName]: (
                                <MultiInput paramItem={paramData[conditionName]} onChange={onChangeMulti} />
                            )
                        }
                    }

                } else if ('default' in paramData[conditionName] && paramData[conditionName].default === 0) {
                    // if data is 0 or 1 it needs a checkbox
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <Form.Check
                                type="checkbox"
                                id={conditionName}
                                checked={coupon.params[conditionName] === 1}
                                onChange={onChangeCheckbox}
                            />
                        )
                    };
                } else {
                    // simple number - textbox
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <SingleInput paramItem={paramData[conditionName]} onChange={onChangeTextbox} />
                        )
                    };
                }
            }
        });
        setParamFormInputs(formInputs);
    },[paramData, coupon.params, paramMultiInputData, selectedConditions, onChangeMulti, onChangeTextbox, onChangeCheckbox]);

    useEffect(() => {
        // remove items from the list if they are already displayed on the page
        // display the select a condition drop-down
        let conditionsInSelect = Object.keys(paramData).filter((conditionName) => !selectedConditions.includes(conditionName));
        if (conditionsInSelect.length > 0) {
            setPagePartSelect(
                <Row>
                    <Col>
                        <Form.Label>Condition</Form.Label>
                        <Form.Control
                            required
                            as="select"
                            name="condition"
                            onClick={onAdd}
                        >
                            <option key={`select-null`} value='' hidden>Select another condition to add...</option>
                            {conditionsInSelect.map(conditionName => (
                                <option key={`select-${paramData[conditionName].id}`} value={paramData[conditionName].id}>{paramData[conditionName].name}</option>
                            ))}
                        </Form.Control>
                    </Col>
                </Row>
            );
        } else {
            setPagePartSelect(
                <div>No more conditions to add.</div>
            );
        }
    },[paramData, selectedConditions, onAdd]);

    useEffect(() => {
        if(paramData) {
            // display the form inputs
            setPagePart(
                <>
                    {selectedConditions.map(conditionName => (
                        <Row key={`row-${conditionName}`}>
                            <Col className="col-sm-auto">
                                <Button variant="light" onClick={onRemove} id={conditionName}><i className="far fa-times m-0" id={conditionName}></i></Button>                                
                            </Col>
                            <Col>
                                <Form.Label>{paramData[conditionName].name}</Form.Label>
                                {paramFormInputs[conditionName]}
                                <p className="subtitle mt-3">
                                    {paramData[conditionName].description}
                                </p>
                            </Col>
                        </Row>
                    ))}
                </>
            );
        }
    },[paramData, paramFormInputs, selectedConditions, onRemove]);

    useEffect(() => {
        let paramErrors = Object.keys(errors).filter(conditionName => selectedConditions.includes(conditionName));
        setPagePartErrors(
            <div className={`err ${paramErrors.length>0 ? "" : "hidden"}`}>
                {paramErrors.map(conditionName => (
                    <div key={`error-${conditionName}`}>{errors[conditionName]}</div>
                ))}
            </div>
        );
    },[errors, selectedConditions]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">What are the conditions to be met?</span>
                <p className="subtitle">
                    Please note that these conditions are cumulative: if you select multiple conditions they must ALL be true in order for the discount to apply.
                </p>
                {pagePart}
                {pagePartSelect}
                {pagePartErrors}
            </Col>
        </Row>
    );
}

export default Conditions;

========================================
>>>File: Coupon.scss

@import '../../assets/css/scss/variables.scss';
@import '../../assets/css/scss/themes.scss';

@media(max-width: 700px) {
    .card-body {
        padding: 0;
    }
}

.coupon-creator {

    // the clear all button for the asynctypeaheads
    .rbt-aux {
        position: absolute;
        top: 6px;
        right: 12px;
    }

    .tokens-list {
        display: flex;
        flex-direction: column;
        padding-left: 1rem;
        padding-right: 1rem;

    }
    
    .form-label.table-mid-header {
        display: block;
        font-size: 1rem;
        font-weight: $bold-font-weight;
        margin-bottom: 0;
        margin-top: 0.5rem;
    }
    .form-row  {
        align-items: center;
    }
    .form-row.stacked  {
        flex-direction: column;
    }
    .form-row.stacked.left  {
        align-items: flex-start;
    }
    .form-row.stacked.float-left  {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
    }

    .button-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 1rem 0;
    }

    .form-check.form-radio {
        width:200px;
        height:60px;
    }

    @media(max-width: 700px) {
        .button-row div {
            flex-grow: 1;
            padding: 0 10px;
        }
        .button-row .btn {
            width: 100%;
        }
        .lg-input {
            width: 95%;
        }
    }

    .form-error {
        color: $error-color;
    }

    .card {
        /* makes sure react-datepicker-popper is visible */
        overflow: visible; 
    }

    .date-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        width: 200px;
    }
    .date-container .form-check {
        margin-top: 5px;
    }
    .card-body .react-datepicker-popper {
        z-index: 99;
    }
    .stacked.summary {
        align-items: flex-start;
        margin-left: 2rem;
    }
    .summary>div {
        margin: 5px 0;
    }
    .type-answer {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .type-answer span {
        margin: 0 10px;
        visibility: hidden;
    }
    .type-answer span.visible {
        margin: 0 10px;
        visibility: visible;
    }

    .err {
        color: $error-color;
        display: flex;
        flex-direction: column;
    }

    .conditionals {
    
        .row {
            margin: 1.5rem 0;
        }

        .form-label {
            margin-top: 12px;
        }
    }

    .horizontal-align {
        display: flex;
        align-items: center;
    }
    .horizontal-align label {
        margin-bottom: 0;
    }
    .btn-remove-condition {
        border-radius: .25rem;
        padding: 2px 6px;
        font-size: 1.0rem;
    }
    .btn-remove-condition i.fas {
        margin: 0;
    }

    .edit-coupon .card-body {
        padding-top: 0;
    }
    .edit-coupon .form-label.question {
        margin-top: 1.5rem;
    }
    .edit-coupon .form-row {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    }
    .edit-coupon .form-check.form-radio {
        width: 160px;
        height: 40px;
    }
    .param-description {
        color: rgb(110, 110, 110);
        margin-top: 4px;
    }

}

========================================
>>>File: Create.js

import React, { useState, useEffect, useCallback, useMemo, Suspense} from 'react';
import { useHistory, useParams, Link } from "react-router-dom";
import { useDispatch, useSelector } from 'react-redux';
import Container from 'react-bootstrap/Container';
import { Col, Row, Button, Breadcrumb, Card, Form } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { format, formatISO, isBefore } from 'date-fns'
import SubHeader from '../../components/common/SubHeader';
import * as actions from '../../store/actions';
import Coupons from '../../api/Coupons';
import ErrorCatcher from '../../components/common/ErrorCatcher';
import Toast from '../../components/Toast';
import MultiStep from '../../components/common/MultiStep';
import Name from './Name';
import Auto from './Auto';
import MaxUses from './MaxUses';
import Dates from './Dates';
import Type from './Type';
import ApplyTo from './ApplyTo';
import Combo from './Combo';
import Conditions from './Conditions';
import Summary from './Summary';
import Status from './Status';

import Products from '../../api/Products';
import Groups from '../../api/Groups';
import Events from '../../api/Events';

import './Coupon.scss';

let defaultCoupon = {
    id: null,
    name: "",
    description: "",
    auto_apply: -1,
    coupon_code: "",
    coupon_code_valid: false,
    unlimited: -1,
    max_uses: "",
    discount_type: -1,
    discount_amount: 0,
    no_end_date: 0,
    apply_to_all: -1,
    combinable: -1,
    valid_from: formatISO(new Date()),
    valid_until: formatISO(new Date()),
};

let params = {
    categories: {
        id: 'categories',
        name: 'Categories',
        method: Products.Categories.get,
        isPost: false,
        default: [],
        description: "Discount will be applied only to products in the Categories selected. If multiple Categories are selected the product need belong to only one of them to get the discount."
    },
    product_types: {
        id: 'product_types',
        name: 'Product Types',
        method: Products.Types.get,
        isPost: false,
        default: [],
        description: "Discount will be applied only to products with the selected Product Type. If multiple Product Types are selected the product need belong to only one of them to get the discount."
    },
    products: {
        id: 'products',
        name: 'Products',
        method: Products.get,
        isPost: true,
        default: [],
        description: "Discount will be applied to the selected products."
    },
    product_min_qty: {
        id: 'product_min_qty',
        name: 'Product Min Quantity',
        default: "",
        description: "Products will have the discount applied only if this quantity or more is in the cart."
    },
    product_max_qty: {
        id: 'product_max_qty',
        name: 'Product Max Quantity',
        default: "",
        description: "Products will have the discount applied only if this quantity or less is in the cart."
    },
    product_combo: {
        id: 'product_combo',
        name: 'Product Combo',
        method: Products.get,
        isPost: true,
        default: [],
        description: "Discount will be applied to the selected products only if ALL of these products are in the cart."
    },
    user_age_min: {
        id: 'user_age_min',
        name: 'User Age Min',
        default: "",
        description: "Discount will be applied if the user is this age or older."
    },
    user_age_max: {
        id: 'user_age_max',
        name: 'User Age Max',
        default: "",
        description: "Discount will be applied if the user is this age or younger."
    },
    groups: {
        id: 'groups',
        name: 'Groups',
        method: Groups.groupFilter,
        isPost: true,
        default: [],
        description: "Discount will be applied if the user belongs to this Group with status CONFIRMED."
    },
    events: {
        id: 'events',
        name: 'Events',
        method: Events.getDetail,
        isPost: true,
        default: [],
        description: "Discount will be applied if the user is registered for this Event with status ATTENDING."
    },
    active_subscriptions: {
        id: 'active_subscriptions',
        name: 'Active Subscription',
        default: 0,
        description: "Discount will be applied if the user has any active subscription."
    },
    current_subscriptions: {
        id: 'current_subscriptions',
        name: 'Current Subscription',
        method: Products.getSubscriptions,
        isPost: false,
        default: [],
        description: "Discount will be applied if the user has one of the selected subscriptions in active status."
    },
    expired_subscriptions: {
        id: 'expired_subscriptions',
        name: 'Expired Subscription',
        method: Products.getSubscriptions,
        isPost: false,
        default: [],
        description: "Discount will be applied if the user has one of the selected subscriptions in expired status."
    },
    min_cart_total: {
        id: 'min_cart_total',
        name: 'Minimum Cart Total',
        default: "0.00", // creates a float instead of an int
        description: "Discount will be applied if cart total is equal to or above this amount."
    },
}

const Create = (props) => {
    let history = useHistory();
    const dispatch = useDispatch();
    const { id } = useParams();

    const coupon = useSelector(state => state.coupon.current);
    const errors = useSelector(state => state.coupon.errors);
    const selectedConditions = useSelector(state => state.coupon.conditions);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const user = useSelector(state => state.auth.user);

    const [loading,setLoading]=useState(true);
    const [pagePart, setPagePart] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [validated, setValidated] = useState();

    function isNumeric(n, min=0) {
        return !isNaN(parseFloat(n)) && isFinite(n) && n>=min;
    }

    const showErrors = useCallback(keys => {
        return (
            <div className="err">
                {keys.forEach(key => {
                    return errors[key] ? errors[key] : "";
                })}
            </div>
        );
    }, [errors]);

    // update all inputs
    const onChangeInput = useCallback(event => {
        dispatch(actions.setErrors({[event.target.name]: false}));
        let value = event.target.value;
        if (event.target.type==="radio") {
            value = parseInt(value);
        } else if (event.target.type==="checkbox") {
            value = event.target.checked ? 1 : 0;
        } else if (event.target.type==="calendar") {
            //
        } else if (event.target.type==="text") {
            event.preventDefault(); // hitting enter key while in textbox should not reload page
        }
        // truncate field lengths
        if (event.target.name==="name" && value.length>45) {
            value = value.slice(0,45);
        }
        if (event.target.name==="coupon_code" && value.length>45) {
            value = value.slice(0,45);
        }
        // Condition params
        dispatch(actions.selectedCoupon({[event.target.name]: value}));
    }, [dispatch]);

    // update conditions inputs
    const onChangeInputCondition = useCallback((name, value) => {
        dispatch(actions.setErrors({[name]: false}));
        dispatch(actions.setParams({ [name]: value }));
    }, [dispatch]);

    const onEnterCode = useCallback(async (event) => {
        //check that the coupon code is valid
        let obj = { 'coupon_code': event.target.value };

        let response=await Coupons.checkName(obj).catch(e => console.error(e));
        try {
            if (response && !response?.errors) {
                dispatch(actions.selectedCoupon({'coupon_code_valid': true}));
            } else { // api returned errors
                dispatch(actions.selectedCoupon({'coupon_code_valid': false}));
                if(Array.isArray(response.errors)) {
                    dispatch(actions.setErrors({'coupon_code': response.errors}));
                } else {
                    setError(<ErrorCatcher error={response.errors} />);
                }
            } 
        }
        catch(e) { //no response at all
            setError(<ErrorCatcher error={e} />);
        }
    }, [dispatch]);

    // The steps are shown in order listed
    const steps = useMemo(()=>[
        {name: 'Name', component: <Name onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Auto', component: <Auto onChangeInput={onChangeInput} showErrors={showErrors} onEnterCode={onEnterCode} />},
        {name: 'Max', component: <MaxUses onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Dates', component: <Dates onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Type', component: <Type onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Apply To', component: <ApplyTo onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Combo', component: <Combo onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Conditions', component: <Conditions onChangeInput={onChangeInputCondition} showErrors={showErrors} />},
    ], [onChangeInput, onChangeInputCondition, showErrors, onEnterCode]);

    const onChangeStep = useCallback(pageName => {
        // do validation stuff on the form data for the current step
        let err = {};
        let savedata = {};

        switch(pageName) {
            case "Name":
                if (!coupon.name || coupon.name==="") {
                   err = {...err, ...{name: "Name is required."}};
                }
                break;
            case "Auto":
                if (coupon.auto_apply!==1 && coupon.auto_apply!==0) {
                    err = {...err, ...{auto_apply: "Auto Apply is required, please select one."}};
                } else if (coupon.auto_apply!==1 && coupon.coupon_code==="") {
                    err = {...err, ...{coupon_code: "Coupon Code is required, please enter one or select Auto Apply."}};
                }
                break;
            case "Max":
                if (coupon.unlimited!==1 && coupon.unlimited!==0) {
                    err = {...err, ...{unlimited: "Unlimited is required, please select one."}};
                } else if (coupon.unlimited===0 && coupon.max_uses==="") {
                    err = {...err, ...{max_uses: "Max number of uses is required, please enter a number or select Unlimited."}};
                } else if (coupon.unlimited===0 && !isNumeric(coupon.max_uses,0.01)) {
                    err = {...err, ...{max_uses: "Max number of uses must be a whole number larger than 0."}};
                } else {
                    // if no errors, format the number string to whole integer, no decimals
                    dispatch(actions.selectedCoupon({max_uses: "" + Math.trunc(parseFloat(coupon.max_uses)) }));
                }
                break;
            case "Dates":
                // Dates are limited by the datepicker and validated when selected
                break;
            case "Type":
                if (coupon.discount_type!==1 && coupon.discount_type!==0) {
                    err = {...err, ...{discount_type: "Type is required, please select one."}};
                } else if (coupon.discount_amount==="") {
                    err = {...err, ...{discount_amount: "Amount is required, please enter a number."}};
                } else if (!isNumeric(coupon.discount_amount, 0.01)) {
                    err = {...err, ...{discount_amount: "Amount must be a number greater than 0."}};
                } else {
                    // if no errors, format the number string to 2 decimals
                    dispatch(actions.selectedCoupon({discount_amount: "" + Math.round(parseFloat(coupon.discount_amount) * 1e2) / 1e2}));
                }
                break;
            case "Apply To":
                if (coupon.apply_to_all!==1 && coupon.apply_to_all!==0) {
                    err = {...err, ...{apply_to_all: "Apply To is required, please select one."}};
                }
                break;
            case "Combo":
                if (coupon.combinable!==1 && coupon.combinable!==0) {
                    err = {...err, ...{combinable: "Combinable is required, please select one."}};
                }
                break;
            case "Conditions":
                if (selectedConditions.includes('product_min_qty') && !isNumeric(coupon.params.product_min_qty, 0.01)) {
                    err = {...err,
                        product_min_qty: "Product Min must be a number greater than 0."
                    };
                }
                if (selectedConditions.includes('product_max_qty') && !isNumeric(coupon.params.product_max_qty, 0.01)) {
                    err = {...err,
                        product_max_qty: "Product Max must be a number greater than 0."
                    };
                }
                if ( (selectedConditions.includes('product_min_qty')
                        && selectedConditions.includes('product_max_qty'))
                        && (parseInt(coupon.params.product_min_qty) > parseInt(coupon.params.product_max_qty)) ) {
                    err = {...err,
                        product_max_qty: "Product Max must be greater than Product Min."
                    };
                }
                if (selectedConditions.includes('min_cart_total') && !isNumeric(coupon.params.min_cart_total, 0.01)) {
                    err = {...err,
                        min_cart_total: "Min Cart Total must be a number greater than 0."
                    };
                }
                break;
            default:
                break;
        }
        if(Object.keys(err).length>0) { // fails validation
            dispatch(actions.addToErrors(err));
            return false;
        } else {
            dispatch(actions.selectedCoupon(savedata));
        }
        return true;
    }, [coupon, dispatch, selectedConditions]);

    const onEnterKey = (e) => {
        e.preventDefault();
        document.activeElement.blur();
    }

    const onSubmit = useCallback(async () => {
        // check all form validation
        let validated = true;
        steps.forEach(step => {
            if (!onChangeStep(step.name)) validated = false;
        });

        if(validated && (onChangeStep("all") || coupon.id)) {
            const formDataObj = coupon;

            if (coupon.id) formDataObj.id = parseInt(coupon.id);
            formDataObj.company_id = user.company_id;

            // convert text inputs to int
            formDataObj.discount_amount = parseFloat(formDataObj.discount_amount);
            formDataObj.max_uses = parseInt(formDataObj.max_uses) || null;

            // some variables have a set value depending on another variable
            formDataObj.coupon_code = (formDataObj.auto_apply) ? null : formDataObj.coupon_code;
            if (formDataObj.unlimited) {
                formDataObj.max_uses = 0;
            }
            if (formDataObj.no_end_date) {
                formDataObj.valid_until = null;
            }
            delete formDataObj.unlimited;
            delete formDataObj.no_end_date;

            // process each param to convert array of objects to array of ids, strings to ints, and remove empty values
            Object.keys(coupon.params).forEach(conditionName => {
                let value = coupon.params[conditionName];
                if (Array.isArray(value) && value.length>0) {
                    value = value.map(item => item.id);
                } else if('default' in paramData[conditionName] && paramData[conditionName].default === "0.00") {
                    value = Number(value).toFixed(2) || null;
                } else {
                    value = parseInt(value) || null;
                }
                if (value===null || value===[]) {
                    delete formDataObj.params[conditionName];
                } else {
                    formDataObj.params = {
                        ...formDataObj.params,
                        [conditionName]: value
                    };
                }
            });
            formDataObj.params = JSON.stringify(formDataObj.params);

            let response;
            if (coupon.id) response=await Coupons.update(formDataObj).catch(e => console.error(e));
            else response=await Coupons.create(formDataObj).catch(e => console.error(e));

            try {
                if (response && !response?.errors) {
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Discount created and saved successfully!</Toast>);
                    const timer = setTimeout(() => {
                        history.push(props.referer || "/p/discount/dashboard"); // pushes to dashboard to avoid resubmission
                    }, 3000);
                } else { // api returned errors
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} />);
                } 
            }
            catch(e) { //no response at all
                setSubmitting(false);
                setError(<ErrorCatcher error={e} />);
            }
        }
    }, [coupon, history, onChangeStep, paramData, props.referer, steps, user.company_id]);


    const stepsCreate = useMemo(()=>[...steps,
        {name: 'Summary', component: <Summary />},
    ], [steps]);

    const stepsEdit = useMemo(()=>[...steps,
        {name: 'Status', component: <Status onChangeInput={onChangeInput} showErrors={showErrors} />},
    ], [steps, onChangeInput, showErrors]);

    //   On page load
    useEffect(() => {
        let mounted = true;
        setLoading(true);

        // set all defaults
        dispatch(actions.selectedCoupon(defaultCoupon));
        dispatch(actions.setParamInitData(params));
        dispatch(actions.setConditions( [] ));

        if(id) {
            Coupons.get({id: id})
            .then(response => {
                if(mounted && response.data[0]) {
                    let cpn = response.data[0];
                    // coupon code
                    cpn.coupon_code = cpn.coupon_code ? cpn.coupon_code : "";
                    //params
                    cpn.params = JSON.parse(cpn.params) || {};
                    //unlimited
                    cpn.unlimited = cpn.max_uses===0 ? 1 : 0;
                    cpn.max_uses = cpn.max_uses===0 ? "" : cpn.max_uses;
                    //no_end_date
                    cpn.no_end_date = cpn.valid_until===null ? 1 : 0;
                    cpn.valid_until = cpn.valid_until===null ? cpn.valid_from : cpn.valid_until;
                    dispatch(actions.selectedCoupon(cpn));
                }
                setLoading(false);
            }).catch(e => console.error(e));
        } else {

            let paramDefaults = {};
            Object.keys(paramData).forEach(conditionName => {
                paramDefaults = {
                    ...paramDefaults,
                    [conditionName]: paramData[conditionName].default
                };
            });
            dispatch(actions.selectedCoupon({ params: paramDefaults }));
        }
        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[props, id, dispatch, paramData]);

    useEffect(() => {
        // check to make sure end date is after start date
        if (isBefore(new Date(coupon.valid_until), new Date(coupon.valid_from))) {
            dispatch(actions.selectedCoupon({valid_until: coupon.valid_from}));
        }

        if (id) {
            setPagePart(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    {stepsEdit.map(step => (
                        React.cloneElement(step.component, {key: `component-${step.name}`})
                    ))}
                    
                    <Row className="button-row">
                        <Button variant="secondary" onClick={() => history.push('/p/discount/dashboard')}>Cancel</Button>
                        <Button variant="primary" onClick={onSubmit}>Save Changes</Button>
                    </Row>
                </Suspense>
            );
        } else {
            setPagePart(
                <MultiStep showNavigation={true} steps={stepsCreate} onChangeStep={onChangeStep} onSubmit={onSubmit} />
            );
        }
    },[coupon, dispatch, history, id, stepsCreate, stepsEdit, onChangeStep, onSubmit]);

    return (
        <Container fluid className={`coupon-creator {id ? "edit-coupon" : "create-coupon"}`}>
            {success}
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/discount/dashboard" }, text: "Discount Dashboard" },
                { text: coupon.id ? "Update Discount" : "Create Discount" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <h4 className="section-title">{coupon.id ? "Update a " : "Create a "}Discount</h4>
                        <hr/>
                        <Card.Body>
                            <Form onSubmit={onEnterKey}>
                                {pagePart}
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
            {error}
        </Container>
    );
}

export default Create;

========================================
>>>File: Dashboard.js

import React, { useState, useEffect } from 'react';
import { useHistory, Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import { Col, Row, DropdownButton, Button, Breadcrumb, Card } from 'react-bootstrap';
import SubHeader from '../../components/common/SubHeader';
import Stack from '../../components/common/Stack';
import Table from '../../components/common/Table';
import Coupons from '../../api/Coupons';

import './Coupon.scss';

const Dashboard = (props) => {

    let history = useHistory();

    const [loading,setLoading]=useState(true);
    const [coupons,setCoupons]=useState([]);

    // first load, get companies from api
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        Coupons.get()
        .then(response => {
            if(mounted) setCoupons(response.data?.map( (cmp, index) => {
                // format some data
                cmp.discount_type = cmp.discount_type===1 ? 'Fixed' : 'Percentage';
                cmp.status = cmp.status===1 ? 'Active' : 'Inactive';
                return cmp;
            }));
            setLoading(false);
        }).catch(e => console.error(e));

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[]);

    const columns = React.useMemo(() => [{
        id: 'table',
        columns: [
            {
                Header: 'Name',
                id: 'name',
                accessor: 'name',
                className: "align-middle",
            },
            {
                Header: 'Coupon Code',
                id: 'coupon_code',
                accessor: 'coupon_code',
                className: "align-middle",
            },
            {
                Header: 'Type',
                id: 'discount_type',
                accessor: 'discount_type',
                className: "align-middle",
            },
            {
                Header: 'Amount',
                id: 'discount_amount',
                accessor: 'discount_amount',
                className: "align-middle",
            },
            {
                Header: 'Status',
                id: 'status',
                accessor: 'status',
                className: "align-middle",
            },
            {
                id: 'id',
                url:"/discount/edit/:id",
                show:false,
            },
        ],
    }],[]);

    return (
        <Container fluid>
            
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Discount Dashboard" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className={`content-card ${loading?" loading":""}`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1">Discount Dashboard</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={() => history.push("/p/discount/create")}>New Discount</Button>
                            </div>
                        </Stack>
                        <hr/>
                        <Table columns={columns} data={coupons} />
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;

========================================
>>>File: Dates.js

import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button } from 'react-bootstrap';
import DatePicker from "react-datepicker";
import { format, formatISO } from 'date-fns';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import * as actions from '../../store/actions';

import "react-datepicker/dist/react-datepicker.css";

import './Coupon.scss';

const defaultUntil = new Date()
function addYears(year) {
    let date = new Date();
    date.setFullYear(date.getFullYear() + year);
    return date;
}

const Dates = ({onChangeInput=()=>{}}) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    const [pagePart, setPagePart] = useState();
    const locations=useSelector(state => state.map.selected_items);
    const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    const changeDateHandler = useCallback((name, e) =>{
        let event = {
            target: {
                type: "calendar",
                name: name,
                value: formatISO(new Date(e.toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}))),
            }
        };
        onChangeInput(event);
    },[onChangeInput]);

    useEffect(() => {
        // don't try to load the datepicker unless a valid date has been loaded
        if (coupon.valid_from) {
            setPagePart(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    <Row>
                        <Col className="col-sm-auto">
                            <Form.Label>Start Date</Form.Label>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={new Date()}
                                maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={new Date(coupon.valid_from)}
                                onChange={(e) => { changeDateHandler('valid_from', e) }}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">{format(new Date(coupon.valid_from), "MM/dd/yyyy")}</Button>
                                }
                            />
                        </Col>
                        <Col className="col-sm-auto">
                            <Form.Label>End Date</Form.Label>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={new Date(coupon.valid_from)}
                                maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={new Date(coupon.valid_until)}
                                onChange={(e) => { changeDateHandler('valid_until', e) }}
                                disabled={coupon.no_end_date}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">{format(new Date(coupon.valid_until), "MM/dd/yyyy")}</Button>
                                }
                            />
                            <Form.Check 
                                type="checkbox"
                                id="unlimited-0"
                                label="No End Date"
                                name="no_end_date"
                                checked={!!coupon.no_end_date}
                                isInvalid={!!errors.no_end_date}
                                onChange={onChangeInput}
                                className="px-0 mt-2"
                            />
                        </Col>
                    </Row>
                </Suspense>                                                                                                                                                                                 
            );
        }
    },[coupon, errors.no_end_date, onChangeInput, changeDateHandler]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">When is it valid from and until?</span>
                {pagePart}
                <div className={`err ${!!errors.valid_from || !!errors.valid_until || !!errors.no_end_date ? "" : "hidden"}`}>
                    {errors.valid_from}
                    {errors.valid_until}
                    {errors.no_end_date}
                </div>
            </Col>
        </Row>
    );
}

export default Dates;

========================================
>>>File: Details.js

import React, { useState, useEffect } from 'react';
import { useHistory, useParams, Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import { Col, Row, DropdownButton, Button, Card } from 'react-bootstrap';
import SubHeader from '../../components/common/SubHeader';
import Table from '../../components/common/Table';
import Coupons from '../../api/Coupons';

import './Coupon.scss';

const Details = (props) => {
    const { id } = useParams();
    let history = useHistory();

    const [loading,setLoading]=useState(true);
    const [coupon,setCoupon]=useState([]);

    // first load, get companies from api
    useEffect(() => {
        let mounted = true;
        setLoading(true);

        if(id) {
            Coupons.get({id: id})
            .then(response => {
                if(mounted && response.data[0]) {
                    let cmp = response.data[0];
                    cmp.name = "Coupon " + cmp.id;
                    setCoupon(cmp);
                }
                setLoading(false);
            }).catch(e => console.error(e));
        }

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[props, id]);

    return (
        <Container fluid>
            
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/discount/dashboard" }, text: "Discount Dashboard" },
                { text: "Details" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <h4>Edit Discount</h4>
                        <hr/>

                        Form goes here.
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Details;

========================================
>>>File: MaxUses.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import Coupons from '../../api/Coupons';
import * as actions from '../../store/actions';

import './Coupon.scss';

const MaxUses = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Is there a max number of times that a user can use this coupon?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="unlimited-1"
                            label="No, Unlimited"
                            name="unlimited"
                            value={1}
                            checked={coupon.unlimited===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.unlimited}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="unlimited-0"
                            label="Yes"
                            name="unlimited"
                            value={0}
                            checked={coupon.unlimited===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.unlimited}
                            className="form-radio"
                        />
                    </Col>
                    {coupon.unlimited===0 &&
                        <>
                        <Col className="col-sm-auto">
                            <i className="far fa-arrow-right mt-4"/>
                        </Col>
                        <Col>
                            <Form.Label>Number amount</Form.Label>
                            <Form.Control
                                type="numeric"
                                id="max_uses"
                                name="max_uses"
                                value={coupon.max_uses}
                                onChange={onChangeInput}
                                isInvalid={!!errors.max_uses}
                                style={{width: "110px"}}
                            />
                        </Col>
                        </>
                    }
                </Row>
                <div className={`err ${!!errors.unlimited || !!errors.max_uses ? "" : "hidden"}`}>
                    {errors.unlimited}
                    {errors.max_uses}
                </div>
            </Col>
        </Row>
    );
}

export default MaxUses;

========================================
>>>File: Name.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Name = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <div className="wizard">
            <span className="title">Coupon Information</span>
            <Row>
                <Col>
                    <Form.Label>Coupon Name</Form.Label>
                    <Form.Control
                        type="text"
                        id="name"
                        name="name"
                        value={coupon.name}
                        onChange={onChangeInput}
                        isInvalid={!!errors.name}
                    />
                    <div className={`err ${!!errors.name ? "" : "hidden"}`}>
                        {errors.name}
                    </div>
                </Col>
                <Col>
                    <Form.Label>Brief description (optional)</Form.Label>
                    <Form.Control
                        type="text"
                        id="description"
                        name="description"
                        value={coupon.description}
                        onChange={onChangeInput}
                        isInvalid={!!errors.description}
                    />
                    <div className={`err ${!!errors.description ? "" : "hidden"}`}>
                        {errors.description}
                    </div>
                </Col>
            </Row>
        </div>
    );
}

export default Name;

========================================
>>>File: Status.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Status = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    function onSwitch(event) {
        let tempEvent = { target:
            {
                id: event.target.id,
                name: event.target.name,
                type: "switch",
                value: event.target.checked
            }
        }
        onChangeInput(tempEvent);
    }

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Coupon is Active?</span>
                <Form.Check
                    type="switch"
                    id="status"
                    name="status"
                    checked={coupon.status === 1}
                    onChange={onChangeInput}
                />
                <div className={`err ${!!errors.status ? "" : "hidden"}`}>
                    {errors.status}
                </div>
            </Col>
        </Row>
    );
}

export default Status;

========================================
>>>File: Summary.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Table } from 'react-bootstrap';
import { format } from 'date-fns';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Summary = (props) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const selectedConditions = useSelector(state => state.coupon.conditions);

    const [pagePartConditions, setPagePartConditions] = useState();

    useEffect(() => {
        if (selectedConditions.length > 0) {
            setPagePartConditions(
                <>
                    {selectedConditions.map(conditionName => {
                        let condition = coupon.params[conditionName];
                        let displayText = "";
                        if(Array.isArray(condition)) {
                            condition.forEach((item, index) => {
                                displayText += (index===0 ? item.name : ", " + item.name);
                            });
                        } else {
                            displayText = condition;
                        }
                        return (
                            <p key={`summary-row-${conditionName}`}>
                                <Form.Label>{paramData[conditionName].name}:</Form.Label><br/>
                                {displayText}
                            </p>
                        )
                    })}
                </>
            );
        } else {
            setPagePartConditions(
                <tr>
                    <td className="summary-param-name">None</td>
                    <td></td>
                </tr>
            );
        }
    },[selectedConditions,coupon,paramData]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Summary</span>

                <div className="table">
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Name</Col>
                        <Col className="table-row">
                            <div>{coupon.name}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Description</Col>
                        <Col className="table-row">
                            <div>{coupon.description}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Applied Automatically</Col>
                        <Col className="table-row">
                            <div>{coupon.auto_apply ? "Applied Automatically" : "Requires Coupon Code: " + coupon.coupon_code}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Max number of uses</Col>
                        <Col className="table-row">
                            <div>{coupon.unlimited ? "Unlimited" : `Limited to ${coupon.max_uses} uses`}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Start Date</Col>
                        <Col className="table-row">
                            <div>{format(new Date(coupon.valid_from), "MM/dd/yyyy")}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">End Date</Col>
                        <Col className="table-row">
                            <div>{format(new Date(coupon.valid_until), "MM/dd/yyyy")}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Discount Type</Col>
                        <Col className="table-row">
                            <div>{coupon.discount_type ? `Fixed amount: $${coupon.discount_amount}` : `Percentage: ${coupon.discount_amount}%`}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Applies to</Col>
                        <Col className="table-row">
                            <div>{coupon.apply_to_all ? "Entire Order" : "Specific Items"}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Can Combine</Col>
                        <Col className="table-row">
                            <div>{coupon.combinable ? "Combinable with other discounts" : "Must be the only discount used on order"}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Additional Conditions</Col>
                        <Col className="table-row">
                            <div>{pagePartConditions}</div>
                        </Col>
                    </Row>
                </div>
            </Col>
        </Row>
    );
}

export default Summary;

========================================
>>>File: Type.js

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, InputGroup } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Type = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Is this a fixed discount amount or a percentage and how much?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="discount_type-1"
                            label="Fixed Amount"
                            name="discount_type"
                            value={1}
                            checked={coupon.discount_type===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.discount_type}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="discount_type-0"
                            label="Percentage"
                            name="discount_type"
                            value={0}
                            checked={coupon.discount_type===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.discount_type}
                            className="form-radio"
                        />
                    </Col>
                    <Col className="col-sm-auto">
                        <i className="far fa-arrow-right mt-4"/>
                    </Col>
                    <Col>
                        <Form.Label>Number amount</Form.Label>
                        <InputGroup style={{width: "150px"}}>
                            {coupon.discount_type===1 &&
                                <InputGroup.Text id="basic-addon1">$</InputGroup.Text>
                            }
                            <Form.Control
                                type="numeric"
                                id="discount_amount"
                                name="discount_amount"
                                value={coupon.discount_amount}
                                onChange={onChangeInput}
                                isInvalid={!!errors.discount_amount}
                                
                            />
                            {coupon.discount_type===0 &&
                                <InputGroup.Text id="basic-addon2">%</InputGroup.Text>
                            }
                        </InputGroup>
                    </Col>
                </Row>
                <div className={`err ${!!errors.discount_type || !!errors.discount_amount ? "" : "hidden"}`}>
                    {errors.discount_type}
                    {errors.discount_amount}
                </div>
            </Col>
        </Row>
    );
}

export default Type;

========================================
>>>File: index.js



========================================
