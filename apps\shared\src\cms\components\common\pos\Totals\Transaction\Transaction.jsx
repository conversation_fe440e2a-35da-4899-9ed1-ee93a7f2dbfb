import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { ListItemButton, ListItemText, Collapse, Typography, Stack, Button, Paper, useMediaQuery } from '@mui/material';

import { formatDate, toCamelCase, capitalize, createCurrencyFormatter } from '../../../../../../utils';
import Line  from '../Line';

export const Transaction = ({transaction, allowVoid = true, allowPrint = true, allowEmail = true}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);    
    const [open, setOpen] = useState(false);
    const [transitionIn, setTransitionIn] = useState(false);

    const Wrapper = useMemo(() => (
        open ? React.forwardRef(({children, ...props}, ref) => <Paper {...props} elevation={1} sx={{mb: 1}}>{children}</Paper>) : "div"
    ), [open]);
    
    const handleItemClick = () => {
        let _open = !open;        
        if (!_open) setTransitionIn(false);
        setOpen(_open);
    }

    const handleVoidClick = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        console.log('void');
    }, []);

    const handlePrintClick = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        console.log('print');
    }, []);

    const handleEmailClick = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        console.log('email');
    }, []);

    useEffect(() => {
        setTransitionIn(open);
    }, [open]);


    const ccNumber = transaction.cc_number || ""; /*? transaction.cc_number.substring(transaction.cc_number.length - 4) : ""*/
    const description = +transaction.transaction_payment_method_id === 1 ?
        `${capitalize(transaction?.cc_type) || t("order:creditCard")} ${transaction?.cc_number ? ` ${/*t("order:endingIn")*/''}${ccNumber}` : ""}`
    : t(`pos:paymentMethods.${toCamelCase(transaction?.transaction_payment_method_name)}`, transaction?.transaction_payment_method_name);

    //console.log(transaction);

    

    return (
        <Wrapper>
            <ListItemButton onClick={handleItemClick}>
                <ListItemText primary={<Line amount={transaction.amount} label={description}/>} />
            </ListItemButton>
            <Collapse in={transitionIn && open} timeout="auto" unmountOnExit>
                <Typography variant="code" component={Stack} spacing={1} direction="column" sx={{p: 2}} /*onClick={handleItemClick}*/>
                    <Typography variant="bold">{`#${transaction.id}`}</Typography>
                    {transaction.transaction_response?.transactionid && 
                        <div>
                            {`${t(`creditCard:gatewayTransaction`)}: `}
                            <Typography variant="bold">{`#${transaction.transaction_response.transactionid}`}</Typography>
                        </div>
                    }
                    <div>
                        {`${t(`general:date`)}: `}
                        <Typography variant="bold">{formatDate(transaction.date, language)}</Typography>
                    </div>
                    {transaction.transaction_response?.check_number && 
                        <div>
                            {`${t(`check:number`)}: `}
                            <Typography variant="bold">{transaction.transaction_response.check_number}</Typography>
                        </div>
                    }
                    {transaction.transaction_response?.check_number && 
                        <div>
                            {`${t(`check:name`)}: `}
                            <Typography variant="bold">{transaction.transaction_response.check_name}</Typography>
                        </div>
                    }
                    {transaction.transaction_response?.cc_number && <Typography variant="bold">{description}</Typography> }
                    <div>
                        {`${t(`order:amount`)}: `}
                        <Typography variant="bold">{currencyFormatter.format(transaction.amount, currency)}</Typography>
                    </div>
                    {transaction?.change > 0 && 
                        <>
                            <div>
                                {`${t(`order:tendered`)}: `}
                                <Typography variant="bold">{currencyFormatter.format(transaction.transaction_response.amount, currency)}</Typography>
                            </div>
                            <div>
                                {`${t(`order:change`)}: `}
                                <Typography variant="bold">{currencyFormatter.format(transaction.transaction_response.change, currency)}</Typography>
                            </div>
                        </>
                    }
                    <div>
                        {t(`status:${toCamelCase(transaction.transaction_status_name)}`, transaction.transaction_status_name)}
                    </div>
                    <Stack direction={{xs: "column", lg: "row"}} spacing={1} useFlexGap flexWrap="wrap" sx={{py: 1}}>
                        {allowVoid && <Button variant="outlined" size="large" onClick={handleVoidClick}>{t('pos:void')}</Button>}
                        {allowPrint && <Button variant="outlined" size="large" onClick={handlePrintClick}>{t('general:print')}</Button>}
                        {allowEmail && <Button variant="outlined" size="large" onClick={handleEmailClick}>{t('general:email')}</Button>}
                        <Button variant="outlined" size="large" onClick={handleItemClick}>{t('general:close')}</Button>
                    </Stack>
                </Typography>
            </Collapse>
        </Wrapper>
    );
};