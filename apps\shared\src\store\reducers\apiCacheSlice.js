import { createSlice } from '@reduxjs/toolkit';
import { format } from 'date-fns';

const deleteRecord = ({state, key = null, index = null}) => {
    if (!key && index === null) return state;
    const record = state.cacheData?.[key] || null;
    if (record){
        const idx = index !== null ? index : record[dataIndex]?.index;
        if (idx !== null) {
            state.cache.splice(idx, 1);
            delete state.cacheData[key];
        }
    }
    return state;
}

const deepSortObject = obj => {
    return obj;
    /*
    if (Array.isArray(obj)) return obj.map(deepSortObject);
    else if (obj !== null && typeof obj === 'object') {
        return Object.keys(obj).sort().reduce((result, key) => {
            result[key] = deepSortObject(obj[key]);
            return result;
        }, {});
    }
        */
    return obj;
};

export const apiCacheSlice = createSlice({
	name: 'apiCache',
	initialState: {
        fetchCounter: 0, // a counter to keep track of the number of fetches
        cache: [], // the data cached
        cacheData: {}, // extra stuff for the cache, endpoint, params, created date, etc
    },
	reducers: {
        /* payload: {
            key: (string - required) the key of the object to be saved in the state
            data: (object - required) the data to be cached
        }

        example payload:
        {key: '/user:{id:1}', data: {errors: null, data: [{id: 1, name: 'John Doe'}]}} 
        

        so the resulting state will look like this:
        {
            cacheData: [
                ...
                {
                    '/user:{id:1}': [
                        {
                            subscriptions: 1,
                            created: '2024-09-09T00:00:00.000Z',
                            index: 5,
                        }
                    ]
                }
            ],
            cache: [
                ...
                {
                    errors: null,
                    data: [{id: 1, name: 'John Doe'}]
                }
            ]
        }

        In cacheData: 
        - subscriptions are the number of components that are using the data, when it reaches 0 (after all components unmount), the data will be removed from the cache. 
        - created is the date when the data was cached, it could be used to implement a cache expiration policy, automatic refresh, etc.

        When retrieving cached data, cacheData should be used to find the correct index in the cache array, like so:

        let data = null;
        const dataRecord = state.cacheData?.['/api/endpoint'] || null
        if (dataRecord) {
            data = state.cache[dataRecord.index];
        }

        Keep in mind this should never be used to store sensitive data (like an unhashed password), and it should not be used outside of our custom useCache hook,
        which is in turn used by the useApi hook.
        */
        saveInfo: (state, action) => {
            if (!action.payload) return;
            if (!action.payload?.key) return;

            let cache = state.cacheData?.[action.payload.key] || null;
            const data = JSON.parse(JSON.stringify(action.payload?.data)) || null;
            let subscriptions = 1, index = null;

            if (!data || !data?.data && !data?.errors && !data?.httpCode) return;

            if (cache) {
                subscriptions = cache.subscriptions + 1;
                index = cache.index;
            } else {
                state.cache = [...state.cache, data];
                index = state.cache.length - 1;
                cache = {};
            }

            state.cacheData = {
                ...state.cacheData, 
                [action.payload.key]: {
                    ...cache, 
                    subscriptions, 
                    index,
                    created: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
                }
            };
        },

        /* update the subscription count
        payload: {key, value}
        key is the key of the object to be updated,
        value can be 1 or -1, to increase or decrease the subscription count
        */
        updateSubscriptions: (state, action) => {
            const dataRecord = state.cacheData?.[action.payload.key] || null;
            if (dataRecord){
                dataRecord.subscriptions += action.payload.value;
                if (dataRecord.subscriptions <= 0) { // remove the data from the cache if there are no subscriptions
                    deleteRecord({state, index: dataRecord.index});
                }
            }
        },
        
        // payload is the key of the object to be removed, for example, '/api/endpoint:{id:10,status_id:2}'
        removeInfo: (state, action) => {
            deleteRecord({state, key: action.payload});
        },

        updateFetchCounter: (state) => {
            state.fetchCounter += 1;
        },

        resetInfo: () => {
            state.cache = [];
            state.cacheData = {};
        }
	},
});

export const { saveInfo, removeInfo, resetInfo, updateSubscriptions, updateFetchCounter } = apiCacheSlice.actions;
export default apiCacheSlice.reducer;