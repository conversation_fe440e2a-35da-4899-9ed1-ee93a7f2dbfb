import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

export const useRoutingTable = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();
    const fixedData = useSelector(state => state.fixedData);

    const fields = useMemo(() => [
        {
            name: 'state', 
            label: 'shipping:state', 
            required: true, 
            value: '', 
            component: "Select", 
            options: fixedData.states?.map(state => ({
                id: state.id,
                slug: state.name
            })) || [],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'fulfillment_facility', 
            label: 'shipping:fulfillmentFacility', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'warehouse_east', slug: 'shipping:facilities.warehouseEast' },
                { id: 'warehouse_west', slug: 'shipping:facilities.warehouseWest' },
                { id: 'warehouse_central', slug: 'shipping:facilities.warehouseCentral' },
                { id: 'distribution_center_a', slug: 'shipping:facilities.distributionCenterA' },
                { id: 'distribution_center_b', slug: 'shipping:facilities.distributionCenterB' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'fulfillment_method', 
            label: 'shipping:fulfillmentMethod', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'standard', slug: 'shipping:fulfillmentMethods.standard' },
                { id: 'expedited', slug: 'shipping:fulfillmentMethods.expedited' },
                { id: 'overnight', slug: 'shipping:fulfillmentMethods.overnight' },
                { id: 'dropship', slug: 'shipping:fulfillmentMethods.dropship' },
                { id: 'pickup', slug: 'shipping:fulfillmentMethods.pickup' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'gateway_manifest', 
            label: 'shipping:gatewayManifest', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'manifest_a', slug: 'shipping:manifests.manifestA' },
                { id: 'manifest_b', slug: 'shipping:manifests.manifestB' },
                { id: 'manifest_c', slug: 'shipping:manifests.manifestC' },
                { id: 'auto_manifest', slug: 'shipping:manifests.autoManifest' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'return_to', 
            label: 'shipping:returnTo', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'origin_warehouse', slug: 'shipping:returnLocations.originWarehouse' },
                { id: 'returns_center', slug: 'shipping:returnLocations.returnsCenter' },
                { id: 'vendor_location', slug: 'shipping:returnLocations.vendorLocation' },
                { id: 'disposal_center', slug: 'shipping:returnLocations.disposalCenter' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'carrier', 
            label: 'shipping:carrier', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'fedex', slug: 'shipping:carriers.fedex' },
                { id: 'ups', slug: 'shipping:carriers.ups' },
                { id: 'usps', slug: 'shipping:carriers.usps' },
                { id: 'dhl', slug: 'shipping:carriers.dhl' },
                { id: 'ontrac', slug: 'shipping:carriers.ontrac' },
                { id: 'lasership', slug: 'shipping:carriers.lasership' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'carrier_method', 
            label: 'shipping:carrierMethod', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'ground', slug: 'shipping:carrierMethods.ground' },
                { id: 'express', slug: 'shipping:carrierMethods.express' },
                { id: 'overnight', slug: 'shipping:carrierMethods.overnight' },
                { id: 'two_day', slug: 'shipping:carrierMethods.twoDay' },
                { id: 'priority', slug: 'shipping:carrierMethods.priority' },
                { id: 'standard', slug: 'shipping:carrierMethods.standard' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'bill_from', 
            label: 'shipping:billFrom', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'merchant_account', slug: 'shipping:billFromOptions.merchantAccount' },
                { id: 'warehouse_account', slug: 'shipping:billFromOptions.warehouseAccount' },
                { id: 'vendor_account', slug: 'shipping:billFromOptions.vendorAccount' },
                { id: 'third_party', slug: 'shipping:billFromOptions.thirdParty' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        }
    ], [fixedData.states]);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/routing-table`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/routing-table`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/routing-table`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/routing-table`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);

    const [modalOpen, setModalOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    }, []);

    const handleToggleModal = useCallback(open => e => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({page: page, per_page: pageSize});
            if (res?.data) return res.data;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);

    const handleEditItem = useCallback(() => {
        if (!selected?.length) return;

        const _data = {
            id: selected[0]?.id,
            state: selected[0]?.state,
            fulfillment_facility: selected[0]?.fulfillment_facility,
            fulfillment_method: selected[0]?.fulfillment_method,
            gateway_manifest: selected[0]?.gateway_manifest,
            return_to: selected[0]?.return_to,
            carrier: selected[0]?.carrier,
            carrier_method: selected[0]?.carrier_method,
            bill_from: selected[0]?.bill_from,
        }
        
        setFormData(_data);
        setModalOpen(true);
    }, [selected]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                await deleteData({endpoint: `/clients/${merchantId}/routing-table/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData();
        }
    }, [deleteData, fetchData, merchantId]);

    const handleSaveItem = useCallback(async () => {
        if (!formData) return;

        if (merchantId){
            // when a merchantId is defined, save directly
            const apiCall = formData.id ? updateData : createData;
            try {
                const res = await apiCall(formData);
                if (res?.data) {
                    fetchData();
                    setModalOpen(false);
                }
                return true;
            } catch(e){
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            onMainFormChange({
                target: {
                    name: 'routing_table',
                    value: {...formData}
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [formData, updateData, createData, fetchData, merchantId, onMainFormChange]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        data?.items?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data]);

    const handleResetForm = useCallback(() => {
        setFormData({});
    }, []);

    // DataTable columns
    const columns = useMemo(() => [
        {
            field: 'state',
            headerName: t('shipping:state'),
            flex: 1,
            minWidth: 120,
            valueGetter: (_, row) => {
                const state = fixedData.states?.find(s => s.id === row.state);
                return state?.name || row.state || '-';
            }
        },
        {
            field: 'fulfillment_facility',
            headerName: t('shipping:fulfillmentFacility'),
            flex: 1,
            minWidth: 150,
            valueGetter: (_, row) => {
                const facility = row.fulfillment_facility;
                return facility ? t(`shipping:facilities.${facility}`) : '-';
            }
        },
        {
            field: 'carrier',
            headerName: t('shipping:carrier'),
            flex: 1,
            minWidth: 120,
            valueGetter: (_, row) => {
                const carrier = row.carrier;
                return carrier ? t(`shipping:carriers.${carrier}`) : '-';
            }
        },
        {
            field: 'carrier_method',
            headerName: t('shipping:carrierMethod'),
            flex: 1,
            minWidth: 130,
            valueGetter: (_, row) => {
                const method = row.carrier_method;
                return method ? t(`shipping:carrierMethods.${method}`) : '-';
            }
        }
    ], [t, fixedData.states]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
    }, [handleLoadItems]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        setFormData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: fields || [],
    };  
};
