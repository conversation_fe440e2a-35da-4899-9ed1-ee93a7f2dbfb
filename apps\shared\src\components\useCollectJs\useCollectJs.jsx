import { useInsertionEffect } from 'react';

export const useCollectJs = ({url, secret_key, isBuilder, ...props}) => {
    useInsertionEffect(() => {
        if (document.querySelector('#collect-js') || !url || !secret_key || isBuilder) return;

        const script = document.createElement('script');
        script.setAttribute('src', url);
        script.setAttribute('data-tokenization-key', secret_key);
        script.setAttribute('data-variant', 'inline');
        script.setAttribute('id', 'collect-js');
        
        document.body.appendChild(script);

        return () => {
            document.body.removeChild(script);
        }
    }, [url, secret_key, isBuilder]);

    return null;
};