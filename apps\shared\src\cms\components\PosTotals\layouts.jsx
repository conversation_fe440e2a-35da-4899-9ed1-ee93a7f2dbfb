import { MoneyOutlined as MoneyIcon, BorderBottomOutlined as StickyIcon } from '@mui/icons-material';

export const widgetIcon = MoneyIcon;
export const layouts = [
    {
        id: 1,
        name: 'Default',
        icon: <MoneyIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
    {
        id: 2,
        name: 'Sticky',
        icon: <StickyIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            cmsContainer: {
                sx: {
                    position: 'sticky',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: theme => theme.zIndex.appBar - 1,
                },
            },
        }
    },
];