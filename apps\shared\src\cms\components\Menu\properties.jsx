import { generateTitle } from "../../../utils";

export const properties = [
    {
        name: 'fetchPages',
        label: 'builder:component.menu.fetchPages',
        component: "Switch",
        value: false,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    /*{
        name: 'type',
        label: 'builder:component.menu.type',
        component: "Select",
        value: 'horizontal',
        size: "small",
        margin: "normal",
        options: [
            {slug: 'builder:component.menu.types.responsive', id: 'responsive'},
            {slug: 'builder:component.menu.types.horizontal', id: 'horizontal'},
            {slug: 'builder:component.menu.types.vertical', id: 'vertical'},
        ],
    },*/
    {
        name: 'items',
        multiple: true,
        component: "local.MultiField",
        value: [],
        children: [{
            name: 'title',
            label: 'builder:component.menu.title',
            component: "TextField",
            value:  '',
            size: "small",
            margin: "normal",
        }, {
                name: 'url',
                label: 'builder:component.menu.url',
                component: "TextField",
                value:  '#',
                size: "small",
                margin: "normal",
            },
            {
                name: 'icon',
                label: 'builder:component.button.icon.title',
                component: "IconSelector",
                value: '',
                size: "medium",
                fullWidth: true,
                variant: 'outlined',
                margin: "normal",
            },
        ]
    },
];