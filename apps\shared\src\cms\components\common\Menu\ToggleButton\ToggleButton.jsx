import React, { useContext } from "react";
import { IconButton } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import { MenuContext } from '../MenuContext';

export const ToggleButton = ({isBuilder, ...props}) => {
    const { handleDrawerToggle = () => {} } = useContext(MenuContext) || {};

    return (
        <IconButton
            size="large"
            aria-label="main menu"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={isBuilder ? undefined : handleDrawerToggle(true)}
            color="inherit"
            sx={{mx: 1}}
        >
            <MenuIcon />
        </IconButton>
    );
};