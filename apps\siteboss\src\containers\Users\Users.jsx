import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import { Container, Drawer, Stack, But<PERSON>, Box, Typography, IconButton, Tooltip } from '@mui/material';
import { ArrowBackOutlined as BackIcon } from '@mui/icons-material';
import { toCamelCase } from '@siteboss-frontend/shared/utils';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { useApi } from '@siteboss-frontend/shared';
import { Title, Confirm, WithDetails } from '@siteboss-frontend/shared/components';

import List from './List';
import Form from './Form';
import UserData from './UserData';

const apiParams = [
    {params: {endpoint: '/user/roles', method: 'GET'}},
    {params: {endpoint: `/user/delete`, method: 'DELETE'}},
];

export const Users = (props) => {
    const { t } = useOutletContext();
    const params = useParams();
    
    const { fetchData:roleFetchData, data:roleData, loading:roleLoading } = useApi(apiParams[0]);
    const { fetchData:processDelete, loading:deleteLoading, ErrorBar:DeleteErrorBar } = useApi(apiParams[1]);

    const ref = useRef(null);

    const [open, setOpen] = useState(false);
    const [isNew, setIsNew] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [fetchCounter, setFetchCounter] = useState(0);

    /*const { permissions } = usePermission({moduleIds: [74, 204]});

    useEffect(() => {
        console.log(permissions);
    }, [permissions]);*/


    let loading = roleLoading || deleteLoading;

    const toggleDrawer = useCallback((open) => (e) => {
        if (e?.type === 'keydown' && (e?.key === 'Tab' || e?.key === 'Shift')) {
          return;
        }
        if (!open && (isNew || isEdit)){
            setFetchCounter(prev => prev + 1); // resets the page and order to trigger a refresh
        }
        if (!open && params.id) {
            setSelectedUsers([]);
            params.id=null;
        }
        setIsNew(false);
        setIsEdit(false);
        setOpen(open);
    }, [isNew, isEdit, params.id]);

    const handleNewClick = () => {
        setIsNew(true);
        setIsEdit(false);
        setOpen(true);
    }

    const handleDelete = useCallback((e, user) => {
        if (user) setDeleteParams([user]);
        else setDeleteParams(selectedUsers);
        if (user || selectedUsers.length > 0) setShowConfirm(true);
    }, [selectedUsers]);

    const handleEdit = useCallback((e, user) => {
        setIsNew(false);
        setIsEdit(true);
        setOpen(true);
    }, []);

    useEffect(() => {
        roleFetchData();
    }, []); // get the roles on load

    useEffect(() => {
        if (selectedUsers.length <= 0) {
            setOpen(false);
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedUsers);
            setShowConfirm(false);
        }
    }, [selectedUsers]);

    return (
        <Container>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title 
                    title={t('user:users')}
                    breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('user:users')}]}
                />
                <Box>
                    <Button size="large" variant="contained" color="primary" onClick={handleNewClick}>{t('user:newUser')}</Button>
                </Box>
            </Stack>

            {showConfirm && deleteParams.length > 0 &&
                <>
                    <Confirm
                        title={t('user:deleteTitle')}
                        message={t('user:deleteMessage')}
                        onConfirm={async () => {
                            if (deleteParams.length > 0) {
                                const _ids = deleteParams.map(s => s.id);
                                const result = await processDelete({ endpoint: "/user/user/"+_ids.join(','), id: _ids });
                                if (result?.data) {
                                    setDeleteParams([]);
                                    setFetchCounter(prev => prev + 1);
                                    setShowConfirm(false);
                                }
                            }
                        }}
                        onDecline={() => {
                            setShowConfirm(false);
                            setDeleteParams([]);
                        }}
                    >
                        <ul>
                            {deleteParams?.map(user => (
                                <Typography key={`delete-user-${user.id}`} variant="body2" component="li">
                                    {user.first_name} {user.last_name}
                                </Typography>
                            ))}
                        </ul>
                    </Confirm>
                </>
            }
            <DeleteErrorBar />

            <List 
                setSelected={setSelectedUsers}
                selected={selectedUsers}
                onExpand={toggleDrawer(true)}
                onDelete={handleDelete}
                fetchCounter={fetchCounter}
                loading={loading}
            />

            <Drawer
                details={1} // this is to flag the drawer as a details drawer in the theme
                anchor='right'
                open={open}
                onClose={toggleDrawer(false)}
            >
                <WithDetails 
                    ref={ref}
                    itemIds={selectedUsers.map(a => ({id: a.id, label: `${a.first_name} ${a.last_name}`}))} 
                    onClose={toggleDrawer(false)}
                    onDelete={handleDelete} 
                    onEdit={handleEdit} 
                    isNew={isNew}
                    isEdit={isEdit} 
                    titles={{
                        new: t('user:newUser'),
                        edit: t('user:editUser'),
                        view: t('user:users'),
                    }}
                    slots={{
                        headerButtons: (props) => isEdit && (
                            <Tooltip title={t("general:back")}>
                                <IconButton size="small" onClick={()=>setIsEdit(false)} {...props}>
                                    <BackIcon fontSize='inherit' />
                                </IconButton>
                            </Tooltip>
                        ),
                        form: (props) => <Form roles={[...roleData?.roles?.map(a => ({...a, name: t(`user:roles.${toCamelCase(a.name)}`)}))] || null} loading={roleLoading} {...props} />,
                        details: (props) => <UserData parentRef={ref} selectedUsers={selectedUsers} roles={[...roleData?.roles?.map(a => ({...a, name: t(`user:roles.${toCamelCase(a.name)}`)}))] || null} loading={roleLoading} {...props} />,
                    }}
                />
            </Drawer>
        </Container>
    );
}