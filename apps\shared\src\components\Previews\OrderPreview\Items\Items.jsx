import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import clsx from 'clsx';
import { Table, TableBody, TableHead, TableRow, TableCell, Typography, Stack } from '@mui/material';
import { createCurrencyFormatter, formatRecurringItem, capitalize, formatDate } from '../../../../utils';

import styles from './Items.module.scss';

export const Items = ({
    data, // the data to display
    className, // additional class names to apply to the wrapper (used for different print formats)
    ...props
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    const recurringInfo = useMemo(() => data?.items?.map(item => formatRecurringItem(item)), [data]);

    return (
        <Table sx={{ my: 4 }} aria-label='order items' className={clsx(styles['items-table'], className)}>
            <TableHead>
                <TableRow>
                    <TableCell>{t('order:item')}</TableCell>
                    <TableCell align='right'>{t('order:qty')}</TableCell>
                    <TableCell align='right'>{t('order:price')}</TableCell>
                </TableRow>
            </TableHead>
            <TableBody>
                {data?.items?.map((item, i) => (
                    <TableRow key={`order-detail-item-${item.id}-${i}`}>
                        <TableCell sx={{verticalAlign: 'top'}}>
                            <Stack direction="column" spacing={0} useFlexGap>
                            {item.product_name}

                            {item?.variant_name &&<Typography variant="caption">{item.variant_name}</Typography>}
                            {recurringInfo?.[i]?.length > 0 && 
                                <Typography variant="caption">
                                    +{+item?.price > 0 && currencyFormatter.format(item.price, currency)}&nbsp;
                                    {capitalize(recurringInfo?.[i]?.map(info => t(info))?.join(' ').toLocaleLowerCase())}
                                </Typography>
                            }
                            {item?.addons?.length > 0 && 
                                <>
                                    <Typography variant="caption" component="div" sx={{mt: 1}}>{t("pos:addons")}</Typography>
                                    <Typography variant="caption" component="ul">
                                        {item.addons.map(a => <li key={a.id}>{a.name || a?.metadata?.name}</li>)}
                                    </Typography>
                                </>
                            }

                            {item?.event?.for_user_id?.length > 0 &&
                                <>
                                    <Typography variant="caption" component="div" sx={{mt: 1}}>{t("pos:forUser")}</Typography>
                                    <Typography variant="caption" component="ul">
                                        {item.event.for_user_id.map((a, i) => (
                                            <li key={a}>
                                                {item.event?.for_user_name?.[i]}
                                                {item.event?.custom_fields?.filter?.(b => b.user_id === a).map(cf => (
                                                    <Stack direction="column" spacing={0} useFlexGap key={`cf-${a.id}`} sx={{pl: 2}}>
                                                        {Object.entries(cf)
                                                            .filter(([key]) => key.startsWith('cf_'))
                                                            .map(([key, value]) => {
                                                                const definition = item.event?.custom_field_definition?.find(def => def.name === key);
                                                                if (!definition) return null;
                                                                return (
                                                                    <Stack direction="row" spacing={0.5} useFlexGap key={key}>
                                                                        <Typography variant="caption" component="span">
                                                                            {definition?.placeholder_text ? `${definition.placeholder_text}: ` : ''}
                                                                        </Typography>
                                                                        <Typography variant="caption" component="span">
                                                                            {value}
                                                                        </Typography>
                                                                    </Stack>
                                                                );
                                                            })}
                                                    </Stack>
                                                ))}
                                            </li>
                                        ))}
                                    </Typography>
                                </>
                            }
                            {item?.gift_card_recipient?.full_name &&
                                <>
                                    <Typography variant="caption" component="div" sx={{mt: 1}}>{t("giftCard:recipient")}</Typography>
                                    <Typography variant="caption" component="div" sx={{pl: 4}}>
                                        {item.gift_card_recipient.full_name}<br/>
                                        {item.gift_card_recipient?.email && 
                                            <>
                                                {item.gift_card_recipient?.email}
                                                <br/>
                                            </>
                                        }
                                        {item.gift_card_recipient?.delivery_date && 
                                            <>
                                                {t("giftCard:deliveryDate")}: {formatDate(item.gift_card_recipient?.delivery_date, language)}
                                                <br/>
                                            </>
                                        }
                                        {item.gift_card_recipient?.message && <Typography variant="caption" component="i">{row.giftCardRecipient?.message}</Typography>}
                                    </Typography>
                                </>
                            }
                            {item?.memo && <Typography variant="caption" component="i" sx={{mt: 1}}><q>{item.memo}</q></Typography>}
                            </Stack>
                        </TableCell>
                        <TableCell align='right' sx={{verticalAlign: 'top'}}>{item.quantity}</TableCell>
                        <TableCell align='right' sx={{verticalAlign: 'top'}}>{currencyFormatter.format(item.final_price)}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
}