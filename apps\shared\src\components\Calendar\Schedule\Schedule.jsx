import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Box, Grid2, Typography, Divider, ButtonBase, useMediaQuery } from '@mui/material';

import Modal from '../../Modal';
import DataTableNoRows from '../../DataTable/NoRows';

import Details from '../Event/Details';
import Event from './Event';

const wordSx = {
    whiteSpace: 'break-spaces',
    wordWrap: 'break-word',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
    hyphens: 'auto',
}

export const Schedule = ({ 
    currentDate, // the current date
    events, // an array of events (optional)
    metaEvents, // an array of meta events (optional)
    colors, // an array of colors (optional)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props // additional props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [open, setOpen] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    const handleClick = useCallback(calendarEvent => e => {
        e.preventDefault();
        e.stopPropagation();
        if (calendarEvent){
            setSelectedEvent(calendarEvent);
            if (onEventClick) onEventClick(calendarEvent);
            else setOpen(true);
        }
    }, [onEventClick]);

    if (!currentDate) return null;

    return (
        <Container disableGutters>
            {!events?.length > 0 &&
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    width: '100%',
                }}>
                    <DataTableNoRows title={t("calendar:noEvents")} />
                </Box>
            }

            <Box sx={{px: 1}}>
                {events?.map((calendarEvent, i) => (
                    <Grid2 
                        container
                        component={ButtonBase} 
                        sx={{mt: 0, width: '100%'}} 
                        textAlign="left" 
                        spacing={2} 
                        key={`schedule-event-${calendarEvent.id}-${i}`}
                        disabled={disabled}
                        onClick={handleClick(calendarEvent)}
                    >
                        <Grid2 size={{xs: "auto"}}>
                            <Event 
                                currentDate={currentDate} 
                                event={calendarEvent} 
                                orderIndex={0} 
                                color={colors?.[i % colors?.length] || null} 
                                disabled={disabled}
                            />
                            {calendarEvent.startDate.getDate() !== calendarEvent.endDate.getDate() &&
                                <>
                                    {isMobile &&
                                        <Divider sx={{my: isMobile ? 2 : undefined}}>
                                            <Typography variant="body3" color="text.disabled">
                                                {t("calendar:to")}
                                            </Typography>
                                        </Divider>
                                    }
                                    <Event 
                                        currentDate={currentDate} 
                                        disabled={disabled} 
                                        event={{...calendarEvent, endDate: null }} 
                                        orderIndex={3} 
                                        color={colors?.[i % colors?.length] || null} 
                                    />
                                </>
                            }
                        </Grid2>
                        <Grid2 size="grow">
                            <Typography variant='body1' component="span" sx={{fontWeight: 600, ...wordSx}}>
                                {calendarEvent.title}
                            </Typography>
                            <Typography variant='body1' component="div" sx={wordSx}>
                                {calendarEvent.short_description}
                            </Typography>
                        </Grid2>
                        <Grid2 size={{xs: 12}}>
                            <Divider 
                                sx={{ 
                                    backgroundColor: theme => isMobile ? (colors[i % colors.length] || (theme.palette.mode === "light" ? theme.palette.primary.light : theme.palette.primary.dark)) : undefined, 
                                    height: isMobile ? 2 : undefined 
                                }}
                            />
                        </Grid2>
                    </Grid2>
                ))}
            </Box>
            <Modal 
                open={open}
                onClose={() => setOpen(false)}
                maxWidth="md"
                fullScreen={isMobile}
                aria-describedby="event-title"
            >
                <Details event={selectedEvent} />
            </Modal>
        </Container>
    );
}
