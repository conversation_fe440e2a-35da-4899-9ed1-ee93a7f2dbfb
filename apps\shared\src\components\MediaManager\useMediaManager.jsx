import { useState, useCallback, useEffect, useMemo } from "react";
import { useTranslation } from 'react-i18next';
import { useSelector } from "react-redux";

import { calculateChunkHash } from "../../utils";
import { useApi } from "../../api";
import { useFileUpload, useUploadProgress } from "../FileUpload";

export const useMediaManager = ({
    size, // number with the maximum file size in bytes
    multiple = true, // boolean to allow multiple files to be uploaded
    message, // string with the message to show on the dropzone
    parentLoading, // the loading state of the parent component
    onSelection, // function to be called when a file is selected
    selectedMediaType, // the media type to show
    fileInputRef, // the ref of the file input element
    ...props
}) => {
    const { t } = useTranslation();
    const user = useSelector(state => state.user.profile.id);
    const { LoadingBar } = useUploadProgress();

    const [loading, setLoading] = useState(false);
    const [mediaType, setMediaType] = useState(selectedMediaType || 1);
    const [showUpload, setShowUpload] = useState(false);
    const [selected, setSelected] = useState([]);
    const [fetchCounter, setFetchCounter] = useState(0);
    const [errors, setErrors] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploadBuffer, setUploadBuffer] = useState(0);

    const acceptedMimeType = useMemo(() => {
        if (mediaType === 4) return 'video/*';
        if (mediaType >= 5 && mediaType <=6 ) return 'application/pdf';
        if (mediaType === 7) return 'audio/*';
        return "image/*";
    }, [mediaType]);

    const apiParams = useMemo(() => ({params: {endpoint: "/media/upload/chunk", method: "POST", maxRedirects: 0, onUploadProgress: e => {
        let percent = Math.round(e.loaded * 100 / e.total);
        if (percent >= 100) setUploadBuffer(100);
        else setUploadBuffer(percent);
    }}}), []);

    const {fetchData: uploadFiles} = useApi(apiParams);

    const handleFileDrop = useCallback(async files => {
        if (files && files.length > 0){
            let uploaded = [];
            // so we're going to have to id 1 by 1 because the api is set up like that (sigh!)
            setLoading(true);
            for (let i = 0; i < files.length; i++){
                setUploadProgress(0);
                setUploadBuffer(0);        
                let data;
                if (files[i].file){
                    // split the file in chunks of 1MB to upload (5MB is the minimum for AWS multipart upload)
                    const chunkSize = 1 * 1024 * 1024;
                    const chunks = Math.ceil(files[i].file.size / chunkSize);

                    let currentChunkIndex = 0;
                    let tries = 0;
                    let uploadId = null;
                    let fileUuid = null;

                    while (currentChunkIndex < chunks) {
                        if (tries > 10) {
                            setErrors(t("error:default"));
                            break;
                        }

                        const start = currentChunkIndex * chunkSize;
                        const end = Math.min(files[i].file.size, start + chunkSize);
                        
                        let file;
                        if (chunks > 1) file = files[i].file.slice(start, end); // slice the file in chunks if its bigger than 5MB
                        else file = files[i].file;
                        const chunkHash = await calculateChunkHash(file);
                    
                        data = new FormData();
                        data.append('file', file);
                        data.append('index', currentChunkIndex);
                        data.append('total', chunks);
                        data.append("hash", chunkHash);
                        //data.append('file', files[i].file);
                        data.append('extension', files[i].file.name.split('.').pop() || "");
                        data.append('type', mediaType);
                        data.append('user_id', user);
                        if (uploadId) data.append('upload_id', uploadId);
                        if (fileUuid) data.append('uuid', fileUuid);
              
                        try {
                            let response = await uploadFiles(data);
                            if (response){
                                if (response?.errors) {
                                    setErrors(response.errors);
                                    break;
                                }
                                else if (response?.data){
                                    if (response.data?.upload_id) uploadId = response.data.upload_id;
                                    if (response.data?.uuid) fileUuid = response.data.uuid;
                                    if (response.data?.id && response.data?.url){
                                        uploaded.push(response.data);
                                        if (onSelection) onSelection(response.data.url);
                                    }
                                    currentChunkIndex++;
                                    tries = -1;
                                    setUploadProgress((currentChunkIndex / chunks) * 100);
                                    setFetchCounter(prev => prev + 1);
                                }
                            }
                        } catch (error) {
                            setErrors(t("error:default"));
                            console.error('Error uploading chunk:', error);
                            break;
                        }
                        tries++;
                    }

                    /* old way
                    data = new FormData();
                    data.append('file', files[i].file);
                    data.append('type', mediaType);
                    data.append('user_id', user);
                    try {
                        let response = await uploadFiles(data);
                        if (response?.data){
                            if (response.data?.length === 1 && onSelection){
                                onSelection(response.data[0].url);
                            }
                            setFetchCounter(fetchCounter + 1);
                        }
                    } catch (error) {
                        setErrors(t("error:default"));
                    } finally {
                        setLoading(false);
                    }*/
                }
                setUploadProgress(0);
                setUploadBuffer(0);
            }
            setLoading(false);
        }        
    }, [user, uploadFiles, mediaType, onSelection, t]);

    const handleFileDelete = file => {
    }

    const {
        isDragging,
        files,
        filePreviews,
        error,
        imageToCrop,
        handleDragOver,
        handleDrop,
        handleDragLeave,
        handleChange,
        handleFileCrop,
        handleFileRemove,
        handleCrop,
        handleHideImageCropper,
        handleButtonClick,
    } = useFileUpload({ onFileDrop: handleFileDrop, onFileRemove: handleFileDelete, message, accept: acceptedMimeType, size, multiple, withPreviews: false, previews: [], loading: parentLoading || loading, fileInputRef });


    const handleFileClick = useCallback(media => e => {
        if (media){
            const _prev = [...selected];
            const index = _prev.findIndex(m => m === media.url);
            if (index > -1) _prev.splice(index, 1);
            else _prev.push(media.url);
            setSelected(_prev);
            if (onSelection) onSelection(_prev);
        }
    }, [onSelection, selected]);

    useEffect(() => {
        if (error) setErrors(error);
    }, [error]);

    return {
        isDragging,
        files,
        filePreviews,
        errors,
        setErrors,
        imageToCrop,
        handleDragOver,
        handleDrop,
        handleDragLeave,
        handleChange,
        handleFileCrop,
        handleFileRemove,
        handleCrop,
        handleHideImageCropper,
        handleButtonClick,
        handleFileClick,
        acceptedMimeType,
        loading,
        mediaType,
        setMediaType,
        showUpload,
        setShowUpload,
        selected,
        uploadProgress,
        uploadBuffer,
        LoadingBar,
        fetchCounter,
    };
}