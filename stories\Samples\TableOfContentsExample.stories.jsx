import React from 'react';
import { Button } from '@mui/material';

/**Most the arg types were ommitted in order to make this doc shorter. This is an example of the included table of contents. It's helpful when we have multiple stories or a large list of parameters.   */
export default {
    title: "Storybook Functionality Sample/Table of Contents",
    component: Button,
    tags: ['autodocs'],
    parameters:{
        docs:{
            toc: true
        }
    },
    argTypes:{
        children:{
            description:"The content of the component",
            control: "text",
            table:{
                defaultValue: {summary: null},
                type: {summary: "node"}
            }
        },variant:{
            description: "The variant to use",
            control: "select",
            options:["text", "outlined", "contained"],
            table:{
                defaultValue: {summary: "text"},
                type: {summary: "string"}
            }
        }
    }
}

export const Playground={
    args:{
        children: "I am Button"
    }
}

export const Colors={
    render:()=>(
        <div>
            <p>
                <Button color="inherit" variant="contained">Inherit</Button>
            </p>
            <p>
                <Button color="primary" variant="contained">Primary</Button>
            </p>
            <p>
                <Button color="secondary" variant="contained">Secondary</Button>
            </p>
            <p>
                <Button color="success" variant="contained">Success</Button>
            </p>
            <p>
                <Button color="error" variant="contained">Error</Button>
            </p>
            <p>
                <Button color="info" variant="contained">Info</Button>
            </p>
            <p>
                <Button color="warning" variant="contained">Warning</Button>
            </p>
        </div>
    )
}

export const Sizes={
    render:()=>(
        <div>
            <p>
                <Button size='small'>Small</Button>
            </p>
            <p>
                <Button size='medium'>Medium</Button>
            </p>
            <p>
                <Button size='large'>Large</Button>
            </p>
        </div>
    )
}

export const Variants={
    render:()=>(
        <div style={{maxWidth: "400px"}}>
            <p>
                <Button variant='text'>Text</Button>
            </p>
            <p>
                <Button variant='outlined'>Outlined</Button>
            </p>
            <p>
                <Button variant='contained'>Contained</Button>
            </p>
            <p>
                <Button disabled>Disabled</Button>
            </p>
            <p>
                <Button disableElevation>Disable Elevation</Button>
            </p>
            <p>
                <Button disableFocusRipple>Disable Focus Ripple</Button>
            </p>
            <p>
                <Button disableRipple>Disable Ripple</Button>
            </p>       
            <p>
                <Button fullWidth>Full Width</Button>
            </p>
        </div>
    )
}