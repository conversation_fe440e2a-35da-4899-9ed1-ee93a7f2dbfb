import React, { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { formatDate, createCurrencyFormatter } from '@siteboss-frontend/shared/utils';
import { Modal, DataTable } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import { OrderPreview } from '../../../../../components/Previews';

const apiParams = [
    {params: {endpoint: `/order`, method: 'POST', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'desc',
    }}},
];

export const List = ({
    fetchCounter = 0, // a counter to trigger a data refresh when changed
    loading:parentLoading, // the loading state of the parent component
    userId = null, // the user id to get the orders for
    registerId = null, // an array of register ids
    locationId = null, // an array of location ids
    statusId = null, // the status id of the orders to get
    compact = false, // whether to show the compact version of the table
    fields = ["id", "created_at", "total_price"], // the fields to display in the table
    onUpdateCount, // a function to update the order count
    displayList = true, // whether to display the list (useful when rendering inside a hidden tab)
    ...props
}) => {
    const { t, language, isMobile, currency } = useOutletContext();
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const [rows, setRows] = useState(null);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'desc'});
    const [selected, setSelected] = useState([]);
    const [open, setOpen] = useState(false);

    const { fetchData, data, ErrorBar, loading, LoadingBar } = useApi(apiParams[0]);    

    const columns = useMemo(() => [
        { field: 'id', headerName: `${t('order:order')} #`, width: 110 },
        //{ field: 'user', headerName: t('order:customer'), minWidth: 300, sortable: false, flex: 1, valueFormatter: value => `${value?.first_name} ${value?.last_name}`},
        { field: 'created_at', headerName: t('order:date'), width: 100, valueFormatter: value => formatDate(new Date(value), language) },
        { field: 'total_price', headerName: t('order:total'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        { field: 'payment_total', headerName: t('order:payments'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        { field: 'balance_due', headerName: t('order:balance'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        //{ field: 'location_name', headerName: t('order:location'), width: 150, sortable: true },
        //{ field: 'register_name', headerName: t('order:register'), width: 150, sortable: true },
        //{ field: 'order_status_name', headerName: t('order:status'), width: 150, sortable: false, valueFormatter: value => t(`status:${toCamelCase(value || "")}`) },
    ], [t, language, currency, currencyFormatter]);//.filter(col => fields.includes(col.field));

    const handleRowSelection = (model) => {
        const _model = [];
        rows?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }

    const togglePreview = open => e => setOpen(open);

    useEffect(() => {
        fetchData({
            page_no: page || 1,
            max_records: pageSize,
            sort_col: order?.column || 'id',
            sort_direction: order.direction || 'desc',
            user_id: userId || null,
            location_id: locationId ? [locationId] : null,
            register_id: registerId ? [registerId] : null,
            order_status_id: statusId || null,    
        })
    }, [page, pageSize, order, locationId, registerId, statusId, userId]);

    useEffect(() => {
        const _rows = [];
        if (data) {
            setPageSize(data.page_record_count);
            setPage(data.this_page);
            setTotalRows(data.total_record_count);
            onUpdateCount(statusId, data.total_record_count);
            setTotalPages(Math.min(data.total_record_count / data.page_record_count));
            if (data?.orders){
                data.orders.map(order => {
                    let balanceDue = order.total_price - order.payment_total;
                    if (balanceDue < 0) balanceDue = 0;
                    if (order.total_price === 0) return;
                    _rows.push({
                        id: order.id,
                        user: {
                            first_name: order.user.first_name,
                            last_name: order.user.last_name,
                            email: order.user.email,
                        },
                        created_at: new Date(order.created_at),
                        total_price: order.total_price,
                        payment_total: order.payment_total,
                        balance_due: balanceDue,
                        location_name: order.location_name,
                        register_name: order.register_name,
                        order_status_name: order.order_status_name,
                        metadata: order,
                    });
                });
            }
            // filter the rows based on the fields
            /*
            if (fields) {
                _rows.forEach(row => {
                    Object.keys(row).forEach(key => {
                        if (!fields.includes(key) && key!=="metadata") delete row[key];
                    });
                });
            }
            */
            setRows(_rows);
        }
    }, [data]);

    if (!displayList) return null;
    
    return (
        <>
            <ErrorBar />
            {!data && <LoadingBar />}
            {rows &&
                <DataTable
                    checkboxSelection
                    hideFooterSelectedRowCount
                    rows={rows}
                    columns={columns}
                    onExpand={togglePreview(true)}
                    onRowSelectionModelChange={model => handleRowSelection(model)}
                    rowSelectionModel={selected.map(s => s.id)}
                    order={order}
                    page={page}
                    pageSize={pageSize}
                    totalPages={totalPages}
                    totalRows={totalRows}
                    setPage={setPage}
                    setPageSize={setPageSize}
                    setOrder={setOrder}
                    loading={loading || parentLoading}
                />
            }
            {selected.length > 0 &&
                <Modal
                    open={open}
                    onClose={togglePreview(false)}
                    title={`${t('order:order')} #${selected[0].id}`}
                    maxWidth="md"
                    fullScreen={isMobile}
                >
                    <OrderPreview id={selected[0].id} />
                </Modal>
            }
        </>
    );
}