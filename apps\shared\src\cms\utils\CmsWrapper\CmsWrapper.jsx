import React, { useState, useCallback } from 'react';
import { Box } from '@mui/material';

// This will wrap components for the builder, to show layout options, make it draggable, show properties on click, etc.
export const CmsWrapper = ({
    layouts = [],
    properties = [],
    layoutId,
    sectionId,
    children,
    selectedSectionId = null, 
    handleSectionSelection = () => {}, 
    handleSectionDeletion = () => {},
    handleLayoutSelection = () => {}, 
    setOpenPropertiesDrawer = () => {},
    ...props
}) => {
    const selected = Boolean(selectedSectionId && sectionId && selectedSectionId === sectionId);

    //const [selected, setSelected] = useState(false);

    const handleClick = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        if (selected) { // if it's already selected, deselect it
            handleSectionSelection(null);
            handleLayoutSelection(null);
            setOpenPropertiesDrawer(false);
        } else { // if it's not selected, select it
            handleSectionSelection(sectionId);
            handleLayoutSelection(layoutId, sectionId, properties);
            setOpenPropertiesDrawer(true);
        }
        //setSelected(!selected);
    }, [selected, handleSectionSelection, handleLayoutSelection, setOpenPropertiesDrawer, sectionId, layoutId, properties]);

    /*
    useEffect(() => {
        const handleDeletePress = e => {
            const isValid = e.target.tagName !== "INPUT" && e.target.tagName !== "TEXTAREA" && e.target.tagName !== "SELECT" && e.target.role !== "textbox";            
            if (e.key === 'Delete' && isValid && selectedSectionId && selected) {
                e.preventDefault();
                handleSectionDeletion(selectedSectionId);
                handleSectionSelection(null);
                handleLayoutSelection(null);
                setOpenPropertiesDrawer(false);    
            }
        };

        window.addEventListener('keydown', handleDeletePress);
        return () => {
            window.removeEventListener('keydown', handleDeletePress);
        }
    }, [selected, selectedSectionId, handleSectionDeletion, handleSectionSelection, handleLayoutSelection, setOpenPropertiesDrawer]);
    */

    return (
        <Box 
            onClick={handleClick}
            sx={{
                backgroundImage: selected ? theme => `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='${encodeURIComponent(theme.palette.secondary[theme.palette.mode === "light" ? "main" : "light"])}' stroke-width='2' stroke-dasharray='2, 4' stroke-dashoffset='5' stroke-linecap='square'/%3e%3c/svg%3e")` : undefined,
                transition: 'background-image 0.15s linear',
                cursor: 'pointer',
                /*'&:hover': {
                    backgroundImage: theme => `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23${theme.palette.mode === "light" ? "333" : "FFF"}' stroke-width='1' stroke-dasharray='3' stroke-dashoffset='5' stroke-linecap='square'/%3e%3c/svg%3e")`,
                },*/
            }}
        >
            {children}
        </Box>
    );
}
