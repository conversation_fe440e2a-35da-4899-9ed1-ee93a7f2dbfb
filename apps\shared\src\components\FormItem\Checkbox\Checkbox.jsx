import React from 'react';
import PropTypes from 'prop-types';
import { FormControl, FormControlLabel, FormHelperText, FormGroup, Checkbox as Mui<PERSON><PERSON><PERSON><PERSON>, Switch, Typography } from '@mui/material';

export const Checkbox = ({
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors, // the errors for the field
    loading, // the loading status of the form so we can disable the fields
    value, // the value of the field
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    isSwitch = false, // if the checkbox should be a switch
    checked, // if the checkbox is checked
    helperText,
    ...props
}) => {
    const Component = isSwitch ? Switch : MuiCheckbox;

    return (
        <FormControl
            required={required || false}
            error={!!errors}
            component="fieldset"
            variant="standard"
            margin={props.margin || undefined}
            sx={props.sx || undefined}
            size={props.size || undefined}
        >
            <FormGroup>
                <FormControlLabel 
                    labelPlacement={props.labelPlacement || "end"}
                    label={props.size === "small" ? 
                        <Typography variant="body2">
                            {label} {required ? "*" : null}
                        </Typography>
                        : 
                        <span>
                            {label} {required ? "*" : null}
                        </span>
                    }
                    control={
                        <Component
                            name={name}
                            checked={checked || false}
                            value={value || ""}
                            disabled={loading}
                            onChange={onChange}
                            size={props.size || undefined}
                            color={props?.color || "primary"}
                            //slotProps={{typography: {variant: "body2"}}}
                        />
                    } 
                />
            </FormGroup>
            {helperText && <FormHelperText>{helperText}</FormHelperText>}
            {errors && <FormHelperText error>{errors}</FormHelperText>}
        </FormControl>
    );
}

Checkbox.propTypes={
    label: PropTypes.string,
    name: PropTypes.string,
    required: PropTypes.bool,
    errors: PropTypes.string,
    loading: PropTypes.bool,
    value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.array,
        PropTypes.object
    ]),
    onChange: PropTypes.func,
    isSwitch: PropTypes.bool,
    checked: PropTypes.bool,
    margin: PropTypes.number,
    sx: PropTypes.object,
    size: PropTypes.string,
    labelPlacement: PropTypes.string,
    helperText: PropTypes.string
}


