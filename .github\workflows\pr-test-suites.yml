name: PR Test Suites (Visual Testing, Component Testing, and E2E Testing)

on: 
    pull_request:
        branches:
            - "main"
jobs:
    chromatic:
        name: Run Chromatic Visual Testing
        runs-on: ubuntu-latest
        steps:
            -   name: CHeckout Code
                uses: actions/checkout@v4
                with: 
                    fetch-depth: 0
            -   name: Setup Node
                uses: actions/setup-node@v4
                with:
                    node-version: 22.12.0
            -   name: Install Dependencies
                run: npm ci
            - name: Run Chromatic
              uses: chromaui/action@latest
              with:
                token: ${{ secrets.CHROMATIC_API_TOKEN }}
                onlyChanged: true
                
