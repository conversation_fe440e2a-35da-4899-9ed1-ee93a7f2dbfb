import { useCallback, useRef, useMemo, useEffect } from 'react';
import { useApi } from '../../api';

export const usePageRouter = ({
    pageRouterId = null,
}) => {
    const initialized = useRef(false);
    const apiParams = useMemo(() => ({enableCache: true, params: {endpoint: `/cms/site/page/${pageRouterId}`, method: 'POST'}}), [pageRouterId]);

    const { fetchData, data, loading, errors } = useApi(apiParams);

    const loadPageRouter = useCallback(async () => {
        if (pageRouterId) {
            try {
                await fetchData();
            } catch (error) {
                console.error('Failed to load page router:', error);
            } finally {
                initialized.current = true;
            }
        }
    }, [pageRouterId, fetchData]);


    useEffect(() => {
        if (!initialized.current) loadPageRouter();
    }, [loadPageRouter]);

    return {
        router: data ? data?.[0]?.content?.properties : null,
        loading,
        errors,
        loadPageRouter,
    };
}