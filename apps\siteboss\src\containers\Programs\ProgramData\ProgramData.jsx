import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { Container } from '@mui/material';
import { Tab<PERSON>anel, TabContext } from '@mui/lab';
import { InfoOutlined as InfoIcon, ContactEmergencyOutlined as GroupTypeIcon, DonutLargeOutlined as ChartIcon, ImageOutlined as ImageIcon } from '@mui/icons-material';
import { MediaUpload, LoadingBar, WithDetailsToolbar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import ProgramInfo from './ProgramInfo';
import EventFilter from './EventFilter';
import GroupType from './GroupType';
import Statistics from '../../Events/EventData/Statistics';

/*
this component is used to display the program's information and tabs with program related data
props:
    id: the id of the program to display
    onDelete: function to be called when the delete button is clicked
    onEdit: function to be called when the edit button is clicked
    onUpdate: function to be called when the data is updated
    loading: loading state for the parent component
    parentRef: a ref to the parent container
*/
export const ProgramData = ({onDelete, onEdit, onUpdate, id, loading:parentLoading, parentRef, fetchCounter, setFetchCounter, selectedPrograms, ...props}) => {    
    const { isMobile } = useOutletContext();
    const contentRef = parentRef || useRef(null);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/event/${id}`, method: 'GET', data: {id, include_custom_fields: 1, include_tags: 1, include_media: 1}}},
        {params: {endpoint: '/event/group_types', method: 'POST', data: {event_id: id}}},
        {params: {endpoint: `/event`, method: 'POST'}},
    ], [id]);

    const { fetchData, data, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: fetchGroupTypes, data: groupTypesData, ErrorBar: GroupTypesErrorBar, loading: groupTypesLoading } = useApi(apiParams[1]);
    const { fetchData: fetchGroupEvents } = useApi(apiParams[2]);

    const [selectedTab, setSelectedTab] = useState(null);
    const [selectedTabProps, setSelectedTabProps] = useState(null);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // generate a list of tabs dynamically based on the group types in the program data
    const tabs = useMemo(() => {
        const _tabs = [];

        let mobileIndex = 0, index = 0;
        if (isMobile) {
            mobileIndex = 1;
            _tabs.push({ id: 'info', groupTypeId: 0, index, slug: 'program:toolbar.basic', moduleId: 25, icon: <InfoIcon/>, component: props => <ProgramInfo onDelete={onDelete} onEdit={onEdit} {...props} />});
            index++;
        }

        if (data?.[0]?.group_types?.length > 0) {
            data[0].group_types.forEach(group => {
                _tabs.push({ id: `groupType${group.id}`, groupTypeId: group.id, index: index + mobileIndex, slug: group.name, moduleId: 25, icon: <GroupTypeIcon/>, component: props => <GroupType {...props} />});
                index++;
            });
        }

        if (selectedEvent) {
            _tabs.push(
                { id: 'statistics', index: index + 1 + mobileIndex, slug: 'event:toolbar.statistics', moduleId: 25, icon: <ChartIcon/>, component: props => <Statistics {...props} eventData={selectedEvent} />},
            );
            index++;
        }

        if (_tabs.length === 0) {
            _tabs.push(
                //{ id: 'groups', index: index + 1 + mobileIndex, slug: 'program:toolbar.groupTypes', moduleId: 25, icon: <GroupTypeIcon/>, component: props => <div />},
                { id: 'images', index: index + 1 + mobileIndex, slug: 'program:toolbar.images', moduleId: 25, icon: <ImageIcon/>, component: props => <MediaUpload {...props} />},
                /*
                { id: 'attendees', index: 2 + mobileIndex, slug: 'program:toolbar.attendees', moduleId: 25, icon: <AttendeeIcon/>, component: props => <Attendees {...props} />},            
                { id: 'fields', index: 3 + mobileIndex, slug: 'program:toolbar.customFields', moduleId: 25, icon: <FieldsIcon/>, component: props => <CustomFields {...props} />},
                { id: 'tree', index: 4 + mobileIndex, slug: 'program:toolbar.tree', moduleId: 25, icon: <TreeIcon/>, component: props => <Tree {...props} />},
                { id: 'statistics', index: 5 + mobileIndex, slug: 'program:toolbar.statistics', moduleId: 25, icon: <ChartIcon/>, component: props => <Statistics {...props} />},
                */
            );
        }

        if (data) setSelectedTab(prev => !prev ? _tabs[0].id : prev);
        
        return _tabs;
    }, [isMobile, onDelete, onEdit, data, selectedEvent]);

    const { permissions } = usePermission({moduleIds: tabs.map(tab => tab.moduleId)});

    const visibleTabs = useMemo(() => tabs.filter(tab => {
        return permissions[tab.moduleId] && ((data?.[0]?.is_meta || data?.[0]?.parent_id || data?.[0]?.children?.length > 0) || tab.id !== 4)
    }), [permissions, data, tabs]);

    // do stuff when a file is dropped in the upload container
    const handleFileDrop = useCallback(files => {
        if (files.length && id){
            const _formdata = new FormData();
            _formdata.append('id', id);
            files.forEach(file => {
                _formdata.append('files[]', file.file);
                _formdata.append('file_ids[]', file.id || null);
                _formdata.append('file_descriptions[]', file.description || null);
            });
            if (onUpdate) onUpdate(_formdata);
        }
    }, [id, onUpdate]);

    // do stuff when a file is removed from the upload container
    const handleFileRemove = useCallback(file => {
        if (file && id){
            //onFileRemove("update", file);
        }
    }, [id, onUpdate]);


    // when the event filter changes
    const handleChangeEvent = useCallback(event => {
        if (event?.id){
            setSelectedEvent(event);
            setSelectedTabProps(prev => ({...prev || {}, selectedEvent: event}));
        }
    }, []);

    // update the program
    const handleUpdate = useCallback(data => {
        if (!data) return;
        if (!data.id) data.id = id;
        if (onUpdate) onUpdate(data);
    }, [id, onUpdate]);

    // handle the toolbar click
    const handleToolbarClick = useCallback((e, action) => {
        if (!action) return;

        let _props = null, groupTypeId = null, _action = action;

        if (_action.startsWith('groupType')) {
            _action = 'groupType';
            groupTypeId = +action.replace('groupType', '') || null;
        }

        switch(_action){
            case 'images': // images
                _props = {
                    onFileDrop: handleFileDrop, 
                    onFileRemove: handleFileRemove, 
                    url: data?.[0]?.images.map(a=> ({...a, url: a.preview_url})) || null, 
                    loading: loading || parentLoading || groupTypesLoading,
                };
                break;
            case 'groupType':
            case 'info':
                _props = {
                    programData: data?.[0], 
                    groupTypes: groupTypesData,
                    onUpdate: handleUpdate, 
                    fetchCounter, 
                    setFetchCounter,
                    groupTypeId,
                    loading: loading || parentLoading,
                    selectedEvent,
                };
                break;
            default:
                break;
        }
        setSelectedTab(`${action}`);
        setSelectedTabProps(_props);
    }, [data, groupTypesData, handleFileDrop, handleFileRemove, handleUpdate, loading, groupTypesLoading, parentLoading, fetchCounter, setFetchCounter, selectedEvent]);

    useEffect(() => {
        const _loadGroupEvents = async ids => {
            const res = await fetchGroupEvents({group_type_id: ids});
            if (res?.data?.events){
                const _events = [];
                res.data.events.forEach(e => {
                    if (e.children) _events.push(...e.children);
                });
                setSelectedTabProps(prev => ({...prev || {}, subEvents: _events}));
            }
        }
        if (selectedTab && selectedEvent) {
            const selectedTabId = tabs?.find(a => a.id === selectedTab)?.groupTypeId;
            if (selectedTabId) _loadGroupEvents([selectedTabId]);
        }
    }, [selectedTab, tabs, selectedEvent, fetchGroupEvents]);
    
    useEffect(() => {
        if (id) {
            fetchData();
            fetchGroupTypes();
        }
    }, [id, fetchGroupTypes, fetchData]);

    if (!id) return null;

    return (
        <Container disableGutters ref={contentRef}>
            <ErrorBar />
            <GroupTypesErrorBar />
            {(loading || parentLoading || !data) && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: 0}} />}
            {data?.[0] &&
                <TabContext value={selectedTab || tabs?.[0]?.id}> 
                    {isMobile ?
                        <EventFilter programData={data?.[0]} loading={loading || parentLoading} onChange={handleChangeEvent} />
                    :
                        <ProgramInfo programData={data?.[0]} onDelete={onDelete} onEdit={onEdit} loading={loading || parentLoading}>
                            <EventFilter programData={data?.[0]} loading={loading || parentLoading} onChange={handleChangeEvent}/>
                        </ProgramInfo>
                    }
                    <WithDetailsToolbar 
                        tabs={visibleTabs}
                        programData={data?.[0]} 
                        parentRef={contentRef} 
                        onSelection={handleToolbarClick} 
                        selectedTab={selectedTab}
                        setSelectedTab={setSelectedTab}
                        sx={{mt: 4, ml: 'auto'}} 
                        stickyTop={selectedPrograms?.length > 1 ? 68 : 38}
                    />
                    <TabPanel value={`${selectedTab || tabs?.[0]?.id}`} sx={{ px: isMobile ? 1 : undefined, pb: isMobile ? theme => theme.sizes.headerHeight : undefined }} >
                        {[tabs?.find(a => a.id === selectedTab)]?.map(tab => (
                            tab && <div key={tab.id}>
                                {tab?.component({...{
                                    programData: data?.[0], 
                                    groupTypes: groupTypesData,
                                    onUpdate: handleUpdate, 
                                    fetchCounter, 
                                    setFetchCounter,
                                    loading: loading || parentLoading,
                                    selectedEvent: selectedEvent,
                                    groupTypeId: tab.groupTypeId
                                }, ...selectedTabProps})}
                            </div>
                        ))}
                    </TabPanel>
                </TabContext>
            }
        </Container>
    );
}