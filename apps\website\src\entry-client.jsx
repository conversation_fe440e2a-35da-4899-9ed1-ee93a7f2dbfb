import { hydrateRoot } from 'react-dom/client';
import createEmotionCache from './createEmotionCache';
import App from './App';
import { getInfo, formatPageSlug } from './utils';

const cache = createEmotionCache({});
(async () => {
    try {
        const url = formatPageSlug(window.location.pathname);
        const { data, error} = await getInfo({ url }, import.meta.env.VITE_API_URL, console.log, console.error);  
        if (error) {
            console.error(error);
            return;
        }
        if (!data) {
            console.error('No data found for this page.');
            return;
        }

        hydrateRoot(
            document.querySelector('#root'), 
            <App 
                cache={cache} 
                company={data?.company}
                indexPage={data?.indexPage}
                url={`/${url}`}
                ssr={false}
                pages={data?.pages}
                themeOverrides={data?.themeOverrides}
            />,
        );
    } catch (err) {
        console.error('An unexpected error occurred:', err);
    }
})();
