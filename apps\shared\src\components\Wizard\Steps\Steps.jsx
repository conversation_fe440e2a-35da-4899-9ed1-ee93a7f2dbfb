import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MobileStepper, Box, Button, useTheme } from '@mui/material';
import { useFormContext } from '../../useForm/FormContext';
import { ErrorBar } from '../../Snacks';
import { KeyboardArrowLeftOutlined as KeyboardArrowLeftIcon, KeyboardArrowRightOutlined as KeyboardArrowRightIcon, DoneAllOutlined as SaveIcon} from '@mui/icons-material';

export const Steps = ({
    steps, // an array of components for each step
    stepList, // a json array with info for each step, like the description, title, etc
    activeStep, // the active step
    loading, // loading state in the parent component
    onError, // function to call when errors are found
    onStepChange, // function to call when the step changes,
    showGroupDescriptionColumn, // show the group description column
    variant,
    onNext,
    onBack,
    onSubmit,
    hasErrors,
    theme = useTheme(),
    ...props
}) => {
    const { t } = useTranslation();
    const { submitForm, formData } = useFormContext();
    const [validationMessage, setValidationMessage] = useState('');
    const [showValidationError, setShowValidationError] = useState(false);

    // Check if the current step has any unfilled required fields
    const validateCurrentStep = useCallback(() => {
        // Get the current step's form data
        const currentStepData = formData?.[activeStep] || [];

        // Check for required fields that are empty
        const requiredFieldsEmpty = currentStepData.some(field =>
            field.required &&
            (field.value === undefined || field.value === null || field.value === '')
        );

        return !requiredFieldsEmpty;
    }, [activeStep, formData]);

    const handleSubmit = useCallback(() => {
        // display the formData
        console.log('Submitting form data', formData);
        if (activeStep === steps?.length - 1) submitForm({flat: true});
    }, [activeStep, steps, submitForm]);

    const handleNext = useCallback(() => {
        if (activeStep === steps?.length - 1) return;

        // Check validation before proceeding to next step
        if (hasErrors) {
            setValidationMessage(t('error:fixErrors'));
            setShowValidationError(true);
            return;
        }

        // Check for required fields
        if (!validateCurrentStep()) {
            setValidationMessage(t('error:requiredFields'));
            setShowValidationError(true);
            return;
        }

        // If validation passes, proceed to next step
        if (onStepChange) onStepChange(prev => prev + 1);
    }, [activeStep, steps, onStepChange, hasErrors, validateCurrentStep, t]);

    const handleBack = useCallback(() => {
        if (activeStep === 0) return;
        // Allow going back regardless of validation state
        if (onStepChange) onStepChange(prev => prev - 1);
    }, [activeStep, onStepChange]);

    const handleCloseValidationError = useCallback(() => {
        setShowValidationError(false);
    }, []);

    // get the form fields for the current step, and pre-fill them if we have data from the server
    const data = useMemo(() => {
        let _data = [];
        // Check if stepList has step functions directly or nested under stepList property
        const stepFunctions = stepList?.stepList ? stepList : stepList;
        if (stepFunctions?.[`step${activeStep + 1}`]) _data = stepFunctions?.[`step${activeStep + 1}`](props.data); // this calls the function for the current step, eg step1(data-to-load)
        return _data;
    }, [activeStep, props?.data, stepList]);

    // Create a wrapper for onStepChange that enforces validation
    const handleStepChange = useCallback((newStep) => {
        // If trying to move forward
        if (typeof newStep === 'number' && newStep > activeStep) {
            // Check validation before proceeding to next step
            if (hasErrors) {
                setValidationMessage(t('error:fixErrors'));
                setShowValidationError(true);
                return;
            }

            // Check for required fields
            if (!validateCurrentStep()) {
                setValidationMessage(t('error:requiredFields'));
                setShowValidationError(true);
                return;
            }
        }

        // If validation passes or moving backward, proceed to the step
        if (onStepChange) onStepChange(newStep);
    }, [activeStep, hasErrors, validateCurrentStep, onStepChange, t]);

    const commonProps = useMemo(() => {
        const extraProps = {}
        if (activeStep === steps.length) {
            extraProps.onSubmit = handleSubmit;
            extraProps.onStepChange = handleStepChange; // Use our validation wrapper
        }

        return ({
            loading,
            onError,
            showGroupDescriptionColumn,
            ...extraProps,
            ...props
        });
    }, [loading, onError, handleStepChange, showGroupDescriptionColumn, props, handleSubmit]);

    const Component = steps[activeStep];

    if (!Component) return null;
    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            {/* Validation Error Message - only shown when trying to navigate with errors */}
            {showValidationError && (
                <ErrorBar
                    open={true}
                    message={validationMessage}
                    onClose={handleCloseValidationError}
                />
            )}

            <Component {...commonProps} data={data} stepData={props.data} />

            {/* Navigation Buttons */}
            <MobileStepper
                variant={variant || "progress"}
                steps={stepList?.stepList?.length || 0}
                activeStep={activeStep}
                position="static"
                sx={{ flexGrow: 1, zIndex: theme => theme.zIndex.appBar}}
                nextButton={
                    <Button size="small" onClick={activeStep === (stepList?.stepList?.length - 1 || steps?.length - 1) ? (onSubmit || handleSubmit) : (onNext || handleNext)} disabled={loading || hasErrors}>
                        {activeStep === (stepList?.stepList?.length - 1 || steps?.length - 1) && <SaveIcon fontSize='small' sx={{mr: 0.5}} />}
                        {activeStep === (stepList?.stepList?.length - 1 || steps?.length - 1) ? t('general:save') : t('general:next')}
                        {activeStep !== (stepList?.stepList?.length - 1 || steps?.length - 1) &&
                            (theme.direction === 'rtl' ? <KeyboardArrowLeftIcon /> : <KeyboardArrowRightIcon />)
                        }
                    </Button>
                }
                backButton={
                    <Button size="small" onClick={onBack || handleBack} disabled={activeStep === 0}>
                        {theme.direction === 'rtl' ? <KeyboardArrowRightIcon /> : <KeyboardArrowLeftIcon />}
                        {t('general:back')}
                    </Button>
                }
            />
        </Box>
    );
}