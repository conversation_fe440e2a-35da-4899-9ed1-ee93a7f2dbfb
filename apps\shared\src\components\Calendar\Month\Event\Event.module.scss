.event{
    position: sticky;
    top: 30px;
    height: 16px;
    line-height: 0.8;
    overflow: hidden;
    display:flex;
    flex-direction: row;
    align-items: center;

    span{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;    
    }
    
    &.just-prev{
        clip-path: polygon(3px 0, 100% 0, 100% 100%, 3px 100%, 0 50%);
    }
    &.just-next{
        clip-path: polygon(0 0, calc(100% - 3px) 0, 100% 50%, calc(100% - 3px) 100%, 0 100%);
    }
    &.both{
        clip-path: polygon(3px 0, calc(100% - 3px) 0, 100% 50%, calc(100% - 3px) 100%, 3px 100%, 0 50%);
    }
}