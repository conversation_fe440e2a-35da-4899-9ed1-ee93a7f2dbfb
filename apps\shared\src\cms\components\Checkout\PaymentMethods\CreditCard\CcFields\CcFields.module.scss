.card{
    padding: 1rem;
    padding-bottom: 2rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    min-height: 150px;

    .field {
        background-color: transparent;
    }

    .cc-header {
        margin-bottom:1rem;

        .container {
            align-items: center; 
            justify-content: space-between; 
            width: 100%;
            margin-top: 1.5rem;

            .touchless {
                background-color: #bdbdbd;
                width: 25px;
                height: 25px;
                mask: url(./touchless.svg) no-repeat center;
                mask-size: contain;
            }
        
            .chip {
                position:relative;
                width: 40px;
                height: 32px;
                background-color:#eeeeee;
                border-radius: 10px;
                //box-shadow: inset 0px 0px 15px 1px rgba(189, 189, 189, 0.1);
        
                &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: #bdbdbd;
                    mask: url(./ccchip2.svg) no-repeat center;
                    mask-size: contain;
                }
            }
        }
    }
}
