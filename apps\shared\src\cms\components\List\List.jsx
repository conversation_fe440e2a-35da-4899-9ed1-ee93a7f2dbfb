import React from 'react';
import { Stack, List as <PERSON>i<PERSON>ist, ListItem, ListItemText, ListItemIcon } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { Icon } from '../common';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

export const List = ({
    id,
    items = [],
    dense,
    disablePadding,
    type,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        list: {},           // MUI List props
        listItem: {},       // MUI ListItem props
        item: {},           // MUI ListItemText props
        icon: {},           // MUI ListItemIcon props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, customCss, canRender, noContent } = prepareComponent({name: "list", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    if (!Array.isArray(items)) items = [items]; // makes sure items is an array

    let listSx, listItemSx;
    if (type !== 'icon' && type !== 'avatar' && type !== 'none') {
        listSx = { listStyleType: type, pl: 6 };
        listItemSx = { display: 'list-item', pl: 0 };
    }

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {!items.length ? noContent :
                    <MuiList 
                        dense={Boolean(dense)} 
                        {...slotProps?.list} 
                        sx={{...listSx, ...slotProps?.list?.sx}}
                    >
                        {items?.map((item, i) => (
                            <ListItem  
                                key={`item-${id}-${i}`} 
                                disablePadding={Boolean(disablePadding)} 
                                {...slotProps?.listItem}
                                sx={{...listItemSx, ...slotProps?.listItem?.sx}}
                            >
                                {type === 'icon' && item.icon &&
                                    <ListItemIcon>
                                        <Icon name={item.icon} {...slotProps?.icon}/>
                                    </ListItemIcon>
                                }
                                <ListItemText primary={item.title} secondary={item.subtitle} {...slotProps.item} />
                            </ListItem>
                        ))}
                    </MuiList>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};