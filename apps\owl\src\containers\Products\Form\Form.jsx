import React, { useRef, useCallback, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Grid2, Button, Box, Paper, IconButton, Tooltip, Collapse } from '@mui/material';
import { SuccessBar, FormItem, WithLinkReveal, MaterialSymbol } from '@siteboss-frontend/shared/components';

import { useFormLogic } from './useFormLogic';
import ChangeSku from './ChangeSku';

const SectionWrapper = ({sectionId, label, errors, onToggle, children, ...props}) => (
    sectionId > 1 
        ? <WithLinkReveal label={label} onToggle={onToggle} hasErrors={Boolean(errors)}>{children}</WithLinkReveal> 
        : children
);

export const Form = ({loading:parentLoading, id, onSuccess, ...props}) => {
    const { t } = useOutletContext();
    const ref = useRef(null);

    const {
        values,
        errors,
        success,
        loading,
        productErrors,
        handleChange,
        handleSubmit,
        ErrorBar,
        LoadingBar,
        ProductErrorBar,
        rows,
    } = useFormLogic({loading:parentLoading, id, onSuccess});

    const [showChangeSku, setShowChangeSku] = useState(false);

    const toggleChangeSKU = useCallback(e => {
        setShowChangeSku(!showChangeSku);
    }, [showChangeSku]);

    const sectionHasErrors = useCallback(sectionId => {
        return values.filter(a => a.row === sectionId).some(a => errors?.[a.name]);
    }, [values, errors]);

    if (productErrors) {
        return (
            <Container>
                <ProductErrorBar />
            </Container>
        );
    }

    return (
        <Container ref={ref}>
            {rows.map(row => (
                <SectionWrapper key={`row-${row.id}`} errors={sectionHasErrors(row.id)} sectionId={row.id} label={row.title}>
                    {values.find(field => field.component && field.row === row.id) &&
                        <Grid2 container columnSpacing={2}>
                            {values.filter(field => field.component && field.row === row.id).map(field => (
                                <Grid2 size={field?.rowSize || row.size} key={`form-${field.name}`}>
                                    <FormItem
                                        {...field}
                                        component={field?.component === "CustomSKU" ? "TextField" : field.component}
                                        parentRef={ref}
                                        errors={errors?.[field.name]}
                                        onChange={handleChange}
                                        loading={loading}
                                        InputProps={field?.component === "CustomSKU" 
                                            ? {endAdornment: (
                                                <Tooltip title={t('product:changeSku')}>
                                                    <IconButton
                                                        aria-label="change sku"
                                                        onClick={toggleChangeSKU}
                                                        edge="end"
                                                    >
                                                        <MaterialSymbol icon="route" size="sm"/>
                                                    </IconButton>
                                                </Tooltip>
                                            )}
                                            : undefined
                                        }
                                    />
                                    {field.component === "CustomSKU" && (
                                        <Collapse in={showChangeSku}>
                                            <ChangeSku field={field} parentRef={ref} loading={loading} onChange={handleChange} onClose={toggleChangeSKU} />
                                        </Collapse>
                                    )}
                                </Grid2>
                            ))}
                        </Grid2>
                    }
                </SectionWrapper>
            ))}
            <LoadingBar />
            <ErrorBar />
            {success && <SuccessBar message={t(`product:${id ? 'updated' : 'saved'}`)} onClose={onSuccess} />}

            <Box component={Paper} elevation={16} square sx={{ 
                display: 'flex',
                justifyContent: 'space-between',
                position: 'sticky',
                bottom: 0,
                boxShadow: 0,
                width: '100%',
                zIndex: theme => theme.zIndex.appBar
            }}>
                <Button variant='contained' color='primary' fullWidth size='large' sx={{mt:1}} onClick={handleSubmit}>
                    {t('general:save')}
                </Button>
            </Box>

        </Container>
    );
}