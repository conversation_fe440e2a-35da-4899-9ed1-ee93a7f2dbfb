import { generateTitle, generateSubTitle } from "../../../utils";

export const properties = [
    {
        name: 'type',
        label: 'builder:component.list.type',
        component: "Select",
        value: 'icon',
        size: "small",
        margin: "normal",
        options: [
            {slug: 'builder:component.list.types.none', id: 'none'},
            {slug: 'builder:component.list.types.icon', id: 'icon'},
            //{slug: 'builder:component.list.types.avatar', id: 'avatar'},
            {slug: 'builder:component.list.types.bullet', id: 'disc'},
            {slug: 'builder:component.list.types.circle', id: 'circle'},
            {slug: 'builder:component.list.types.square', id: 'square'},
            {slug: 'builder:component.list.types.decimal', id: 'decimal'},
            {slug: 'builder:component.list.types.decimalLeadingZero', id: 'decimal-leading-zero'},
            {slug: 'builder:component.list.types.lowerAlpha', id: 'lower-alpha'},
            {slug: 'builder:component.list.types.upperAlpha', id: 'upper-alpha'},
            {slug: 'builder:component.list.types.lowerRoman', id: 'lower-roman'},
            {slug: 'builder:component.list.types.upperRoman', id: 'upper-roman'},
        ],
    },
    {
        name: 'dense',
        label: 'builder:component.list.dense',
        component: "Switch",
        value: false,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'disablePadding',
        label: 'builder:component.list.disablePadding',
        component: "Switch",
        value: false,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'items',
        multiple: true,
        component: "local.MultiField",
        value: [
            {title: generateTitle(), subtitle: generateSubTitle(), icon: 'SportsFootball'},
        ],
        children: [
            {
                name: 'title',
                label: 'builder:component.heading.title',
                component: "TextField",
                value:  generateTitle(),
                size: "small",
                margin: "normal",
            },
            {
                name: 'subtitle',
                label: 'builder:component.heading.subtitle',
                component: "TextField",
                value:  generateSubTitle(),
                size: "small",
                margin: "normal",
            },
            {
                name: 'icon',
                label: 'builder:component.button.icon.title',
                component: "IconSelector",
                value: '',
                size: "medium",
                fullWidth: true,
                variant: 'outlined',
                margin: "normal",
            },
        ]
    },
];
