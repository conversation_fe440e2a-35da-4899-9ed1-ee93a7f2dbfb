import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Container, Grid2, Typography, Button, Divider, useTheme, useMediaQuery } from '@mui/material';
import { Logo } from '@siteboss-frontend/shared/components';

import Background from './Background';
import Form from './Form';
//import Logout from './Logout';

export const Login = props => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const theme = useTheme();

    const navigate = useNavigate();

    const user = useSelector(state => state.user);
    const company = useSelector(state => state.company);
    const colorMode = theme.palette.mode;

    useEffect(() => {
        if (user.token) {
            let prevUrl = localStorage.getItem('prevUrl');
            if (prevUrl) {
                localStorage.removeItem('prevUrl');
                prevUrl = prevUrl.replace(import.meta.env.VITE_BASE_PATH, ""); // remove the base path so we don't redirected to something buggy like /p/cms/p/cms
            }
            if (!prevUrl) prevUrl = "/";            
            navigate(prevUrl, {replace: true});
        }
    }, [user.token, navigate]);

    return (
        <Box display="flex" direction="column" justifyContent="center" alignItems="center" spacing={2} sx={{
            height: `calc(100vh - ${theme.sizes.headerHeight})`,
            width: '100%', 
            overflow: 'hidden', 
            position: 'relative'
        }}>
            <Background url={company?.login_background_url || "https://siteboss.s3.amazonaws.com/themes/1/siteboss-1.mp4"}/>

            <Container maxWidth="xs" sx={{display: "flex", flexDirection: "column", alignItems: isMobile ? "center" : "flex-start", bgcolor: 'background.paper', py: 5}}>
                <Logo size="lg" noLink solid={colorMode !== "light"} />
                <Typography variant="h6">{t("login:title")}</Typography>
                <Grid2 container spacing={2} sx={{mt: 1}}>
                    <Grid2 size={{xs: 12}}>
                        <Form />
                    </Grid2>
                </Grid2>
            </Container>
        </Box>
    );
}