import { useState, useEffect, useCallback } from 'react';
import { useOutletContext, useNavigate } from 'react-router-dom';
import { Container, Stack, Button, Box, Typography } from '@mui/material';
import { useApi } from '@siteboss-frontend/shared';
import { Title, Confirm } from '@siteboss-frontend/shared/components';

import List from './List';

const apiParams = [
    {params: {endpoint: `/owl/merchant/delete`, method: 'DELETE'}},
];

export const Merchants = () => {
    const { t } = useOutletContext();
    const navigate = useNavigate();

    const { fetchData:processDelete, loading:deleteLoading, ErrorBar:DeleteErrorBar } = useApi(apiParams[0]);

    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [loading, setLoading] = useState(false);

    const handleNewClick = () => {
        navigate('/merchants/new');
    };

    const handleDelete = useCallback((_, item) => {
        if (item) setDeleteParams([item]);
        else setDeleteParams(selectedItems);
        if (item || selectedItems.length > 0) setShowConfirm(true);
    }, [selectedItems]);

    const handleEdit = useCallback((_, merchant) => {
        if (merchant?.id) {
            navigate(`/merchants/edit/${merchant.id}`);
        }
    }, [navigate]);

    useEffect(() => {
        setLoading(deleteLoading);
    }, [deleteLoading]);

    useEffect(() => {
        if (selectedItems.length <= 0) {
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedItems);
            setShowConfirm(false);
        }
    }, [selectedItems]);

    return (
        <Container>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title
                    title={t('merchant:merchants')}
                    breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('merchant:merchants')}]}
                />
                <Box>
                    <Button size="large" variant="contained" color="primary" onClick={handleNewClick}>
                        {t('merchant:newMerchant')}
                    </Button>
                </Box>
            </Stack>

            {showConfirm && deleteParams.length > 0 &&
                <>
                    <Confirm
                        title={t('merchant:deleteTitle')}
                        message={t('merchant:deleteMessage')}
                        onConfirm={async () => {
                            if (deleteParams.length > 0) {
                                const _ids = deleteParams.map(s => s.id);
                                const result = await processDelete({ endpoint: "/owl/merchant/"+_ids.join(','), id: _ids });
                                if (result?.data) {
                                    setDeleteParams([]);
                                    setShowConfirm(false);
                                }
                            }
                        }}
                        onDecline={() => {
                            setShowConfirm(false);
                            setDeleteParams([]);
                        }}
                    >
                        <ul>
                            {deleteParams?.map(param => (
                                <Typography key={`delete-merchant-${param.id}`} variant="body2" component="li">
                                    {param.code} - {param.contact}
                                </Typography>
                            ))}
                        </ul>
                    </Confirm>
                </>
            }
            <DeleteErrorBar />

            <List
                setSelected={setSelectedItems}
                selected={selectedItems}
                onEdit={handleEdit}
                onDelete={handleDelete}
                loading={loading}
            />
        </Container>
    );
}
