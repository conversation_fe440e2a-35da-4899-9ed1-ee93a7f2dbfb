import React, { useRef, useState } from 'react';
import { ContextMenu } from './ContextMenu';
import { action } from 'storybook/actions';
import { Button } from '@mui/material';

export default {
    title: 'CMS/Components/Context Menu',
    component: ContextMenu,
    tags: ['autodocs'],
    argTypes: {
        menuId: {
            description: "Unique identifier for the menu",
            control: 'text',
            table: {
                type: {
                summary: 'string'
                }
            }
        },
        items: {
            description: "Array of menu items",
            control: 'object',
            type: { required: true },
            table: {
                type: {
                summary: 'array'
                },
                detail: `
                    Array of objects: [{id, name, icon}]
                `
            }
        },
        openHandler: {
            description: "Function to handle opening/closing the menu",
            action: action('open-handler'),
            type: { required: true },
            table: {
                type: {
                summary: 'function'
                }
            }
        },
        clickHandler: {
            description: "Function to handle item click",
            action: action('click-handler'),
            type: { required: true },
            table: {
                type: {
                summary: 'function'
                }
            }
        },
        withStatus: {
            description: "Whether to show status options",
            control: 'boolean',
            table: {
                type: {
                summary: 'boolean'
                },
                defaultValue: {
                summary: false
                }
            }
        },
        status: {
            description: "Current status (active/inactive)",
            control: 'radio',
            options: ['active', 'inactive'],
            table: {
                type: {
                summary: 'string'
                }
            }
        },
        open:{

        },
        id:{
            
        }
    }
};

// Wrapper component to manage the open state and anchor element
const ContextMenuWrapper = (props) => {
    const [open, setOpen] = useState(false);
    const anchorRef = useRef(null);

    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    return (
        <>
            <Button ref={anchorRef} onClick={handleOpen}>
                Open Context Menu
            </Button>
            <ContextMenu
                {...props}
                ref={anchorRef}
                open={open}
                openHandler={handleClose}
            />
        </>
    );
};

// Playground story with required props
export const Playground = {
  render: (args) => <ContextMenuWrapper {...args} />,
  args: {
    menuId: 'playground-menu',
    items: [
      { id: 'edit', name: 'Edit', icon: 'fas fa-edit' },
      { id: 'delete', name: 'Delete', icon: 'fas fa-trash' }
    ],
    clickHandler: action('item-clicked')
  }
};

// Story with status options
export const WithStatusOptions = {
  render: (args) => <ContextMenuWrapper {...args} />,
  args: {
    menuId: 'status-menu',
    items: [
      { id: 'view', name: 'View', icon: 'fas fa-eye' },
      { id: 'edit', name: 'Edit', icon: 'fas fa-edit' }
    ],
    withStatus: true,
    status: 'active',
    clickHandler: action('item-clicked')
  }
};

// Story with many items
export const ManyItems = {
  render: (args) => <ContextMenuWrapper {...args} />,
  args: {
    menuId: 'many-items-menu',
    items: [
      { id: 'view', name: 'View', icon: 'fas fa-eye' },
      { id: 'edit', name: 'Edit', icon: 'fas fa-edit' },
      { id: 'copy', name: 'Copy', icon: 'fas fa-copy' },
      { id: 'move', name: 'Move', icon: 'fas fa-arrows-alt' },
      { id: 'delete', name: 'Delete', icon: 'fas fa-trash' },
      { id: 'archive', name: 'Archive', icon: 'fas fa-archive' }
    ],
    clickHandler: action('item-clicked')
  }
};

// A whimsical story
export const EmojiMenu = {
  render: (args) => <ContextMenuWrapper {...args} />,
  args: {
    menuId: 'emoji-menu',
    items: [
      { id: 'happy', name: 'Happy', icon: '😊' },
      { id: 'sad', name: 'Sad', icon: '😢' },
      { id: 'angry', name: 'Angry', icon: '😡' },
      { id: 'surprised', name: 'Surprised', icon: '😲' }
    ],
    clickHandler: action('emoji-clicked')
  }
};