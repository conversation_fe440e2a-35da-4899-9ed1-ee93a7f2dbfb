import React, { useCallback, useMemo } from 'react';
import { Navigate } from 'react-router-dom';
import { Stack } from '@mui/material';

import { Modal, LoadingBar } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import EmptyCart from '../common/pos/EmptyCart';
import ProductDetail from '../ProductDetail';
import { layouts, fakeItems } from './Layouts';
import { properties } from './properties';
import { usePosCart } from './usePosCart';

export const Cart = ({
    id,
    layoutId,
    redirectToProductPage = false,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        table: {},          // MUI table props
        productDetails: {
            modalSize: "sm",    // size of the modal
        },
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    builderProps,
    children,
    ...props
}) => {

    const {
        loading,
        reduxStore,
        openProductDetail,
        handleDelete,
        handleDuplicate,
        handleDetailsOpen,
        handleDetailsClose,
        handleEditItem,
        productRoute,
        productsRoute,
        sharedDetailsProps,
        selectedProduct,
        cart,
    } = usePosCart({slotProps});

    const { slotProps: updatedSlotProps, isMobile, t, canRender, customCss } = prepareComponent({name: "cart", layoutId, layouts, slotProps, isBuilder, condition, localState: {
        openProductDetail,
        selectedProduct,
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;

    const handleDeleteClick = useCallback(id => e => {
        e.preventDefault();
        e.stopPropagation();
        if (!id) return;
        handleDelete(id);
    }, [handleDelete]);

    const handleDuplicateClick = useCallback(id => e => {
        e.preventDefault();
        e.stopPropagation();
        if (!id) return;
        handleDuplicate(id);
    }, [handleDuplicate]);

    const handleRowClick = useCallback(id => e => {
        if (!id) return;
        handleDetailsOpen(id);
    }, [handleDetailsOpen]);

    const handleEditClick = useCallback((id, values) => e => {
        if (!id) return;
        handleEditItem(id, values)
    }, [handleDetailsOpen]);

    const Layout = useMemo(() => layouts.find(layout => layout.id === layoutId)?.component, [layoutId]);
    
    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack direction={{xs: "column", lg: "row"}} spacing={1} useFlexGap flexWrap="wrap" justifyContent="space-between" position="relative" {...slotProps?.cmsStack}>
                {loading && <LoadingBar type="linear" sx={{height: "2px", position: "absolute", top: 0, left: 0, zIndex: theme => theme.zIndex.appBar}} />}
                <div style={{flexGrow: 1}}>
                    <Layout 
                        isBuilder={isBuilder} 
                        onEdit={handleEditClick} 
                        onRowClick={handleRowClick} 
                        onDelete={handleDeleteClick} 
                        onDuplicate={handleDuplicateClick} 
                        slotProps={slotProps?.table}
                        items={isBuilder ? fakeItems : (cart?.cart || [])}
                    />
                    {!isBuilder && !cart?.cart?.length > 0 && <EmptyCart showBackButton={redirectToProductPage} />}
                </div>
            </Stack>
            {children}

            {!isBuilder && 
                <>
                    {redirectToProductPage && productRoute && openProductDetail ? <Navigate to={productRoute} />
                    :
                        <Modal 
                            open={openProductDetail} 
                            onClose={handleDetailsClose}
                            maxWidth={slotProps?.productDetails?.modalSize || "sm"}
                            fullScreen={isMobile}
                            aria-describedby={t(`product:productDetails`)}
                        >
                            <ProductDetail {...sharedDetailsProps} fullPage={false} />
                        </Modal>
                    }
                </>
            }
        </CmsContainer>
    );
}