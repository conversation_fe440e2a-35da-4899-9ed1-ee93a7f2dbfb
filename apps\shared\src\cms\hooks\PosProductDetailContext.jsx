import React, { createContext, useState, useEffect, useCallback } from 'react';
import { useProduct } from './useProduct';

export const PosProductDetailContext = createContext();

export const PosProductDetailProvider = ({ cartItemId, productId, variantId, fullPage:pFullPage, modalSize, cartRoute, children }) => {
    const [updateRootView, setUpdateRootView] = useState(false);    // if a value in the root view changes, the children will be reset (eg. selecting a new variant will reload addons for that variant)
    const [viewIndex, setViewIndex] = useState([]);                 // we create an array of views, because sometimes we need to replace a view inside a modal (eg. selecting a family member triggers the customs fields)
    const [fullPage, setFullPage] = useState(pFullPage || false);   // sometimes we need to know if this is a full page or a modal to load components differently
    const [loading, setLoading] = useState(false);                  // to keep track of all loading states in one place
    const [errors, setErrors] = useState(null);                     // to keep track of all errors in one place

    const posProductDetailHook = useProduct({cartItemId, productId, variantId});

    const changeView = useCallback(view => {
        const index = viewIndex.indexOf(view);
        if (index !== -1) setViewIndex(viewIndex.slice(0, index + 1));
        else setViewIndex([...viewIndex, view]);
    }, [viewIndex]);

    const goToPreviousView = useCallback(() => {
        if (viewIndex.length > 1) {
            const index = viewIndex.length - 2;
            changeView(viewIndex[index]);
        }
    }, [viewIndex, changeView]);

    useEffect(() => {
        if ((viewIndex.length === 0 || updateRootView) && children ) {
            setViewIndex([children]);
            setUpdateRootView(false);
        }
    }, [children, viewIndex, updateRootView]);

    useEffect(() => {
        if (posProductDetailHook?.loading) setLoading(posProductDetailHook?.loading);
        if (posProductDetailHook?.errors) setErrors(posProductDetailHook?.errors);
    }, [posProductDetailHook?.loading, posProductDetailHook?.errors]);

    return (
        <PosProductDetailContext.Provider value={{ 
            changeView, 
            goToPreviousView, 
            setUpdateRootView, 
            modalSize,
            fullPage, 
            setFullPage, 
            loading, 
            setLoading,
            errors,
            setErrors,
            cartRoute,
            ...posProductDetailHook,
        }}>
            {viewIndex[viewIndex.length - 1]}
        </PosProductDetailContext.Provider>
    );
}