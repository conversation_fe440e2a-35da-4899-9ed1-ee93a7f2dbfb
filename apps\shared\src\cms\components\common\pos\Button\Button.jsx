import React, { useCallback, useState } from 'react';
import clsx from 'clsx';
import { Stack, Container, Paper, ButtonBase, lighten, FormHelperText, useMediaQuery } from '@mui/material';
import styles from './Button.module.scss';

import Modal from '../../../../../components/Modal';

export const Button = ({ item, selected, disabled, onClick, slotProps, children, className, ...props }) => (
    <Paper 
        key={item.id}
        component={ButtonBase} 
        disabled={disabled} 
        elevation={0} 
        variant="tileButton" 
        size="sm"
        selected={selected ? 1 : 0}
        className={clsx(styles.button, styles?.[className], (selected && slotProps?.fullPage) ? styles.active : null)} 
        onClick={onClick}
        value={item.id}
        sx={{
            backgroundColor: theme => lighten(theme.palette.background.paper, 0.01),
        }}
        {...slotProps}
        {...props}
    >
        {children}
    </Paper>
);

export const ButtonWrapper = ({ items, selected, disabled, onClick, slotProps, slots, className, children, ...props }) => {
    if (!Array.isArray(selected)) selected = [selected];
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [openModalId, setOpenModalId] = useState(null);
    
    const clickHandler = useCallback(e => {
        const id = e.currentTarget.value;
        if (id){
            const found = selected.find(a => +a?.id === +id);
            const item = items.find(a => +a?.id === +id);
            if (onClick) onClick(item);

            // replaces the modal content with whatever component is passed in the extraInfo slot
            if (slots?.extraInfo && slotProps?.extraInfo?.show && item && !found) {
                if (slotProps?.fullPage) setOpenModalId(item.id);
                else if (slotProps?.changeView) slotProps.changeView(slots?.extraInfo({item}));
            }

        }
    }, [onClick, items, slotProps, slots, selected]);    

    return (
        <Stack 
            component={Container} 
            direction="row" 
            spacing={1}
            useFlexGap 
            flexWrap="wrap"
            maxWidth={undefined}
            disableGutters
            {...slotProps?.stack} 
            sx={{
                py: 1,
                overflow: 'hidden', 
                overflowY: 'auto', 
                justifyContent: 'center',
                ...slotProps?.sx
            }}
        >
            {items.map((item, i) => {
                let checked = Boolean(selected?.find(a => +a?.id === +item.id)), _disabled = disabled, helperText;
                if (slots?.validate && !disabled) {
                    let valid = slots.validate(item);
                    if (typeof valid === 'string') {
                        helperText = valid;
                        valid = false;
                    }
                    valid = Boolean(valid);
                    checked = valid ? checked : false;
                    _disabled = !valid;
                }                
                return (
                    <React.Fragment key={item.id}>
                        <Button 
                            item={item}
                            disabled={_disabled} 
                            selected={checked} 
                            onClick={clickHandler}
                            slotProps={slotProps?.button}
                            value={item.id}
                            className={className}
                            {...props}
                        >
                            <Stack direction="column" spacing={1} useFlexGap sx={{width: '100%'}}>
                                {children?.[i]}
                                {helperText && <FormHelperText sx={{textAlign: "center"}}>{helperText}</FormHelperText>}
                            </Stack>
                        </Button>
                        {!_disabled && slots?.extraInfo && slotProps?.fullPage && slotProps?.extraInfo?.show && 
                            <Modal 
                                open={openModalId === item.id} 
                                onClose={() => setOpenModalId(null)}
                                maxWidth={"sm"}
                                fullScreen={isMobile}
                            >
                                {slots?.extraInfo({item, onClose: () => setOpenModalId(null)})}
                            </Modal>
                        }
                    </React.Fragment>
                );
            })}
        </Stack>
    );
}