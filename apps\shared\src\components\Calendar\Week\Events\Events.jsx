import React, { useMemo } from 'react';
import { startOfDay, endOfDay } from 'date-fns';

import EventBase from '../../Event';

import styles from './Events.module.scss';

const minutesInDay = 1440;

const calculateEventWidths = (events) => {
    // sort events by start time
    events.sort((a, b) => a.startDate - b.startDate);

    let timeline = [];
    let eventMap = new Map();

    // build a timeline of all start/end points
    events.forEach(event => {
        timeline.push({ time: event.startDate, type: 'start', id: event.id });
        timeline.push({ time: event.endDate, type: 'end', id: event.id });
        eventMap.set(event.id, event);
    });

    // sort the timeline by time
    timeline.sort((a, b) => a.time - b.time);

    let activeEvents = new Set(); // track active events
    let maxConcurrentEvents = 0; // track max concurrent events

    // scan through the timeline to determine max overlaps
    timeline.forEach((point, i) => {
        if (point.type === 'start') {
            activeEvents.add(i);
            maxConcurrentEvents = Math.max(maxConcurrentEvents, activeEvents.size);
        } else {
            activeEvents.delete(i);
        }
    });

    // assign widths based on max overlaps
    events.forEach(event => {
        event.width = 100 / maxConcurrentEvents;
    });

    return events;
}
const getEventStyle = (event, day) => {
    const dayStart = startOfDay(day);
    const dayEnd = endOfDay(day);

    let eventBoundaries ={ startsBefore: false, endsAfter: false};
        
    let startMinutesFromDayStart;
    if (event.startDate >= dayStart) { // event starts on the current day
        startMinutesFromDayStart = (event.startDate.getHours() * 60) + event.startDate.getMinutes();
    } else { // for events that don't start on the current day, top should be 0
        startMinutesFromDayStart = 0;
    }
    const topPercentage = (startMinutesFromDayStart / minutesInDay) * 100;

    // calculate height
    let eventDurationMinutes = 0;
    if(event.startDate < dayStart) { // event started before today
        eventBoundaries.startsBefore = true;

        if (event.endDate > dayEnd) { // event spans the entire day
            eventBoundaries.endsAfter = true;
            eventDurationMinutes = minutesInDay; 
        } else { // event ends today
            eventBoundaries.endsAfter = false;
            eventDurationMinutes = ((event.endDate - dayStart) / (1000 * 60)); 
        }
    } else { // event starts today
        if(event.endDate > dayEnd) { // event goes beyond today
            eventBoundaries.endsAfter = true;
            eventDurationMinutes = ((dayEnd - event.startDate) / (1000 * 60));
        } else { // event starts and ends today
            eventDurationMinutes = ((event.endDate - event.startDate) / (1000 * 60));
        }
    }

    const heightPercentage = (eventDurationMinutes / minutesInDay) * 100;

    return {
        top: `${topPercentage}%`, 
        height: `calc(${heightPercentage}% - 1px)`, 
        borderTopLeftRadius: eventBoundaries.startsBefore ? 0 : undefined, 
        borderTopRightRadius: eventBoundaries.startsBefore ? 0 : undefined, 
        borderBottomLeftRadius: eventBoundaries.endsAfter ? 0 : undefined, 
        borderBottomRightRadius: eventBoundaries.endsAfter ? 0 : undefined,
    };
};

export const Events = ({
    currentDate, // the current date (Date object)
    events, // an array of events (optional)
    colors, // an array of colors (optional)
    disabled,
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props
}) => {
    //const [eventProps, setEventProps] = useState(null);

    const eventProps = useMemo(() => {
        if (!currentDate || !events) return null;        
        const adjustedEvents = calculateEventWidths(events); // this will set the width for each event, in case they overlap

        if (!adjustedEvents) return null;
        return adjustedEvents?.map((event, i) => ({
            id: event.id,
            title: event.title,
            width: event.width,
            style: getEventStyle(event, currentDate),
            length: adjustedEvents.length,
            color: colors?.[i] || colors?.[i % colors.length] || undefined,
            event: event,
        }));
    }, [currentDate, events, colors]);

    if (!currentDate) return null;

    return (
        <>
            {eventProps?.map((event, i) => (
                <EventBase
                    event={event.event}
                    key={`event-${event.id}-${i}`}
                    className={styles.event}
                    sx={{
                        ...event.style, 
                        left: `${event.width * i }%`,
                        width: `calc(${event.width}% - ${i < event.length - 1 ? '1px' : '0px'})`,
                        background: theme => event.color || (theme.palette.mode === "light" ? theme.palette.primary.dark : theme.palette.primary.light),
                        color: theme => event.color ? theme.palette.getContrastText(event.color) : theme.palette.primary.contrastText,
                        zIndex: theme => theme.zIndex.mobileStepper + 1,
                    }}
                    disabled={disabled}
                    onEventClick={onEventClick}
                />
            ))}
        </>
    );
};