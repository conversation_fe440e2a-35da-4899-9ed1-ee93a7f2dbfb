import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Paper, Typography, ImageListItem, ImageListItemBar, IconButton, Tooltip } from '@mui/material';
import { DeleteOutline as DeleteIcon, CropOutlined as CropIcon } from '@mui/icons-material';

import styles from './PreviewCard.module.scss';

export const PreviewCard = ({ media, onClick, onCrop, onRemove, selected, loading, ...props }) => {
    const { t } = useTranslation();
    
    let description = (media.description && isNaN(media.description)) ? media.description : null;
    if (media.metadata && !description){
        let meta = media.metadata;
        if (typeof meta === 'string') meta = JSON.parse(meta);
        if (meta.original_filename) description = meta.original_filename;
    }

    return (
        <Box 
            onClick={(onClick && !loading) ? onClick(media) : undefined} 
            sx={{
                position: 'relative',
                cursor: onClick ? "pointer" : undefined,
                mb: 0.5,
                border: 2,
                borderRadius: 1,
                borderColor: selected ? 'primary.main' : 'transparent',
                /*'&:hover > div:first-of-type > div:first-of-type:after': {
                    content: '""',
                    backgroundColor: 'rgba(0,0,0,0.25)',
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    top: 0,
                    left: 0,
                },*/
                '&:hover': {
                    borderColor: 'secondary.main',
                }
            }}
        >
            <ImageListItem component="div" className={styles['image-container']}>
                {(media.media_type === 1 || media.media_type === 9) ? 
                    <div style={{position:'relative'}}>
                        <Paper elevation={0} component='img' src={media.url} alt={description} loading='lazy' />
                    </div>
                : 
                    <Paper variant='outlined' elevation={0} className={styles.file}>
                        <Typography variant='subtitle3'>{description}</Typography>
                    </Paper>
                }
                <ImageListItemBar
                    subtitle={description}
                    position='below'
                    sx={{mt:0.5}}
                    actionIcon={
                        <div className={styles.actions}>
                            {onCrop && (media.media_type === 1 || media.media_type === 9) &&
                                <Tooltip title={t('general:crop')}>
                                    <IconButton
                                        aria-label={`crop ${description}`}
                                        //</Tooltip>onClick={onCrop(files?.processed?.[i])}
                                        size='small'
                                        disabled={loading}
                                    >
                                        <CropIcon fontSize='inherit'/>
                                    </IconButton>
                                </Tooltip>
                            }
                            {onRemove &&
                                <Tooltip title={t('general:remove')}>
                                    <IconButton
                                        aria-label={`remove ${description}`}
                                        onClick={onRemove(media)}
                                        size='small'
                                        disabled={loading}
                                    >
                                        <DeleteIcon fontSize='inherit'/>
                                    </IconButton>
                                </Tooltip>
                            }
                        </div>
                    }
                />
            </ImageListItem>
        </Box>
    );
}