import React, { useState, useCallback, memo } from 'react';
import { Routes as RrdRout<PERSON>, create<PERSON>rowser<PERSON><PERSON><PERSON>, BrowserR<PERSON>er, RouterProvider, Route, Outlet } from 'react-router-dom';

import { StaticRouter } from 'react-router-dom/server';
import { Loader } from '@siteboss-frontend/shared';
import { PosProvider } from '@siteboss-frontend/shared/cms/hooks';
import { Fzf } from '@siteboss-frontend/shared/components';

import Site from './containers/Site';

/*

export const buildRoutes = (pages, company) => {
    const lookup = pages.reduce((acc, item) => {
        acc[item.id] = { ...item, children: [] };
        if (item?.parent) acc[item.parent_id] = { ...item.parent, children: [] };
        return acc;
    }, {});

    const formatPageSlug = slug => slug.replace(/^\/|\/$/g, '').split("/").filter(Boolean).join("/");
    const getAbsolutePath = item => {
        if (!item.parent_id || !lookup[item.parent_id]) return formatPageSlug(item.slug);
        const parentPath = getAbsolutePath(lookup[item.parent_id]);
        return formatPageSlug(parentPath === '/' ? 
            `/${item.slug}` : 
            `${parentPath}/${item.slug}`.replace(/\/+/g, '/'));
    }

    const renderRoutes = ({route, company = null}) => {
        let page = <Site 
            moduleId={999} 
            data={route.page_type_id === 1 ? route.content : null} 
            pageId={route.id} 
            websiteId={route.website_id} 
            templateId={route.template_id}
        />;

        // ecommerce site
        const registerId = +route?.content?.properties?.register_id || +route?.parent?.content?.properties?.register_id || +company?.config?.default_patron_register || null;
        const shopId = +route.page_type_id === 14 ? +route?.id : (+route?.parent?.page_type_id === 14 ? +route?.parent_id : null);
        if (registerId && shopId) {
            page = <PosProvider registerId={registerId || 1} shopId={shopId} pageId={route.id}>{page}</PosProvider>;
        }

        // blog
        /* uncomment when the blog provider is ready
        let blogId = null;
        if (+route.page_type_id === 4) blogId = +route?.id;
        else if (+route?.parent?.page_type_id === 4) blogId = +route?.parent_id;
        if (blogId) page = <BlogProvider blogId={blogId}>{page}</BlogProvider>;
        *//*

        const absPath = route?.full_slug || getAbsolutePath(route);
        const routePath = absPath === '/' ? '/:id?/:slug?' : `/${absPath}/:id?/:slug?`;
        
        return { 
            path: routePath, 
            element: page, 
            handle: { 
                title: `${route?.title ? `${route.title} - ` : ""}${route?.website?.name ? `${route?.website?.name} | ` : ""}${company?.name || ""}` || null,
            },
        };
    }

    // we render the route as absolute paths (no cool react-router-dom nested routes because we need to append the optional params)
    return pages.map(route => renderRoutes({route, company})).sort((a, b) => {
        const cleanPathA = a.path.replace(/\/:[^/]+\??/g, '');
        const cleanPathB = b.path.replace(/\/:[^/]+\??/g, '');
        const nameCompare = cleanPathA.localeCompare(cleanPathB);
        const slashDiff = b.path.split('/').length - a.path.split('/').length;
        return cleanPathA.startsWith(cleanPathB) || cleanPathB.startsWith(cleanPathA) 
            ? slashDiff 
            : nameCompare;
    });
}
    */

const WithOutlet = memo(({ element }) => (
    <>
        {element}
        <Outlet />
    </>
));

export const buildRoutes = (pages, company, indexPage) => {
    const defaultRegister = +company?.config?.default_patron_register || null;
    const companyName = company?.name || '';

    const slashRegex = /^\/|\/$/g;
    
    const { roots } = pages.reduce((acc, page) => {
        const pageWithChildren = { ...page, children: [] };
        acc.pageMap[page.id] = pageWithChildren;
        
        if (page.parent_id) {
            const parentInMap = acc.pageMap[page.parent_id];
            if (!parentInMap) {
                acc.pageMap[page.parent_id] = { ...page.parent, children: [pageWithChildren] };
                if (page?.parent?.page_type_id !== 1) acc.roots.push(acc.pageMap[page.parent_id]);
            } else {
                parentInMap.children.push(pageWithChildren);
                if (page?.parent?.page_type_id !== 1 && !acc.roots.includes(parentInMap)) acc.roots.push(parentInMap);
            }
        } else acc.roots.push(pageWithChildren);
        return acc;
    }, { pageMap: Object.create(null), roots: [] });

    const formatPageSlug = slug => {
        if (!slug) return '';
        return slug        
            .replace(/[^a-zA-Z0-9_]/g, ' ').trim()
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .replace(/\s+/g, '-').toLowerCase().trim()
            .replace(slashRegex, '').split("/").filter(Boolean).join("/");
    };

    const getPath = (path, isRoot, hasChildren) => {
        const optionalParams = ':id?/:slug?/:extra?';
        return path ? 
            `${isRoot ? '/' : ''}${path}${!hasChildren ? `/${optionalParams}` : ''}` : 
            ``;
    };

    const buildRoute = (node, isRoot = false) => {
        const path = formatPageSlug(node.slug);
        const hasChildren = node.children?.length > 0;
        const routePath = getPath(path, isRoot, hasChildren);
        
        const childrenRoutes = hasChildren ? node.children.map(child => buildRoute(child, false)) : [];

        let element = <Outlet />;
        const pageTypeId = +node?.page_type_id;

        switch(pageTypeId) {
            case 4: // blog
                break;
            case 14: { // shop
                const registerId = +node.content?.properties?.register_id || defaultRegister;
                element = (
                    <PosProvider registerId={registerId} shopId={node.id} selectLoggedInUser={true}>
                        {element}
                    </PosProvider>
                );
                break;
            }
            default: {
                element = (
                    <Site
                        moduleId={999}
                        pageId={node.id}
                        websiteId={node.website_id}
                        templateId={node.template_id}
                    />
                );
                if (childrenRoutes.length) element = <WithOutlet element={element} />;
                break;
            }
        }

        const websiteName = node.website?.name ? `${node.website.name} | ` : '';
        const title = `${node.title ? `${node.title} - ` : ''}${websiteName}${companyName}`;

        const route = {
            element,
            handle: { title },
            ...(childrenRoutes.length && { children: childrenRoutes }),
        };
        if (routePath) route.path = routePath;
        else route.index = true;

        return route;
    };

    const routes = [
        ...(indexPage ? [{
            path: '/',
            element: <Site 
                moduleId={999}
                pageId={indexPage?.id}
                websiteId={indexPage?.website_id}
                templateId={indexPage?.template_id}
            />
        }] : []),
        ...roots.map(root => buildRoute(root, true)),
        {
            path: '*',
            element: <Fzf />,
            handle: { title: companyName }
        }
    ];

    return routes;
};

const recursiveRoute = route => {
    return (
        <Route key={`route-${route.path}`} path={route.path} element={route.element}>
            {route.children && route.children.map(child => recursiveRoute(child))}
        </Route>
    );
}

export const Routes = ({ssr = true, indexPage, company, url, pages = [], routes = []}) => {
    const [loadingComplete, setLoadingComplete] = useState(false);

    const handleLoadComplete = useCallback(() => {
        setLoadingComplete(true);
    }, []);

    if (!routes?.length) routes = buildRoutes(pages, company, indexPage);

    const routerParams = {
        location: url, // Use the URL passed from the server
        basename: import.meta.env.VITE_BASE_PATH, 
        future: {
            v7_startTransition: true, 
            v7_relativeSplatPath:true, 
            v7_partialHydration: true, 
            v7_skipActionErrorRevalidation: true,
            v7_fetcherPersist: true,
            v7_normalizeFormMethod: true,
        },
    }

    if (ssr) {
        return (
            <StaticRouter {...routerParams}>
                {loadingComplete ?
                    <RrdRoutes>
                        {routes?.map(route => recursiveRoute(route))}
                    </RrdRoutes>
                : <Loader onLoadComplete={handleLoadComplete} noLogin />}
            </StaticRouter>
        );
    } else {
        if (loadingComplete){
            return (
                <RouterProvider 
                    router={createBrowserRouter(routes, {future: routerParams.future})} 
                    future={routerParams.future} 
                />
            );
        } else {
            return (
                <BrowserRouter {...routerParams}>
                    <Loader onLoadComplete={handleLoadComplete} noLogin />
                </BrowserRouter>
            );
        }
    }
};
