import React, { createContext, useContext, useState, useCallback } from 'react';

const FlatFormContext = createContext();

export const FlatFormProvider = ({ children, values, onSubmit = () => {} }) => {
    console.log('FlatFormProvider - Initial Values:', values);
    const [formData, setFormData] = useState(values || {});

    const resetForm = useCallback(() => setFormData({}), []);

    // Save data directly to the form state with key-value pairs
    const saveData = useCallback((key, value) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    }, []);

    // Submit the form data directly
    const submitForm = useCallback(() => {
        onSubmit(formData, resetForm);
    }, [formData, onSubmit, resetForm]);

    return (
        <FlatFormContext.Provider value={{ formData, saveData, submitForm, resetForm }}>
            {children}
        </FlatFormContext.Provider>
    );
};

export const useFlatFormContext = () => useContext(FlatFormContext);
