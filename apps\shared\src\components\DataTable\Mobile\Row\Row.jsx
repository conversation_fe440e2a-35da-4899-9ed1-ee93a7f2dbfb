import React, { useCallback } from 'react';
import { ListItem, ListItemText, Typography, Stack } from '@mui/material';

export const Row = ({ onExpand, onDelete, onRowSelectionModelChange, row, columns, ...props }) => {
    
    const handleClick = useCallback(e => {
        if (onRowSelectionModelChange && row.id) {
            onRowSelectionModelChange([row.id]);
        }
        if (onExpand) onExpand();
    }, [onRowSelectionModelChange, onExpand, row]);

    const getRowValue = useCallback(column => {
        if (column.valueFormatter){
            return column.valueFormatter(row?.[column.field] !== undefined ? row[column.field] : row)
        } else if (column.valueGetter){
            return column.valueGetter(row?.[column.field], row);
        }
        return row?.[column.field];
    }, [row]);

    return (
        <ListItem sx={{ my:2 }} disableGutters onClick={handleClick}>
            <ListItemText component="div" sx={{position:'relative'}}>
                {columns.map((column, j) => (
                    <Stack direction="column" spacing={0} useFlexGap flexWrap="wrap" key={`datagrid-mobile-field-${j}`} sx={{my: 1}}>
                        <Typography variant="subtitle3">{column.headerName}</Typography>
                        {column?.renderCell 
                            ? <div>{column.renderCell({row, column, value: row?.[column.field] || row?.[column.id]})}</div> 
                            :
                            <Typography variant="body1" component="div" sx={{mb: j < columns.length - 1 ? 1 : 0}}>
                                {getRowValue(column)}
                            </Typography>
                        }
                    </Stack>
                ))}
            </ListItemText>
        </ListItem>
    );
}