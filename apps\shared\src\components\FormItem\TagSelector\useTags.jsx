import { useMemo, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useApi } from "../../../api";
import useForm from "../../useForm";

const fields = [{name: 'name', type: 'text', label: 'tag:name', required: true, value: '', component: "TextField", margin: "normal"}];

export const useTags = ({ 
    name, // the name of the field
    onChange, // callback function to handle the selection of categories
    extraFields // extra fields to include in the api requests
 }) => {
    const { t } = useTranslation();

    const apiParams = useMemo(() => [
        {enableCache: true, params: {endpoint: "/tag", method: "POST", data: {...extraFields}}},
        {params: {endpoint: "/tag/add", method: "POST", data: {...extraFields}}},
    ], [extraFields]);

    const {fetchData: fetchTags, data, loading: tagsLoading, ErrorBar: TagsErrorBar} = useApi(apiParams[0]);
    const {fetchData: addTag, loading: addTagLoading, ErrorBar: AddTagErrorBar} = useApi(apiParams[1]);

    const [addSuccess, setAddSuccess] = useState(false);
    const [formModalOpen, setFormModalOpen] = useState(false);

    const handleDialogToggle = useCallback((value = false) => e => {
        setFormModalOpen(value);
    }, []);

    const handleSelection = useCallback(values => {
        if (onChange) {
            const e = {target: {name, value: values.map(a => a?.id || a)}};
            onChange(e);
        }
    }, [onChange, name]);

    const handleFormSubmit = useCallback(async values => {
        try {
            const res = await addTag(values);
            if (res?.errors) {
                console.error(res.errors);
            } else if (res?.data) {
                await fetchTags({___: new Date().getTime()});
                setAddSuccess(true);
                //setFormModalOpen(false);        
            }
        } catch (error) {
            console.error(error);
        }
    }, [addTag, fetchTags]);

    const handleFormValidate = useCallback(fields => {
        let _error = {};
        //const _fields = fields.filter(a => a.required && !a.value);
        fields.forEach(field => {
            if (field.required && !field.value) _error[field.name] = t("error:required");
            // checks that the tag name does not already exist
            if (data && data.some(a => a?.[field.name] === field.value)) _error[field.name] = t("error:exists");
        });

        return _error;
    }, [data]);

    const {values, errors, handleChange, handleSubmit, resetForm } = useForm(fields, { validate: handleFormValidate, onSubmit: handleFormSubmit, onAfterSubmit: () => {
        resetForm();
    }});

    return {
        data,
        loading: tagsLoading || addTagLoading,
        fetchTags,
        addSuccess,
        setAddSuccess,
        errorBars: [TagsErrorBar, AddTagErrorBar],
        formFields: values,
        formErrors: errors,
        handleChange,
        handleSubmit,
        handleSelection,
        handleDialogToggle,
        formModalOpen,
    }
}