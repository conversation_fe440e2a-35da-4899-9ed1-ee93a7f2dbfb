import React, { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Box, Stack } from '@mui/material';
import { Masonry } from '@mui/lab';
import { DataTableNoRows, DataTableSearchInput} from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import GroupCards from '../../../../components/GroupCards';
import styles from './Groups.module.scss';

const menuItems = [
    {id: 'edit', slug: 'general:edit', icon: null},
    {id: 'add', slug: 'group:addMember', icon: null},
    {id: 'invite', slug: 'group:inviteMember', icon: null},
    {id: 'schedule', slug: 'calendar:schedule', icon: null},
    {id: 'leave', slug: 'group:leave', icon: null},
];

export const Groups = ({ userData, loading, groupTypeId = null, ...props }) => {
    const { t, isMobile } = useOutletContext();

    const [rows, setRows] = useState([]);
    const [searchText, setSearchText] = useState('');    

    const apiParams = useMemo(() => [
        {params: {endpoint: '/group/list', method: 'POST', data: {
            page_no: 1,
            max_records: 1000,
            sort_col: 'id',
            sort_direction: 'desc',
            filters:{
                search_words: searchText,
                user_id: userData.id,
                group_type_id: groupTypeId || undefined,
            },
        }}},
    ], [searchText, userData.id, groupTypeId]);

    const { fetchData, ErrorBar, loading:loadingGroups, LoadingBar } = useApi(apiParams[0]);

    const handleMenuSelection = item => {
        switch (item.id){
            case 'edit':
                break;
            case 'leave':
                break;
            case 'add':
                break;
            case 'invite':
                break;
            default:
                break;
        }
    }

    const handleSearch = value => {
        if (value === '') setSearchText('');
        else if (value.length > 2) setSearchText(value);
    }

    useEffect(() => {
        const _loadData= async () => {
            const result = await fetchData({
                filters:{
                    search_words: searchText,
                    user_id: userData.id,
                    group_type_id: groupTypeId || undefined,
                },        
            });
            if (result?.data){
                const _rows = [];
                let _data = result.data?.groups;
                if (!groupTypeId) _data = _data.filter(group => group.group_type_id !== 4); // remove the family group if not specified
                if (_data){
                    _data.map(group => {
                        _rows.push({
                            id: group.id,
                            name: group.name,
                            group_type_name: group.group_type_name,
                            group_status_name: group.group_status_name,
                            image_url: group.image_url,
                            metadata: group,
                        });
                    });
                }
                setRows(_rows);
            }
        }
        _loadData();
        
    }, [searchText, userData.id, groupTypeId, fetchData]);

    return (
        <Container disableGutters>
            <Box sx={{pb:2, width: '100%'}}>
                <DataTableSearchInput onSearchChange={value => handleSearch(value)} variant="standard" />
            </Box>
            <LoadingBar />
            <ErrorBar />

            {!loading && !loadingGroups && (!rows || rows.length === 0) &&
                <Stack>
                    <DataTableNoRows title={t("group:empty")} />
                </Stack>
            }
            {!loading && !loadingGroups && rows && rows.length > 0 &&
                <GroupCards 
                    component={Masonry}
                    columns={isMobile ? 1 : 2} 
                    spacing={2}
                    groups={rows} 
                    menuItems={menuItems} 
                    onMenuSelection={handleMenuSelection} 
                    showMembersButton={false}
                    showScheduleButton={false}
                    showLogo={groupTypeId === 4 ? false : true}
                    showMembers
                    slotProps={{avatar: {size: 'md'}}}
                />
            }
        </Container>
    );
}