import { useOutletContext } from 'react-router-dom';
import { Button, Stack } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { DataTable, Modal } from '@siteboss-frontend/shared/components';

import BasicInfo from '../BasicInfo';
import { useRoutingTable } from './useRoutingTable';

export const RoutingTable = ({ value, errors, onChange, loading, merchantId }) => {
    const { t, isMobile } = useOutletContext();

    const {
        data,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: apiLoading,
        errorBars,
        fieldDefinitions,
        selected,
        setSelected,
        handleRowSelection,
        handleResetForm,
    } = useRoutingTable({ merchantId, onMainFormChange: onChange });
    
    return (
        <Stack spacing={2} direction="column" useFlexGap>
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                    variant="contained"
                    color="secondary"
                    startIcon={<AddIcon />}
                    loading={loading || apiLoading}
                    onClick={e => {
                        handleResetForm();
                        handleToggleModal(true)(e);
                    }}
                >
                    {t('shipping:addRoutingRule')}
                </Button>
            </div>
            
            <DataTable
                rows={data || value || []}
                columns={columns}
                page={page}
                pageSize={pageSize}
                totalPages={totalPages}
                setPage={setPage}
                setPageSize={setPageSize}
                onExpand={handleEditItem}
                onDelete={handleDeleteItem}
                loading={loading || apiLoading}
                disableSearch
                hideFooterSelectedRowCount
                onRowSelectionModelChange={model => handleRowSelection(model)}
                rowSelectionModel={selected.map(s => s.id)}
                setSelected={setSelected}
            />

            <Modal
                open={modalOpen}
                onClose={handleToggleModal(false)}
                title={formData?.id ? t('shipping:editRoutingRule') : t('shipping:addRoutingRule')}
                maxWidth="md"
                fullScreen={isMobile}
            >
                {errorBars.map((ErrorBar, i) => ErrorBar && <ErrorBar key={i} />)}

                <BasicInfo
                    fields={fieldDefinitions}
                    values={formData}
                    errors={errors}
                    onChange={handleChange}
                    loading={loading || apiLoading}
                />
                <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mt: 2 }}>
                    <Button
                        variant="outlined"
                        onClick={handleToggleModal(false)}
                        loading={loading || apiLoading}
                    >
                        {t('general:cancel')}
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSaveItem}
                        loading={loading || apiLoading}
                    >
                        {formData?.id ? t('general:update') : t('general:add')}
                    </Button>
                </Stack>
            </Modal>
        </Stack>
    );
};
