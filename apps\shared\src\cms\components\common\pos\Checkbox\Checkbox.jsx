import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, Divider, FormControlLabel, FormGroup, Checkbox as MuiCheckbox, FormControl, FormHelperText, IconButton, useMediaQuery } from '@mui/material';
import { ChevronRightOutlined as MoreIcon } from '@mui/icons-material';

import Modal from '../../../../../components/Modal';

/*
Checkboxes to be used throughout the POS. It can handle replacing modal content, or loading extra content in a collapsible section via slots and slotProps.
*/
export const Checkbox = ({ item, checked, disabled, onChange, slotProps, children, ...props }) => (
    <FormControlLabel 
        value={item.id} 
        control={
            <MuiCheckbox 
                color="secondary" 
                size="small"
                checked={checked} 
                onChange={onChange}
                {...slotProps} 
            />
        }
        slotProps={{typography: {component: 'div', sx: {width: '100%'}}}}
        disabled={disabled}
        label={children}
    />
);

export const CheckboxWrapper = ({ items, selected, disabled, onChange, slotProps, slots, children, ...props }) => {
    if (!Array.isArray(selected)) selected = [selected];
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    
    const [openModalId, setOpenModalId] = useState(null);

    const handleExpand = useCallback(item => e => {
        // replaces the modal content with whatever component is passed in the extraInfo slot
        if (slots?.extraInfo && !slotProps?.fullPage && slotProps?.extraInfo?.show && slotProps?.changeView && item) {
            slotProps.changeView(slots?.extraInfo({item}));
        }
    }, [slotProps, slots]);

    const handleChange = useCallback(item => e => {
        if (onChange) onChange(item);
        if (e.currentTarget.checked) {
            if (slotProps?.fullPage) setOpenModalId(item.id);
            else handleExpand(item)(e);
        }
    }, [onChange, handleExpand, slotProps?.fullPage]);

    return (
        <FormControl sx={{p: 2, width: '100%'}} component="fieldset" variant="standard">
            <FormGroup>
                {items.map((item, i) => { 
                    let checked = Boolean(selected?.find(a => +a?.id === +item.id)), _disabled = disabled, helperText;
                    if (slots?.validate && !disabled) {
                        let valid = slots.validate(item);
                        if (typeof valid === 'string') {
                            helperText = valid;
                            valid = false;
                        }
                        valid = Boolean(valid);
                        checked = valid ? checked : false;
                        _disabled = !valid;
                    }
                    return (
                        <React.Fragment key={item.id}>
                            <Checkbox item={item} checked={checked} disabled={_disabled} onChange={handleChange(item)} slotProps={slotProps?.checkbox}>
                                <Stack spacing={2} flexDirection="row" useFlexGap flexWrap="wrap" justifyContent="space-between" alignItems="center" sx={{ml: 1}} {...slotProps?.stack}>
                                    {children?.[i]}
                                    {!_disabled && slots?.extraInfo && slotProps?.extraInfo?.show && /*!slotProps?.fullPage &&*/
                                        <>
                                            {checked ?
                                                <IconButton size="small" onClick={handleExpand(item)}>
                                                    <MoreIcon fontSize='small'/>
                                                </IconButton>
                                            : <MoreIcon fontSize='small'/> }
                                        </>
                                    }
                                    {helperText && <FormHelperText>{helperText}</FormHelperText>}
                                </Stack>
                            </Checkbox>
                            {slots?.extraInfo && slotProps?.fullPage && slotProps?.extraInfo?.show && 
                                <Modal 
                                    open={openModalId === item.id} 
                                    onClose={() => setOpenModalId(null)}
                                    maxWidth={"sm"}
                                    fullScreen={isMobile}
                                >
                                    {slots?.extraInfo({item, onClose: () => setOpenModalId(null)})}
                                </Modal>
                            }
                            <Divider />
                        </React.Fragment>
                    );
                })}
            </FormGroup>
        </FormControl>
    );
};
