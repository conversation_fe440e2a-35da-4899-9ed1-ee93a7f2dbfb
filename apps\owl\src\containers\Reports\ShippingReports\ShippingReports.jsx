import { useState } from 'react';
import {
  Grid2 as <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Typo<PERSON>,
  <PERSON>,
  Button
} from '@mui/material';
import {
  LocalShipping as ShippingIcon,
  Speed as SpeedIcon,
  AttachMoney as CostIcon,
  LocationOn as LocationIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendIcon
} from '@mui/icons-material';
import { LineChart, PieChart, DataTable, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';
import { ReportCard, ReportFilters } from '../components';

export const ShippingReports = () => {
  const [filters, setFilters] = useState({});

  // Mock data for charts
  const carrierPerformanceData = {
    data: [
      {
        id: 'FedEx',
        data: [
          { x: 'Mon', y: 95 },
          { x: 'Tue', y: 97 },
          { x: 'Wed', y: 94 },
          { x: 'Thu', y: 96 },
          { x: 'Fri', y: 98 },
          { x: 'Sat', y: 92 },
          { x: 'Sun', y: 93 }
        ]
      },
      {
        id: 'UPS',
        data: [
          { x: 'Mon', y: 92 },
          { x: 'Tue', y: 94 },
          { x: 'Wed', y: 91 },
          { x: 'Thu', y: 93 },
          { x: 'Fri', y: 95 },
          { x: 'Sat', y: 89 },
          { x: 'Sun', y: 90 }
        ]
      }
    ]
  };

  const shippingCostData = {
    data: [
      { id: 'FedEx', value: 45, color: '#4CAF50' },
      { id: 'UPS', value: 35, color: '#2196F3' },
      { id: 'USPS', value: 15, color: '#FF9800' },
      { id: 'DHL', value: 5, color: '#9C27B0' }
    ]
  };

  const shipmentsTableData = {
    columns: [
      { field: 'id', headerName: 'Tracking ID', width: 150 },
      { field: 'carrier', headerName: 'Carrier', width: 120 },
      { field: 'destination', headerName: 'Destination', width: 200 },
      { field: 'status', headerName: 'Status', width: 120 },
      { field: 'cost', headerName: 'Cost', width: 100 },
      { field: 'eta', headerName: 'ETA', width: 150 }
    ],
    rows: [
      { id: 'FDX123456789', carrier: 'FedEx', destination: 'New York, NY', status: 'In Transit', cost: '$12.50', eta: '2024-01-16' },
      { id: 'UPS987654321', carrier: 'UPS', destination: 'Los Angeles, CA', status: 'Delivered', cost: '$15.75', eta: '2024-01-15' },
      { id: 'USPS456789123', carrier: 'USPS', destination: 'Chicago, IL', status: 'Out for Delivery', cost: '$8.25', eta: '2024-01-15' },
      { id: 'DHL789123456', carrier: 'DHL', destination: 'Miami, FL', status: 'Processing', cost: '$22.00', eta: '2024-01-17' },
      { id: 'FDX555666777', carrier: 'FedEx', destination: 'Seattle, WA', status: 'In Transit', cost: '$14.25', eta: '2024-01-16' }
    ]
  };

  const reports = [
    {
      title: 'Carrier Performance',
      description: 'Compare delivery times and success rates by carrier',
      icon: <SpeedIcon />,
      color: '#4CAF50',
      metrics: [
        { label: 'On-Time Rate', value: '94.2%' },
        { label: 'Avg Delivery', value: '2.1 days' },
        { label: 'Best Carrier', value: 'FedEx' }
      ],
      tags: ['Performance', 'SLA', 'Comparison'],
      lastUpdated: '1 hour ago'
    },
    {
      title: 'Shipping Cost Analysis',
      description: 'Track shipping expenses and cost per carrier',
      icon: <CostIcon />,
      color: '#2196F3',
      metrics: [
        { label: 'Total Cost', value: '$12,450' },
        { label: 'Avg Cost', value: '$13.25' },
        { label: 'Savings', value: '$2,100' }
      ],
      tags: ['Costs', 'Budget', 'Optimization'],
      lastUpdated: '2 hours ago'
    },
    {
      title: 'Delivery Zone Analysis',
      description: 'Performance metrics by geographic regions',
      icon: <LocationIcon />,
      color: '#FF9800',
      metrics: [
        { label: 'Zones Covered', value: '48' },
        { label: 'Fastest Zone', value: 'Zone 1' },
        { label: 'Avg Distance', value: '245 mi' }
      ],
      tags: ['Geography', 'Zones', 'Distance'],
      lastUpdated: '3 hours ago'
    },
    {
      title: 'Shipping Trends',
      description: 'Historical shipping volume and pattern analysis',
      icon: <TrendIcon />,
      color: '#9C27B0',
      metrics: [
        { label: 'Volume Growth', value: '+15.3%' },
        { label: 'Peak Day', value: 'Thursday' },
        { label: 'Seasonal Trend', value: 'Up' }
      ],
      tags: ['Trends', 'Volume', 'Patterns'],
      lastUpdated: '1 hour ago'
    }
  ];

  const customFilters = [
    {
      key: 'carrier',
      label: 'Carrier',
      type: 'select',
      options: [
        { value: 'fedex', label: 'FedEx' },
        { value: 'ups', label: 'UPS' },
        { value: 'usps', label: 'USPS' },
        { value: 'dhl', label: 'DHL' }
      ]
    },
    {
      key: 'zone',
      label: 'Shipping Zone',
      type: 'select',
      options: [
        { value: 'zone1', label: 'Zone 1 (Local)' },
        { value: 'zone2', label: 'Zone 2 (Regional)' },
        { value: 'zone3', label: 'Zone 3 (National)' },
        { value: 'zone4', label: 'Zone 4 (International)' }
      ]
    }
  ];

  const handleViewReport = (reportTitle) => {
    console.log('Viewing report:', reportTitle);
  };

  const handleDownloadReport = (reportTitle) => {
    console.log('Downloading report:', reportTitle);
  };

  const handlePrintReport = (reportTitle) => {
    console.log('Printing report:', reportTitle);
  };

  return (
    <Box>
      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={() => setFilters({})}
        dateRange={true}
        statusFilter={true}
        locationFilter={true}
        customFilters={customFilters}
      />

      {/* Key Metrics */}
      <MetricCardGroup sx={{ mb: 4 }}>
        <MetricCard
          title="Active Shipments"
          value="89"
          change="+7"
          changeType="positive"
          icon={<ShippingIcon />}
          color="primary"
        />
        <MetricCard
          title="On-Time Delivery"
          value="94.2%"
          change="+2.1%"
          changeType="positive"
          icon={<SpeedIcon />}
          color="success"
        />
        <MetricCard
          title="Avg Shipping Cost"
          value="$13.25"
          change="-$1.50"
          changeType="positive"
          icon={<CostIcon />}
          color="warning"
        />
        <MetricCard
          title="Delivery Zones"
          value="48"
          change="+3"
          changeType="positive"
          icon={<LocationIcon />}
          color="info"
        />
      </MetricCardGroup>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Carrier Performance Comparison
              </Typography>
              <Box sx={{ height: 300 }}>
                <LineChart data={carrierPerformanceData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Shipping Cost Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <PieChart data={shippingCostData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Report Cards */}
      <Typography variant="h5" sx={{ mb: 3 }}>
        Available Reports
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reports.map((report, index) => (
          <Grid size={{ xs: 12, md: 6, lg: 3 }} key={index}>
            <ReportCard
              {...report}
              onView={() => handleViewReport(report.title)}
              onDownload={() => handleDownloadReport(report.title)}
              onPrint={() => handlePrintReport(report.title)}
            />
          </Grid>
        ))}
      </Grid>

      {/* Recent Shipments Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Recent Shipments
            </Typography>
            <Button variant="outlined" size="small">
              View All Shipments
            </Button>
          </Box>
          <DataTable
            columns={shipmentsTableData.columns}
            rows={shipmentsTableData.rows}
            pageSize={5}
            disableSelectionOnClick
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default ShippingReports;
