import { useApi } from '@siteboss-frontend/shared';

export const services = ({type, params = {}, fetchCounter, ...props}) => {
	let endpointParams = {};
	let shouldValidateParams = false;

	switch (type){
		case "get":
			endpointParams = {
				endpoint: "/service" + (params?.id ? "/"+params.id : ""),
				method: "GET"
			};
			break;
		case "getAll":
			endpointParams = {
				endpoint: "/service",
				method: "POST",
				data: {
					page_no: 1,
					max_records: 50,
					sort_col: 'name',
					sort_direction: 'asc',
					...params
				},
			};
			break;
		case "create":
			endpointParams = {
				endpoint: "/service/add",
				method: "POST",
				data: {...params},
			}
			break;
		case "edit":
			endpointParams = {
				endpoint: "/service/edit",
				method: "PUT",
				data: {...params},
			}
			break;
		case "delete":
			endpointParams = {
				endpoint: "/service/delete",
				method: "DELETE",
				data: {...params},
			}
			break;
		case "createBooking":
			endpointParams = {
				endpoint: "/service/add_booking",
				method: "POST",
				data: {...params},
			}
			break;
		case "cancelBooking":
			endpointParams = {
				endpoint: "/service/cancel_booking",
				method: "POST",
				data: {...params},
			}
			break;
		case "wizard":
			endpointParams = {
				endpoint: "/service/wizard",
				method: "POST",
				data: {...params},
			}
			break;
		default:
			break;
	}

	return useApi({
		params: endpointParams,
		shouldValidateParams,
		fetchCounter,
		fetchOnMount: true
	});
}
