import { tenantConfig } from './tenantConfig';

const localTenantConfig = tenantConfig();

const keycloakConfig = {
  url: localTenantConfig?.authProvider?.server_url || import.meta.env.VITE_KEYCLOAK_URL || 'http://kc.dev.sbo.li/auth',
  realm: localTenantConfig?.authProvider?.realm || import.meta.env.VITE_KEYCLOAK_REALM || 'spk',
  clientId: localTenantConfig?.authProvider?.client_id || import.meta.env.VITE_KEYCLOAK_CLIENT_ID || 'om1',
};

// Construct redirect URI to match your app's actual URL structure
const basePath = import.meta.env.VITE_BASE_PATH || '';
const redirectUri = `${window.location.origin}${basePath}/`;

const keycloakInitOptions = {
  onLoad: 'check-sso',
  silentCheckSsoRedirectUri: redirectUri + 'silent-check-sso.html',
  checkLoginIframe: false,
  checkLoginIframeInterval: 0,
  pkceMethod: 'S256',
  enableLogging: import.meta.env.DEV,
  silentCheckSsoFallback: false,
};

const keycloakLogoutOptions = {
  redirectUri,
};

export {
  keycloakConfig,
  keycloakInitOptions,
  keycloakLogoutOptions,
};