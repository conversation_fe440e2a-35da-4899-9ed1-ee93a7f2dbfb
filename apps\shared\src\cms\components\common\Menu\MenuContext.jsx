import React, { createContext } from 'react';
import { useMenu } from './useMenu';

export const MenuContext = createContext();
export const MenuProvider = ({ 
    items = [], 
    type = "horizontal", 
    fetchPages = false, 
    showCart = false,
    cartType = "icon",
    showProfile = false,
    profileType = "icon",
    shopId = null,
    isBuilder, 
    children, 
    ...props 
}) => {
    const MenuHook = useMenu({items, showCart, cartType, showProfile, profileType, fetchPages, shopId, isBuilder});

    return (
        <MenuContext.Provider value={{...MenuHook, type, isBuilder}}>
            {children}
        </MenuContext.Provider>
    );
};