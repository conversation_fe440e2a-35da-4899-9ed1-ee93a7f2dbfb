import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';

/**
 * MetricCard component for displaying key metrics with colorful backgrounds and icons
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Title of the metric
 * @param {string|number} props.value - Value of the metric
 * @param {React.ReactNode} props.icon - Icon to display
 * @param {string} props.color - Color theme of the card ('blue', 'green', 'orange', 'purple', or custom gradient)
 * @param {string} props.trend - Trend text to display (e.g., "+15% from last month")
 * @param {Object} props.sx - Additional styles to apply to the card
 */
const MetricCard = ({ title, value, icon, color = 'blue', trend, sx = {}, ...props }) => {
  // Predefined gradient backgrounds
  const gradients = {
    blue: 'linear-gradient(135deg, #2196F3 0%, #03A9F4 100%)',
    green: 'linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)',
    orange: 'linear-gradient(135deg, #FF9800 0%, #FFC107 100%)',
    purple: 'linear-gradient(135deg, #9C27B0 0%, #E040FB 100%)',
    red: 'linear-gradient(135deg, #F44336 0%, #FF5252 100%)',
    teal: 'linear-gradient(135deg, #009688 0%, #4DB6AC 100%)',
    indigo: 'linear-gradient(135deg, #3F51B5 0%, #5C6BC0 100%)',
  };

  // Shadow colors based on the card color
  const shadowColors = {
    blue: 'rgba(33, 150, 243, 0.14), 0 7px 10px -5px rgba(33, 150, 243, 0.4)',
    green: 'rgba(76, 175, 80, 0.14), 0 7px 10px -5px rgba(76, 175, 80, 0.4)',
    orange: 'rgba(255, 152, 0, 0.14), 0 7px 10px -5px rgba(255, 152, 0, 0.4)',
    purple: 'rgba(156, 39, 176, 0.14), 0 7px 10px -5px rgba(156, 39, 176, 0.4)',
    red: 'rgba(244, 67, 54, 0.14), 0 7px 10px -5px rgba(244, 67, 54, 0.4)',
    teal: 'rgba(0, 150, 136, 0.14), 0 7px 10px -5px rgba(0, 150, 136, 0.4)',
    indigo: 'rgba(63, 81, 181, 0.14), 0 7px 10px -5px rgba(63, 81, 181, 0.4)',
  };

  // Determine the background gradient and shadow
  const background = gradients[color] || color; // Use predefined gradient or custom value
  const boxShadow = `0 4px 20px 0 ${shadowColors[color] || shadowColors.blue}`;

  return (
    <Card
      sx={{
        height: '100%',
        background,
        color: 'white',
        overflow: 'hidden',
        position: 'relative',
        ...sx
      }}
      {...props}
    >
      <CardContent sx={{ position: 'relative', zIndex: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: 'medium' }}>
            {title}
          </Typography>
          {icon && (
            <Box sx={{
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '50%',
              p: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {icon}
            </Box>
          )}
        </Box>
        <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
          {value}
        </Typography>
        {trend && (
          <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
            {trend}
          </Typography>
        )}
      </CardContent>
      <Box sx={{
        position: 'absolute',
        top: -20,
        right: -20,
        width: 150,
        height: 150,
        borderRadius: '50%',
        bgcolor: 'rgba(255, 255, 255, 0.1)',
        zIndex: 0
      }} />
    </Card>
  );
};

export default MetricCard;
