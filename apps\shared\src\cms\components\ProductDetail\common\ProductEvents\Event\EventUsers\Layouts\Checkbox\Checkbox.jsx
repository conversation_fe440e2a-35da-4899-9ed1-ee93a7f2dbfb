import React from 'react';
import { differenceInYears } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { Stack } from '@mui/material';

import UserAvatar from '../../../../../../../../../components/ToolbarButtons/UserAvatar';
import { Caption, CheckboxWrapper } from '../../../../../../../common/pos';

export const Checkbox = ({ items, selected, disabled, onSelect, slotProps, slots, ...props }) => {
    const { t } = useTranslation();

    return (
        <CheckboxWrapper items={items} selected={selected} disabled={disabled} onChange={onSelect} slotProps={slotProps} slots={slots}>
            {items.map(item => (
                <React.Fragment key={item.id}>
                    <UserAvatar size='md' variant="circular" hideStatus hideOptions userData={item} src={item.profile_img_path} label={item.first_name} component="div" />
                    <Stack direction="column" spacing={0} sx={{flexGrow: 1, py: 1}}>
                        {item?.first_name && <Caption variant="body1" component="div" text={item.first_name} bold />}
                        {item?.role_name && <Caption variant="subtitle3" component="div" text={t(`user:relationships.${item.role_name.toLowerCase()}`, item.role_name)}/>}
                    </Stack>
                    {item?.dob && <Caption variant="body2" component="div" text={
                        `${differenceInYears(new Date(), new Date(item.dob))} ${t("calendar:yearsOld")}`
                    } />}
                </React.Fragment>
            ))}
        </CheckboxWrapper>
    );
};