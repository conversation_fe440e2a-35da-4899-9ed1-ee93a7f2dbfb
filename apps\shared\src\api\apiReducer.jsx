export const apiReducer = (state, action) => {
    switch (action.type) {
        case 'FETCH_INIT':
            return { ...state, loading: true, errors: null };
        case 'FETCH_SUCCESS':
            return {
                ...state,
                loading: false,
                data: action.payload.data,
                httpCode: action.payload.httpCode,
            };
        case 'FETCH_FAILURE':
            return {
                ...state,
                loading: false,
                data: null,
                errors: action.payload.errors,
                httpCode: action.payload.httpCode,
            };
        case 'SET_STATE':
            return { ...state, ...action.payload };
        default:
            return state;
    }
};
