import React, { useState, useRef, useCallback, Children, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Collapse, Zoom, Slide, Divider, Stack, useTheme, Typography, FormHelperText } from '@mui/material';
import { KeyboardArrowDown as ExpandIcon } from '@mui/icons-material';

export const WithLinkReveal = ({
    label, 
    onToggle, 
    children,
    slotProps,
    hasErrors = false,
    ...props
}) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const ref = useRef(null);
    const [show, setShow] = useState(hasErrors || false);
    
    const handleToggle = useCallback(e => {
        if (!hasErrors) {
            setShow(!show);
            if (onToggle) onToggle(!show, e);
        }
    }, [show, onToggle, hasErrors]);

    useEffect(() => {
        if (hasErrors) setShow(true);
    }, [hasErrors]);

    if (!Children.toArray(children).length > 0 && !Children.count(children)) return null;

    return (
        <>
            <Stack ref={ref} direction="row" justifyContent="flex-start" alignItems="flex-start" sx={{mt: 1}} {...slotProps?.container}>                        
                {show &&
                    <Slide direction="right" in={show} container={ref.current} {...slotProps?.slide}>
                        <Divider flexItem textAlign="left" sx={{my: 'auto', mr: 0.5, width: 5}} {...slotProps?.divider} />
                    </Slide>
                }
                <Button 
                    variant={show ? "text" : (slotProps?.button?.variant || "text")} 
                    color={slotProps?.button?.color || theme.palette.mode === 'dark' ? "inherit" : "primary"}
                    onClick={handleToggle}
                    sx={{fontWeight: 500, pl: 0, ...slotProps?.button?.sx}}
                >
                    <ExpandIcon fontSize="small" sx={{mr: 0.5, transform: show ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s ease-in-out'}} />
                    {t(label)}                    
                </Button>
                <Zoom in={show} {...slotProps?.zoom}>
                    <Divider flexItem textAlign="left" sx={{flex: 1, my: 'auto', ml: 0.5}} {...slotProps?.divider} />
                </Zoom>
                {hasErrors &&
                    <>
                        <Slide direction="left" in={hasErrors} container={ref.current} {...slotProps?.slide}>
                            <FormHelperText error sx={{my: 'auto', mx: 0.5, pl: 0.5}} {...slotProps?.label}>
                                {t("error:simple")}
                            </FormHelperText>
                        </Slide>
                        <Slide direction="left" in={hasErrors} container={ref.current} {...slotProps?.slide}>
                            <Divider flexItem textAlign="left" sx={{my: 'auto', mx: 0.5, width: 15}} {...slotProps?.divider} />
                        </Slide>
                    </>
                }
            </Stack>
            <Collapse in={show} {...slotProps?.collapse}>
                {(Children.toArray(children).length > 0 || Children.count(children) > 0) ? children : 
                    <Typography variant="subtitle2" sx={{p: 1}} {...slotProps?.typography}>
                        {t('general:noContent')}
                    </Typography>
                }
            </Collapse>
        </>
    );
}