import { But<PERSON> } from '@mui/material';
import NonControlStoryTemplate from '../../stories/Templates/NonControlStoryTemplate.mdx';

/** This is a sample to show that it's using a template created within our own project.  The template leaves out the control portion so it isn't interactive.  This template also only allows for a singular, default story to be displayed, indicating there could be one primary use case for this component (the button here is a place holder, does not follow under that fact). */
export default {
    title: 'Storybook Functionality Sample/Template Usage Example',
    component: Button,
    tags: ['autodocs'],
    parameters:{
        // docs: {
        //     page: NonControlStoryTemplate,
        // }
    },
    argTypes:{
        children:{
            description:"The content of the component",
            control: "text",
            table:{
                defaultValue: {summary: null},
                type: {summary: "node"}
            }
        },color:{
            description: " The color of the component.  It supports both default and custom theme colors, which can be added as shown in the https://mui.com/material-ui/customization/palette/#custom-colors",
            control: "select",
            options:["inherit", "primary", "secondary", "success", "error", "info", "warning", "string"],
            table:{
                defaultValue:{summary: "primary"},
                type: {summary: "string"}
            }
        },variant:{
            description: "The variant to use",
            control: "select",
            options:["text", "outlined", "contained"],
            table:{
                defaultValue: {summary: "text"},
                type: {summary: "string"}
            }
        }
    }
}

export const Default={
    args:{
        children: "Hi",
        variant:"contained"
    }
}