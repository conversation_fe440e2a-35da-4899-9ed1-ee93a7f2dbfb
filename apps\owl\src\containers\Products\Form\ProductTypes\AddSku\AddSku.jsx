import React, { useState, useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { But<PERSON>, Stack, Container } from '@mui/material';
import { AddOutlined as AddIcon } from '@mui/icons-material';
import { DataTable, FormItem, useForm, Modal } from '@siteboss-frontend/shared/components';


// Mock data for SKUs - in a real implementation, this would come from an API
const skuOptions = [
    { id: '1001', name: 'Wine A - Cabernet', product_type_name: 'Wine', size: '750ml' },
    { id: '1002', name: 'Wine B - Merlot', product_type_name: 'Wine', size: '750ml' },
    { id: '1003', name: 'Food A - Cheese', product_type_name: 'Food', size: '8oz' },
    { id: '1004', name: 'Merch A - T-Shirt', product_type_name: 'Merchandise', size: 'L' },
    { id: '1005', name: 'Collateral A - Brochure', product_type_name: 'Collateral', size: 'Standard' }
];

export const AddSku = () => {
    const { t } = useOutletContext();

    const [products, setProducts] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(1000);

    const totalPages = Math.ceil(products.length / pageSize);

    const formFields = useMemo(() => [
        { component: 'Autocomplete', name: 'selectedSku', label: t('product:selectSku'), required: true, options: skuOptions, margin: 'normal', filterSelectedOptions: true },
        { component: 'NumberField', name: 'quantity', label: t('product:quantity'), required: true, margin: 'normal', min: 1, value: 1, showControls: true }
    ], [t, skuOptions]);

    const columns = useMemo(() => [
        { field: 'sku', headerName: t('product:sku'), width: 100 },
        { field: 'name', headerName: t('product:name'), width: 200, flex: 1 },
        { field: 'product_type_name', headerName: t('product:productType'), width: 150 },
        { field: 'size', headerName: t('product:size'), width: 100 },
        { field: 'quantity', headerName: t('product:quantity'), width: 100 }
    ], [t]);

    const {
        values,
        errors,
        handleChange,
        handleSubmit,
        resetForm
    } = useForm(formFields, {
        validate: (formValues) => {
            const validationErrors = {};

            // Validate that a SKU is selected
            const selectedSkuField = formValues.find(field => field.name === 'selectedSku');
            if (!selectedSkuField || !selectedSkuField.value) {
                validationErrors.selectedSku = t('error:required');
            }

            // Custom validation for quantity
            const quantityField = formValues.find(field => field.name === 'quantity');
            if (quantityField && parseInt(quantityField.value) < 1) {
                validationErrors.quantity = t('error:minValue', { min: 1 });
            }

            return validationErrors;
        },
        onSubmit: (formData) => {
            // Get the selected SKU
            const selectedSku = formData.selectedSku;

            if (!selectedSku) {
                return false;
            }

            // Add the selected SKU to the list with the specified quantity
            const productToAdd = {
                id: Date.now().toString(),
                sku: selectedSku.id,
                name: selectedSku.name,
                product_type_name: selectedSku.product_type_name,
                size: selectedSku.size || '',
                quantity: formData.quantity
            };

            setProducts(prev => [...prev, productToAdd]);
            handleCloseDialog();
            return true;
        }
    });

    const handleOpenDialog = useCallback(() => {
        resetForm();
        setOpenDialog(true);
    }, [resetForm]);

    const handleCloseDialog = useCallback(() => {
        setOpenDialog(false);
    }, []);

    const handleDeleteProduct = useCallback((id) => {
        setProducts(prev => prev.filter(product => product.id !== id));
    }, []);

    return (
        <Stack spacing={2} direction="column" useFlexGap>
            <div>
                <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleOpenDialog}
                >
                    {t('product:addSku')}
                </Button>
            </div>
            
            {products.length > 0 &&
                <DataTable
                    rows={products}
                    columns={columns}
                    page={page}
                    pageSize={pageSize}
                    totalPages={totalPages}
                    setPage={setPage}
                    setPageSize={setPageSize}
                    onDelete={handleDeleteProduct}
                    disableSearch
                />
            }

            <Modal
                open={openDialog}
                onClose={handleCloseDialog}
                title={null}
                maxWidth="sm"
                slots={{
                    actions: (
                        <>
                            <Button onClick={handleCloseDialog}>{t('general:cancel')}</Button>
                            <Button
                                onClick={handleSubmit}
                                variant="contained"
                                color="primary"
                                disabled={!values.find(v => v.name === 'selectedSku')?.value}
                            >
                                {t('general:add')}
                            </Button>
                        </>
                    )
                }}
            >
                <Container disableGutters>
                    {formFields.map(field => (
                        <FormItem
                            key={field.name}
                            {...field}
                            onChange={handleChange}
                            errors={errors?.[field.name]}
                            value={values.find(v => v.name === field.name)?.value}
                        />
                    ))}
                </Container>
            </Modal>
        </Stack>
    );
};