import React, { useRef, useState, useEffect, useCallback, useMemo, useId } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { <PERSON>ack, Typo<PERSON>, <PERSON><PERSON>, Button } from '@mui/material';

import { usaStates, createCurrencyFormatter } from '../../../../../utils';

import <PERSON>c<PERSON>ields from './CcFields';
import BasicFields from './BasicFields';

const fields = [
    {
        name: 'first_name',
        label: 'user:firstName',
        required: true,
        component: 'TextField',
        margin: "dense",
        rowId: 1,
    },
    {
        name: 'last_name',
        label: 'user:lastName',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 1,
    },
    {
        name: 'bill_address1',
        label: 'user:address',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 2,
    },
    {
        name: 'bill_address2',
        label: 'user:address2',
        required: false,
        component: 'TextField',
        margin: 'dense',
        rowId: 3,
    },
    {
        name: 'bill_city',
        label: 'user:city',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 4,
    },
    {
        name: 'bill_state',
        label: 'user:state',
        required: true,
        component: 'Select',
        options: usaStates({key: 'label', value: 'id', short: true}),
        margin: 'dense',
        rowId: 5,
    },
    {
        name: 'bill_postalcode',
        label: 'user:zip',
        required: true,
        component: 'TextField',
        margin: 'dense',
        rowId: 5,
    },
    {
        name: 'amount',
        label: 'pos:amount',
        required: true,
        component: 'MoneyField',
        margin: 'dense',
        rowId: 6,
    },
    { name: 'ccnumber', required: true, rowId: 0 },
    { name: 'ccexp', required: true, rowId: 0 },
    { name: 'cvv', required: true, rowId: 0 },
];

export const CreditCard = ({
    paymentMethod, 
    paymentMethodId, 
    amount, 
    loading: parentLoading, 
    removeCashDiscount,
    cashDiscount = 0,
    onPaymentProcess, 
    onPaymentChange, 
    slotProps, 
    ...props
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);   
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const ccRef = useRef();
    const id = useId();

    //const user = useSelector(state => state.user.profile);

    const [token, setToken] = useState(null);
    const [loading, setLoading] = useState(false);
    const [values, setValues] = useState({amount, removeCashDiscount});
    const [errors, setErrors] = useState({});

    const isValid = useMemo(() => {
        let valid = true;
        for (let i = 0; i < fields.length; i++) {
            let _valid = values?.[fields[i].name] && !errors?.[fields[i].name];
            if (["ccnumber", "ccexp", "cvv"].includes(fields[i].name)) _valid = !errors?.[fields[i].name];
            if (fields[i].required && !_valid) {
                valid = false;
                break;
            }
        }
        return valid;
    }, [errors, values, t]);

    const handleSubmit = useCallback(async () => {
        if (!window.CollectJS) return;
        if (onPaymentProcess && isValid) await onPaymentProcess({values, paymentMethod, paymentMethodId})
    }, [values, isValid, onPaymentProcess, paymentMethod, paymentMethodId]);

    const onFieldChange = useCallback(_values => {
        if (_values.token) {
            setToken(_values.token);
            setLoading(false);
        }
        setValues(prev => ({...prev, ..._values, payment_token: _values.token}));
    }, []);

    useEffect(() => {
        if (isValid) {
            if (token){
                if (onPaymentChange) onPaymentChange({id, values, paymentMethod, paymentMethodId});
            } else {
                setLoading(true);
                window.CollectJS.startPaymentRequest();
            }
        }
    }, [isValid, token, values, onPaymentChange, id, paymentMethod, paymentMethodId]);

    if (!window.CollectJS) {
        return <Typography variant="body1" color="error" component="div">{t("creditCard:gatewayNotConfigured")}</Typography>;
    }

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            <BasicFields fields={fields} amount={amount} onFieldChange={onFieldChange} slotProps={slotProps?.basicInfo} />
            <CcFields ref={ccRef} fields={fields} amount={amount} onErrors={setErrors} onTokenCapture={onFieldChange} slotProps={slotProps?.creditCard}/>

            <Button 
                loading={loading || parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl"
                fullWidth
                disabled={parentLoading || !isValid || loading || !token || +values?.amount === 0} 
                onClick={handleSubmit} 
                {...slotProps?.button}
            >
                {t(`pos:${+values?.amount < +amount ? 'processPartialPayment' : 'processPayment'}`)}
            </Button>
            {removeCashDiscount &&
                <Alert variant="outlined" severity="warning">{t("pos:warnings.removeCashDiscount", {amount: currencyFormatter.format(cashDiscount, currency)})}</Alert>
            }
        </Stack>
    );
}