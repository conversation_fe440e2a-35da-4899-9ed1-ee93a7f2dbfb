import Orders from './index';
import List from './List/index';
import Details from './Details/index';
import Statuses from './Statuses/index';

/**Infinite loop warning */

export default {
    title: "Siteboss/Containers/Orders",
    component: Orders,
    // subcomponents: { List, Details, Statuses },
    tags: ['autodocs', 'hidden-story'],
}

export const Playground = {
    args:{}
};

// export const Default = () => <Orders />;