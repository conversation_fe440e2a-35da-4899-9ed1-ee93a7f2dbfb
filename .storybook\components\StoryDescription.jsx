import React from 'react';
import { Box, Typography } from '@mui/material';

/**
 * A component to display story descriptions in a consistent way.
 * This component is only used in Storybook stories.
 */
export const StoryDescription = ({ title, description }) => {
    return (
        <Box
            sx={{
                padding: '5px 8px',
                marginBottom: '10px',
                backgroundColor: 'rgba(102, 45, 145, 0.1)',
                border: '1px solid rgb(102, 45, 145)',
                borderRadius: '4px',
                backdropFilter: 'blur(4px)',
                h4: {
                    margin: '2px'
                }
            }}
        >
            {title && (
                <Typography
                    variant="subtitle1"
                    align="center"
                    sx={{
                        fontWeight: 'bold',
                        color: '#662d91', // Company purple
                        marginBottom: description ? 1 : 0
                    }}
                >
                    {title}
                </Typography>
            )}
            {description && (
                <Typography variant="body2" align="center">
                    {description}
                </Typography>
            )}
        </Box>
    );
};

export default StoryDescription;