import { ExpandableText } from './ExpandableText';

export default{
    title: "Shared/Components/Expandable Text",
    tags:['autodocs'],
    component: ExpandableText,
    argTypes:{
        variant:{
            description: "Will dictate the overall style of the text",
            control: "select",
            options: [
                "body1", "body2",
                "button", 
                "caption",
                "h1", "h2", "h3", "h4", "h5", "h6",
                "inherit",
                "overline",
                "subtitle1", "suntitle2"
            ],
            table:{
                type:{summary: "string"},
                defaultValue:{
                    summary: "body1"
                }
            }
        },
        children:{
            description: "The text to be shown",
            control: "text",
            table:{
                type: {summary: "string" },
                defaultValue:{summary: undefined}
            }
        }
    }
}

export const Playground = {
    args:{
    }
}

export const LongText={
    args:{
        children: "We have to create al ong block of text to see how this component works, I think. I have no idea.  There are no notes about it, but given the code and the name of it, perhaps this is right.  Just keep on typing and we'll find out at some point.  Typing about typing.  Hmm.  What a thing.  And stuff.  And typing."
    }
}