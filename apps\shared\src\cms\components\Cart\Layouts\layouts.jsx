import { ShoppingCartOutlined as <PERSON>t<PERSON>con, FormatListBulletedOutlined as ListIcon, TableRowsOutlined as TableIcon} from '@mui/icons-material';
import { generateParagraph, generateTitle, generateSubTitle } from '../../../../utils';

import Table from './Table';
import List from './List';

export const widgetIcon = CartIcon;
export const layouts = [
    {
        id: 1,
        name: 'List',
        component: List,
        icon: <ListIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
    {
        id: 2,
        name: 'Table',
        component: Table,
        icon: <TableIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
];

export const fakeItems = [{
    id: "1",
    name: generateTitle(),
    memo: generateParagraph(1),
    qty: 1,
    price: 10,
    total: 10,
    metadata: {
        variant_name: generateSubTitle(),
    },
    addons: [{id: "1", name: generateSubTitle(), price: 0}, {id: "2", name: generateSubTitle(), price: 0}],
    productMedia: [{
        path: `https://placehold.co/180?font=source-sans-pro&text=${generateTitle()}`,
    }]
}, {
    id: "2",
    name: generateTitle(),
    qty: 1,
    price: 50,
    total: 50,
    metadata: {
        variant_name: generateSubTitle(),
    },
    productMedia: [{
        path: `https://placehold.co/180?font=source-sans-pro&text=${generateTitle()}`,
    }],
    eventId: "1",
    eventName: generateTitle(),
    forUserIds: [{id: "1", firstName: "John", lastName: "Doe"}, {id: "2", firstName: "Jane", lastName: "Doe"}],

},
{
    id: "3",
    name: generateTitle(),
    qty: 1,
    price: 20,
    total: 20,
    productMedia: [{
        path: `https://placehold.co/180?font=source-sans-pro&text=${generateTitle()}`,
    }],
    giftCardRecipient:{
        full_name: "John Doe",
        email: "<EMAIL>",
        delivery_date: new Date(),
        message: generateParagraph(1),
    }
}];