import React, { useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Stack } from '@mui/material';
import { useApi } from '@siteboss-frontend/shared';
import { Title } from '@siteboss-frontend/shared/components';
import { toCamelCase } from '@siteboss-frontend/shared/utils';

import MembersList from '../../../../../../components/GroupCards/GroupMemberList/MembersList';

export const Group = ({ userId, groupTypeId = 4, onClose, ...props }) => {
    const { t, isMobile } = useOutletContext();

    const apiParams = useMemo(() => [
        {enableCache:true, params: {endpoint: '/group/list', method: 'POST', data: {
            page_no: 1,
            max_records: 1,
            sort_col: 'id',
            sort_direction: 'desc',
            filters:{
                user_id: userId,
                group_type_id: groupTypeId || undefined,
            },
        }}},
    ], [userId, groupTypeId]);

    const { fetchData, data, loading, ErrorBar, LoadingBar } = useApi(apiParams[0]);

    useEffect(() => {
        fetchData().then(res => {
            if (!res?.errors && res?.data?.groups?.length === 0 && onClose) onClose();
        });
    }, [fetchData, onClose]);

    if (!userId) return null;

    return (
        <Container>
            <LoadingBar />
            <ErrorBar />

            {!loading && data?.groups?.length > 0 &&
                <Stack direction="column" spacing={2} useFlexGap>
                    {data?.groups?.map(group => (
                        <React.Fragment key={group.id}>
                            <Title title={group.name} subtitle={t(`group:type.${toCamelCase(group.group_type_name)}`, group.group_type_name)} />
                            <MembersList data={group?.group_members || []} loading={loading} />
                        </React.Fragment>
                    ))} 
                </Stack>
            }
        </Container>
    );
}