import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, TextField, Box, Slider, InputAdornment } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step7 } from './stepList';

const Step7Cancellation = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const [localFormData, setLocalFormData] = useState(formData || {});

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        // Convert string values to numbers for hours field
        const parsedValue = field === 'cancellation_hours' 
            ? parseInt(value, 10) || 0 
            : value;

        setLocalFormData(prev => {
            const updated = { ...prev, [field]: parsedValue };
            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Get step fields
    const fields = step7(localFormData);

    return (
        <Grid container spacing={3}>
            {fields.map(section => (
                <Grid item xs={12} key={section.id}>
                    <Typography variant="h6" gutterBottom>
                        {section.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                        {section.description}
                    </Typography>
                    <Grid container spacing={3}>
                        {section.fields.map(field => (
                            <Grid item xs={12} key={field.name}>
                                {field.component === "TextField" && field.name === 'cancellation_policy' && (
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {field.label}
                                        </Typography>
                                        <TextField
                                            fullWidth
                                            name={field.name}
                                            multiline
                                            minRows={4}
                                            value={localFormData[field.name] || ''}
                                            onChange={(e) => handleChange(field.name, e.target.value)}
                                            required={field.required}
                                            margin={field.margin}
                                            disabled={loading}
                                            error={field.error}
                                            helperText={field.helperText}
                                            placeholder="Enter the cancellation policy for this service..."
                                        />
                                    </Box>
                                )}
                                {field.component === "TextField" && field.name === 'cancellation_hours' && (
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {field.label}
                                        </Typography>
                                        <TextField
                                            fullWidth
                                            name={field.name}
                                            type="number"
                                            value={localFormData[field.name] || ''}
                                            onChange={(e) => handleChange(field.name, e.target.value)}
                                            required={field.required}
                                            margin={field.margin}
                                            disabled={loading}
                                            error={field.error}
                                            helperText={field.helperText}
                                            InputProps={{
                                                inputProps: { min: 0, max: 168 },
                                                endAdornment: (
                                                    <InputAdornment position="end">hours</InputAdornment>
                                                )
                                            }}
                                        />
                                        <Box sx={{ px: 2, mt: 2 }}>
                                            <Slider
                                                value={localFormData.cancellation_hours || 24}
                                                onChange={(e, newValue) => handleChange('cancellation_hours', newValue)}
                                                step={1}
                                                marks={[
                                                    { value: 0, label: '0h' },
                                                    { value: 24, label: '24h' },
                                                    { value: 48, label: '48h' },
                                                    { value: 72, label: '72h' },
                                                    { value: 168, label: '1 week' }
                                                ]}
                                                min={0}
                                                max={168}
                                                valueLabelDisplay="auto"
                                                disabled={loading}
                                            />
                                        </Box>
                                    </Box>
                                )}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default Step7Cancellation;
