import { useEffect, useCallback, useState, useLayoutEffect } from 'react';
import { useMatches } from 'react-router-dom';
import { Container } from '@mui/material';
import { useClientRender, CustomCssInjector } from '@siteboss-frontend/shared/cms/utils';
import { useFieldsFromStore } from '@siteboss-frontend/shared';

//import { fullList } from '../../../../cms/src/components/CmsComponents';

export const Site = ({pageId = null, websiteId = null, templateId = null, showHeader = true, showFooter = true, data = null}) => {
    const [page, setPage] = useState(data);
    const [pageTitle, setPageTitle] = useState(null);

    const matches = useMatches();

    const reduxStore = useFieldsFromStore(["user.id:user.userId"]);
    const { renderStaticSection, getPageDefinition, getWebsiteTemplate, errors } = useClientRender({
        //componentList: fullList,
        store: reduxStore,
        extraProps: {
            websiteId,
        }
    });

    const renderPage = useCallback(async pageId => {
        const visitedPageIds = new Set();
        let promises = [];
        try {
            // template or the default template
            promises.push(templateId ? getPageDefinition({section: {pageId: templateId}}, visitedPageIds) : getWebsiteTemplate({websiteId}));

            // the page
            promises.push(pageId ? getPageDefinition({section: {pageId}}, visitedPageIds) : null);

            const res = await Promise.all(promises);
            if (res?.length > 0){
                let templateRes, pageRes;
				if (res.length > 1) {
                    templateRes = res[0];
                    pageRes = res[1];
                } else pageRes = res[0];

                // insert the page content into the template's body section
				if (templateRes && pageRes) {
					const pageContent = pageRes.filter(section => section.id !== 'header' && section.id !== 'footer');						
					const bodyIdx = templateRes.findIndex(a => a.id === 'body');
					if (bodyIdx > -1) {
						templateRes[bodyIdx] = {
							...templateRes[bodyIdx],
							component: {
								...templateRes[bodyIdx]?.component,
								content: { children: pageContent}
							}
						};
						pageRes = templateRes;
					}

					if (showHeader === false) pageRes = pageObject.filter(section => section.id !== 'header');
					if (showFooter === false) pageRes = pageObject.filter(section => section.id !== 'footer');
				}

                if (pageRes) setPage(pageRes);
            }
        } catch (error) {
            console.log(error);
        }
    }, [getPageDefinition, getWebsiteTemplate, templateId, showHeader, showFooter]);

    useLayoutEffect(() => {
        if (!matches) return;
        let _title = matches.reduce((acc, match) => (match.handle && match.handle.title) ? match.handle.title : acc, 'SiteBoss');
        if (_title.includes('{') && _title.includes('}')) {
            setPageTitle(_title);
            _title = _title.replace(/\{([^}]+)\}\s*-?\s*/g, ''); // temporarily remove the {value} from the title (the corresponding widget will replace it)
        }        
        document.title = _title;
    }, [matches]);

    useEffect(() => {
        if (pageId && !data) renderPage(pageId);
    }, [pageId, data, renderPage]);

    useLayoutEffect(() => {
        document.title = matches.reduce((acc, match) => {
            if (match.handle && match.handle.title) {
              return match.handle.title;
            }
            return acc;
        }, 'SiteBoss');
    }, [matches]);

    return (
        <>
            <CustomCssInjector />
            <Container 
                disableGutters 
                maxWidth={false} 
                sx={{display: "flex", flexDirection: "column", minHeight: '100vh', width: '100%'}}
            >
                {renderStaticSection({data: page || data || {}, extraProps: {replacePageTitle: pageTitle, websiteId, pageId, pageTypeId: 1}})}
                {errors && errors}
            </Container>
        </>
    );
}
