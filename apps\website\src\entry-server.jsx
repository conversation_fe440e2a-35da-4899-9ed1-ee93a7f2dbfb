import React from 'react';
import { matchRoutes } from 'react-router-dom';
import { renderToPipeableStream } from 'react-dom/server';
import createEmotionServer from '@emotion/server/create-instance';
import createEmotionCache from './createEmotionCache';
import { Writable } from 'stream';
import App from './App';
import { buildRoutes } from './Routes';


const getPageTitle = (url, routes) => {
    const matches = matchRoutes(routes, `/${url}`);
    let title = 'SiteBoss';
    if (matches) {
      matches.forEach(match => {
        if (match.route.handle && match.route.handle.title) {
          title = match.route.handle.title;
        }
      });
    }
    return title;
};

export function render({ company, pages = [], indexPage, url, themeOverrides }) {
    const cache = createEmotionCache({});
    const { extractCriticalToChunks, constructStyleTagsFromChunks } = createEmotionServer(cache);

    const routes = buildRoutes(pages, company);
    const pageTitle = getPageTitle(url, routes);

    return new Promise((resolve, reject) => {
        let didError = false;
        const chunks = [];
        
        const { pipe } = renderToPipeableStream(
            <App 
                cache={cache} 
                company={company}
                indexPage={indexPage} 
                url={`/${url}`}
                ssr={true}
                pages={pages}
                routes={routes}
                themeOverrides={themeOverrides}
            />,
            {
                onAllReady() {
                    // Create a Node.js Writable stream
                    const writable = new Writable({
                        write(chunk, encoding, callback) {
                            chunks.push(chunk);
                            callback();
                        }
                    });

                    writable.on('finish', () => {
                        if (!didError) {
                            const html = Buffer.concat(chunks).toString();
                            const emotionChunks = extractCriticalToChunks(html);
                            const css = constructStyleTagsFromChunks(emotionChunks);
                            resolve({ html, css, title: pageTitle });
                        }
                    });

                    // Pipe the stream
                    pipe(writable);
                },
                onShellError(error) {
                    didError = true;
                    reject(error);
                },
                onError(error) {
                    didError = true;
                    console.error('Error during streaming render:', error);
                    reject(error);
                }
            }
        );
    });
}
