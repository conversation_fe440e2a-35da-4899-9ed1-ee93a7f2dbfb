import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, shallowEqual } from 'react-redux';
import { Divider, Stack, List} from '@mui/material';
import { toCamelCase } from '../../../../../utils';

import Line from './Line';
import Transaction from './Transaction';

export const Totals = ({items = ["subtotal", "tax"], extraFields = [], slotProps, ...props}) => {
    const { t } = useTranslation();
    const cart = useSelector(state => state.cart, shallowEqual);
    const balance = (+cart?.totals?.total - +cart?.totals?.payments) || 0;

    return (
        <div style={{position: "relative", marginTop: '1rem', marginBottom: '1rem', minWidth: 300}}>
            <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
                {items.map(item => {
                    let Component = null;
                    switch (item) {
                        case "priceAdjustments":
                            Component = 
                                <React.Fragment key={item}>
                                    {cart?.totals?.priceAdjustments?.map((adjustment, i) => (
                                        <Line 
                                            key={i} 
                                            amount={adjustment.amount} 
                                            label={`order:${toCamelCase(adjustment.price_adjustment_type_name)}`}
                                            slotProps={slotProps?.label} 
                                        />
                                    ))}
                                </React.Fragment>
                            break;
                        default:
                            Component = 
                                <Line 
                                    key={item} 
                                    amount={cart?.totals?.[item] || 0} 
                                    label={`order:${item}`} 
                                    slotProps={slotProps?.label} 
                                    slots={item === "payments" && cart?.transactions?.length > 0 && {
                                        //expandIcon: PlusIcon,
                                        /*options: () => cart?.transactions?.map(transaction => (
                                            <Box key={transaction.id} sx={{position:'relative', ml: 1.5}}>
                                                <Line 
                                                    amount={transaction.amount}
                                                    label={`pos:paymentMethods.${toCamelCase(transaction?.transaction_payment_method_name)}`}
                                                    metadata={transaction}
                                                    slotProps={slotProps?.label}
                                                    slots={{
                                                        options: transaction => (
                                                            <Typography variant="subtitle2" component={Stack} direction="column" key={transaction.id} sx={{my: 2, px: 1}}>
                                                                <Typography variant="bold">{`#${transaction.id}`}</Typography>
                                                                <div>
                                                                    {`${t(`general:date`)}: `}
                                                                    <Typography variant="bold">{formatDate(transaction.date, language)}</Typography>
                                                                </div>
                                                                {t(`status:${toCamelCase(transaction.transaction_status_name)}`, transaction.transaction_status_name)}
                                                            </Typography>
                                                        ),
                                                    }}
                                                />
                                                <Divider />
                                            </Box>
                                        )),*/
                                        options: () => (
                                            <List dense disablePadding aria-labelledby={t("order:payments")}>
                                                {cart?.transactions?.map(transaction => (
                                                    <React.Fragment key={transaction.id}>
                                                        <Transaction transaction={transaction} />
                                                        <Divider />
                                                    </React.Fragment>
                                                ))}
                                            </List>
                                        ),
                                    }}
                                />;
                            break;

                    }
                    return Component;
                })}
                <Line amount={+cart?.totals?.total || 0} label={"order:total"} variant={(balance !== 0 && balance < +cart?.totals?.total) ? "h6" : "h4"} slotProps={slotProps?.total} />
                {(balance !== 0 && balance < +cart?.totals?.total) &&
                    <Line amount={balance} label={"order:balance"} variant="h4" slotProps={slotProps?.total} />
                }

                {extraFields?.length > 0 && 
                    <>
                        <Divider />
                        {extraFields?.map((field, i) => (
                            <Line key={i} {...field} amount={field.amount} label={field.label} slotProps={{...slotProps?.label, ...field?.slotProps}} />
                        ))}
                    </>
                }
            </Stack>
        </div>
    );
}