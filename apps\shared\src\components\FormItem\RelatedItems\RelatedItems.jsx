import React, { useMemo } from 'react';
import { useApi } from '../../../api';

import Autocomplete from '../Autocomplete';

export const RelatedItems = ({ 
    endpoint = "/product", // The endpoint to fetch the related items
    method = "POST", // The method to use for the request
    params = {}, // The params to pass to the request
    dataField = "products", // The field in the response data object that contains the related items, null if the response is the related items
    ...props
}) => {
    const { fetchData } = useApi({params: {endpoint, method, data: {...params}}});

    const searchParams = useMemo(() => {
        if (endpoint === "/product") return search => ({search});
        return null;
    }, [endpoint]);

    return (
        <Autocomplete
            isAsync
            multiple
            fetchData={fetchData}
            fetchParams={params}
            dataField={dataField}
            getSearchParams={searchParams}
            {...props}
        />
    )
}