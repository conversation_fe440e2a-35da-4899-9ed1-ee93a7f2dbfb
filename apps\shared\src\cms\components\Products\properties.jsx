
const typeTitle = [
    { id: 'h1', slug: 'builder:component.heading.types.h1' },
    { id: 'h2', slug: 'builder:component.heading.types.h2' },
    { id: 'h3', slug: 'builder:component.heading.types.h3' },
    { id: 'h4', slug: 'builder:component.heading.types.h4' },
    { id: 'h5', slug: 'builder:component.heading.types.h5' },
    { id: 'h6', slug: 'builder:component.heading.types.h6' },
];

const typeText = [
    { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
    { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
    { id: 'body1', slug: 'builder:component.heading.types.body1' },
    { id: 'body2', slug: 'builder:component.heading.types.body2' },
    { id: 'caption', slug: 'builder:component.heading.types.caption' },
    { id: 'overline', slug: 'builder:component.heading.types.overline' },
    { id: 'p', slug: 'builder:component.heading.types.p' },
    { id: 'code', slug: 'builder:component.heading.types.code' },
];

export const properties = [
    {
        name: 'shopId',
        label: 'builder:component.products.shopId',
        component: "local.ShopId",//"GeneralItemSelector",
        value: 0,
        /*fetchParams: {params: {endpoint: "/cms/site/page", data: {website_id: websiteId, page_type_id: 14}, method: 'POST'}},
        valueFormatter: value => {
            if (!value) return null;
            if (!Array.isArray(value)) value = [value];
            return value.map(v => ({id: v?.id, slug: v?.title}));
        },*/
        size: "small",
        margin: "normal",
    },
    {
        name: 'layoutType',
        label: 'builder:component.products.layoutType',
        component: "Select",
        options: [
            {id: 'card', slug: 'builder:component.pos.layoutTypes.card'},
            {id: 'button', slug: 'builder:component.pos.layoutTypes.button'},
        ],
        value: 'card',
        size: "small",
        margin: "normal",
    },
    {
        name: 'itemsToLoad',
        label: 'builder:component.products.itemsToLoad',
        component: "NumberField",
        value: 30,
        size: "small",
        margin: "normal",
    },
    {
        name: 'galleryLayoutType',
        label: 'builder:component.products.galleryLayoutType',
        component: "Select",
        options: [
            {id: 'carousel', slug: 'builder:component.gallery.layouts.carousel'},
            {id: 'list', slug: 'builder:component.gallery.layouts.list'},
            {id: 'masonry', slug: 'builder:component.gallery.layouts.masonry'},
        ],
        value: 'carousel',
        size: "small",
        margin: "normal",
    },
    {
        name: 'variantsLayoutType',
        label: 'builder:component.products.variantsLayoutType',
        component: "Select",
        options: [
            {id: 'radio', slug: 'builder:component.pos.layoutTypes.radio'},
            {id: 'button', slug: 'builder:component.pos.layoutTypes.button'},
        ],
        value: 'radio',
        size: "small",
        margin: "normal",
    },
    {
        name: 'addonsLayoutType',
        label: 'builder:component.products.addonsLayoutType',
        component: "Select",
        options: [
            {id: 'checkbox', slug: 'builder:component.pos.layoutTypes.checkbox'},
            {id: 'radio', slug: 'builder:component.pos.layoutTypes.radio'},
            {id: 'button', slug: 'builder:component.pos.layoutTypes.button'},
        ],
        value: 'checkbox',
        size: "small",
        margin: "normal",
    },
    {
        name: 'eventUsersLayoutType',
        label: 'builder:component.products.eventUsersLayoutType',
        component: "Select",
        options: [
            {id: 'checkbox', slug: 'builder:component.pos.layoutTypes.checkbox'},
            {id: 'radio', slug: 'builder:component.pos.layoutTypes.radio'},
            {id: 'button', slug: 'builder:component.pos.layoutTypes.button'},
        ],
        value: 'checkbox',
        size: "small",
        margin: "normal",
    },
    {
        name: 'memoLayoutType',
        label: 'builder:component.products.memoLayoutType',
        component: "Select",
        options: [
            {id: 'link', slug: 'builder:component.pos.layoutTypes.link'},
            {id: 'button', slug: 'builder:component.pos.layoutTypes.button'},
        ],
        value: 'link',
        size: "small",
        margin: "normal",
    },
    {
        name: 'fullPage',
        label: 'builder:component.products.fullPage',
        component: "Switch",
        value: true,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'modalSize',
        label: 'builder:component.products.modalSize',
        component: "Select",
        options: [
            {id: 'sm', slug: 'builder:component.pos.sizes.small'},
            {id: 'md', slug: 'builder:component.pos.sizes.medium'},
            {id: 'lg', slug: 'builder:component.pos.sizes.large'},
            {id: 'xl', slug: 'builder:component.pos.sizes.extraLarge'},
        ],
        value: 'sm',
        size: "small",
        margin: "normal",
        condition: { field: 'fullPage', value: false}
    },

];