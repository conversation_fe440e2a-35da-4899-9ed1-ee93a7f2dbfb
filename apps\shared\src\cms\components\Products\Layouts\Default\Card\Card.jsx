import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Card as MuiCard, CardContent, CardMedia, CardActionArea, CardActions, Button, Stack } from '@mui/material';

import Caption from '../../../../common/pos/Caption';
import styles from './Card.module.scss';

export const Card = ({item, content, disabled, selected, onSelect, onAddToCart, direction = "column", ...props}) => {
    const { t } = useTranslation();
    const user = useSelector(state => state.user);

    const handleButtonClick = useCallback(async e => {
        e.preventDefault();
        if (onAddToCart) {
            const res = await onAddToCart(item);
            if (res === 2 && onSelect) await onSelect(item);
        }
    }, [onAddToCart, onSelect, item]);

    const handleProductClick = useCallback(async e => {
        e.preventDefault();
        if (onSelect) await onSelect(item);
    }, [onSelect, item]);

    return (
        <MuiCard className={styles.card}>
            <CardActionArea 
                component={Stack}
                direction="column"
                spacing={0}
                useFlexGap
                onClick={handleProductClick} 
                disabled={disabled} 
                sx={{width: "100%", height: "100%", justifyContent: "flex-start"}}
            >
                <CardMedia
                    component="img"
                    image={item?.media?.[0]?.preview_url || `https://placehold.co/300?font=source-sans-pro&text=${item.name}`}
                    alt={item.name}
                    loading="lazy"
                />
                <CardContent>
                    {item?.name && <Caption variant="body1" component="div" text={item.name} bold gutterBottom />}
                    {content?.price?.map((p, i) => (
                        <Caption key={i} variant={p?.amount ? "h5" : "body3"} component="div" text={p?.amount || p} sx={{py: p?.amount ? 0.5 : undefined, color: p?.amount ? undefined : "text.secondary"}} />
                    ))}
                    {content?.text && <Caption variant="body3" component="div" text={content.text} sx={{color: "text.secondary"}} />}
                </CardContent>
            </CardActionArea>
            {user?.token &&
                <CardActions sx={{mt: "auto"}}>
                    <Button variant="contained" color="primary" disabled={disabled} onClick={handleButtonClick}>{t("pos:addToCart")}</Button>
                </CardActions>
            }
        </MuiCard>
    );
}