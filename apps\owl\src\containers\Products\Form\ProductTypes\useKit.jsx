import React, { use<PERSON>emo } from "react";
import AddSku from "./AddSku";

const rowProps = {
    row: 3,
    rowSize: {xs: 12, sm: 6, md: 4, lg: 3},
    margin: 'normal',
    group: 'kit',
};

const fields = [
    {name: 'kit_type', label: 'product:kit.type', required: true, value: '', component: "Select", options: [], ...rowProps},
    {name: 'slot_count', label: 'product:slotCount', required: false, value: 0, component: "NumberField", ...rowProps},
    {name: 'slot_equivalent', label: 'product:slotEquivalent', required: false, value: '', component: "Select", options: [], ...rowProps},
    {name: 'kit_notes', label: 'product:kit.notes', required: false, value: '', component: "TextField", multiline: true, ...{...rowProps, rowSize: {xs: 12}}},
    {name: 'dimensions', type: 'number', label: 'product:dimensions', required: false, component: "Measurement", value: {width: 0, height: 0, length: 0, weight: 0}, measurements: ['width', 'height', 'length', 'weight'], unit: ['in', 'in', 'in', 'lbs'], gridSize: 3, ...{...rowProps, rowSize: {xs: 12}, margin: 'dense'}},
    {name: 'kit_refrigeration', label: 'product:kit.refrigeration', required: false, value: 1, checked: false, component: "Switch", ...{...rowProps, rowSize: {xs: 12}, margin: 'none'}},
    {name: 'kit_ship_alone', label: 'product:kit.shipAlone', required: false, value: 1, checked: false, component: "Switch", ...{...rowProps, rowSize: {xs: 12}, margin: 'none'}},
    {name: '_customKit', component: AddSku, ...{...rowProps, rowSize: {xs: 12}}},
];

export const useKit = ({rowId = 3}) => {
    const _fields = useMemo(() => fields.map(field => ({...field, row: rowId})), [rowId]);
    
    return {
        fields: _fields,
    }
};