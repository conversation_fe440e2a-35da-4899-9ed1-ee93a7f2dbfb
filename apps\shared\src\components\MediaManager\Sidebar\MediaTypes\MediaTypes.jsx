import React from 'react';
import { useTranslation } from 'react-i18next';
import { Tooltip, ToggleButtonGroup, ToggleButton, useMediaQuery } from '@mui/material';
import { ImageOutlined as ImageIcon, PlayCircleOutlineOutlined as VideoIcon, DescriptionOutlined as DocumentIcon, DrawOutlined as WaiverIcon, LocalFloristOutlined as LogoIcon} from '@mui/icons-material';

const buttons = [
    { slug: 'image', value: 1, icon: ImageIcon },
    { slug: 'video', value: 4, icon: VideoIcon },
    { slug: 'document', value: 5, icon: DocumentIcon },
    { slug: 'logo', value: 9, icon: LogoIcon },
];

export const MediaTypes = ({ value = 1, onChange }) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    return (
        <ToggleButtonGroup 
            size="small" 
            color="secondary" 
            exclusive 
            fullWidth 
            orientation={isMobile ? "vertical" : "horizontal"} 
            aria-label='media types' 
            value={value} 
            onChange={onChange}
        >
            {buttons.map(button => (
                <Tooltip title={t(`media:types.${button.slug}`)} key={button.value}>
                    <ToggleButton value={button.value}>
                        <button.icon />
                    </ToggleButton>
                </Tooltip>
            ))}
        </ToggleButtonGroup>
    );
}