import React from 'react';
import { Box } from '@mui/material';
import { Robot } from './Robot';

export const ErrorBox = ({width = 300, noEffects = false}) => {
    const boxWidth = width * 1.1;
    const radialWidth = width / 1.1;
    const radialSpread = boxWidth - width;
    return (
        <Box sx={{
            width: boxWidth,
            height: boxWidth,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '100%',
            boxShadow: theme => (theme.palette.mode === "dark" && !noEffects) ? `inset rgba(0, 0, 0, 0.01) 0px 0px ${radialWidth}px ${radialSpread}px` : undefined,
            background: theme => (theme.palette.mode === "dark" && !noEffects) ? `radial-gradient(circle, rgba(255, 255, 255, 0.45) 0%, transparent 60%)` : undefined,
        }}>
            <Robot width={width} />
        </Box>
    );
}