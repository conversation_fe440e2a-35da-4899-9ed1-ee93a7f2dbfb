import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setSelectedConditions, addCondition, removeCondition } from '../../../../store/reducers/discountWizardSlice';
import axios from 'axios';
import {
    Typography,
    Paper,
    Box,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    IconButton,
    TextField,
    FormHelperText,
    Checkbox,
    FormControlLabel,
    Stack,
    CircularProgress
} from '@mui/material';
import { Delete as DeleteIcon } from '@mui/icons-material';
import { Autocomplete } from '@siteboss-frontend/shared/components';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { formatDateTime } from '@siteboss-frontend/shared/utils';

// Define condition types
const conditionTypes = [
    { id: 'categories', name: 'Categories', type: 'multi', description: 'Discount will be applied only to products in the selected categories', endpoint: '/category', method: 'POST', dynamic: true },
    { id: 'product_types', name: 'Product Types', type: 'multi', description: 'Discount will be applied only to products with the selected product types', endpoint: '/product/type', method: 'GET' },
    { id: 'products', name: 'Products', type: 'multi', description: 'Discount will be applied to the selected products', endpoint: '/product', method: 'POST' },
    { id: 'product_min_qty', name: 'Product Min Quantity', type: 'number', description: 'Products will have the discount applied only if this quantity or more is in the cart' },
    { id: 'product_max_qty', name: 'Product Max Quantity', type: 'number', description: 'Products will have the discount applied only if this quantity or less is in the cart' },
    { id: 'product_combo', name: 'Product Combo', type: 'multi', description: 'Discount will be applied to the selected products only if ALL of these products are in the cart', endpoint: '/product', method: 'POST' },
    { id: 'user_age_min', name: 'User Age Min', type: 'number', description: 'Discount will be applied if the user is this age or older' },
    { id: 'user_age_max', name: 'User Age Max', type: 'number', description: 'Discount will be applied if the user is this age or younger' },
    { id: 'groups', name: 'Groups', type: 'multi', description: 'Discount will be applied if the user belongs to this Group with status CONFIRMED', endpoint: '/group', method: 'GET' },
    { id: 'events', name: 'Events', type: 'multi', description: 'Discount will be applied if the user is registered for this Event with status ATTENDING', endpoint: '/event', method: 'POST' },
    { id: 'active_subscriptions', name: 'Active Subscription', type: 'boolean', description: 'Discount will be applied if the user has any active subscription' },
    { id: 'current_subscriptions', name: 'Current Subscription', type: 'multi', description: 'Discount will be applied if the user has one of the selected subscriptions in active status', endpoint: '/product', method: 'POST', params: { product_type_id: 1 } },
    { id: 'expired_subscriptions', name: 'Expired Subscription', type: 'multi', description: 'Discount will be applied if the user has one of the selected subscriptions in expired status', endpoint: '/product', method: 'POST', params: { product_type_id: 1 } },
    { id: 'min_cart_total', name: 'Minimum Cart Total', type: 'currency', description: 'Discount will be applied if cart total is equal to or above this amount' },
];

// Get API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_REMOTE;

// Condition component based on type
const ConditionInput = ({ condition, value, onChange, options = [], error, loading = false, searchEvents, searchProducts, searchCategories, fetchCategoryById, fetchProductById, fetchEventById, productTypeFilter = null }) => {

    // Find the full option objects that match the selected values
    const getSelectedOptions = useCallback(() => {

        // 1. Handle undefined/null/empty array value immediately
        if (!value || !Array.isArray(value) || value.length === 0) {
            return [];
        }

        // 2. If value is already an array of objects, use it directly
        if (typeof value[0] === 'object' && value[0]?.id !== undefined) {
            return value;
        }

        // 3. If value is an array of IDs (numbers or strings)
        if (typeof value[0] === 'number' || typeof value[0] === 'string') {
            // If options are not loaded yet, create placeholders
            if (!Array.isArray(options) || options.length === 0) {
                const placeholders = value.map(id => ({ id: String(id), name: `Loading... (ID: ${id})` }));

                // For categories, products, or events, fetch details for each ID if we have the fetch function
                if ((condition.id === 'categories' && condition.dynamic && fetchCategoryById) ||
                    ((condition.id === 'products' || condition.id === 'product_combo') && fetchProductById) ||
                    (condition.id === 'events' && fetchEventById)) {
                    // Use setTimeout to avoid blocking the UI
                    setTimeout(() => {
                        // Use the appropriate fetch function based on condition type
                        let fetchFunction;
                        if (condition.id === 'categories') {
                            fetchFunction = fetchCategoryById;
                        } else if (condition.id === 'events') {
                            fetchFunction = fetchEventById;
                        } else {
                            fetchFunction = fetchProductById;
                        }

                        Promise.all(value.map(id => fetchFunction(id)))
                            .then(results => {
                                const validResults = results.filter(Boolean);
                                if (validResults.length > 0) {
                                    // If we got results, trigger a re-render by calling onChange with the same value
                                    // but with the full objects
                                    onChange(validResults);
                                }
                            })
                            .catch(error => {
                                console.error(`Error fetching ${condition.id} details:`, error);
                            });
                    }, 0);
                }

                return placeholders;
            }
            // Options are loaded, map IDs to full option objects
            else {
                const mappedOptions = value.map(id => {
                    const foundOption = options.find(opt => String(opt.id) === String(id));
                    return foundOption || { id: String(id), name: `Not Found (ID: ${id})` }; // Handle case where ID might not be in options
                });
                return mappedOptions;
            }
        }

        // 4. Fallback for unexpected value format
        console.warn(`getSelectedOptions for ${condition.id} encountered unexpected value format:`, JSON.stringify(value));
        return [];

    }, [value, options, loading, condition.id, condition.dynamic, fetchCategoryById, fetchProductById, fetchEventById, onChange]);

    switch (condition.type) {
        case 'multi':
            // Special handling for events, products, and categories - use onInputChange to trigger search
            if (condition.id === 'events' || condition.id === 'products' || condition.id === 'product_combo' || (condition.id === 'categories' && condition.dynamic)) {
                // Determine the appropriate helper text based on condition ID
                let helperText = 'Type to search';
                if (condition.id === 'events') {
                    helperText = 'Type to search for events';
                } else if (condition.id === 'categories') {
                    helperText = 'Type to search for categories';
                } else {
                    helperText = 'Type to search for products';
                }

                return (
                    <Autocomplete
                        multiple
                        name={condition.id}
                        label={condition.name}
                        errors={error}
                        loading={loading}
                        value={getSelectedOptions()}
                        onChange={({ target }) => {
                            // Only update the value if it's an array (meaning a selection was made)
                            // If it's not an array, it's just the user typing, so we don't update the form value
                            if (Array.isArray(target.value)) {
                                onChange(target.value);
                            }
                        }}
                        isAsync={true}
                        clearOnBlur={false}
                        selectOnFocus={true}
                        freeSolo={true}
                        disableClearable={false}
                        fetchData={async (params) => {
                            if (params.search) {
                                try {
                                    // Use the appropriate search function based on condition ID
                                    let results;
                                    if (condition.id === 'events') {
                                        results = await searchEvents(params.search);
                                    } else if (condition.id === 'products' || condition.id === 'product_combo') {
                                        results = await searchProducts(params.search);
                                    } else if (condition.id === 'categories') {
                                        results = await searchCategories(params.search);
                                    } else {
                                        return { data: [] };
                                    }
                                    return { data: results };
                                } catch (error) {
                                    console.error(`Error searching for ${condition.id}:`, error);
                                    return { data: [] };
                                }
                            }
                            return { data: [] };
                        }}
                        getSearchParams={(search) => ({ search, max_records: 10 })}
                        filterOptions={(x) => x}
                        openOnFocus={true}
                        renderOption={(props, option) => (
                            <li {...props} key={JSON.stringify(option)}>
                                {option.name}
                                {condition.id === 'events' && option.start_datetime &&
                                    `(${formatDateTime(new Date(option.start_datetime), 'en-US', 'M/d/yy')})`
                                }
                            </li>
                        )}
                        helperText={helperText}
                        filterSelectedOptions
                    />
                );
            }
            // Default handling for other multi-select conditions
            return (
                <Autocomplete
                    multiple
                    options={options}
                    getOptionLabel={(option) => option.name || (option.id ? `ID: ${option.id}` : '')}
                    isOptionEqualToValue={(option, val) => {
                        if (!option?.id || !val?.id) return false;
                        return String(option.id) === String(val.id);
                    }}
                    value={getSelectedOptions()}
                    onChange={(event, newValue) => { // Use 'event' as the first argument name
                        let finalValue = newValue; // Assume newValue is correct initially

                        // --- Correction Logic ---
                        // If newValue is undefined, but the event object has the value, use that instead.
                        if (finalValue === undefined && event?.target?.value && Array.isArray(event.target.value)) {
                            console.warn(`Autocomplete onChange provided undefined newValue for ${condition.id}. Using event.target.value instead.`);
                            finalValue = event.target.value;
                        }
                        // Ensure we always pass an array, even if something went wrong
                        if (!Array.isArray(finalValue)) {
                             console.error(`Autocomplete onChange resulted in non-array value for ${condition.id}. Defaulting to empty array. Value was:`, finalValue);
                             finalValue = [];
                        }
                        // --- End Correction Logic ---

                        onChange(finalValue); // Pass the potentially corrected value up
                    }}
                    loading={loading}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label={condition.name}
                            error={!!error}
                            helperText={error || (loading ? 'Loading options...' : '')}
                            InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                    <React.Fragment>
                                        {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                        {params.InputProps.endAdornment}
                                    </React.Fragment>
                                ),
                            }}
                        />
                    )}
                    noOptionsText={loading ? 'Loading options...' : 'No options available'}
                />
            );
        case 'number':
            return (
                <TextField
                    label={condition.name}
                    type="number"
                    value={value || ''}
                    onChange={(e) => onChange(e.target.value)}
                    fullWidth
                    // Using inputProps for min value
                    inputProps={{ min: 0 }}
                    error={!!error}
                    helperText={error}
                />
            );
        case 'currency':
            return (
                <TextField
                    label={condition.name}
                    type="number"
                    value={value || ''}
                    onChange={(e) => onChange(e.target.value)}
                    fullWidth
                    // Using inputProps for min and step values
                    inputProps={{ min: 0, step: 0.01 }}
                    error={!!error}
                    helperText={error}
                    // Using InputProps for startAdornment
                    InputProps={{
                        startAdornment: <span>$</span>,
                    }}
                />
            );
        case 'boolean':
            return (
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={value === 1}
                            onChange={(e) => onChange(e.target.checked ? 1 : 0)}
                        />
                    }
                    label={condition.name}
                />
            );
        default:
            return <div>Unknown condition type</div>;
    }
};

const Step8Conditions = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);
    const selectedConditions = useSelector(state => state.discountWizard.selectedConditions);
    const [availableConditions, setAvailableConditions] = useState(conditionTypes);
    const [conditionErrors, setConditionErrors] = useState({});
    const [optionsCache, setOptionsCache] = useState({});
    const [loadingOptions, setLoadingOptions] = useState({});
    const [params, setParams] = useState({});
    const [isInitialized, setIsInitialized] = useState(false);

    // Generic search function for entities (events, products, etc.)
    const searchEntity = useCallback(async (entityType, searchTerm, customRequestObj = null) => {
        if (!searchTerm || searchTerm.trim().length < 1) {
            return [];
        }

        // Set loading state for the entity
        setLoadingOptions(prev => ({ ...prev, [entityType]: true }));

        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Determine the endpoint based on conditionType
            const endpoint = conditionTypes.find(c => c.id === entityType)?.endpoint || '';

            // Create request object
            const requestObj = customRequestObj || {
                search: searchTerm,
                max_records: 10
            };

            // Make the API request
            const response = await axios.post(
                `${API_BASE_URL}${endpoint}`,
                requestObj,
                {
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                }
            );

            // Process the response
            let options = [];
            if (response.data && response.data.data) {
                if (Array.isArray(response.data.data)) {
                    options = response.data.data;
                } else if (typeof response.data.data === 'object') {
                    // Try to find arrays in the response
                    const possibleArrays = Object.values(response.data.data)
                        .filter(value => Array.isArray(value));

                    if (possibleArrays.length > 0) {
                        options = possibleArrays[0];
                    } else {
                        options = [response.data.data];
                    }
                }

                // Cache the options
                setOptionsCache(prev => ({ ...prev, [entityType]: options }));
            }

            return options;
        } catch (error) {
            console.error(`Error searching for ${entityType}:`, error);
            return [];
        } finally {
            // Clear loading state
            setLoadingOptions(prev => ({ ...prev, [entityType]: false }));
        }
    }, [API_BASE_URL]);

    // Search for events based on search term
    const searchEvents = useCallback((searchTerm) => {
        return searchEntity('events', searchTerm);
    }, [searchEntity]);

    // Search for categories based on search term
    const searchCategories = useCallback((searchTerm) => {
        return searchEntity('categories', searchTerm);
    }, [searchEntity]);

    // Fetch category details by ID
    const fetchCategoryById = useCallback(async (categoryId) => {
        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Make the API request to get category details
            const response = await axios.get(
                `${API_BASE_URL}/category/${categoryId}`,
                {
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                }
            );

            // Check if the response data is an array and has at least one item
            if (response.data && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
                // Return the first item in the data array
                return response.data.data[0];
            }

            return null;
        } catch (error) {
            console.error(`Error fetching category details for ID ${categoryId}:`, error);
            return null;
        }
    }, [API_BASE_URL]);

    // Fetch product details by ID
    const fetchProductById = useCallback(async (productId) => {
        console.log(`Fetching product details for ID: ${productId}`);
        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Make the API request to get product details
            const response = await axios.get(
                `${API_BASE_URL}/product/product/${productId}`,
                {
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log(`Product details response for ID ${productId}:`, response.data);

            if (response.data && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
                // Return the first item in the data array
                return response.data.data[0];
            }

            return null;
        } catch (error) {
            console.error(`Error fetching product details for ID ${productId}:`, error);
            return null;
        }
    }, [API_BASE_URL]);

    // Fetch event details by ID
    const fetchEventById = useCallback(async (eventId) => {
        console.log(`Fetching event details for ID: ${eventId}`);
        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Make the API request to get event details
            const response = await axios.get(
                `${API_BASE_URL}/event/${eventId}`,
                {
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log(`Event details response for ID ${eventId}:`, response.data);

            if (response.data && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
                // Return the first item in the data array
                return response.data.data[0];
            }

            return null;
        } catch (error) {
            console.error(`Error fetching event details for ID ${eventId}:`, error);
            return null;
        }
    }, [API_BASE_URL]);

    // State for product type filter
    const [selectedProductType, setSelectedProductType] = useState(null);
    const [productTypes, setProductTypes] = useState([]);
    const [loadingProductTypes, setLoadingProductTypes] = useState(false);

    // Fetch product types
    const fetchProductTypes = useCallback(async () => {
        setLoadingProductTypes(true);
        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Make the API request
            const response = await axios.get(
                `${API_BASE_URL}/product/type`,
                {
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data && response.data.data) {
                let options = [];
                if (Array.isArray(response.data.data)) {
                    options = response.data.data;
                } else if (typeof response.data.data === 'object') {
                    // Try to find arrays in the response
                    const possibleArrays = Object.values(response.data.data)
                        .filter(value => Array.isArray(value));

                    if (possibleArrays.length > 0) {
                        options = possibleArrays[0];
                    } else {
                        options = [response.data.data];
                    }
                }

                // Add 'All' option at the beginning
                const allOption = { id: null, name: 'All Product Types' };
                setProductTypes([allOption, ...options]);
            }
        } catch (error) {
            console.error('Error fetching product types:', error);
        } finally {
            setLoadingProductTypes(false);
        }
    }, [API_BASE_URL]);

    // Fetch product types on component mount
    useEffect(() => {
        fetchProductTypes();
    }, [fetchProductTypes]);

    // Search for products based on search term
    const searchProducts = useCallback((searchTerm) => {
        // Create request object with search term
        const requestObj = {
            search: searchTerm,
            max_records: 10
        };

        // Add product_type_id filter if a product type is selected
        if (selectedProductType) {
            requestObj.product_type_id = selectedProductType;
        }

        return searchEntity('products', searchTerm, requestObj);
    }, [searchEntity, selectedProductType]);

    // We're using the direct searchEvents function instead of a debounced version
    // to make sure it's called immediately when the user types

    // Fetch options for multi-select conditions
    const fetchOptionsForCondition = useCallback(async (conditionId) => {
        const condition = conditionTypes.find(c => c.id === conditionId);
        if (!condition || !condition.endpoint) {
            return [];
        }

        // Skip pre-fetching for events, products, and categories - they will be fetched on search
        if (conditionId === 'events' || conditionId === 'products' || conditionId === 'product_combo' || (conditionId === 'categories' && conditionTypes.find(c => c.id === 'categories')?.dynamic)) {
            return [];
        }

        // If we already have options cached, return them
        if (optionsCache[conditionId] && optionsCache[conditionId].length > 0) {
            return optionsCache[conditionId];
        }

        // Set loading state for this condition
        setLoadingOptions(prev => ({ ...prev, [conditionId]: true }));

        try {
            // Get the token from localStorage
            let token = '';
            const localuser = localStorage.getItem('user');
            if (localuser) {
                try {
                    const parsedUser = JSON.parse(localuser);
                    token = parsedUser.token;
                } catch (error) {
                    console.error('Error parsing user from localStorage:', error);
                }
            }

            // Make the API request
            let response;
            if (condition.method === 'GET') {
                // For GET requests
                response = await axios.get(
                    `${API_BASE_URL}${condition.endpoint}`,
                    {
                        headers: {
                            'Authorization': token,
                            'Content-Type': 'application/json'
                        },
                        params: condition.params || {}
                    }
                );
            } else {
                // Default to POST for all other methods
                response = await axios.post(
                    `${API_BASE_URL}${condition.endpoint}`,
                    condition.params || {},
                    {
                        headers: {
                            'Authorization': token,
                            'Content-Type': 'application/json'
                        }
                    }
                );
            }

            // Process the response
            let options = [];
            if (response.data && response.data.data) {
                // Handle different response structures based on the endpoint
                if (conditionId === 'categories' && response.data.data.categories) {
                    // Categories endpoint returns data.categories array
                    options = response.data.data.categories;
                } else if (Array.isArray(response.data.data)) {
                    // Some endpoints return data as an array directly
                    options = response.data.data;
                } else if (typeof response.data.data === 'object') {
                    // Some endpoints might return data as an object with arrays inside
                    // Try to find arrays in the response
                    const possibleArrays = Object.values(response.data.data)
                        .filter(value => Array.isArray(value));

                    if (possibleArrays.length > 0) {
                        // Use the first array found
                        options = possibleArrays[0];
                    } else {
                        // If no arrays found, use the data object as is
                        options = [response.data.data];
                    }
                }

                // Cache the options
                setOptionsCache(prev => ({ ...prev, [conditionId]: options }));
            }

            return options;
        } catch (error) {
            console.error(`Error fetching options for ${conditionId}:`, error);
            return [];
        } finally {
            // Clear loading state
            setLoadingOptions(prev => ({ ...prev, [conditionId]: false }));
        }
    }, [optionsCache, API_BASE_URL]);

    // Get options for a condition (either from cache or fetch them)
    const getOptionsForCondition = useCallback((conditionId) => {
        // For events, products, and categories, we don't pre-fetch - we'll handle this separately
        if (conditionId === 'events' || conditionId === 'products' || conditionId === 'product_combo' || (conditionId === 'categories' && conditionTypes.find(c => c.id === 'categories')?.dynamic)) {
            // Just return cached options if available
            return optionsCache[conditionId] || [];
        }

        // Return cached options if available
        if (optionsCache[conditionId] && optionsCache[conditionId].length > 0) {
            return optionsCache[conditionId];
        }

        // If we're not already loading these options, trigger a fetch
        if (!loadingOptions[conditionId]) {
            // Use requestAnimationFrame to avoid blocking the UI and prevent render cycle issues
            requestAnimationFrame(() => {
                fetchOptionsForCondition(conditionId);
            });
        }

        // Return empty array while loading
        return [];
    }, [optionsCache, loadingOptions, fetchOptionsForCondition]);

    // Function to fetch and cache category details for selected categories
    const fetchSelectedCategoryDetails = useCallback(async (categoryIds) => {
        if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
            return [];
        }

        console.log('Fetching details for selected categories:', categoryIds);
        setLoadingOptions(prev => ({ ...prev, categories: true }));

        try {
            // Fetch details for each category ID
            const categoryPromises = categoryIds.map(id => fetchCategoryById(id));
            const categoryResults = await Promise.all(categoryPromises);

            // Filter out null results and create category objects
            const categories = categoryResults.filter(Boolean);
            console.log('Fetched category details:', categories);

            // Cache the category details
            if (categories.length > 0) {
                setOptionsCache(prev => ({
                    ...prev,
                    categories: [...(prev.categories || []), ...categories]
                }));
            }

            return categories;
        } catch (error) {
            console.error('Error fetching category details:', error);
            return [];
        } finally {
            setLoadingOptions(prev => ({ ...prev, categories: false }));
        }
    }, [fetchCategoryById]);

    // Function to fetch and cache product details for selected products
    const fetchSelectedProductDetails = useCallback(async (productIds) => {
        if (!Array.isArray(productIds) || productIds.length === 0) {
            return [];
        }

        console.log('Fetching details for selected products:', productIds);
        setLoadingOptions(prev => ({ ...prev, products: true }));

        try {
            // Fetch details for each product ID
            const productPromises = productIds.map(id => fetchProductById(id));
            const productResults = await Promise.all(productPromises);

            // Filter out null results and create product objects
            const products = productResults.filter(Boolean);
            console.log('Fetched product details:', products);

            // Cache the product details
            if (products.length > 0) {
                setOptionsCache(prev => ({
                    ...prev,
                    products: [...(prev.products || []), ...products]
                }));
            }

            return products;
        } catch (error) {
            console.error('Error fetching product details:', error);
            return [];
        } finally {
            setLoadingOptions(prev => ({ ...prev, products: false }));
        }
    }, [fetchProductById]);

    // Function to fetch and cache event details for selected events
    const fetchSelectedEventDetails = useCallback(async (eventIds) => {
        if (!Array.isArray(eventIds) || eventIds.length === 0) {
            return [];
        }

        console.log('Fetching details for selected events:', eventIds);
        setLoadingOptions(prev => ({ ...prev, events: true }));

        try {
            // Fetch details for each event ID
            const eventPromises = eventIds.map(id => fetchEventById(id));
            const eventResults = await Promise.all(eventPromises);

            // Filter out null results and create event objects
            const events = eventResults.filter(Boolean);
            console.log('Fetched event details:', events);

            // Cache the event details
            if (events.length > 0) {
                setOptionsCache(prev => ({
                    ...prev,
                    events: [...(prev.events || []), ...events]
                }));
            }

            return events;
        } catch (error) {
            console.error('Error fetching event details:', error);
            return [];
        } finally {
            setLoadingOptions(prev => ({ ...prev, events: false }));
        }
    }, [fetchEventById]);

    // Single initialization effect
    useEffect(() => {
        // Prevent multiple initializations
        if (isInitialized) return;

        // If we have formData with params, initialize from that
        if (formData?.params && Object.keys(formData.params).length > 0) {
            setParams(formData.params);

            // Find all conditions that have values in the params
            const initialConditions = Object.keys(formData.params)
                .filter(key => formData.params[key] !== null && formData.params[key] !== undefined)
                .map(key => conditionTypes.find(c => c.id === key))
                .filter(Boolean);

            // Set the selected conditions in Redux if not already set
            if (initialConditions.length > 0 && selectedConditions.length === 0) {
                dispatch(setSelectedConditions(initialConditions));
            }

            // Pre-fetch options for conditions that need them
            initialConditions.forEach(condition => {
                if (condition.type === 'multi' && condition.endpoint) {
                    if (condition.id === 'categories' && condition.dynamic && formData.params?.categories) {
                        const categoryIds = formData.params.categories;
                        if (Array.isArray(categoryIds) && categoryIds.length > 0) {
                            const ids = categoryIds.map(cat => typeof cat === 'object' && cat.id ? cat.id : cat);
                            fetchSelectedCategoryDetails(ids);
                        }
                    } else if (condition.id === 'events' && formData.params?.events) {
                        const eventIds = formData.params.events;
                        if (Array.isArray(eventIds) && eventIds.length > 0) {
                            const ids = eventIds.map(evt => typeof evt === 'object' && evt.id ? evt.id : evt);
                            fetchSelectedEventDetails(ids);
                        }
                    } else {
                        fetchOptionsForCondition(condition.id);
                    }
                }
            });

            setIsInitialized(true);
        }
        // If we have data but no formData, initialize from data
        else if (data && data.params) {
            const parsedParams = typeof data.params === 'string' ? JSON.parse(data.params) : data.params;
            setParams(parsedParams);
            
            const initialConditions = Object.keys(parsedParams)
                .filter(key => parsedParams[key] !== null && parsedParams[key] !== undefined)
                .map(key => conditionTypes.find(c => c.id === key))
                .filter(Boolean);

            if (initialConditions.length > 0) {
                dispatch(setSelectedConditions(initialConditions));
            }

            setIsInitialized(true);
        }
    }, [
        formData,
        data,
        dispatch,
        fetchOptionsForCondition,
        fetchSelectedCategoryDetails,
        fetchSelectedEventDetails,
        selectedConditions.length,
        isInitialized
    ]);    

    // Update available conditions when selected conditions change
    useEffect(() => {
        const selectedIds = selectedConditions.map(c => c.id);
        setAvailableConditions(conditionTypes.filter(c => !selectedIds.includes(c.id)));
    }, [selectedConditions]);

    // Update local state from Redux store - only update params, not selectedConditions
    // Use a ref to track if we're currently updating to prevent loops
    const updatingParamsRef = useRef(false);

    useEffect(() => {
        // Skip if we're currently updating, don't have formData, or we're still initializing
        if (updatingParamsRef.current || !formData || !isInitialized) {
            return;
        }

        // Skip if we don't have params
        if (!formData.params) {
            return;
        }

        // Set updating flag to prevent loops
        updatingParamsRef.current = true;

        try {
            // Only update if the params have actually changed
            const currentParamsStr = JSON.stringify(params);
            const newParamsStr = JSON.stringify(formData.params);

            if (currentParamsStr !== newParamsStr) {
                setParams(formData.params);
            }
        } finally {
            // Reset updating flag
            updatingParamsRef.current = false;
        }
    }, [formData, params, isInitialized]);

    // Add a new condition
    const handleAddCondition = useCallback(async (conditionId) => { // Added useCallback wrapper if missing, ensure dependencies are correct
        if (!conditionId) return;

        const condition = conditionTypes.find(c => c.id === conditionId);
        if (!condition) return;

        // Add the condition to the selected conditions in Redux
        dispatch(addCondition(condition));

        // Initialize the condition value in params while preserving existing params
        const newParams = { ...params };

        // Set the initial value based on the condition type
        switch (condition.type) {
            case 'multi':
                newParams[condition.id] = [];
                // Fetch options for this condition if it's a multi-select
                if (condition.endpoint && !optionsCache[conditionId]) {
                    fetchOptionsForCondition(conditionId);
                }
                break;
            case 'boolean':
                newParams[condition.id] = 0;
                break;
            case 'number':
            case 'currency':
                newParams[condition.id] = '';
                break;
            default:
                newParams[condition.id] = null;
        }

        // Update local state
        setParams(newParams);

        // Create a new formData object that preserves all existing formData
        const updatedFormData = {
            ...formData,
            params: newParams
        };

        // Save to Redux
        dispatch(updateFormData(updatedFormData));
    }, [dispatch, params, optionsCache, fetchOptionsForCondition, formData]);

    // Remove a condition
    const handleRemoveCondition = useCallback((conditionId) => {

        // Remove the condition from the selected conditions in Redux
        dispatch(removeCondition(conditionId));

        // Remove the condition from params while preserving other params
        const newParams = { ...params };
        delete newParams[conditionId];

        // Update local state
        setParams(newParams);

        // Create a new formData object that preserves all existing formData
        const updatedFormData = {
            ...formData,
            params: newParams
        };

        // Save to Redux
        dispatch(updateFormData(updatedFormData));

        // Clear any errors for this condition
        const newErrors = { ...conditionErrors };
        delete newErrors[conditionId];
        setConditionErrors(newErrors);
    }, [dispatch, params, formData, conditionErrors]);

    // Validate a condition
    const validateCondition = useCallback((conditionId, value) => {
        const condition = conditionTypes.find(c => c.id === conditionId);
        if (!condition) return;

        const newErrors = { ...conditionErrors };

        switch (condition.type) {
            case 'multi':
                if (Array.isArray(value) && value.length === 0) {
                    newErrors[conditionId] = `Please select at least one ${condition.name.toLowerCase()}`;
                } else {
                    delete newErrors[conditionId];
                }
                break;
            case 'number':
            case 'currency':
                if (value === '' || isNaN(parseFloat(value)) || parseFloat(value) < 0) {
                    newErrors[conditionId] = `Please enter a valid number for ${condition.name}`;
                } else {
                    delete newErrors[conditionId];
                }
                break;
            default:
                delete newErrors[conditionId];
        }

        // Update the errors state
        setConditionErrors(newErrors);
    }, [conditionErrors]);

    // Update condition value
    const handleConditionChange = useCallback((conditionId, value) => {

        // --- GUARD CLAUSE (REVISED) ---
        // We now expect an empty array instead of undefined if Autocomplete fails
        // You might decide if an empty array should clear the condition or be ignored
        if (value === undefined) { // Keep this check just in case the Autocomplete wrapper is bypassed
             console.error(`handleConditionChange received undefined for ${conditionId}. Aborting.`);
             return;
        }
        // If you receive an empty array, decide how to handle it.
        // Maybe you want to ignore it if the previous value wasn't empty?
        // Or maybe setting it to empty IS the desired behavior.
        // For now, we'll proceed, assuming setting to empty is okay.


        // 1. Get the most current formData from Redux state
        const currentFormData = formData;

        // Safety check
        if (!currentFormData || typeof currentFormData !== 'object') {
            console.error("handleConditionChange: formData from Redux is invalid. Aborting update.", currentFormData);
            return;
        }

        // 2. Create the new params object starting from the CURRENT formData.params
        const baseParams = currentFormData.params && typeof currentFormData.params === 'object'
            ? { ...currentFormData.params }
            : {};

        const newParams = { ...baseParams }; // Create a mutable copy

        // 3. Update the specific condition within the newParams object
        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].id !== undefined) {
            newParams[conditionId] = value; // Store full objects
        } else {
            newParams[conditionId] = value; // Store other values
        }

        // 4. *** REMOVED setParams(newParams); *** Let Redux handle the state update

        // 5. Construct the final payload for Redux
        const updatedFormData = {
            ...currentFormData,
            params: newParams
        };

        // 6. Dispatch the update to Redux
        dispatch(updateFormData(updatedFormData));

        // 7. Validate the changed condition
        validateCondition(conditionId, value);
    }, [dispatch, params, formData, validateCondition]);

    return (
        <Stack spacing={3}>
            <Box>
                <Typography variant="h6" gutterBottom>
                    Set conditions for this discount
                </Typography>

                <Typography variant="body1" sx={{ mb: 2 }}>
                    Add conditions that must be met for this discount to be applied.
                    All conditions are cumulative - they must ALL be true for the discount to apply.
                </Typography>

                {/* Add condition selector */}
                <Box sx={{ mb: 3 }}>
                    <FormControl fullWidth>
                        <InputLabel>Add a condition</InputLabel>
                        <Select
                            value=""
                            label="Add a condition"
                            onChange={(e) => handleAddCondition(e.target.value)}
                            disabled={availableConditions.length === 0}
                        >
                            {availableConditions.map((condition) => (
                                <MenuItem key={condition.id} value={condition.id}>
                                    {condition.name}
                                </MenuItem>
                            ))}
                        </Select>
                        {availableConditions.length === 0 && (
                            <FormHelperText>All available conditions have been added</FormHelperText>
                        )}
                    </FormControl>
                </Box>

                {/* Selected conditions */}
                {selectedConditions.length > 0 ? (
                    selectedConditions.map((condition) => (
                        <Paper key={condition.id} sx={{ p: 2, mb: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="subtitle1">{condition.name}</Typography>
                                <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => handleRemoveCondition(condition.id)}
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Box>

                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                {condition.description}
                            </Typography>

                            {/* Add Product Type filter above Products autocomplete */}
                            {(condition.id === 'products' || condition.id === 'product_combo') && (
                                <Box sx={{ mb: 2 }}>
                                    <FormControl fullWidth size="small">
                                        <InputLabel>Filter by Product Type</InputLabel>
                                        <Select
                                            value={selectedProductType || ''}
                                            onChange={(e) => setSelectedProductType(e.target.value === '' ? null : e.target.value)}
                                            label="Filter by Product Type"
                                        >
                                            <MenuItem value="">All Product Types</MenuItem>
                                            {productTypes.filter(pt => pt.id !== null).map((type) => (
                                                <MenuItem key={type.id} value={type.id}>{type.name}</MenuItem>
                                            ))}
                                        </Select>
                                        <FormHelperText>
                                            Select a product type to filter the products dropdown
                                        </FormHelperText>
                                    </FormControl>
                                </Box>
                            )}
                            <ConditionInput
                                condition={condition}
                                value={params[condition.id]}
                                onChange={(value) => handleConditionChange(condition.id, value)}
                                options={getOptionsForCondition(condition.id)}
                                error={conditionErrors[condition.id]}
                                loading={loadingOptions[condition.id]}
                                searchEvents={searchEvents}
                                searchProducts={searchProducts}
                                searchCategories={searchCategories}
                                fetchCategoryById={fetchCategoryById}
                                fetchProductById={fetchProductById}
                                fetchEventById={fetchEventById}
                            />
                        </Paper>
                    ))
                ) : (
                    <Typography variant="body1" color="text.secondary" align="center" sx={{ py: 4 }}>
                        No conditions added. Add a condition using the dropdown above.
                    </Typography>
                )}
            </Box>
        </Stack>
    );
};

export default Step8Conditions;
