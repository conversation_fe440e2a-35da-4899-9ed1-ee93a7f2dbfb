import { useEffect, useContext } from 'react';
import { useSelector } from 'react-redux';

import { LoginForm } from '../../components';
import { PosContext } from '.';

export const useLoggedInUser = ({selectLoggedInUser = false, selectedUser, onUserSelection}) => {
    const { handleUserSelection = () => {}, selectedUser: contextUser } = useContext(PosContext) || {};
    const user = useSelector(state => state.user);

    useEffect(() => {
        if (user?.token && !selectedUser && !contextUser && selectLoggedInUser) {
            const selectionFunction = onUserSelection || handleUserSelection;
            selectionFunction(user?.profile);
        }
    }, [user, handleUserSelection, onUserSelection, selectedUser, contextUser, selectLoggedInUser]);

    return {
        user,
        selectedUser,
        LoginForm: () => !user?.token ? <LoginForm onAfterLogin={user => handleUserSelection(user?.profile)}/> : null,
    }
}