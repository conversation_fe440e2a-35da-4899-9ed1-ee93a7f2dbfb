{"name": "@siteboss-frontend/website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node --max-old-space-size=8192 server", "build:client": "vite build --ssrManifest --outDir dist/client", "build:server": "vite build --ssr src/entry-server.jsx --outDir dist/server", "build": "npm run build:client && npm run build:server", "preview": "cross-env NODE_ENV=production node --max-old-space-size=8192 server", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@dnd-kit/accessibility": "^3.1.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^6.0.0", "@mui/lab": "^6.0.0-beta.10", "@mui/material": "^6.0.0", "@mui/x-data-grid": "^7.18.0", "@mui/x-date-pickers": "^7.18.0", "@mui/x-date-pickers-pro": "^7.3.2", "@mui/x-tree-view": "^7.18.0", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@nivo/pie": "^0.88.0", "@reduxjs/toolkit": "^2.5.1", "@siteboss-frontend/shared": "*", "axios": "^1.7.7", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.2.6", "i18next": "^23.16.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-redux": "^9.1.0", "react-router-dom": "^6.28.0", "redux-undo": "^1.1.0", "sass": "^1.72.0", "vite": "^5.4.11", "webfontloader": "^1.6.28", "compression": "^1.7.5", "dotenv": "^16.4.5", "express": "^4.21.1", "madge": "^8.0.0", "sirv": "^3.0.0", "sitemap": "^8.0.0"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prop-types": "^15.8.1", "vite": "^5.4.11"}}