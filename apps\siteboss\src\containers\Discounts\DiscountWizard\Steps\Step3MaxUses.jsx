import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, FormHelperText, TextField } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step3 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';
import AllInclusiveIcon from '@mui/icons-material/AllInclusive';
import LooksOneIcon from '@mui/icons-material/LooksOne';

const Step3MaxUses = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);

    // Initialize state for unlimited and max_uses values
    const [unlimited, setUnlimited] = useState(0);
    const [maxUses, setMaxUses] = useState('');

    // Initialize local state from Redux store
    useEffect(() => {
        if (formData?.unlimited !== undefined) {
            setUnlimited(formData.unlimited);
        }
        if (formData?.max_uses !== undefined) {
            // Convert to string if it's a number
            const maxUsesValue = typeof formData.max_uses === 'number' ?
                formData.max_uses.toString() : formData.max_uses;
            setMaxUses(maxUsesValue);

            // If max_uses is 0 (or '0'), ensure unlimited is set to 1
            if (formData.max_uses == 0 && formData.unlimited !== 1) {
                setUnlimited(1);
                dispatch(updateFormData({
                    ...formData,
                    unlimited: 1
                }));
            }
        }
    }, [formData, dispatch]);

    // Initialize form data from API response - only run once when data changes
    useEffect(() => {

        // Check if we have data from the API and we need to initialize
        if (data && !formData?.initialized) {
            const updatedData = { ...formData, initialized: true };

            // Handle unlimited field
            if (data.unlimited !== undefined) {
                const value = parseInt(data.unlimited) || 0;
                setUnlimited(value);
                updatedData.unlimited = value;
            }

            // If max_uses is 0, it means unlimited is true
            // Use loose equality (==) to handle both number 0 and string '0'
            if (data.max_uses == 0) {
                setUnlimited(1);
                updatedData.unlimited = 1;
                updatedData.max_uses = '0'; // Store 0 for API compatibility
                setMaxUses('0');
            } else if (data.max_uses !== undefined) {
                // Handle max_uses field
                const maxUsesValue = parseInt(data.max_uses);
                // Ensure max_uses is not negative
                if (maxUsesValue < 0) {
                    setMaxUses('1');
                    updatedData.max_uses = '1';
                } else {
                    setMaxUses(maxUsesValue.toString());
                    updatedData.max_uses = maxUsesValue.toString();
                }
            }

            // Dispatch a single update with all changes
            dispatch(updateFormData(updatedData));
        }
    }, [data, dispatch, formData]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Set usage limits for this discount
                </Typography>
                <StyledRadioGroup
                    name="unlimited"
                    label="Usage Limit"
                    required
                    options={[
                        {
                            id: 1,
                            value: 1,
                            label: 'Unlimited uses',
                            icon: <AllInclusiveIcon />
                        },
                        {
                            id: 0,
                            value: 0,
                            label: 'Limited number of uses',
                            icon: <LooksOneIcon />
                        }
                    ]}
                    value={unlimited}
                    onChange={(e) => {
                        const value = parseInt(e.target.value);
                        setUnlimited(value);

                        // If unlimited is selected, set max_uses to 0
                        if (value === 1) {
                            setMaxUses('0');
                            dispatch(updateFormData({
                                ...formData,
                                unlimited: value,
                                max_uses: '0' // Store 0 for API compatibility
                            }));
                        } else {
                            // If limited is selected, set max_uses to 1 if it was previously unlimited
                            if (maxUses === '0' || maxUses === '' || maxUses == 0) {
                                setMaxUses('1');
                                dispatch(updateFormData({
                                    ...formData,
                                    unlimited: value,
                                    max_uses: '1'
                                }));
                            } else {
                                // Otherwise just update the unlimited value
                                dispatch(updateFormData({
                                    ...formData,
                                    unlimited: value
                                }));
                            }
                        }
                    }}
                    helperText="Choose whether this discount has a usage limit"
                />

                {unlimited === 0 && (
                    <TextField
                        name="max_uses"
                        label="Maximum Number of Uses"
                        type="number"
                        fullWidth
                        required
                        margin="normal"
                        helperText="Enter the maximum number of times this discount can be used (must be 1 or greater)"
                        inputProps={{ min: 1 }}
                        value={maxUses}
                        onChange={(e) => {
                            let value = e.target.value;

                            // Ensure value is not negative
                            const numValue = parseInt(value);
                            if (numValue < 1 && value !== '') {
                                value = '1';
                            }

                            setMaxUses(value);
                            dispatch(updateFormData({
                                ...formData,
                                max_uses: value
                            }));
                        }}
                        onBlur={(e) => {
                            // If the field is empty on blur, set it to 1
                            if (e.target.value === '') {
                                setMaxUses('1');
                                dispatch(updateFormData({
                                    ...formData,
                                    max_uses: '1'
                                }));
                            }
                        }}
                    />
                )}
            </Grid>
        </Grid>
    );
};

export default Step3MaxUses;
