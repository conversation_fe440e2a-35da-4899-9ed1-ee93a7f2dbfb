import { Meta } from '@storybook/addon-docs';

<Meta title="Docs/Storybook/Overview" />

## Welcome to Siteboss Storybook!

Storybook can be used as a documentation hub, a development tool, a showcase, and a test suite.  As Storybook isn't capable of the powers of End to End testing like Cypress is, we can utilize all other aspects of Storybook

For a quick rundown about how to use Storybook, checkout our local [ClickUp Doc](https://app.clickup.com/9006034209/docs/8ccub91-1640).  This will be kept in ClickUp as it's easier to have pictures there. That doc goes over how to run storybook, how to create stories, details between args and arg types, and tips and tricks that can help to make robust stories.

For a full scale dive, there are, of course, [Storybook's own docs](https://storybook.js.org/docs/writing-stories)

## What can Storybook do for us?

1. **Provide a documentation hub**
   Storybook provides a way to clearly define what props a component takes in.  It also allows for a description to be provided for the component without relying entirely on comments throughout that aren't always easy to read at a glance.  It also allows for straight documentation to be provided as well, where we can also create our tutorials so the data is near to the components and easy to update as components change and grow.

   One of the nice features is that we can create different stories to display different variants and versions of a component for quick reference.  We can easily display an input in its base state, in an error state, and in a disabled state, all side by side for an easy glance.  We can do this with any component, just changing the props we pass to it when we display it, which we can do through args or rendering.

2. **Development Tool**
   Probably the most important, Storybook is used as a development tool.  Storybook allows you to spell out each and every prop a component can make use of.  In putting the component in isolation, will also let you know if there's a problem with that component when it's by itself or missing props.  The documentation being side by side with the component saves time on the development side in both reference and in updating the docs.  It also allows you to see what happens when props change, what happens if you're missing a prop, and, of course, the different renders for different props.

3. **Showcase Features to future clients in an interactive way**
   And finally, we can use storybook as a way to showcase certain stories as features to prospective clients.  "Want to play with some of the things we can offer you? See them in different sizes and colors?"  "Want a quick snippet of what this feature looks like in action?"  While not every perspective client would want to play with the things we've created, future clients who may be seeking additional features may appreciate seeing some of the features in isolation without having to create accounts, get a full demo, or learn how to use the system in its entirety.  In creating more prop driven common components, it would also be much easier to mock up some things in a style/theme for a perspective client in storybook than it would be in an actual codebase.  And there's no harm or need for another environment.

## Storybook Aspects

Storybook has access to the theme provider, redux, router context, and the api provider. These are wrapped around the decorator so that they're applied to each story. Eventually, we should add the different themes to be able to quickly and easily swap out the company theme to be able to view themes on the fly for a company to ensure a component looks proper in all sorts of different set ups.

We have the power to hide/show stories as we desire using tags.  In the 'manager.js' file, we can set a filter to filter what tags we want to show.  We could add tags for features to each component (example, "common", "transactions", "required") in line with features for permissions.  That way if we want to showcase to someone "Here's what we can offer you with our transaction feature", Storybook can quickly and easily be built/shown in a way that highlights only that feature and nothing else.

## Ways to use Storybook

Each component can be documented individually, which allows for great detail.  However, if some components work together or you have small subcomponents that don't need very much documanation, you can document them as subcomponents.  See the Dashboard story as an example.  It has subcomponent instead of documenting all those subcomponents individually.  The downside to doing it this way is that we lack detail on the smaller components and lack the ability to manually adjust the props in storybook.
