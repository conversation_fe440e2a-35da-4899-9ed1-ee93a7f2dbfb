import { ArticleOutlined as ArticleIcon } from '@mui/icons-material';

export const widgetIcon = ArticleIcon;
export const layouts = [
    {
        id: 1,
        name: 'Standard',
        icon:  <ArticleIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            cmsStack: {
                spacing: 3,
            },
            title: {
                variant: 'h1',
                gutterBottom: true,
            },
            meta: {
                variant: 'subtitle2',
                color: 'text.secondary',
                gutterBottom: true,
            },
            content: {
                variant: 'body1',
            },
            images: {
                mb: 3,
            }
        }
    }
];