import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack } from '@mui/material';
import { createCurrencyFormatter, capitalize, formatRecurringItem } from '../../../../../../../utils';
import { Caption, RadioWrapper } from '../../../../../common/pos';
import CustomPrice from '../CustomPrice';

export const Radio = ({ items, selected, product_type_id, disabled, onSelect, slots, slotProps, ...props }) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);    
    const currencyFormatter = createCurrencyFormatter(language, currency);
    
    return (
        <RadioWrapper items={items} selected={selected} onChange={onSelect} slots={slots} slotProps={slotProps} disabled={disabled}>
            {items.map(item => {
                let price = +item.price;
                let recurringInfo = "";
                let isCustomAmount = false, name = item.name.toLowerCase() === "default" ? t("pos:fullPrice") : item.name;
                if (+item.price === 0 && +product_type_id === 12) {
                    isCustomAmount = true;
                    name = t("giftCard:customAmount");
                }
                if (+item.activation_fee > 0) {
                    price = +item.activation_fee;
                    let text = formatRecurringItem(item);
                    if (text?.length > 0) {
                        if (+item.price > 0) text = [`+${currencyFormatter.format(item.price, currency)}`, ...text];
                        recurringInfo = capitalize(text?.map(info => t(info))?.join(' ').toLocaleLowerCase());
                    }
                }
                
                return (
                    <React.Fragment key={item.id}>
                        <Stack direction="column" spacing={0} sx={{flexGrow: 1, py: 1}}>
                            <Caption variant="body1" component="span" text={name} />
                            {recurringInfo && <Caption variant="subtitle3" component="span" text={recurringInfo} />}
                        </Stack>
                        {isCustomAmount 
                            ? <CustomPrice item={item} onSelect={onSelect} slotProps={slotProps} label={null} /> 
                            : price && <Caption variant="body1" component="span" /*bold*/ text={currencyFormatter.format(price, currency)} />
                        }
                    </React.Fragment>
                );
            })}
        </RadioWrapper>
    );
};