import React from 'react';
import { useTranslation } from 'react-i18next';
import { MobileStepper, Button, useTheme } from '@mui/material';
import { KeyboardArrowLeftOutlined as KeyboardArrowLeftIcon, KeyboardArrowRightOutlined as KeyboardArrowRightIcon, DoneAllOutlined as SaveIcon} from '@mui/icons-material';

export const Mobile = ({
    loading,
    hasErrors,
    stepList,
    activeStep,
    onStepChange,
    onSubmit,
    onNext,
    onBack,
    onReset,
    onStep,
    visitedSteps = [],
    isEditing = false,
    variant,
    ...props
}) => {
    const { t } = useTranslation();
    const theme = useTheme();

    return (
        <MobileStepper
            variant={variant || "progress"}
            steps={stepList?.length || 0}
            activeStep={activeStep}
            position="static"
            sx={{ flexGrow: 1, zIndex: theme => theme.zIndex.appBar}}
            nextButton={
                <Button size="small" onClick={activeStep === stepList.length - 1 ? onSubmit : onNext} disabled={loading || hasErrors}>
                    {activeStep === stepList.length - 1 && <SaveIcon fontSize='small' sx={{mr: 0.5}} />}
                    {activeStep === stepList.length - 1 ? t('general:save') : t('general:next')}
                    {activeStep !== stepList.length - 1 &&
                        (theme.direction === 'rtl' ? <KeyboardArrowLeftIcon /> : <KeyboardArrowRightIcon />)
                    }
                </Button>
            }
            backButton={
                <Button size="small" onClick={onBack} disabled={activeStep === 0}>
                    {theme.direction === 'rtl' ? <KeyboardArrowRightIcon /> : <KeyboardArrowLeftIcon />}
                    {t('general:back')}
                </Button>
            }
        />
    );
}