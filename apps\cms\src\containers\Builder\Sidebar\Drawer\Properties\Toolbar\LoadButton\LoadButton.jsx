import React, { useState, useCallback, useEffect } from 'react';
import { IconButton, Menu, MenuItem, Tooltip, Stack } from '@mui/material';
import { DeleteOutlined as DeleteIcon } from '@mui/icons-material';

export const LoadButton = ({
    selectedSectionId,
    onLoad,
    onDelete,
    slots,
    items = [],
    children,
    ...props
}) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const [open, setOpen] = useState(false);

    const Wrapper = slots?.wrapper || Tooltip;
    const Button = slots?.button || IconButton;

    const handleClick = e => {
        e.preventDefault();
        e.stopPropagation();
        if (e.currentTarget){
            setAnchorEl(e.currentTarget);
            setOpen(true);            
        }
    }
    
    const handleClose = e => {
        setAnchorEl(null);
        setOpen(false);
    }

    const handleDelete = useCallback(widgetId => async e => {
        e.preventDefault();
        e.stopPropagation();
        setAnchorEl(null);
        setOpen(false);
        if (onDelete) await onDelete(widgetId);
    }, [onDelete]);

    const handleLoad = useCallback(widgetId => async e => {
        e.preventDefault();
        e.stopPropagation();
        setAnchorEl(null);
        setOpen(false);
        if (onLoad) {
            let content = null;
            const item = items.find(item => item.id === widgetId);
            if (item) content = item.content;
            await onLoad({sectionId: selectedSectionId, widgetId, content});
        }
    }, [onLoad, selectedSectionId, items]);

    useEffect(() => {
        setAnchorEl(null);
        setOpen(false);
    }, [selectedSectionId]);

    if (!items.length) return null;

    return (
        <>
            <Wrapper>
                <Button 
                    onClick={handleClick}
                    id="load-widget-button"
                    aria-controls={open ? 'load-widget-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                >
                    {children}
                </Button>
            </Wrapper>
            <Menu
                id="load-widget-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{'aria-labelledby': 'load-widget-button'}}
            >
                {items.map(item => (
                    <MenuItem key={item.id} dense onClick={handleLoad(item.id)}>
                        <Stack spacing={3} direction="row" useFlexGap alignItems="center" justifyContent="space-between" width="100%">
                            {item.title}
                            <IconButton size="small" edge="end" onClick={handleDelete(item.id)}>
                                <DeleteIcon fontSize="inherit" />
                            </IconButton>
                        </Stack>
                    </MenuItem>
                ))}

            </Menu>
        </>
    );
}