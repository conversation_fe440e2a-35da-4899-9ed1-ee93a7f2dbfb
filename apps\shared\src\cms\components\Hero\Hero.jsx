import React from 'react';
import { Link } from 'react-router-dom';
import { Box, Stack, Typography, Button, Container } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';
import styles from './Hero.module.scss';

export const Hero = ({
    id,
    image,
    height,
    title,
    titleVariant,
    body,
    bodyVariant,
    callToActionUrl,
    callToActionText,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        wrapper: {},        // MUI box props
        container: {},      // MUI container props
        //image: {},          // MUI box props
        title: {},          // MUI typography props        
        body: {},           // MUI typography props
        callToAction: {},   // MUI button props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, customCss, canRender, t, noContent } = prepareComponent({name: "hero", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {!image && !title && !body && !callToActionUrl ? noContent :
                <Stack 
                    direction="row"
                    className={styles[`wrapper-${layoutId}`]}
                    {...slotProps?.wrapper}
                    sx={{...slotProps?.wrapper?.sx, height: height || { xs: '70vh', md: '100vh' }, backgroundImage: image && layoutId === 1 && `url(${image})`}}
                >
                    {layoutId === 2 && image &&
                        <Box 
                            className={styles.wrapper}
                            {...slotProps?.image}
                            sx={{...slotProps?.image?.sx, height: height || { xs: '70vh', md: '100vh' }, backgroundImage: `url(${image})`}}
                        />
                    }

                    <Container 
                        {...slotProps?.container} 
                        sx={{...slotProps?.container?.sx, position: 'relative', zIndex: theme => theme.zIndex.drawer}}
                    >
                        {title &&
                            <Typography 
                                variant={titleVariant || "h1"} 
                                component="h1" 
                                gutterBottom {...slotProps?.title}
                            >
                                {title}
                            </Typography>
                        }
                        {body &&
                            <Typography 
                                variant={bodyVariant || "body1"} 
                                component="div" 
                                gutterBottom 
                                {...slotProps?.body} 
                                style={{whiteSpace: 'pre-line'}}
                            >
                                {body}
                            </Typography>
                        }
                        {callToActionUrl && 
                            <Button 
                                component={Link}
                                variant="contained" 
                                size="large" 
                                to={callToActionUrl || "#!"} 
                                {...slotProps.callToAction}
                                style={{pointerEvents: isBuilder ? "none" : "auto"}}
                            >
                                {callToActionText || t("general:learnMore")}
                            </Button>
                        }
                    </Container>

                    {layoutId === 3 && image &&
                        <Box 
                            className={styles.wrapper}
                            {...slotProps?.image}
                            sx={{...slotProps?.image?.sx, height: { xs: '70vh', md: '100vh' }, backgroundImage: `url(${image})`}}
                        />
                    }
                </Stack>
                }
            </Stack>
        </CmsContainer>
    );
};