import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Stack, useMediaQuery } from '@mui/material';
import { TimelineDot } from '@mui/lab';
import { format, isToday } from 'date-fns';
import { formatDate } from '../../../../utils';

export const Event = ({ 
    currentDate, // the current date
    event, // the event to render
    color, // the color to use for the event (optional)
    orderIndex = 0, // the order in which elements should be displayed (used to re-order elements on mobile)
    ...props // additional props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);

    if (!event) return null;

    return (
        <Stack 
            direction={{xs: "column", lg: "row"}}
            spacing={{xs: 1, lg: 2}}
            alignItems='center' 
            justifyContent='center'
        >
            {!isMobile &&
                <Box sx={{width: 16, py: 1}}>
                    {event.endDate && 
                        <TimelineDot 
                            color="primary" 
                            sx={{ 
                                backgroundColor: theme => color || (theme.palette.mode === "light" ? theme.palette.primary.dark : theme.palette.primary.light),
                                width: 16, 
                                height: 16, 
                                m: 'auto!important' 
                            }}
                        />
                    }
                </Box>
            }
            <Typography variant='h4' order={ orderIndex + (isMobile ? 1 : 0) } sx={{ width: isMobile ? 80 : 80 }} textAlign='center'>
                {isToday(event.startDate) ? t("calendar:today") : format(event.startDate, "d")}
            </Typography>
            <Typography variant='body2' order={ orderIndex + (isMobile ? 0 : 1) } sx={{ width: isMobile ? 80 : 80, textTransform: 'capitalize', m: '0!important' }} textAlign='center'>
                {formatDate(event.startDate, language.code, "MMM, EEE")}
            </Typography>
            <Typography variant='body2' order={ orderIndex + 2 } textAlign='center' sx={{ width: 60, m: '0!important' }}>
                {format(event.startDate, "h aaa")}
                {event.endDate && event.endDate.getDate() === event.startDate.getDate() && event.endDate.getTime() !== event.startDate.getTime() && 
                    <>
                        <br/>
                        {format(event.endDate, "h aaa")}
                    </>
                }
            </Typography>
        </Stack>        
    );
}