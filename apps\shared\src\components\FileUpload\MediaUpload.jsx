import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FileUpload } from './FileUpload';

/*
BASIC IMAGE UPLOAD WRAPPER
props:
    onFileDrop: function to be called when a file is dropped in the upload container
    onFileRemove: function to be called when a file is removed from the upload container
    url: the url of the images to display. Can be an object ({name, description, url}), a FILE, a url string, or an array of objects / FILEs / url strings
    loading: the loading state of the parent component
*/
export const MediaUpload = ({ onFileDrop, onFileRemove, url, loading, multiple = true, ...props }) => {
    const { t } = useTranslation();
    
    // create the preview thumbnails
    const previews = useMemo(() => {
        if (url) {
            let _files = url;
            if (!Array.isArray(url)) _files = [url];
            else _files = [...url];

            return _files.map(file => {
                let _data = null;
                // the file var can have 3 formats (sigh! I know!) so we handle them here
                if (file instanceof File) _data = { name: file.name, type: 'image', url: URL.createObjectURL(file) }; // if its a file (user just dragged the image)
                else if (typeof file === 'object') _data = { name: file?.description || "", type: 'image', url: file?.url, id: file?.id || undefined }; // if its an object (its comming from the api)
                else _data = { name: "", type: 'image', url: file }; // if its a string (just in case)
                return _data;
            });
        }
        return null;
    }, [url]);

    // do stuff when a file is dropped in the upload container
    const handleFileDrop = useCallback(files => {
        if (files.length) onFileDrop(files);
    }, [onFileDrop]);

    // do stuff when a file is removed from the upload container
    const handleFileRemove = useCallback(file => {
        if (file) onFileRemove(file);
    }, [onFileRemove]);

    return (
        <FileUpload 
            accept='image/*' 
            size={5242880}
            onFileDrop={handleFileDrop}
            onFileRemove={handleFileRemove}
            withPreviews
            multiple={multiple}
            loading={loading}
            previews={previews || undefined}
            message={props?.message || t('file:dropImageOrClick')}
            sx={props?.sx}
        />
    );
}