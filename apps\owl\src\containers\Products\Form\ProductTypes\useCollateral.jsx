import React, { useMemo } from "react";

const rowProps = {
    row: 3,
    rowSize: {xs: 12, sm: 6, md: 4, lg: 3},
    margin: 'normal',
    group: 'collateral',
};

const fields = [
    {name: 'collateral_type', label: 'product:collateral.type', required: true, value: '', component: "Select", options: [], ...rowProps},
    {name: 'slot_count', label: 'product:slotCount', required: false, value: 0, component: "NumberField", ...rowProps},
    {name: 'slot_equivalent', label: 'product:slotEquivalent', required: false, value: '', component: "Select", options: [], ...rowProps},
{name: 'dimensions', type: 'number', label: 'product:dimensions', required: false, component: "Measurement", value: {width: 0, height: 0, length: 0, weight: 0}, measurements: ['width', 'height', 'length', 'weight'], unit: ['in', 'in', 'in', 'lbs'], gridSize: 3, ...{...rowProps, rowSize: {xs: 12}, margin: 'dense'}},
];

export const useCollateral = ({rowId = 3}) => {
    const _fields = useMemo(() => fields.map(field => ({...field, row: rowId})), [rowId]);
    
    return {
        fields: _fields,
    }
};