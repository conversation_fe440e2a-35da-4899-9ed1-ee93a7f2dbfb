import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Container,
  Paper,
  Tabs,
  Tab,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Switch,
  FormControlLabel,
  Chip
} from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

// Sample data for carriers
const carriers = [
  { id: 1, name: 'FedEx', services: ['Ground', 'Express', '2Day'], active: true, lastUpdated: '2023-04-15' },
  { id: 2, name: 'UPS', services: ['Ground', 'Next Day Air', '3 Day Select'], active: true, lastUpdated: '2023-04-10' },
  { id: 3, name: 'USPS', services: ['First Class', 'Priority', 'Express'], active: false, lastUpdated: '2023-03-22' },
  { id: 4, name: 'DHL', services: ['Express', 'International'], active: true, lastUpdated: '2023-04-05' },
];

// Sample data for rate tables
const rateTables = [
  { id: 1, name: 'Standard Domestic Rates', carrier: 'FedEx', effectiveDate: '2023-01-01', expirationDate: '2023-12-31' },
  { id: 2, name: 'Express International', carrier: 'DHL', effectiveDate: '2023-02-15', expirationDate: '2023-12-31' },
  { id: 3, name: 'Ground Services', carrier: 'UPS', effectiveDate: '2023-03-01', expirationDate: '2023-12-31' },
];

export const CarrierConfigurations = (props) => {
  const { t } = useOutletContext();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container>
      <Title 
        title={t('carriers:carrierConfigurations')}
        breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('carriers:carrierConfigurations')}]}
      />
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="carrier configuration tabs">
          <Tab label="Carriers" />
          <Tab label="Rate Tables" />
          <Tab label="Service Options" />
        </Tabs>
      </Paper>

      {/* Carriers Tab */}
      {tabValue === 0 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Carriers</Typography>
            <Button variant="contained" color="primary">Add Carrier</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Carrier</TableCell>
                  <TableCell>Services</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {carriers.map((carrier) => (
                  <TableRow key={carrier.id}>
                    <TableCell>{carrier.name}</TableCell>
                    <TableCell>
                      {carrier.services.map((service, index) => (
                        <Chip 
                          key={index} 
                          label={service} 
                          size="small" 
                          sx={{ mr: 0.5, mb: 0.5 }} 
                        />
                      ))}
                    </TableCell>
                    <TableCell>
                      <FormControlLabel
                        control={
                          <Switch 
                            checked={carrier.active} 
                            size="small"
                          />
                        }
                        label={carrier.active ? "Active" : "Inactive"}
                      />
                    </TableCell>
                    <TableCell>{carrier.lastUpdated}</TableCell>
                    <TableCell>
                      <Button size="small">Configure</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Rate Tables Tab */}
      {tabValue === 1 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Rate Tables</Typography>
            <Button variant="contained" color="primary">Add Rate Table</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Carrier</TableCell>
                  <TableCell>Effective Date</TableCell>
                  <TableCell>Expiration Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rateTables.map((table) => (
                  <TableRow key={table.id}>
                    <TableCell>{table.name}</TableCell>
                    <TableCell>{table.carrier}</TableCell>
                    <TableCell>{table.effectiveDate}</TableCell>
                    <TableCell>{table.expirationDate}</TableCell>
                    <TableCell>
                      <Button size="small">View</Button>
                      <Button size="small" color="primary" sx={{ ml: 1 }}>Edit</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Service Options Tab */}
      {tabValue === 2 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Service Options</Typography>
            <Button variant="contained" color="primary">Configure Options</Button>
          </Box>
          <Typography variant="body1" paragraph>
            Configure additional service options such as insurance, signature requirements, and delivery preferences.
          </Typography>
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>Global Settings</Typography>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Enable address validation"
            />
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Show estimated delivery dates"
            />
            <FormControlLabel
              control={<Switch />}
              label="Require signature for all shipments"
            />
          </Box>
        </Paper>
      )}
    </Container>
  );
}
