import React, { useMemo, useCallback, useContext, useEffect, useRef, useState, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Stack, Button, Container } from '@mui/material';

import { Modal } from '../../../components';
import { prepareComponent, CmsContainer, usePageRouter } from '../../utils';
import { PosContext, useProducts, useProductEffects } from '../../hooks';
import { toKebabCase, formatSlug } from '../../../utils';

import ProductDetail from '../ProductDetail';

import { layouts, widgetIcon } from './Layouts';
import { properties } from './properties';

export const Products = memo(({
    id,
    shopId, // this will tell us the routes of the other pages we need to see product datails, cart page, etc
    layoutType,
    fullPage = true,
    modalSize = "sm",
    itemsToLoad = 30,
    galleryLayoutType = 'carousel', // carousel, list, masonry, 
    variantsLayoutType = 'radio', // radio, button
    addonsLayoutType = 'checkbox', // radio, checkbox, button
    eventUsersLayoutType = 'checkbox', // button, radio, checkbox
    memoLayoutType = 'link', // link, button
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        itemContainer: {},  // MUI stack props
        card: {},           // MUI card props
        button: {},         // MUI button props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const navigate = useNavigate();
    const { slotProps: updatedSlotProps, customCss, canRender, noContent, isMobile, t } = prepareComponent({name: "products", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const user = useSelector(state => state.user);
    const { shopId: contextShopId, handleAddToCart, LoginForm } = useContext(PosContext) || {};
    const { router } = usePageRouter({pageRouterId: shopId || contextShopId});

    const containerRef = useRef(null);
    const thresholdRef = useRef(null);

    const [openProductDetail, setOpenProductDetail] = useState(false);

    const Layout = useMemo(() => layouts.find(a => a.id === layoutId)?.component, [layoutId]);

    const {
        products,
        allProducts,
        loading,
        setLoading,
        ErrorBar,
        fetchData,
        setFilteredProducts,
        selectedProduct,
        handleSelectProduct,
    } = useProducts({itemsToLoad: isBuilder ? 6 : itemsToLoad, isBuilder});

    const { hasMore, fetchMore } = useProductEffects({
        allProducts, 
        itemsToLoad, 
        loading, 
        setLoading, 
        setFilteredProducts,
        containerRef, 
        thresholdRef,
        isBuilder,
    });

    const handleProductClick = useCallback(item => {
        if (isBuilder) return;
        if (!user?.token) {
            setOpenProductDetail(true);
            return;
        }
        if (item.id) handleSelectProduct(item);
        if (fullPage) {
            let route;
            if (item?.id) {
                if (router?.product) {
                    route = formatSlug(`${router?.slug?.value}/${router?.product?.value}/${item.id}/${toKebabCase(item.name)}`);
                }
                if (item?.events?.length > 0 && router?.event) {
                    route = formatSlug(`${router?.slug?.value}/${router?.event?.value}/${item.events[0]?.id}/${toKebabCase(item.events[0]?.name)}`);
                }
                navigate(`/${route}`);
            }
        } else setOpenProductDetail(true);
    }, [router, navigate, fullPage, isBuilder, user?.token, handleSelectProduct]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            //ref={containerRef}
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {(!shopId && !contextShopId && !products.length) ? noContent :
                    <>
                        <ErrorBar />
                        <Layout
                            layoutType={layoutType}
                            disabled={loading}
                            items={products}
                            slotProps={slotProps}
                            onAddToCart={handleAddToCart}
                            onSelect={handleProductClick}
                            isBuilder={isBuilder}
                        />
                        <Container  sx={{textAlign: "center", py: isBuilder ? 0 : 2}}>
                            {hasMore && !isBuilder &&
                                <Button variant="text" size="xs" disabled={loading} loading={loading} onClick={fetchMore}>
                                    {t("general:showMore")}
                                </Button>
                            }
                        </Container>
                        <Modal 
                            open={openProductDetail} 
                            onClose={e => setOpenProductDetail(false)}
                            maxWidth={modalSize || "sm"}
                            fullScreen={isMobile}
                            aria-describedby={t(user?.token ? `product:productDetails` : `login:login`)}
                        >
                            {user?.token ?
                                <ProductDetail 
                                    loadType={2} 
                                    productId={selectedProduct?.id} 
                                    variantId={selectedProduct?.product_variants?.[0]?.id}
                                    fullPage={false}
                                    modalSize={modalSize}
                                    onAddToCart={handleAddToCart}
                                    slotProps={slotProps}
                                    gallery={{layoutType: galleryLayoutType}}
                                    variants={{layoutType: variantsLayoutType}}
                                    addons={{layoutType: addonsLayoutType}}
                                    eventUsers={{layoutType: eventUsersLayoutType}}
                                    memo={{layoutType: memoLayoutType}}
                                />
                            : <LoginForm />}
                        </Modal>
                    </>
                }
            </Stack>
            {children}
            <div ref={thresholdRef} />
        </CmsContainer>
    );
});
