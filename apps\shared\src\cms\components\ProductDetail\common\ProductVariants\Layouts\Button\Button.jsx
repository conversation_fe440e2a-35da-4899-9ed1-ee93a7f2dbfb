import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack } from '@mui/material';
import { createCurrencyFormatter, capitalize, formatRecurringItem } from '../../../../../../../utils';
import { ButtonWrapper, Caption } from '../../../../../common/pos';
import CustomPrice from '../CustomPrice';

export const Button = ({ items, selected, product_type_id, disabled, onSelect, slots, slotProps, ...props }) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    return (
        <ButtonWrapper items={items} selected={[{id: selected}]} disabled={disabled} onClick={onSelect} slots={slots} slotProps={slotProps}>
            {items.map(item => { 
                let price = +item.price;
                let recurringInfo = "";
                let isCustomAmount = false, name = item.name.toLowerCase() === "default" ? t("pos:fullPrice") : item.name;
                if (+item.price === 0 && +product_type_id === 12) {
                    isCustomAmount = true;
                    name = t("giftCard:customAmount");
                }
                if (+item.activation_fee > 0) {
                    price = +item.activation_fee;
                    let text = formatRecurringItem(item);
                    if (text?.length > 0) recurringInfo = capitalize(text?.map(info => t(info))?.join(' ').toLocaleLowerCase());
                }
                return (
                    <React.Fragment key={item.id}>
                        <Stack direction="column" spacing={0} sx={{flexGrow: 1, py: 1}}>
                            <Caption variant="subtitle1" component="span" text={name} bold={Boolean(recurringInfo)} />
                            {recurringInfo && <Caption variant="subtitle2" component="span" text={recurringInfo} />}
                        </Stack>
                        {isCustomAmount 
                            ? <CustomPrice item={item} onSelect={onSelect} label={null} slotProps={{...slotProps, customAmount: {size: "large", variant: "filled"}, container: {sx: {mt: 2, width: "100%"}}}} />
                            : price && <Caption variant="subtitle2" component="span" text={currencyFormatter.format(price, currency)} bold />
                        }
                    </React.Fragment>
                );
            })}
        </ButtonWrapper>
    );
};