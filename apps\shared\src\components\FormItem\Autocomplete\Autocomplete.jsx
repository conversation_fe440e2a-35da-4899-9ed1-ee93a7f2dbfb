import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Autocomplete as MuiAutocomplete, TextField, Chip, CircularProgress } from '@mui/material';

import { debounceAsync } from '../../../utils';

const optionCompare = (option, value) => {
    if (!value) return false;
    if (Array.isArray(value)) return value.some(v => v?.id === option?.id || v === option);
    if (value?.id ) return value.id === option?.id || value.id === option;
    return value === option?.id || value === option;
}

export const Autocomplete = ({
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors: initialErrors, // the errors for the field
    disabled, // if the field is disabled
    loading: loadingState, // the loading status of the form so we can disable the fields
    value: initialValue, // the value of the field
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onClear, // the function to be called when the field is cleared
    options: fieldOptions, // the options for the autocomplete field
    multiple, // if the field should allow multiple selections
    filterSelectedOptions, // if the field should filter selected options from the options list
    getOptionLabel, // the function to get the label of the option
    selectOnFocus, // if the field should select the option on focus
    clearOnBlur = false, // if the field should clear the input value on blur
    optionFormat, // function to format the options of the autocomplete
    renderOption, // function to render the options of the autocomplete
    freeSolo = false, // if the field should allow free text
    fullWidth = true, // if the field should be full width

    // the following props are used to fetch data from the server
    isAsync = false, // if true, the field will fetch data from the server
    fetchData, // function to fetch data
    fetchParams, // object with parameters to be sent to the fetch function, for example: {event_id: 1, status_id: 2}
    getSearchParams, // function that accepts a search word and returns the parameters to be sent to the fetch function, for example: (search) => ({filter: {search_words: search}})
    dataField, // the name of the field in the response data object that contains the data to be displayed, for example: users for response.data.users

    ...props
}) => {
    const { t } = useTranslation();

    const [loading, setLoading] = useState(loadingState || false);
    const [errors, setErrors] = useState(initialErrors || null);
    const [options, setOptions] = useState(fieldOptions || []);
    const [inputValue, setInputValue] = useState('');
    const [value, setValue] = useState(multiple ? [] : null);

    //const selectedValue = useMemo(() => value || (multiple ? [] : null), [value, multiple]);

    const handleChange = useCallback((e, newValue, reason) => {
        if (clearOnBlur){
            setInputValue('');
            setValue(null);
        }
        if (reason === 'clear') {
            if (onClear) onClear();
        } 
        if (onChange) onChange({preventDefault: e.preventDefault, target: { name, value: newValue }});
    }, [name, onChange, onClear, clearOnBlur]);

    const getData = useCallback(debounceAsync(async search => {
        setLoading(true);
        let _options = [];
        try {
            let searchParams = {};
            if (getSearchParams) searchParams = getSearchParams(search);
            if (fetchData){
                const result = await fetchData({...fetchParams, ...searchParams});
                if (result){
                    if (result?.errors) setErrors(result?.errors);
                    else if (result?.data){
                        if (dataField && result.data?.[dataField]) _options = result.data[dataField];
                        else if (Array.isArray(result.data)) _options = result.data;
                    }
                }
            }
        } catch (error) {
            setErrors(error?.message || error);
        } finally {
            setOptions(_options || []);
            setLoading(false);
        }
    }, 300), [fetchData, fetchParams, dataField, getSearchParams]);

    const handleInputChange = useCallback((e, newInputValue, reason) => {
        let _inputValue = newInputValue;
        if (options){
            const selectedOption = options?.find(option => optionCompare(option, newInputValue));
            if (selectedOption) {
                _inputValue = selectedOption?.slug ? t(selectedOption.slug) : (selectedOption?.name || newInputValue);
            }
        }
        setInputValue(_inputValue);
        if (isAsync) getData(_inputValue);
    }, [getData, isAsync, options, t]);

    const defaultGetOptionLabel = useCallback(option => {
        if (!option) return '';
        if (optionFormat && typeof optionFormat === 'function') {
            return optionFormat(option);
        }
        return option?.name || option?.slug || option.toString();
    }, [optionFormat]);

    useEffect(() => {
        if (inputValue === '' && clearOnBlur) {
            setOptions(fieldOptions || []);
        }
    }, [inputValue, clearOnBlur]);

    useEffect(() => {
        if (fieldOptions && fieldOptions?.length) {
            setOptions(fieldOptions);
        }
    }, [fieldOptions]);

    useEffect(() => {
        setValue(initialValue);
        const selectedOption = options?.find(option => optionCompare(option, initialValue));
        setInputValue(selectedOption?.slug ? t(selectedOption.slug) : (selectedOption?.name || ''));
        setLoading(false);
    }, [initialValue, options, t]);

    useEffect(() => {
        setErrors(initialErrors);
    }, [initialErrors]);

    return (
        <MuiAutocomplete
            autoComplete
            multiple={multiple || false}
            selectOnFocus={selectOnFocus || true}
            //clearOnBlur={clearOnBlur || false}
            handleHomeEndKeys
            options={options || []}
            getOptionLabel={getOptionLabel || defaultGetOptionLabel}
            isOptionEqualToValue={optionCompare}
            renderOption={(props, option) => {
                if (renderOption) return renderOption(props, option);
                return (
                    <li {...props} key={option?.key || JSON.stringify(option)} style={{paddingLeft: option?.depth ? option.depth * 32 : 16}}>
                        {option?.slug ? t(option.slug) : (option?.name || option)}
                    </li>
                );
            }}
            renderTags={(tagValue, getTagProps) => {
                if (!tagValue?.length) return null; 
                return tagValue?.map?.((option, index) => {
                    const {key, ...params} = getTagProps({ index });
                    return (
                        <Chip 
                            key={key || JSON.stringify(option)} 
                            id={option?.id || JSON.stringify(option)}
                            label={option?.slug ? t(option.slug) : (option.name || option)} 
                            {...params} 
                        />
                    )
                }
            )}}
            filterSelectedOptions={filterSelectedOptions || false}
            filterOptions={x => x}
            onChange={(e, newValue, reason) => {
                setValue(newValue);
                handleChange(e, newValue, reason);
            }}
            onInputChange={handleInputChange}
            value={value}
            loading={loading}
            inputValue={inputValue}
            disabled={disabled}
            loadingText={t('general:loading')}
            noOptionsText={t('general:noResults')}
            clearText={t('general:clear')}
            closeText={t('general:close')}
            openText={t('general:open')}
            freeSolo={freeSolo}
            fullWidth={fullWidth === false ? false : true}
            renderInput={params => (
                <TextField
                    {...params}
                    label={t(label)}
                    name={name}
                    value={inputValue}
                    required={required || false}
                    variant="outlined"
                    fullWidth={fullWidth === false ? false : true}
                    error={!!errors}
                    helperText={errors || undefined}
                    margin={props.margin || undefined}
                    slotProps={{
                        ...params?.slotProps,
                        input: {
                            ...props?.slotProps?.input,
                            ...params.InputProps,
                            endAdornment: (
                                <>
                                    {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                    {params.InputProps.endAdornment}
                                </>
                            ),
                        }
                    }}                    
                />
            )}
            sx={props.sx || undefined}
        />
    );
}