export const formatPageSlug = slug => {
	slug = slug.replace(/^\/|\/$/g, '');
	slug = slug.split("/").filter(el => el !== "");
	slug = slug.join("/");
	return slug;
}

export const getFirstSlugPart = slug => {
    slug = slug.replace(/^\/|\/$/g, '');
    const firstPart = slug.split('/')[0];
    return firstPart || "";
}

export const removeFirstSlash = str => {
	if (str.charAt(0) === '/') {
		return str.slice(1);
	}
	return str;
}

export const getAbsolutePath = (item, lookup) => {
	if (!item.parent_id || !lookup[item.parent_id]) return formatPageSlug(item.slug);
	const parentPath = getAbsolutePath(lookup[item.parent_id]);
	return formatPageSlug(parentPath === '/' ? 
		`/${item.slug}` : 
		`${parentPath}/${item.slug}`.replace(/\/+/g, '/'));
}

export const buildTree = items => {
	const lookup = {};
	const tree = [];
  
	items.forEach(item => {
		lookup[item.id] = { ...item, children: [] };
	});
  
	items.forEach(item => {
	  	if (item.parent_id) {
			// if parent exists in our lookup, attach as child.
			if (lookup[item.parent_id]) {
				lookup[item.parent_id].children.push(lookup[item.id]);
			}
			
			// if parent isn’t in our items but its definition is provided on the child.
			else if (item.parent && item.parent.id) {
				if (!lookup[item.parent.id]) {
					// create a dummy parent node from the child's parent definition.
					lookup[item.parent.id] = { ...item.parent, children: [] };
					tree.push(lookup[item.parent.id]);
				}
				lookup[item.parent.id].children.push(lookup[item.id]);
			} else {
				// fallback: if no parent info, treat as root.
				tree.push(lookup[item.id]);
			}
	  	} else {
			// no parent_id means a root node.
			tree.push(lookup[item.id]);
	  	}
	});
	return tree;
}
  
// recursively create a page definition, even if a section has a pageId
export const getPageDefinition = async ({section}, visitedPageIds, apiUrl, logData, logError) => {
	if (!section.pageId && !section.component) return null;
	if (section.id) logData(`Processing section: ${section.id}`);
	if (section.pageId) logData(`Processing page id: ${section.pageId}`);

	if (section.component) {
		const children = (await Promise.all(
			section.component?.content?.children?.map(async (child, i) => {
			try {
				return await getPageDefinition({section: child}, visitedPageIds, apiUrl, logData, logError);
			} catch (error) {
				logError(error);
				return null;
			}
			}) || []
		)).filter(Boolean);
  
		if (children.length) section.component.content.children = children;
		return section;
	} else if (section.pageId) {
		if (visitedPageIds.has(section.pageId)) return null;
		visitedPageIds.add(section.pageId);
		try {
			logData(`Fetching page definition for pageId: ${section.pageId}`);
			const res = await fetch(`${apiUrl}/cms/site/page/${section.pageId}`, {
				method: 'POST',
				headers: {'Content-Type': 'application/json'},
			});
			const pageData = await res.json();
			if (pageData.error) throw new Error(pageData.error);
			else if (pageData?.data?.[0]?.content) {
				let _content = pageData.data[0].content;
				if (_content && !Array.isArray(_content)) _content = [_content];
		
				return (await Promise.all(
					_content?.map(async (block, i) => {
						try {
							return await getPageDefinition({section: block}, visitedPageIds, apiUrl, logData, logError);
						} catch (error) {
							logError(error);
							return null;
						}
				}) || [])).filter(Boolean);
			}
		} catch (error) {
			logError(error);
		}
	}
	return null;
};


// get theme, company, and page data
export const getThemeInfo = async (apiUrl, logData, logError, host = null) => {
	const _return = {data: null, errors: null};
	try {
		logData(`Fetching theme data for ${host}...`);
		const headers = { 'Content-Type': 'application/json' };
		if (host) headers['referer'] = `https://${host}/`;
		
		const themeRes = await fetch(`${apiUrl}/cms/my_theme?cms_version=2`, { method: 'GET', headers });
		const themeData = await themeRes.json();
		if (themeData.error) {
			_return.errors = themeData.error;
			logError(themeData.error);
		} else if (themeData.data){
			_return.data = themeData.data?.[0];
		}
	} catch (e) {
		_return.errors = e;
		logError(e);
	} 
	return _return;
}

export const findPage = (slug, pages) => {
	if (!slug) return null;
	const page = pages?.find(a => a.full_slug === slug);
	if (page) return page;
	const newSlug = slug.split('/').slice(0, -1).join('/');
	return findPage(newSlug, pages);
}

// get theme, company, and page data
export const getInfo = async ({url, infoData, host = null}, apiUrl, logData, logError) => {
	let themeData = infoData || null;
	let company = null, pages = [], page = null, metadata = null, error = null, themeOverrides = null, indexPage = null;

	if (infoData) logData('Using cached theme data...');
	if (!themeData) {
		const { data, errors } = await getThemeInfo(apiUrl, logData, logError, host);
		if (errors) {
			error = errors;
			return { error };
		}
		themeData = data;
	}

	if (!themeData) return;

	company = themeData?.company;
	themeOverrides = themeData?.content?.v2;
	if (company) company.logo = themeData?.logo;

	// 404 page, but since we dont have it...
	//pageId = themeData.data[0]?.pages[0].id;
	const metadata404 = {title: "Page Not Found"};

	if (!themeData?.pages?.length){
		metadata = metadata404;
	} else {
		pages = themeData?.pages;

		const lookup = pages.reduce((acc, item) => {
			acc[item.id] = { ...item, children: [] };
			if (item?.parent) acc[item.parent_id] = { ...item.parent, children: [] };
			return acc;
		}, {});

		pages = pages.map(page => ({...page, full_slug: getAbsolutePath(page, lookup)}));

		if (themeData?.index_page){
			indexPage = pages?.find(a => a.full_slug === removeFirstSlash(themeData?.index_page));
		}

		if (url && url !== '/') {
			page = findPage(url, pages);
		}

		if (!page && themeData?.index_page){
			page = {...indexPage};
		}

		if (!page){
			metadata = metadata404;
		} else {
			metadata = {description: page.description, keywords: page.keywords, title: page.title};
		}
	}

	return {data: {company, pages, metadata, indexPage, themeOverrides}, error};
}