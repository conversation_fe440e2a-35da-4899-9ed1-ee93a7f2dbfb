# Material Symbols Component

This component provides an easy way to use Google's [Material Symbols](https://fonts.google.com/icons?icon.set=Material+Symbols) in your React application.

## Usage

### 1. Load the Material Symbols font

First, include the `MaterialSymbolsLoader` component in your application's `<head>` section. You can add it to your main layout component or use it in specific pages where you need Material Symbols:

```jsx
import { MaterialSymbolsLoader } from '@siteboss-frontend/shared/components';

function App() {
  return (
    <>
      <head>
        <MaterialSymbolsLoader />
      </head>
      <body>
        {/* Rest of your application */}
      </body>
    </>
  );
}
```

You can specify which variants to load:

```jsx
<MaterialSymbolsLoader variants={['outlined', 'rounded']} />
```

Alternatively, you can add the stylesheet link directly to your HTML template:

```html
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined&family=Material+Symbols+Rounded&family=Material+Symbols+Sharp&display=block" rel="stylesheet" />
```

### 2. Use the Material Symbol component

```jsx
import { MaterialSymbol } from '@siteboss-frontend/shared/components';

function MyComponent() {
  return (
    <div>
      <MaterialSymbol icon="home" />
      <MaterialSymbol icon="settings" variant="rounded" />
      <MaterialSymbol icon="favorite" fill={1} color="error.main" />
    </div>
  );
}
```

## Props

The `MaterialSymbol` component renders icons as square elements with centered content for consistent alignment and spacing. It accepts the following props:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `icon` | string | (required) | The name of the icon to display |
| `variant` | string | 'outlined' | The variant of the icon: 'outlined', 'rounded', or 'sharp' |
| `fill` | number | 0 | Whether the icon should be filled (0 or 1) |
| `weight` | number | 400 | The weight of the icon (100-700) |
| `grade` | number | 0 | The grade of the icon (-25 to 200) |
| `size` | string/number | 'md' | The size of the icon: 'xs', 'sm', 'md', 'lg', 'xl', or a custom size in px. This determines both width and height to ensure square icons |
| `color` | string | (inherit) | The color of the icon |
| `sx` | object | {} | Additional styles to apply to the icon |

## Example

See the `MaterialSymbolExample` component for a comprehensive demonstration of the various options.

```jsx
import { MaterialSymbolExample } from '@siteboss-frontend/shared/components/MaterialSymbol';

function Demo() {
  return <MaterialSymbolExample />;
}
```

## Finding Icons

Browse the available icons at [Google Fonts Material Symbols](https://fonts.google.com/icons?icon.set=Material+Symbols).

When you find an icon you want to use, note its name (displayed at the top of the icon details panel) and use that as the `icon` prop.
