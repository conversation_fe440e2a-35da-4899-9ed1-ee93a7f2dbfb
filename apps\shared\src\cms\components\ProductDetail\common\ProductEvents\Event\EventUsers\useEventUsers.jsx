import { useEffect, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { useApi } from '../../../../../../../api/useApi';

const apiParams = {enableCache: false, params: {endpoint: `/user/user/`, method: 'GET', params: {include_group_members: 1}}};

export const useEventUsers = ({event, userId = null, ...props}) => {
    const registerUser = useSelector(state => state.currentShopItem?.userId);

    const [user, setUser] = useState(null);
    const [family, setFamily] = useState([]);
    const [groups, setGroups] = useState([]);

    const { fetchData, loading, errors} = useApi(apiParams);

    const processUserData = useCallback(async (userId) => {
        if (!userId) return;
        try {
            const res = await fetchData({endpoint: `/user/user/${userId}`});
            if (res.data) {
                // transform the data for easier use and group family and groups in separate arrays
                if (res.data?.[0].groups?.length > 0){
                    const _family = [], _groups = [], _user = res.data?.[0];
                    let prevId = null;
                    res.data?.[0].groups?.forEach(group => {
                        const _data = group.group_members.map(member => ({
                            id: member.user_id,
                            dob: member.dob,
                            email: member.email,
                            first_name: member.first_name,
                            last_name: member.last_name,
                            group_member_id: member.group_member_id,
                            role_id: member.group_member_role_id,
                            role_name: member.group_member_role_name,
                            status_id: member.group_member_status_id,
                            status_name: member.group_member_status_name,
                            profile_img_path: member.profile_img_path,
                            updated_at: member.updated_at,
                            created_at: member.created_at,
                        }));
        
                        if (group.group_type_id === 4 && (!prevId || prevId === group.id)) {
                            prevId = group.id;
                            _family.push(..._data);
                        }
                        else _groups.push(..._data);
                    });
                    // remove duplicates
                    _family.filter((v, i, a) => a.findIndex(t => (t.id === v.id)) === i);
                    _groups.filter((v, i, a) => a.findIndex(t => (t.id === v.id)) === i);
        
                    setFamily(_family);
                    setGroups(_groups);
                    setUser(_user);
                }
                            
            }
        } catch(e) {
            console.error(e);
        }
    }, [fetchData]);

    useEffect(() => {
        processUserData(userId || registerUser);
    }, [processUserData, userId, registerUser]);

    return {
        user,
        family,
        groups,
        loading,
        errors,
    };
};
