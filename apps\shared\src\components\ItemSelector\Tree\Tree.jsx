import React, { useCallback } from 'react';
import { <PERSON>ack, Chip, Button, Typography, FormControlLabel, Checkbox, Collapse, Paper } from '@mui/material';

import { flattenTree } from '../../../utils';
import FormItem from '../../FormItem';

const Component = ({type, label, children, ...props}) => {
    return (
        <Stack direction="column">
            {type === "chip" && <Chip label={label} {...props} />}
            {type === "button" && <Button {...props}>{label}</Button>}
            {type === "checkbox" && <FormControlLabel control={<Checkbox {...props} />} label={props.size === "small" ? <Typography variant="body2">{label}</Typography>: label} />}
            {children}
        </Stack>
    );
};

export const Tree = ({ 
    items = [],
    type,
    variant,
    color,
    selected,
    selectedColor,
    onSelect,
    disabled,
    label,
    name,
    ...props
}) => {
    //if (!items.length) return null;

    const handleChange = useCallback(e => {
        let { value } = e.target;
        if (!Array.isArray(value)) value = [value];
        if (onSelect){
            if (!value?.length) onSelect(null)(e);
            value?.forEach(val => {
                onSelect(val)(e);
            });
        }
    }, [onSelect]);

    if (type === "autocomplete") {
        return (
            <FormItem
                component="Autocomplete"
                label={label}
                name={name}
                loading={disabled}
                value={selected}
                onChange={handleChange}
                options={flattenTree(items)}
                multiple
                filterSelectedOptions
                fullWidth
                {...props}
            />
        );
    } else {
        return items.map(item => (
            <Stack direction={"row"} spacing={1} flexWrap="wrap" useFlexGap key={item.id || item}>
                <Component
                    type={type}
                    label={item?.name || item} 
                    variant={(type === "chip" || type === "button") ? (variant || type === "button" ? 'outlined' : 'filled') : undefined}
                    color={type ==="chip" ? (selected.find(a => item?.id ? (a.id === item.id) : (a === item)) ? (selectedColor || 'primary') : (color || 'default')) : (color || 'default')}
                    onClick={onSelect(item)} 
                    disabled={disabled}
                    {...props}
                />
                {item?.children?.length > 0 && 
                    <Collapse in={Boolean(selected.find(a => item?.id ? (a.id === item.id) : (a === item)))} unmountOnExit>
                        <Paper variant="outlined" sx={{ p: 1, px: 2, width: '100%', height: '100%', overflow: 'auto' }}>
                            <Tree
                                items={item.children}
                                type={type}
                                variant={variant}
                                color={color}
                                selected={selected}
                                selectedColor={selectedColor}
                                onSelect={onSelect}
                                disabled={disabled}
                                {...props}
                            />
                        </Paper>
                    </Collapse>
                }
            </Stack>
        ));
    }
};