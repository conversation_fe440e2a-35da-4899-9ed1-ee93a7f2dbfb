import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { DataTable } from '@siteboss-frontend/shared/components';
import { useEventList } from './useEventList';

export const List = ({ onExpand, onDelete, setSelected, selected, fetchCounter = 0, loading:parentLoading, ...props}) => {
    const params = useParams();

    const {
        data,
        rows,
        columns,
        order,
        page,
        pageSize,
        totalPages,
        totalRows,
        loading,
        ErrorBar,
        LoadingBar,
        handleRowSelection,
        setSearchText,
        setOrder,
        setPage,
        setPageSize,
    } = useEventList({setSelected, fetchCounter, params});

    useEffect(() => {
        if (params.id && rows && (!selected || selected.length === 0 || !selected.find(r => +r.id === +params.id))) {
            setSelected(rows.filter(r => +r.id === +params.id));
        }
    }, [rows, params.id, setSelected, selected]);

    useEffect(() => {
        // Open drawer whenever there's a selection, regardless of params.id
        if (selected.length > 0 && onExpand) {
            onExpand();
            console.log('Opening drawer for selected event:', selected[0]);
        }
    }, [selected, onExpand]);

    return (
        <>
            <ErrorBar />
            {!data && <LoadingBar />}
            {data && rows &&
                <DataTable
                    hideFooterSelectedRowCount
                    rows={rows}
                    columns={columns}
                    rowSelectionModel={selected.map(s => s.id)}
                    setSelected={setSelected}
                    onRowClick={(params) => {
                        // Handle row click for selection
                        handleRowSelection([params.id]);
                        // Open the drawer to show event details
                        if (onExpand) onExpand();
                    }}
                    disableRowSelectionOnClick={false}
                    //rowHeight="auto"
                    onExpand={onExpand}
                    onDelete={onDelete}
                    order={order}
                    page={page}
                    pageSize={pageSize}
                    totalPages={totalPages}
                    totalRows={totalRows}
                    setPage={setPage}
                    setPageSize={setPageSize}
                    setOrder={setOrder}
                    setSearchText={setSearchText}
                    loading={loading || parentLoading}
                    /*customStyles={{
                        '& .MuiDataGrid-row': {
                            cursor: 'pointer',
                            '&:hover': {
                                backgroundColor: theme => theme.palette.action.hover,
                            }
                        },
                        '& .MuiDataGrid-row.Mui-selected': {
                            backgroundColor: theme =>
                                theme.palette.mode === 'dark'
                                    ? theme.palette.action.selected
                                    : theme.palette.primary.lighter || theme.palette.primary.light + '33',
                            '&:hover': {
                                backgroundColor: theme =>
                                    theme.palette.mode === 'dark'
                                        ? theme.palette.action.selected
                                        : theme.palette.primary.lighter || theme.palette.primary.light + '33',
                            }
                        },
                        '& .MuiDataGrid-cell': {
                            padding: '0px 10px',
                        },
                        '& .MuiDataGrid-virtualScroller': {
                            overflow: 'visible !important',
                        },
                        '& .MuiDataGrid-virtualScrollerContent': {
                            overflow: 'visible !important',
                        },
                        '& .MuiDataGrid-virtualScrollerRenderZone': {
                            overflow: 'visible !important',
                        }
                    }}*/
                />
            }
        </>
    );
}