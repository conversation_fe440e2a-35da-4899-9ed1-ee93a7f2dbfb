import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Container,
  Paper,
  Tabs,
  Tab,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  LinearProgress
} from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

// Sample data for email campaigns
const emailCampaigns = [
  { id: 1, name: 'Spring Promotion', status: 'Sent', sentDate: '2023-04-10', opens: 245, clicks: 68, recipients: 500 },
  { id: 2, name: 'New Service Announcement', status: 'Draft', sentDate: null, opens: 0, clicks: 0, recipients: 0 },
  { id: 3, name: 'Customer Satisfaction Survey', status: 'Scheduled', sentDate: '2023-05-15', opens: 0, clicks: 0, recipients: 350 },
];

// Sample data for SMS campaigns
const smsCampaigns = [
  { id: 1, name: 'Shipping Notification', status: 'Active', messages: 1250, cost: 125.00 },
  { id: 2, name: 'Delivery Updates', status: 'Active', messages: 875, cost: 87.50 },
];

// Sample data for templates
const templates = [
  { id: 1, name: 'Order Confirmation', type: 'Email', lastUsed: '2023-04-20' },
  { id: 2, name: 'Shipping Notification', type: 'Email', lastUsed: '2023-04-22' },
  { id: 3, name: 'Delivery Confirmation', type: 'SMS', lastUsed: '2023-04-21' },
  { id: 4, name: 'Customer Survey', type: 'Email', lastUsed: '2023-03-15' },
];

export const MarketingTools = (props) => {
  const { t } = useOutletContext();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container>
      <Title 
        title={t('marketing:marketingTools')}
        breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('marketing:marketingTools')}]}
      />
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="marketing tools tabs">
          <Tab label="Email Campaigns" />
          <Tab label="SMS Notifications" />
          <Tab label="Templates" />
          <Tab label="Analytics" />
        </Tabs>
      </Paper>

      {/* Email Campaigns Tab */}
      {tabValue === 0 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Email Campaigns</Typography>
            <Button variant="contained" color="primary">Create Campaign</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Campaign Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Sent Date</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {emailCampaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>{campaign.name}</TableCell>
                    <TableCell>
                      <Chip 
                        label={campaign.status} 
                        color={
                          campaign.status === 'Sent' ? 'success' : 
                          campaign.status === 'Draft' ? 'default' : 'primary'
                        } 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>{campaign.sentDate || 'Not sent'}</TableCell>
                    <TableCell>
                      {campaign.status === 'Sent' ? (
                        <Box>
                          <Typography variant="body2">
                            Opens: {campaign.opens} ({Math.round(campaign.opens/campaign.recipients*100)}%)
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={Math.round(campaign.opens/campaign.recipients*100)} 
                            sx={{ mb: 1, mt: 0.5 }}
                          />
                          <Typography variant="body2">
                            Clicks: {campaign.clicks} ({Math.round(campaign.clicks/campaign.recipients*100)}%)
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={Math.round(campaign.clicks/campaign.recipients*100)} 
                            color="success"
                          />
                        </Box>
                      ) : (
                        'No data yet'
                      )}
                    </TableCell>
                    <TableCell>
                      <Button size="small">View</Button>
                      {campaign.status !== 'Sent' && (
                        <Button size="small" color="primary" sx={{ ml: 1 }}>Edit</Button>
                      )}
                      {campaign.status === 'Sent' && (
                        <Button size="small" color="primary" sx={{ ml: 1 }}>Duplicate</Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* SMS Notifications Tab */}
      {tabValue === 1 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">SMS Notifications</Typography>
            <Button variant="contained" color="primary">Configure SMS</Button>
          </Box>
          
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>SMS Usage</Typography>
                  <Typography variant="h4">2,125</Typography>
                  <Typography variant="body2" color="text.secondary">Messages sent this month</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Cost</Typography>
                  <Typography variant="h4">$212.50</Typography>
                  <Typography variant="body2" color="text.secondary">Current month charges</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          <Typography variant="subtitle1" gutterBottom>Active SMS Campaigns</Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Campaign Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Messages Sent</TableCell>
                  <TableCell>Cost</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {smsCampaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>{campaign.name}</TableCell>
                    <TableCell>
                      <Chip 
                        label={campaign.status} 
                        color={campaign.status === 'Active' ? 'success' : 'default'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>{campaign.messages}</TableCell>
                    <TableCell>${campaign.cost.toFixed(2)}</TableCell>
                    <TableCell>
                      <Button size="small">Configure</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Templates Tab */}
      {tabValue === 2 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Message Templates</Typography>
            <Button variant="contained" color="primary">Create Template</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Template Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Last Used</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell>{template.name}</TableCell>
                    <TableCell>{template.type}</TableCell>
                    <TableCell>{template.lastUsed}</TableCell>
                    <TableCell>
                      <Button size="small">View</Button>
                      <Button size="small" color="primary" sx={{ ml: 1 }}>Edit</Button>
                      <Button size="small" color="primary" sx={{ ml: 1 }}>Duplicate</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Analytics Tab */}
      {tabValue === 3 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Marketing Analytics</Typography>
            <Button variant="contained" color="primary">Export Report</Button>
          </Box>
          <Typography variant="body1" paragraph>
            View detailed analytics for your marketing campaigns, including open rates, click-through rates, and conversion metrics.
          </Typography>
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>Performance Overview</Typography>
            <Typography variant="body2">
              Analytics dashboard will be displayed here with charts and metrics.
            </Typography>
          </Box>
        </Paper>
      )}
    </Container>
  );
}
