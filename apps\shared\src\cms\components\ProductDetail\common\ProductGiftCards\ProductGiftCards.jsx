import React, { useContext, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Container, Grid2, Typography } from '@mui/material';
import FormItem from '../../../../../components/FormItem';

import { setInfo } from '../../../../../store/reducers/currentShopItemSlice';
import { PosProductDetailContext } from '../../../../hooks/PosProductDetailContext';
import { useProductGiftCards } from './useProductGiftCards';

export const ProductGiftCards = ({layoutType = null, recipient, product, onSelect, slotProps, ...props}) => {
    const dispatch = useDispatch();
    const { t } = useTranslation();

    const { fullPage, loading, goToPreviousView, setErrors } = useContext(PosProductDetailContext) || {};
    const { fields, formFields, errors:giftCardErrors, handleChange, handleBlur } = useProductGiftCards({goToPreviousView, recipient, onSelect});

    // group by row id
    const rows = useMemo(() => (
        [...(formFields || [])].reduce((acc, field) => { 
            const group = acc.find(g => g[0]?.rowId === field.rowId);
            if (group) group.push(field);
            else acc.push([field]);
            return acc;
        }, [])
    ), [formFields]);

    useEffect(() => {
        if ((!product || product?.product_type_id !== 12) && !fullPage) goToPreviousView();
    }, [product, fullPage, goToPreviousView]);

    useEffect(() => {
        if (product?.product_type_id === 12) dispatch(setInfo({hasGiftCard: true}));
    }, [product?.product_type_id, dispatch]);

    useEffect(() => {
        setErrors(giftCardErrors);
    }, [giftCardErrors]);

    if (!product || product?.product_type_id !== 12) return null;

    return (
        <Container disableGutters {...slotProps?.container}>
            <Typography variant="subtitle2" component="div">
                <Typography variant="bold" component="div">{t("pos:whoWillReceiveGiftCard")}</Typography>
            </Typography>

            {rows.map((_, i) => (
                <Grid2 key={i} container spacing={2}>
                    {formFields?.filter(a => a.rowId === i+1).map(field => (
                        <Grid2 key={field.name} size={{ xs: 12, lg: "grow" }}>
                            <FormItem 
                                key={field.name}
                                {...field}
                                label={field.label}
                                required={Boolean(field.required)}
                                component={field.component}
                                name={field.name}
                                options={field?.options?.length > 0 ? field.options : null}
                                margin="normal"
                                value={fields?.[field.name]}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                errors={giftCardErrors?.[field.name]}
                                disabled={loading}
                            />
                        </Grid2>
                    ))}
                </Grid2>
            ))}
        </Container>
    );
}