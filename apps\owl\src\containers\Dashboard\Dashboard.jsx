import React from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  LocalShipping as ShippingIcon,
  Inventory as InventoryIcon,
  Receipt as BillingIcon,
  People as CustomerIcon,
  MonitorHeart as ServiceIcon
} from '@mui/icons-material';

import { Title } from '@siteboss-frontend/shared/components';

export const Dashboard = () => {
  const { t } = useOutletContext();

  return (
    <Container>
      <Title
        title={t('dashboard:dashboard')}
        breadcrumbs={[{title: t('dashboard:dashboard')}]}
      />

      {/* Metric Cards */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #2196F3 0%, #03A9F4 100%)',
              color: 'white',
              boxShadow: '0 4px 20px 0 rgba(33, 150, 243, 0.14), 0 7px 10px -5px rgba(33, 150, 243, 0.4)',
              borderRadius: 2,
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" component="div" sx={{ fontWeight: 'medium' }}>
                    Active Shipments
                  </Typography>
                  <Box sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '50%',
                    p: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <ShippingIcon />
                  </Box>
                </Box>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  128
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                  +12% from last week
                </Typography>
              </CardContent>
              <Box sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 150,
                height: 150,
                borderRadius: '50%',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                zIndex: 0
              }} />
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)',
              color: 'white',
              boxShadow: '0 4px 20px 0 rgba(76, 175, 80, 0.14), 0 7px 10px -5px rgba(76, 175, 80, 0.4)',
              borderRadius: 2,
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" component="div" sx={{ fontWeight: 'medium' }}>
                    Inventory Items
                  </Typography>
                  <Box sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '50%',
                    p: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <InventoryIcon />
                  </Box>
                </Box>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  1,542
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                  85 items low stock
                </Typography>
              </CardContent>
              <Box sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 150,
                height: 150,
                borderRadius: '50%',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                zIndex: 0
              }} />
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #9C27B0 0%, #E040FB 100%)',
              color: 'white',
              boxShadow: '0 4px 20px 0 rgba(156, 39, 176, 0.14), 0 7px 10px -5px rgba(156, 39, 176, 0.4)',
              borderRadius: 2,
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" component="div" sx={{ fontWeight: 'medium' }}>
                    Monthly Revenue
                  </Typography>
                  <Box sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '50%',
                    p: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <BillingIcon />
                  </Box>
                </Box>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  $24,500
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                  +8% from last month
                </Typography>
              </CardContent>
              <Box sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 150,
                height: 150,
                borderRadius: '50%',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                zIndex: 0
              }} />
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #FF9800 0%, #FFC107 100%)',
              color: 'white',
              boxShadow: '0 4px 20px 0 rgba(255, 152, 0, 0.14), 0 7px 10px -5px rgba(255, 152, 0, 0.4)',
              borderRadius: 2,
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" component="div" sx={{ fontWeight: 'medium' }}>
                    Active Customers
                  </Typography>
                  <Box sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '50%',
                    p: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CustomerIcon />
                  </Box>
                </Box>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  42
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                  3 new this month
                </Typography>
              </CardContent>
              <Box sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 150,
                height: 150,
                borderRadius: '50%',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                zIndex: 0
              }} />
            </Card>
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={3}>
        {/* Welcome Message */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h5" gutterBottom>
              Welcome to the OWL Application
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" paragraph>
                This is the customer-facing portal for Order and Warehouse Management clients.
              </Typography>
              <Typography variant="body1" paragraph>
                Use the navigation menu to access different sections of the application.
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Service Status */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ServiceIcon sx={{ mr: 1, color: 'success.main' }} />
              <Typography variant="h5">
                Service Status
              </Typography>
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" color="success.main" sx={{ fontWeight: 'bold', mb: 1 }}>
                All Systems Operational
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Last Updated: {new Date().toLocaleTimeString()}
              </Typography>
              <Box sx={{
                p: 1,
                borderLeft: '4px solid',
                borderColor: 'success.main',
                bgcolor: 'success.light',
                color: 'success.contrastText',
                borderRadius: 1
              }}>
                <Typography variant="body2">
                  No incidents reported in the last 24 hours
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Recent Activity
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: 'primary.main',
                  display: 'inline-block',
                  mr: 1
                }}/>
                New carrier rate updated (2 hours ago)
              </Typography>
              <Typography variant="body1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: 'primary.main',
                  display: 'inline-block',
                  mr: 1
                }}/>
                Customer invoice generated (1 day ago)
              </Typography>
              <Typography variant="body1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: 'primary.main',
                  display: 'inline-block',
                  mr: 1
                }}/>
                Marketing campaign sent (2 days ago)
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}
