import { Crop32Outlined as ButtonIcon, ListAltOutlined as SelectIcon, SegmentOutlined as IndentedIcon, ReorderOutlined as UnindentedIcon } from '@mui/icons-material';

export const widgetIcon = IndentedIcon;
export const layouts = [
    {
        id: 1,
        name: 'Buttons',
        icon: <ButtonIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            buttons: {
                spacing: 0.5,
                button: {
                    sx: {
                        minWidth: 80,
                        minHeight: 64,
                        borderColor: 'divider'
                    },
                }
            },
        },
    },
    {
        id: 2,
        name: 'Selection',
        icon: <SelectIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            select: {
                variant: "filled",
            }
        },
    },
    {
        id: 3,
        name: 'Indented list',
        icon: <IndentedIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            list: {
                dense: false,
                indented: true,
            }
        },
    },
    {
        id: 4,
        name: 'Unindented list',
        icon: <UnindentedIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            list: {
                dense: false,
                indented: false,
            }
        },
    },
];