import React from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Paper, Button, Container, IconButton, Tooltip, Typography, useMediaQuery } from '@mui/material';
import { ArrowBackOutlined as BackIcon } from '@mui/icons-material';

import Title from '../../../../../components/Title';

export const WithExtraInfo = ({title, subtitle, heading, fullPage, goToPreviousView, onSave, slotProps, children, ...props}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    return (
        <>
            {!fullPage &&
                <Stack component={Paper} elevation={24} direction="row" spacing={2} useFlexGap sx={{
                    boxShadow:'none',
                    width:'100%', 
                    position: (!fullPage && !isMobile) ? 'sticky' : 'relative', 
                    top: 0,
                    pb: 2,
                }}>
                    <div>
                        <Tooltip title={t("general:back")}>
                            <IconButton size="small" onClick={goToPreviousView}>
                                <BackIcon fontSize='inherit' />
                            </IconButton>
                        </Tooltip>
                    </div>
                    <div>
                        <Title variant="h6" title={title} subtitle={subtitle} />
                    </div>                    
                </Stack>
            }
            <Container disableGutters {...slotProps?.container}>
                {heading && 
                    <Typography variant="subtitle2" component="div">
                        <Typography variant="bold" component="div">{heading}</Typography>
                    </Typography>
                }

                {children}

                <Stack direction="row" spacing={1} useFlexGap sx={{my: 2}}>
                    <Button variant="text" color="inherit" size="large" fullWidth onClick={goToPreviousView}>
                        {t("general:cancel")}
                    </Button>
                    <Button variant="contained" color="primary" size="large" fullWidth onClick={onSave}>
                        {t("general:save")}
                    </Button>
                </Stack>
            </Container>
        </>
    );
}