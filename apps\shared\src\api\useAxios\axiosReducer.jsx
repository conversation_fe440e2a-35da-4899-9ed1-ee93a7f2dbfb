export const axiosReducer = (state, action) => {
    switch (action.type) {
        case 'REQUEST_INIT':
            return {
                ...state,
                loading: true,
                errors: null,
                httpCode: null,
            };
        case 'REQUEST_END':
            return {
                ...state,
                loading: false,
            };
        case 'REQUEST_SUCCESS':
            return {
                ...state,
                loading: false,
                data: action.payload.data,
                errors: null,
                httpCode: action.payload.httpCode,
            };
        case 'REQUEST_FAILURE':
            return {
                ...state,
                loading: false,
                data: null,
                errors: action.payload.errors,
                httpCode: action.payload.httpCode,
            };
        case 'REQUEST_CANCEL':
            return {
                ...state,
                loading: false,
                data: null,
                errors: null,
                httpCode: null,
            };
        case 'SET_STATE':
            return {
                ...state,
                ...action.payload,
            };
        default:
            return state;
    }
};