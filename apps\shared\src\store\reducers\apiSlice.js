import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const toCamelCase = str => {
    if (!str) return str;
    str = str.replace(/[^a-zA-Z0-9]/g, ' ');
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
        return index === 0 ? word.toLowerCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
};

// Load token from local storage
const loadLocalToken = () => {
  const localUser = localStorage.getItem('user');
  if (localUser) {
    try {
      const parsedUser = JSON.parse(localUser);
      return parsedUser.token || null;
    } catch (error) {}
  }
  return null;
};

export const apiSlice = createApi({
    reducerPath: 'api',
    keepUnusedDataFor: 300,
    refetchOnMountOrArgChange: true,
    baseQuery: fetchBaseQuery({
        baseUrl: import.meta.env.VITE_API_URL,
        prepareHeaders: headers => {
            let token = loadLocalToken();
            // if the token does not start with Bear<PERSON>, add it
            if (token && !token.startsWith("Bearer ")) {
                token = "Bearer " + token;
            }
            if (token) {
                headers.set('Authorization', token);
            }
            return headers;
        },
    }),
    endpoints: build => ({
        formData: build.mutation({
            query: ({ url, method = 'POST', body, headers }) => ({
                url,
                method,
                body,
                headers,
            }),
            serializeQueryArgs: () => '',
            invalidatesTags: (result, error, arg) => [],
        }),        
        dynamic: build.query({
            queryFn: async (args, queryApi, extraOptions, baseQuery) => {
                if (!args || !args.url) return { data: null };

                const { url, method = 'GET', body, params, headers } = args;
                const result = await baseQuery({ url, method, body, params, headers });
                const returnData = { meta: result.meta };
                if (result.error) {
                    returnData.error = {
                        status: result.error?.originalStatus,
                        code: result.error?.status,
                        data: result.error.data,
                    };
                } else if (result.data) {
                    returnData.data = {
                        ...result.data,
                        status: result.meta?.response?.status,
                    };
                }
                return returnData;
            },
            providesTags: (result, error, arg) => {
                if (arg && arg.url) return [{ type: toCamelCase(arg.url), id: arg.url }];
                return [];
            },
            serializeQueryArgs: ({ queryArgs }) => {
                const { url, method, params, body, isFormData } = queryArgs;
                if (!url) return '';

                if (isFormData) return `${method}-${url}`;
                return `${method}-${url}-${JSON.stringify(params || body || "")}`;
            },
            forceRefetch({ currentArg }) {
                return currentArg?.isFormData;
            },            
        }),
    }),
});
  
export const { useDynamicQuery, useLazyDynamicQuery, useFormDataMutation } = apiSlice;
export default apiSlice.reducer;