import { MoneyField } from '../MoneyField/MoneyField';
import { action } from 'storybook/actions';
import { Provider } from 'react-redux';
import { createStore } from 'redux';

// // Mock Redux store
// const mockStore = createStore(() => ({
//   language: { code: 'en-US' }
// }));

export default {
    title: 'Shared/Component Groups/Form Items/Money Field',
    component: MoneyField,
    tags:['autodocs'],
    parameters:{
        docs:{
            toc:true
        }
    },
    argTypes: {
        value: {
            description: "Initial value of the field",
            control: 'number',
            table: {
                type: {
                    summary: 'number'
                },
                defaultValue: {
                    summary: 0
                }
            }
        },
        min: {
            description: "Minimum allowed value",
            control: 'number',
            table: {
                type: {
                    summary: 'number'
                },
                defaultValue: {
                    summary: 0
                }
            }
        },
        max: {
            description: "Maximum allowed value",
            control: 'number',
            table: {
                type: {
                    summary: 'number'
                },
                defaultValue: {
                    summary: 9999999999999
                }
            }
        },
        step: {
            description: "Step value for arrow key increments/decrements",
            control: 'number',
            table: {
                type: {
                summary: 'number'
                }
            }
        },
        label: {
            description: "Label for the field",
            control: 'text',
            table: {
                type: {
                    summary: 'string'
                }
            }
        },
        required: {
            description: "Whether the field is required",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        disabled: {
            description: "Whether the field is disabled",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        loading:{
            description: "Passed to the field if it's loading or not",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        error: {
            description: "Whether the field is in an error state",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        helperText: {
            description: "Helper text to display below the field",
            control: 'text',
            table: {
                type: {
                summary: 'string'
                }
            }
        },
        onChange: {
            action: 'changed',
            table: {
                type: {
                summary: 'function'
                }
            }
        },
        name:{
            description: "Passed to onChange as target: {name: props.name}.  Also passed to the text field",
            control: "text",
            table: {
                defaultValue: { summary: undefined},
                type:{summary: "string"}
            }
        },
        id: {
            description: "Prop passed to the text field",
            control: "text",
            table: {
                defaultValue: { summary: undefined},
                type:{summary: "string"}
            }
        },
        fullWidth: {
            description: "Prop passed to the text field",
            control: "boolean",
            table: {
                defaultValue: { summary: true},
                type:{summary: "boolean"}
            }
        },
        variant:{
            description: "Prop passed to the text field",
            control: "select",
            options: ["standard", "filled", "outlined"],
            table: {
                defaultValue: { summary: "outlined"},
                type:{summary: "string"}
            }
        },
        placeholder:{
            description: "Prop passed to the text field",
            control: "text",
            table: {
                defaultValue: { summary: undefined},
                type:{summary: "string"}
            }
        },
        margin:{
            description: "Prop passed to the text field",
            control: "select",
            options:[
                "none",
                "dense",
                "normal"
            ],
        }
    }
};

// Playground story with default props
export const Playground = {
  args: {
    label: 'Amount',
    onChange: action('onChange')
  }
};

// Story with initial value
export const WithInitialValue = {
  args: {
    ...Playground.args,
    value: 1234.56
  }
};

// Story with min and max values
export const WithMinAndMax = {
  args: {
    ...Playground.args,
    min: 100,
    max: 1000
  }
};

// Story with custom step
export const WithCustomStep = {
  args: {
    ...Playground.args,
    step: 0.5
  }
};

// Story with error state
export const WithError = {
  args: {
    ...Playground.args,
    error: true,
    helperText: 'Invalid amount'
  }
};

// Story with disabled state
export const Disabled = {
  args: {
    ...Playground.args,
    disabled: true
  }
};

// A whimsical story
export const BigMoney = {
  args: {
    ...Playground.args,
    label: '💰 Show Me The Money! 💰',
    value: 1000000,
    onChange: (e) => {
      action('onChange')(e);
      if (e.target.value >= 1000000) {
        alert('🎉 You\'re a millionaire! 🎉');
      }
    }
  }
};