export const properties = [
    /*{
        name: 'orientation',
        label: 'builder:component.divider.orientation',
        component: "Select",
        value: 'horizontal',
        size: "small",
        required: true,
        margin: "normal",
        options: [
            {id: 'horizontal', slug: 'builder:component.divider.orientations.horizontal'},
            {id: 'vertical', slug: 'builder:component.divider.orientations.vertical'},
        ],
    },*/
    {
        name: 'variant',
        label: 'builder:component.divider.variant',
        component: "Select",
        options: [
            {id: 'fullWidth', slug: 'builder:component.divider.variants.fullWidth'},
            {id: 'inset', slug: 'builder:component.divider.variants.inset'},
            {id: 'middle', slug: 'builder:component.divider.variants.middle'},
        ],
        value: 'fullWidth',
        size: "small",
        margin: "normal",
    },
    {
        name: 'textAlign',
        label: 'builder:component.divider.textAlign',
        component: "Select",
        options: [
            {id: 'center', slug: 'builder:component.divider.aligns.center'},
            {id: 'left', slug: 'builder:component.divider.aligns.left'},
            {id: 'right', slug: 'builder:component.divider.aligns.right'},
        ],
        value: 'center',
        size: "small",
        margin: "normal",
    },
    {
        name: 'flexItem',
        label: 'builder:component.divider.flexItem',
        component: "Switch",
        value: 1,
        checked: false,
        size: "small",
        margin: "normal",
        helperText: 'builder:component.divider.flexItemHelper',
        sx: {ml: 1},
    },
];
