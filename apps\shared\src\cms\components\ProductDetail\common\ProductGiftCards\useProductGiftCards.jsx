import { useState, useCallback, useMemo } from 'react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';

export const useProductGiftCards = ({ recipient = null, goToPreviousView, onSelect }) => {
    const { t } = useTranslation();

    const [errors, setErrors] = useState(null);
    const [fields, setFields] = useState({delivery_date: format(new Date(), 'yyyy-MM-dd'), ...recipient});

    const formFields = useMemo(() => [
            {name: 'full_name', type: 'text', label: 'giftCard:recipientFullName', required: true, value: recipient?.full_name || "", component: "TextField", margin: "normal", rowId: 1},
            {name: 'email', type: 'text', label: 'giftCard:recipientEmail', required: true, value: recipient?.email || "", component: "TextField", margin: "normal", rowId: 2},
            {name: 'delivery_date', type: 'text', label: 'giftCard:deliveryDate', required: false, value: new Date(recipient?.delivery_date) || new Date(), component: "DatePicker", margin: "normal", disablePast: true, rowId: 2},
            {name: 'message', type: 'text', label: 'giftCard:message', required: false, value: recipient?.message || "", component: "TextField", margin: "normal", minRows: 3, rowId: 3},
    ], [recipient]);

    // validate required fields
    const validateFields = useCallback(() => {
        let valid = true;
        const requiredFields = formFields?.filter(field => field.required);
        if (!requiredFields || requiredFields.length === 0) return valid;

        for (let i = 0; i < requiredFields.length; i++) {
            if (!fields[requiredFields[i].name]){
                setErrors(prev => ({
                    ...(prev || {}),
                    [requiredFields[i].name]: t("error:required"),
                }));
                valid = false;
            }
        }
        return valid;
    }, [fields, formFields]);

    const handleSubmit = useCallback((getOut = true) => {
        setErrors(null);
        const valid = validateFields();
        if (valid) {
            //const res = await handleAddUser(fields);
            if (getOut) goToPreviousView();
            return valid;
        }
        return valid;
    }, [validateFields, goToPreviousView, fields]);

    const handleChange = useCallback(e => {
        if (e.target.name === "delivery_date") e.target.value = format(new Date(e.target.value), 'yyyy-MM-dd');
        setFields(prev => ({...prev, [e.target.name]: e.target.value}));
    }, []);

    const handleBlur = useCallback(e => {
        if (onSelect) onSelect(fields);
    }, [fields, onSelect]);

    return {
        errors,
        fields,
        formFields,
        validateFields,
        handleSubmit,
        handleChange,
        handleBlur,
    };
}