import React from 'react';
import Format from '../../usePrint/Format'; 

export const Print = ({printRef, format, onPrint, className, slots, slotProps, printCount, ...props}) => {
    return (
        <Format 
            ref={printRef} 
            format={format} 
            onRender={onPrint}
            className={className}
            slots={slots} 
            slotProps={slotProps}
            //slotProps={{...slotProps, iframe: {style: {display: 'none'}, ...slotProps?.iframe}}}
        /> 
    );
}