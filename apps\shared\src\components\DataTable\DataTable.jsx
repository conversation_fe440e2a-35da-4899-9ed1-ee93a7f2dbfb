import React, { useCallback, useMemo, lazy, Suspense } from 'react';
import { LinearProgress, useMediaQuery, Stack, Skeleton } from '@mui/material';
import { debounce } from '../../utils';

import SearchInput from './SearchInput';
import NoRows from './NoRows';
import Mobile from './Mobile';
import CardView from './CardView';

const Table = lazy(() => import('./Table/index.js'));

const getCellClassName = params => {
    const depth = params.row.depth;
    if (params.field === '__check__' || depth === undefined) return 'no-indent';
    return 'indent';
}

const getRowClassName = params => {
    const depth = params.row.depth;
    if (depth === undefined) return '';
    return `depth-${depth}`;
}

export const DataTable = ({
    page,
    pageSize: pageSz,
    totalPages,
    toolbarSlots,
    toolbarSlotsProps,
    onExpand,
    onDelete,
    setPage,
    setPageSize,
    setOrder,
    setSearchText,
    setSelected,
    disableSearch,
    compact,
    loading,
    allRows,
    indentSize = 20,
    view = 'table', // card (if on mobile, the mobile view will be used for either view)
    //rowHeight, // Custom row height
    //dynamicRowHeight = false, // Whether to use dynamic row height
    //customStyles = {}, // Custom styles for the DataGrid
    slots,
    slotsProps,
    ...props
}) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const pageSizeOptions = useMemo(() => props.pageSizeOptions || [10, 25, 50, 100], [props.pageSizeOptions]);
    const pageSize = useMemo(() => pageSizeOptions.length ? pageSizeOptions.reduce((prev, curr) => Math.abs(curr - pageSz) < Math.abs(prev - pageSz) ? curr : prev) : props.pageSizeOptions?.[0] || 10, [pageSizeOptions, pageSz, props.pageSizeOptions]);

    const handleSearch = useCallback(debounce(value => {
        setSearchText(value);
    }, 200), [setSearchText]);

    const handleRowClick = useCallback(row => {
        if (props?.onRowClick) props.onRowClick(row);
        else {
            if (!(props?.disableRowSelectionOnClick || props?.checkboxSelection)) return;
            if (setSelected){
                setSelected([row.row]);
                if (onExpand) onExpand();
            }
        }
    }, [props?.onRowClick, props?.disableRowSelectionOnClick, props?.checkboxSelection, setSelected, onExpand]);

    const handleSortModelChange = useCallback(model => {
        if (model.length === 0 || !Boolean(loading)) return;
        if (setOrder) setOrder({column: model[0].field, direction: model[0].sort});
    }, [loading, setOrder]);

    const handlePaginationModelChange = useCallback(({ page, pageSize }) => {
        if (Boolean(loading)) return;
        page = Math.round(page) + 1;
        if (page > totalPages) page = totalPages;
        if (setPage) setPage(page);
        if (setPageSize) setPageSize(pageSize);
    }, [loading, totalPages, setPage, setPageSize]);

    return (
        <>
            {!disableSearch && 
                <SearchInput onSearchChange={handleSearch} slots={slots} slotProps={slotsProps}/>
            }

            {(isMobile || compact) &&
                    <Mobile
                        {...props}
                        page={Math.round(page)}
                        setPage={setPage}
                        loading={Boolean(loading)}
                        pageSize={pageSize}
                        onExpand={onExpand}
                        onDelete={onDelete}
                        totalPages={totalPages}
                        allRows={allRows}
                        rowSelectionModel={props.rowSelectionModel}
                    />
            }

            {view === 'card' && !(isMobile || compact) &&
                <CardView
                    {...props}
                    page={Math.round(page)}
                    setPage={setPage}
                    loading={Boolean(loading)}
                    pageSize={pageSize}
                    pageSizeOptions={pageSizeOptions}
                    onExpand={onExpand}
                    onDelete={onDelete}
                    totalPages={totalPages}
                    allRows={allRows}
                    rowSelectionModel={props.rowSelectionModel}
                    onPaginationModelChange={handlePaginationModelChange}
                    onClick={handleRowClick}
                />
            }

            {view === 'table' && !(isMobile || compact) &&
                <Suspense fallback={
                    <Stack direction="column" spacing={0.5} useFlexGap sx={{width: '100%'}}>
                        {[...Array(5)].map((_, i) => (
                            <Skeleton key={i} animation="wave" variant="rounded" width="100%" height={45} />
                        ))}
                    </Stack>
                }>
                    <Table
                        onExpand={onExpand}
                        onDelete={onDelete}
                        allRows={allRows}
                        toolbarSlots={toolbarSlots}
                        toolbarSlotsProps={toolbarSlotsProps}

                        initialState={props.initialState || {
                            pagination: {
                                paginationModel: {
                                    page: Math.round(page),
                                    pageSize: pageSize,
                                },
                            },
                        }}
                        pageSizeOptions={pageSizeOptions}
                        page={Math.round(page)}
                        rowLength={pageSize}
                        rowCount={props.totalRows || props.rows.length}
                        paginationMode="server"
                        paginationModel={{
                            page: Math.round(page) - 1,
                            pageSize: pageSize,
                        }}
                        onPaginationModelChange={handlePaginationModelChange}
                        onSortModelChange={handleSortModelChange}
                        onRowClick={handleRowClick}
                        checkboxSelection={props.checkboxSelection || false}
                        disableRowSelectionOnClick={props.disableRowSelectionOnClick || props.checkboxSelection}
                        hideFooterSelectedRowCount={props.hideFooterSelectedRowCount || false}
                        loading={Boolean(loading)}
                        getCellClassName={getCellClassName}
                        getRowClassName={getRowClassName}
                        slots={{
                            noRowsOverlay: NoRows,
                            noResultsOverlay: NoRows,
                            loadingOverlay: () => <LinearProgress color="secondary" sx={{height: "2px"}} />,
                        }}
                        sx={{
                            ...Array.from({ length: 10 }, (_, i) => ({
                                [`& .depth-${i}`]: {
                                    '& .MuiDataGrid-cell.indent': {
                                        pl: `${i * indentSize}px`,
                                    }
                                }
                            })).reduce((acc, obj) => ({ ...acc, ...obj }), {}),

                            // Default styles for all tables
                            /*
                            '& .MuiDataGrid-viewport': {
                                overflow: dynamicRowHeight ? 'visible' : 'auto',
                            },
                            '& .MuiDataGrid-renderingZone': {
                                overflow: dynamicRowHeight ? 'visible' : 'auto',
                            },
                            '& .MuiDataGrid-row': {
                                overflow: 'visible !important',
                                ...(rowHeight && rowHeight !== 'auto' && {
                                    minHeight: `${rowHeight}px !important`,
                                    // No maxHeight to allow content to expand
                                }),
                            },
                            '& .MuiDataGrid-cell': {
                                overflow: 'visible !important',
                                whiteSpace: 'normal !important',
                                lineHeight: 'normal',
                                display: 'flex',
                                alignItems: 'flex-start',
                                padding: '8px 16px',
                                ...(rowHeight && rowHeight !== 'auto' && {
                                    minHeight: `${rowHeight}px !important`,
                                    // No maxHeight to allow content to expand
                                }),
                            },
                            '& .MuiDataGrid-virtualScroller': {
                                overflowX: 'hidden',
                            },
                            '& .MuiDataGrid-main': {
                                overflow: dynamicRowHeight ? 'visible' : 'auto',
                            },
                            */

                            // Apply any custom styles passed in
                            //...customStyles,
                            ...props?.sx
                        }}
                        {...props}
                        pagination
                        ignoreDiacritics
                    />
                </Suspense>
            }
        </>
    );
}