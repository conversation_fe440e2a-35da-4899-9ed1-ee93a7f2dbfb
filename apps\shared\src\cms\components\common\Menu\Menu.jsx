
import React, { useContext, memo } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>ge, <PERSON><PERSON>, Drawer, List, ListItem, ListItemButton, ListItemText, ListItemIcon, IconButton, lighten, useMediaQuery } from '@mui/material';
import { ChevronLeft as CloseIcon } from '@mui/icons-material';

import { theme } from '../../../../theme';
import { usePortalIframe } from '../../../../components';
import Icon from '../Icon';
import { MenuContext } from './MenuContext';

const CartBadge = ({children, isCart, ...props}) => {
    const cart = useSelector(state => state.cart?.cart);
    if (!isCart) return children;
    return (
        <Badge badgeContent={cart?.length || 0} color="primary" max={99} overlap="circular" {...props}>
            {children}
        </Badge>
    );
}


export const Menu = memo(({
    slotProps,
    disabled = false,
    ...props
}) => {
    const _theme = theme();
    //const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const { handleDrawerToggle = () => {}, open, items = [], type, isBuilder = false } = useContext(MenuContext) || {};

    const contentWindow = isBuilder ? usePortalIframe() : null;
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined});

    if (!type) return null;
    if (type === "vertical" || type === "responsive") {
        return (
            <Drawer
                variant={(type === "responsive" && isMobile) ? "persistent" : "permanent"} 
                open={(type === "responsive" && isMobile) ? open : true}
                onClose={handleDrawerToggle(false)}
                ModalProps={{
                    keepMounted: true,
                }}
                PaperProps={{
                    sx: {                            
                        boxSizing: 'border-box', 
                        width: _theme.sizes.menuWidth, 
                    }
                }}
                sx={{width: _theme.sizes.menuWidth, flexShrink: 0, zIndex: theme => theme.zIndex.drawer + 2}}
            >
                <List {...slotProps?.wrapper}>
                    {type === "responsive" && isMobile && 
                        <IconButton 
                            onClick={handleDrawerToggle(false)} 
                            size="small" 
                            color={_theme.palette.mode === "light" ? "primary" : "inherit"}                
                            sx={{
                                position: "fixed",
                                left: _theme.sizes.menuWidth,
                                top: 15,
                                transform: 'translateX(-50%)',
                                zIndex: theme => theme.zIndex.drawer + 3,
                                border: theme => `1px solid ${theme.palette.divider}`, 
                                backgroundColor: theme => theme.palette.background.paper,
                                '&:hover': {
                                    backgroundColor: _theme.palette.mode === "light" ? lighten(_theme.palette.primary.main, 0.95) : lighten(_theme.palette.background.paper, 0.05)
                                },
                            }} 
                            {...slotProps?.closeButton}
                        >
                            <CloseIcon fontSize="inherit" />
                        </IconButton>
                    }
                    {items?.map(item => (
                        <ListItem key={item.id || item.title+item.url} disablePadding {...slotProps?.item}>
                            <CartBadge isCart={item?.isCart} overlap="rectangular">
                                <ListItemButton 
                                    component={Link} 
                                    to={isBuilder ? undefined : item.url} 
                                    disabled={disabled || isBuilder} 
                                    color="inherit"
                                    {...slotProps?.button}
                                >
                                    {item.icon && 
                                        <ListItemIcon>
                                            <Icon fontSize="small" name={item.icon} {...slotProps?.icon}/>
                                        </ListItemIcon>
                                    }
                                    <ListItemText {...slotProps?.text} primary={item.title} />
                                </ListItemButton>
                            </CartBadge>
                        </ListItem>
                    ))}
                </List>
            </Drawer>
        );
    }

    return (
        <Stack direction="row" useFlexGap flexWrap="wrap" spacing={1} {...slotProps?.wrapper} sx={{...(isBuilder ? {minHeight: 40} : {}), mx: 2,...slotProps?.wrapper?.sx}}>
            {items?.map(item => {
                if (item.icon && (item?.isCart || item?.isProfile)) {
                    return (
                        <CartBadge key={item.id || item.title+item.url} isCart={item?.isCart}>
                            <IconButton 
                                component={Link} 
                                to={isBuilder ? "#" : item.url} 
                                disabled={disabled || isBuilder} 
                                {...slotProps?.button} 
                            >
                                <Icon fontSize="small" name={item.icon} {...slotProps?.icon}/>
                            </IconButton>
                        </CartBadge>
                    );
                }
                return (
                    <Button 
                        key={item.id || item.title+item.url} 
                        component={Link}  
                        to={isBuilder ? "#" : item.url} 
                        disabled={disabled || isBuilder}
                        startIcon={item?.icon ? <Icon name={item.icon} {...slotProps?.icon}/> : undefined}
                        {...slotProps?.button}
                    >
                        {item.title}
                    </Button>
                );
            })}
        </Stack>
    );    
});