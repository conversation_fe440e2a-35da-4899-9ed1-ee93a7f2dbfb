import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, shallowEqual } from 'react-redux';
import { Stack, Container } from '@mui/material';
import { createCurrencyFormatter, capitalize } from '../../../../../utils';

import { utils } from '../../../common/pos';
import Button from './Button';
import Card from './Card';

export const Default = ({ 
    layoutType = 'card', 
    slotProps = {}, 
    items = [], 
    disabled, 
    isBuilder,
    onSelect, 
    onAddToCart, 
    ...props 
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const currentRegister = useSelector(state => state.currentRegister, shallowEqual);

    const Component = layoutType === 'card' ? Card : Button;

    const componentProps = useMemo(() => ({
        ...slotProps?.[layoutType],
        onSelect,
        disableRipple: Boolean(items.length > 100) || isBuilder,
        size: "sm",
        disabled: disabled || (currentRegister?.forUserIds?.length === 0 && currentRegister?.hasEvent) || isBuilder,
    }), [slotProps, layoutType, onSelect, disabled, currentRegister?.forUserIds, currentRegister?.hasEvent, items.length, disabled]);

    const processItemContent = useCallback(item => {
        let price = [];
        let text = [];
        if (item){
            if (item?.product_variants?.length > 0){
                price = [{amount: currencyFormatter.format(item.product_variants[0].activation_fee || item.product_variants[0].price, currency)}];
                if (item.product_variants.length > 1) price = [t(`pos:priceStartingFrom`), ...price];
                //if (item.is_taxable) price = [...price, t(`pos:plusTax`)];

                let _text = utils.formatRecurringItem(item.product_variants[0]);
                if (_text?.length > 0) {
                    text = [...text, ..._text?.map(info => t(info))];
                }
            }
        }

        return {
            price,
            text: capitalize(text.join(' ').toLocaleLowerCase()),
        }
    }, [t, language, currencyFormatter]);

    if (!items.length) return null;

    return (
        <Stack 
            component={Container} 
            direction="row" 
            spacing={layoutType === "card" ? 2 : 1} 
            useFlexGap 
            flexWrap="wrap"
            maxWidth={false}
            disableGutters
            {...slotProps?.itemContainer}
            sx={{
                py: 1,
                mx: 0,
                minWidth: 280,
                maxWidth: '100%',
                overflow: 'hidden', 
                overflowY: 'auto', 
                justifyContent: 'center',
                ...slotProps?.itemContainer?.sx
            }}
        >
            {items.map(item => {
                const itemContent = processItemContent(item);
                return (
                    <Component 
                        key={item.id} 
                        {...componentProps} 
                        item={item} 
                        content={itemContent} 
                        onAddToCart={onAddToCart} 
                    />
                );
            })}
        </Stack>
    )
}