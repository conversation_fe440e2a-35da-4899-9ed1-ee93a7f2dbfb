![Siteboss Logo](./assets/siteboss.png)

# SITEBOSS-FRONTEND

This repository contains the frontend code for the Siteboss project, a comprehensive content management and point-of-sale system with SSR-enabled public website capabilities.

## Overview

Siteboss is a modular application consisting of four main applications and a shared component library:

- **website:** Server-side rendered public website with dynamic content
- **pos:** A modern Point of Sale system for retail operations
- **cms:** A powerful Content Management System with server-side rendering capabilities
- **siteboss:** The main administrative portal for managing all aspects of the system
- **shared:** A library of reusable components and utilities used across all applications

## Key Features

### Public Website (SSR)
- Server-side rendering using Vite
- Dynamic content rendering from CMS
- SEO-optimized page delivery
- Emotion for CSS-in-JS with SSR support
- Automatic sitemap generation
- Meta tag management
- LLMs.txt generation for AI crawlers
- Production-ready compression and static asset serving

### Point of Sale (POS)
[Previous POS section content]

### Content Management System (CMS)
[Previous CMS section content]

### Siteboss Portal
[Previous Portal section content]

### Shared Components
[Previous Shared Components section content]

## Technical Stack

- **Frontend Framework:** React
- **UI Library:** Material-UI (MUI)
- **State Management:** Redux
- **Routing:** React Router
- **SSR Engine:** Vite
- **CSS-in-JS:** Emotion
- **Component Development:** Storybook
- **Testing:** 
  - Cypress (E2E, Component, Unit)
  - Jest and React Testing Library
- **Build System:** Vite
- **Package Management:** npm workspaces

## Cypress Testing

Cypress is used for three types of testing:
- End-to-End (E2E) testing
- Component testing
- Unit testing

### Running Cypress Tests

```bash
# Open Cypress Test Runner
npx cypress open

# Run all tests headlessly
npx cypress run

# Run component tests only
npx cypress run --component

# Run specific test file
npx cypress run --spec "cypress/e2e/my-test.cy.js"
```

### Project Structure

Tests are located close to their respective components:
```
src/
├── components/
│   └── MyComponent/
│       ├── MyComponent.jsx
│       ├── MyComponent.cy.jsx        # Component test
│       └── CypressFixtures/          # Test fixtures
└── utils/
    └── myFunction/
        ├── myFunction.js
        └── myFunction.cy.js          # Unit test
```

### Component Testing

```jsx
// MyComponent.cy.jsx
describe("MyComponent", () => {
    beforeEach(() => {
        cy.mount(<MyComponent />)
    })

    it("renders correctly", () => {
        cy.get('[data-cy="my-component"]').should('exist')
    })
})
```

### Best Practices

1. **Data Attributes:**
   - Use `data-cy` attributes for test selectors
   - Follow naming convention: `data-cy="component-name-element"`

2. **Test Organization:**
   - Keep tests close to components
   - Use descriptive test names
   - Group related tests using `context`

3. **Network Requests:**
   - Intercept and stub API calls
   - Use fixtures for response data
   - Test error scenarios

4. **Configuration:**
   - Environment variables in `cypress.env.json`
   - Custom commands in `cypress/support`
   - Shared plugins in `cypress/plugins`

### Environment Setup

Create `cypress.env.json` for sensitive data:
```json
{
    "apiKey": "your-api-key",
    "testUser": "<EMAIL>",
    "testPassword": "password123"
}
```

### Visual Testing

Configure viewport for consistent testing:
```javascript
// In test file
beforeEach(() => {
    cy.viewport(1200, 800)
})
```

### Continuous Integration

Run Cypress in CI:
```bash
# Record results to Cypress Dashboard
npx cypress run --record --key=your-key

# Parallel test execution
npx cypress run --parallel --record --key=your-key
```

## Storybook Development

Storybook is integrated across all applications for component development, documentation, and testing.

### Running Storybook

Start Storybook for any application:

```bash
# From the specific app directory
cd apps/website
npm run storybook

# Or using workspaces from root
npm run storybook --workspace=@siteboss-frontend/website
```

Storybook will be available at http://localhost:6006

### Creating Stories

1. Create a new story file next to your component:
```jsx
// MyComponent.stories.jsx
import { MyComponent } from './MyComponent';

export default {
    title: 'Components/MyComponent',
    component: MyComponent,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    }
};

export const Default = {
    args: {
        // component props here
    }
};
```

2. Access built-in features:
- **Controls:** Modify component props in real-time
- **Actions:** Track event handlers
- **Docs:** Auto-generated documentation
- **Accessibility:** Test a11y compliance
- **Design:** View and compare with Figma designs

### Storybook Addons

The project includes several key addons:
- **@storybook/addon-a11y:** Accessibility testing
- **@storybook/addon-designs:** Figma integration
- **@chromatic-com/storybook:** Visual regression testing

### Visual Testing with Chromatic

Run visual tests:
```bash
npm run chromatic
```

### Best Practices

1. **Component Development:**
   - Develop components in isolation using Storybook
   - Test different states and edge cases
   - Verify responsive behavior

2. **Documentation:**
   - Use autodocs for automatic API documentation
   - Include usage examples
   - Document component variants

3. **Testing:**
   - Test interaction patterns
   - Verify accessibility
   - Run visual regression tests

4. **Organization:**
   - Group related components
   - Use consistent naming
   - Include component status (stable, beta, deprecated)

## Project Structure

```
siteboss-frontend/
├── apps/
│   ├── website/           # SSR-enabled public website
│   │   ├── src/
│   │   │   ├── entry-client.jsx    # Client-side entry
│   │   │   ├── entry-server.jsx    # Server-side entry
│   │   │   └── server.js           # SSR server
│   ├── pos/               # Point of Sale application
│   ├── cms/               # Content Management System
│   ├── siteboss/         # Admin Portal
│   └── shared/           # Shared components and utilities
├── packages/             # Common packages and configurations
├── docker/              # Docker configuration files
└── docs/               # Project documentation
```

## Getting Started

To get started, first ensure you've installed all dependencies at the root level by running:

```bash
npm install
```

### Development

#### Running the Website (SSR)

Navigate to the website directory and start the SSR development server:

```bash
cd apps/website
npm run dev
```

Or use npm workspaces from the root:

```bash
npm run dev --workspace=@siteboss-frontend/website
```

The SSR server will start on port 3000 (configurable via VITE_APP_PORT).

#### Building for Production

Build the website with SSR support:

```bash
cd apps/website
npm run build
```

This creates:
- `dist/client/` - Client-side assets
- `dist/server/` - Server-side rendering bundle

#### Environment Configuration

Website environment variables:
```bash
VITE_APP_PORT=3000           # Server port
VITE_APP_BASE=/              # Base path
VITE_API_URL=https://api...  # API endpoint
```

[Previous sections for other apps]

### Docker Development Environment

1. Build and start the containers:
```bash
docker compose up --build
```

2. Access the applications:
- Public Website: http://localhost:3000
- CMS: http://localhost:3001
- POS: http://localhost:3002
- Siteboss Portal: http://localhost:3003
- Storybook: http://localhost:6006

### Production Deployment

The website requires special consideration for SSR deployment:

```bash
# Build all applications including SSR website
npm run build

# Start production SSR server
cd apps/website
npm run preview
```

Using Docker:
```bash
# Build production image
docker build -f Dockerfile -t siteboss-frontend .

# Run container
docker run -p 3000:3000 siteboss-frontend
```

