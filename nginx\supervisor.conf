[supervisord]
nodaemon=true

[program:ssr]
command=node server.js
directory=/app/website
environment=NODE_ENV=production,VITE_API_URL=https://api-dev.impactathleticsny.com/api
autorestart=true
stdout_logfile=/var/log/ssr.log
stderr_logfile=/var/log/ssr_error.log

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autorestart=true
stdout_logfile=/var/log/nginx.log
stderr_logfile=/var/log/nginx_error.log
