This is the API from our old SiteBoss system. 
This is a single file compilation of all of the Api calls that were made and should be used as a reference for the new API.
In the new frontend, API calls are embedded in the components.
Directory Structure:
====================
📄 Api.js
📄 Cms.js
📄 Common.js
📄 Companies.js
📄 Config.js
📄 Coupons.js
📄 Discounts.js
📄 Email.js
📄 Error.js
📄 Events.js
📄 GiftCards.js
📄 Groups.js
📄 Locations.js
📄 Media.js
📄 Memberships.js
📄 Menu.js
📄 Notes.js
📄 Notifications.js
📄 OpenAI.js
📄 Order.js
📄 Permissions.js
📄 Pos.js
📄 Products.js
📄 Provinces.js
📄 Registers.js
📄 Roles.js
📄 Routes.js
📄 Services.js
📄 States.js
📄 Subscriptions.js
📄 Tags.js
📄 Tasks.js
📄 Themes.js
📄 Transactions.js
📄 Upload.js
📄 Users.js

>>>File: Api.js

import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { confirm } from '../components/Confirmation';

const { REACT_APP_API_URL } = process.env;
const qs = new URLSearchParams(window.location.search);
/**
 * Request Wrapper with default success/error actions
 */
const Request = async (props) => {

	const CancelToken = axios.CancelToken;
	const source = props.cancelTokenSource || CancelToken.source();
	const timeoutMs = props.timeout || 10000; // Default timeout: 10s
    const timeoutId = setTimeout(() => source.cancel("Request timeout"), timeoutMs);

	// mocks an api call
	if (props.test){
		return new Promise(resolve => {
			const data = props.test;
			setTimeout(() => {
			  resolve(data);
			}, Math.random() * 20);
		});
	}	

	// regular api calls
    // Can't pull from redux as this is not a functional react component
	let token="";
	let localuser = localStorage.getItem("user");
	if (localuser) {
		try{
			localuser=JSON.parse(localuser);
			token=localuser.token;
		}catch(error){}
	}

	let params = {};
	if (qs){
		qs.forEach((value, key) => {
			params[key]=value;
		});		
	}
	if (props.data) params ={...params, ...props.data};

	const client = axios.create({
		...props.config,
		baseURL: REACT_APP_API_URL,
		headers: props.headers || {'Authorization': token}, /* for sending files {'Content-Type': 'multipart/form-data' } */ 
		method: props.method || "GET",
		data: props.method!=="GET" ? params || {} : undefined,
		params: !props.method || props.method==="GET" ? params || {} : undefined,
		cancelToken: source.token
	});

	const onSuccess = (response) => {
		clearTimeout(timeoutId)
        let responseObject = {
            status: response.status,
            ...response.data
        }
		return responseObject;
  	}

  	const onError = (error=null) => {
		clearTimeout(timeoutId)
		let errordata={
			message: "An error ocurred.",
			request: error?.config || null
		};
		//timeout needs to come first here because it's also a response, 
		//but lacks all the other things for a normal response as the timeout is defined by the frontend, not the back
		 if (error === "timeout"){ 
			errordata={
				status: 408, //checked code, according the MDN
				error: "Your request has timed out."
			}
		} else if (error?.response) {
			errordata={
				...errordata,
				status: error.response.status,
				data: null,
				headers: error.response.headers,
                error: error.response.data?.error || error.response.data || null
			}
		} else if (error?.message) {
            errordata.message = error.message;
        }

		return errordata;
	}

    // this will execute on every request right after the call to await client (before success or error)
	client.interceptors.response.use(config => config, err => {
		let skip401 = false;
		const error = err.response;
		const code = err.code;
		const msg = err.message;
		if(error?.data?.error === "Invalid username or password.") skip401 = true; //so we don't see if for login errors
		if(window?.location?.pathname?.includes("signout")) skip401 = true //so we don't see it if any late calls happen after logging out
        // this only happens when the token is expired, not when login has wrong credentials
		//just checking for the 401 now, as the other things that held 401 have been changed
		if (error?.status===401 && !skip401 && !error?.config.__isRetryRequest) {
			onError(error);
            // open a modal
            if (!localStorage.getItem('expired-auth')) {
                localStorage.setItem('expired-auth', true); // prevent multiple modals stacking on top of each other
                confirm(props.text,{
                    title:"Your session has expired",
                    confirmation: "Please sign in again to continue.",
                    okText:"Sign Back In",
                    // timeout: 30000, // 30 seconds
                }).then(result =>{
                    window.location.href = "/signout";
                });
            }
	   	} 
		if(code === "ECONNABORTED" || msg?.includes("timeout")){
			let timeoutError = "timeout"
			return Promise.reject(timeoutError);
		}
        return Promise.reject(err);
	});

    try {
		const response = await client({...props});
        // console.log("response",response);
        if (!response) {
            return onError();
        }
		return onSuccess(response);
	} catch (error) {
		return onError(error);
	}
}

export default Request;

========================================
>>>File: Cms.js

import Request from './Api';


// ai functions
const ai = async (props) => {
    return (
        Request({
            url: "/cms/ai",
            method: "POST",
            data: props,
        })
    );
}

// website functions
const websites={
    get: async (props) => {
        return (
            Request({
                url: "/cms/site"+(props?.id?"/"+props.id:""),
                method: "POST",
                data: props,
            })
        );
    },

    create: async (props) => {
        if (props?.id){
            return (
                Request({
                    url: "/cms/site/edit",
                    data: props,
                    method: "PUT",
                })
            );
        } else {
            return (
                Request({
                    url: "/cms/site/create",
                    data: props,
                    method: "POST",
                })
            );
        }
    }

}

// page functions
const pages={
    history:{
        get: async (props) => {
            return (
                Request({
                    url: "/cms/site/page/history"+(props?.id?"/"+props.id:""),
                    method: "POST",
                    data: props,
                })
            );
        },
    
        create: async (props) => {
            if (props?.id){
                return (
                    Request({
                        url: "/cms/site/page/history/edit",
                        data: props,
                        method: "PUT",
                    })
                );
            } else {
                return (
                    Request({
                        url: "/cms/site/page/history/create",
                        data: props,
                        method: "POST",
                    })
                );
            }
        },
    
        delete: async (props) => {
            return (
                Request({
                    url: "/cms/site/page/history/delete",
                    data: props,
                    method: "DELETE",
                })
            );
        }
    },

    get: async (props) => {
        return (
            Request({
                url: "/cms/site/page"+(props?.id?"/"+props.id:""),
                method: "POST",
                data: props,
            })
        );
    },

    create: async (props) => {
        if (props?.id){
            return (
                Request({
                    url: "/cms/site/page/edit",
                    data: props,
                    method: "PUT",
                })
            );
        } else {
            return (
                Request({
                    url: "/cms/site/page/create",
                    data: props,
                    method: "POST",
                })
            );
        }
    },

    copy: async (props) => {
        return (
            Request({
                url: "/cms/site/page/copy",
                data: props,
                method: "POST",
            })
        );
    },

    delete: async (props) => {
        return (
            Request({
                url: "/cms/site/page/delete",
                data: props,
                method: "DELETE",
            })
        );
    }

}

// page functions
const pageTypes={
    get: async (props) => {
        return (
            Request({
                url: "/cms/site/page/type"+(props?.id?"/"+props.id:""),
                method: "POST",
                data: props,
            })
        );
    },
}

// url functions
const urls={
    /*  Create a url for the company
        ----------------------------
        params:
        -------
        company_id: id of the company, required
        website_id: id of the website to link to that url, can be null to display the default placeholder page
        domain: domain name, required but should default to siteboss.net
        subdomain: subdomain name, required
    */
    create: async (props) => {
        if (!props.domain) props.domain="siteboss.net";

        if (props?.id){
            return (
                Request({
                    url: "/cms/site/url/edit",
                    data: props,
                    method: "PUT",
                })
            );
        } else {
            return (
                Request({
                    url: "/cms/site/url/create",
                    data: props,
                    method: "POST",
                })
            );
        }
    },

    /*  Get a list of urls for a company
        ----------------------------
        params:
        -------
        company_id: id of the company, required
        website_id: id of the website to link to that url
        subdomain: subdomain name
        domain: domain name
    */
    get: async (props) => {
        return (
            Request({
                url: "/cms/site/url"+(props?.id?"/"+props.id:""),
                data: props,
            })
        );
    },
    validate: async (props) => {
        return (
            Request({
                url: "/cms/site/url/validate",
                data: props,
                method: "POST",
            })
        );
    },
    getAll: async(props)=>{
        return(
            Request({
                url: "/cms/site/url",
                data: props,
                method: "GET",
            })
        )
    },
    delete: async(props)=>{
        return(
            Request({
                url: "/cms/site/url/delete",
                data: props,
                method: "DELETE"
            })
        )
    }
}

const themes={
    get: async (props) => {
        return (
            Request({
                url: "/cms/theme"+(props?.id?"/"+props.id:""),
                data: props,
            })
        );
    },

    create: async (props) => {
        if (props?.id){
            return (
                Request({
                    url: "/cms/theme/edit",
                    data: props,
                    method: "PUT",
                })
            );
        } else {
            return (
                Request({
                    url: "/cms/theme/create",
                    data: props,
                    method: "POST",
                })
            );
        }
    },

    copy: async (props) => {
        return (
            Request({
                url: "/cms/theme/copy",
                data: props,
                method: "POST",
            })
        );
    },


    delete: async (props) => {
        return (
            Request({
                url: "/cms/theme/delete",
                data: props,
                method: "DELETE",
            })
        );
    },

    css: {
        get: async (props) => {
            return (
                Request({
                    url: "/cms/theme/css"+(props?.id?"/"+props.id:""),
                    data: props,
                })
            );
        },

        create: async (props) => {
            if (props?.id){
                return (
                    Request({
                        url: "/cms/theme/css/edit",
                        data: props,
                        method: "PUT",
                    })
                );
            } else {
                return (
                    Request({
                        url: "/cms/theme/css/create",
                        data: props,
                        method: "POST",
                    })
                );
            }
        },

        delete: async (props) => {
            return (
                Request({
                    url: "/cms/theme/css/delete",
                    data: props,
                    method: "DELETE",
                })
            );
        }

    }
}

const sendmail = async (props,send_to) => {
    if (typeof send_to === "string") send_to = JSON.parse(send_to);
    props.send_to = send_to;
    return (
        Request({
            url: "/cms/contact/sendmail",
            method: "POST",
            data: props,
        })
    );
}

const forms = {
    get: async (props) => {
        return (
            Request({
                url: "/form/submissions"+(props?.id?"/"+props.id:""),
                data: props,
            })
        );
    },
    send: async (props) => {
        let header_data={};
        if (props.hash){
            // re-do the header to include the hash
            let token="";
            let localuser = localStorage.getItem("user");
            if (localuser) {
                try{
                    localuser=JSON.parse(localuser);
                    token=localuser.token;
                }catch(error){}
            }
            header_data = {'Authorization': token, 'X-Anti-Spam-Hash': props.hash};
            delete props.hash;
        }
        return (
            Request({
                url: "/form/submissions/create",
                method: "POST",
                data: props,
                headers: header_data || undefined,
            })
        );
    }    
}

const deviceList = () => {
    return [
        {
            "name": "iPhone SE",
            "width": 320,
            "height": 568,
            "resX": 640,
            "resY": 1136,
            "pixelRatio": 2
        },
        {
            "name": "iPhone XR",
            "width": 414,
            "height": 896,
            "resX": 828,
            "resY": 1792,
            "pixelRatio": 2
        },
        {
            "name": "iPhone 12 Pro",
            "width": 390,
            "height": 844,
            "resX": 780,
            "resY": 1684,
            "pixelRatio": 2
        },
        {
            "name": "Pixel 5",
            "width": 144,
            "height": 70.4,
            "resX": 1080,
            "resY": 2340,
            "pixelRatio": 4
        },
        {
            "name": "Samsung Galaxy S8+",
            "width": 159.5,
            "height": 73.4,
            "resX": 1440,
            "resY": 2960,
            "pixelRatio": 4
        },
        {
            "name": "Samsung Galaxy S20 Ultra",
            "width": 166.9,
            "height": 76.0,
            "resX": 1440,
            "resY": 3200,
            "pixelRatio": 4
        },
        {
            "name": "iPad Air",
            "width": 250.6,
            "height": 174.1,
            "resX": 2048,
            "resY": 1536,
            "pixelRatio": 2
        },
        {
            "name": "iPad Mini",
            "width": 203.2,
            "height": 134.8,
            "resX": 2048,
            "resY": 1536,
            "pixelRatio": 2
        },
        {
            "name": "Surface Pro 7",
            "width": 292,
            "height": 201,
            "resX": 2736,
            "resY": 1824,
            "pixelRatio": 3
        },
        {
            "name": "Surface Duo",
            "width": 270,
            "height": 185,
            "resX": 1800,
            "resY": 1350,
            "pixelRatio": 2
        },
        {
            "name": "Galaxy Fold",
            "width": 160.9,
            "height": 62.9,
            "resX": 1536,
            "resY": 720,
            "pixelRatio": 4.6
        },
        {
            "name": "Samsung Galaxy A51/71",
            "width": 159.0,
            "height": 75.1,
            "resX": 1080,
            "resY": 2400,
            "pixelRatio": 4
        },
        {
            "name": "Nest Hub",
            "width": 214.6,
            "height": 124.8,
            "resX": 1280,
            "resY": 800,
            "pixelRatio": 2
        },
        {
            "name": "Nest Hub Max",
            "width": 214.6,
            "height": 124.8,
            "resX": 1280,
            "resY": 800,
            "pixelRatio": 2
        },
    ];            
}

const Cms = {
   urls, pages, pageTypes, websites, themes, sendmail, deviceList, ai, forms
}
  
export default Cms;

========================================
>>>File: Common.js

export const formatDate = (date="") => {
    let separator;
    if (date.indexOf("-")>=0) separator="-";
    if (date.indexOf("/")>=0) separator="/";
    if (date){
        let [year,month,day,time]=date.split(separator);
        if (!time) time="00:00:00";
        date=new Date(`${year}-${+month}-${day} ${time}`);
    }
    return date;
}

export const stringToUUID = (str) => {
    if (str === undefined || !str.length) str = "" + Math.random() * new Date().getTime() + Math.random();

    let c = 0, r = "";
    for (let i = 0; i < str.length; i++){
        c = (c + (str.charCodeAt(i) * (i + 1) - 1)) & 0xfffffffffffff;
    }
    
    str = str.substr(str.length / 2) + c.toString(16) + str.substr(0, str.length / 2);
    
    for (let i = 0, p = c + str.length; i < 32; i++){
        if (i === 8 || i === 12 || i === 16 || i === 20) r += "-";

        c = p = (str[(i ** i + p + 1) % str.length]).charCodeAt(0) + p + i;
        if (i === 12) c = (c % 5) + 1; //1-5
        else if (i === 16) c = (c % 4) + 8; //8-B
        else c %= 16; //0-F

        r += c.toString(16);
    }

    return r.toUpperCase();
}

========================================
>>>File: Companies.js

import Request from './Api';
//import {stringToUUID} from './Common';

// get company(s)
const getall = async (props) => {
    return (
        Request({
            url: "/company/getall",
            data: props,
            //test: {data:company}  // send mock data to simulate an api call
        })
    );
}

const get = async (props) => {

    // use test data on localhost only
    //let company={};
    //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

        // gets mock data for testing - will be removed when api is ready
        //let mockdata = await test();

        /*if (!props) company=mockdata;
        else {
            mockdata.forEach((item,i,test) => {
                if (props.id) {
                    if (item.id===parseInt(props.id)){
                        company=test[i];
                        return false;
                    }
                }
            });
        }*/
    //}
    
    return (
        Request({
            url: "/company" + (!props ? "/get" : "/company/"+props.id),
            data: props,
            //test: {data:company}  // send mock data to simulate an api call
        })
    );
}

// create with wizard
const wizard = async (props) => {
    return (
        Request({
            url: "/company/wizard",
            data: props,
            method: "POST"
        })
    );
}

// create
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Company Name is required", "Email is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/company/create",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update
const update = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Company Name is required", "Email is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/company/edit",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// add user to company
const addUser = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["User id is required"]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/company/users/add",
            data: {props},
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// get users in company
const getUsers = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    /*let mockdata = {
        data:[
            {
                id: 1,
                first_name: "Tom",
                last_name: "Huerter",
                role_id: 1,
                role_name: "Admin"
            },
            {
                id: 2,
                first_name: "Joe",
                last_name: "Gero",
                role_id: 1,
                role_name: "Admin"
            },
            {
                id: 3,
                first_name: "Will",
                last_name: "Pedicone",
                role_id: 1,
                role_name: "Admin"
            },
            {
                id: 4,
                first_name: "David",
                last_name: "Oppenheim",
                role_id: 1,
                role_name: "Admin"
            },
            {
                id: 5,
                first_name: "Leo",
                last_name: "Castro",
                role_id: 1,
                role_name: "Admin"
            },
            {
                id: 6,
                first_name: "Raymond",
                last_name: "Elias",
                role_id: 1,
                role_name: "Admin"
            }
        ],
        errors:null
    };*/
    
    return (
        Request({
            url: "/company/users",
            data: props,
            //test: mockdata // send mock data to simulate an api call
        })
    );
}



// sets up mock data
/*const test = async() => {
    return new Promise((resolve, reject) => {
        let dummydata=[
            {
                id: 1,
                name: "Impact Athletics",
                owner_name: "Tom Huerter",
                email: "<EMAIL>",
                primary_number: "+1234567890",
                fax_number: "+1234567890",
                address_street: "Route 146",
                address_street2: "",
                address_city: "Halfmoon",
                address_state: "NY",
                address_postcode: "12065",
                api_key: stringToUUID("1"),
            },
            {
                id: 2,
                name: "WizardPig",
                owner_name: "David Oppenheim",
                email: "<EMAIL>",
                primary_number: "+1 413-650-9300",
                fax_number: "+1 413-650-9300",
                address_street: "115 East St.",
                address_street2: "",
                address_city: "Easthampton",
                address_state: "MA",
                address_postcode: "01027",
                api_key: stringToUUID("2")
            },
        ];

        let promises=[];
        for (let i=0;i<dummydata.length;i++){
            promises.push(getUsers()
            .then(users =>{
                dummydata[i].users=users.data;
            }).catch(e => console.error(e)));
        }

        Promise.all(promises)
        .then(response => {
            resolve(dummydata);
        }).catch(e => console.error(e));

    });
}*/

const getConfig = async (props) => {
    return (
        Request({
            url: "/company_config",
            data: props,
            method: "POST"
        })
    );    
};

const urls = async (props) =>{
    return(
        Request({
            url: "/company/urls",
            data: props,
            method: "POST"
        })
    )
}

const Companies = {
    get, getall, wizard, create, update, addUser, getUsers, getConfig, urls //, delete, etc. ...
}
  
export default Companies;

========================================
>>>File: Config.js

import Request from './Api';

const CompanyConfig = {
    get: async (props) => {
        return (
            Request({
                url: "/company_config",
                method: "POST",
                data: props
            })
        )
    },
    create: async (props) => {
        return (
            Request({
                url: "/company_config/create",
                method: "POST",
                data: props
            })
        );
    },
    update: async (props) =>{
        return (
            Request({
                url: "/company_config/edit",
                method: "PUT",
                data: props
            })
        )
    },
    delete: async (props) => {
        return (
            Request({
                url: "/company_config/delete",
                method: "DELETE",
                data: props
            })
        );
    }
}

const ConfigTypes = {
    get: async (props) => {
        return (
            Request({
                url: "/company_config/type",
                method: "POST",
                data: props
            })
        )
    },
    create: async (props) => {
        return (
            Request({
                url: "/company_config/type/create",
                method: "POST",
                data: props
            })
        );
    },
    delete: async (props) =>{
        return (
            Request({
                url: "/company_config/type/delete",
                method: "DELETE",
                data: props
            })
        );
    },
    edit: async (props)=>{
        return(
            Request({
                url: "/company_config/type/edit",
                method: "PUT",
                data: props
            })
        )
    }
}

const Config = {
    CompanyConfig, ConfigTypes
}

export default Config;

========================================
>>>File: Coupons.js

import Request from './Api';

// get coupon(s)
const get = async (props) => {

    return (
        Request({
            url: "/coupon" + (!props ? "" : "/" + props.id),
            method: "GET"
        })
    );
}
 
// create coupon
const create = async (props) => {

    return (
        Request({
            url: "/coupon/create",
            data: props,
            method: "POST"
        })
    );
}

// update coupon
const update = async (props) => {

    return (
        Request({
            url: "/coupon/edit",
            data: props,
            method: "PUT"
        })
    );
}

// delete coupon
const remove = async (props) => {

    return (
        Request({
            url: "/coupon/delete",
            data: props,
            method: "DELETE"
        })
    );
}

// check coupon name
const checkName = async (props) => {
    return (
        Request({
            url: "/coupon/checkname",
            data: props,
            method: "POST"
        })
    );
}

// checks a coupon code for validity in an order - can this be applied to the items in the cart?
const verifyCode = async props => {
    return (
        Request({
            url: "/coupon/code",
            data: props,
            method: "POST",
        })
    );
}

const Modules = {
    get, create, update, remove, checkName, verifyCode
}
  
export default Modules;


========================================
>>>File: Discounts.js

import Request from './Api';

const get = async (props) => {

    // use test data on localhost only

    let discount={};
    //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

    // gets mock data for testing - will be removed when api is ready
    let mockdata = await test();
    if (!props) discount=mockdata;
    else {
        mockdata.forEach((item,i,test) => {
            if (props.id) {
                if (item.id===parseInt(props.id)){
                    discount=test[i];
                    return false;
                }
            }
        });
    }

    return (
        Request({
            url: "/discounts/" + (!props ? "" : props.id),
            test: {data:discount} // send mock data to simulate an api call
        })
    );
}

// create
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    let mockdata = {data:1,errors:null};

    return (
        Request({
            url: "/discounts/create",
            data: {props},
            test: mockdata // send mock data to simulate an api call
        })
    );
}

// update
const update = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    let mockdata = {data:1,errors:null};

    return (
        Request({
            url: "/discounts/update",
            data: {props},
            test: mockdata // send mock data to simulate an api call
        })
    );
}

// sets up mock data
const test = async() => {
    return [
		{
			id: 1,
			name: "Product Discount",
			code: "var discount, product_id;if (product_id === 1 || 1===1) {discount = 5;}"
		},
		{
			id: 2,
			name: "Black Friday",
			code: "var discount, coupon_code;if (coupon_code === 'BLACKFRIDAYLIVESMATTER') {discount = 30;}"
		},
	];
}


const Discounts = {
	get, create, update/*, delete, etc. ...*/
}
  
export default Discounts;

========================================
>>>File: Email.js

import Request from './Api';

const variables={
    get: async (props) =>{
        return(
            Request({
                url: "/template_variable/template_variable/",
                method: "GET",
                data: props
            })
        )
    },
    getOne: async (props) =>{
        return(
            Request({
                url: `/template_variable/template_variable/" + ${props?.id ? "/" + props?.id : ""}`,
                method: "GET",
                data: props
            })
        )
    }, 
    add: async (props) =>{
        return(
            Request({
                url: "/template_variable/add",
                method: "POST",
                data: props
            })
        )
    },
    update: async (props) =>{
        return(
            Request({
                url: "/template_variable/edit",
                method: "POST",
                data: props
            })
        )   
    },
    delete: async (props) =>{
        return(
            Request({
                url: "/template_variable/delete",
                method: "POST",
                data: props
            })
        )
    }
}

const templates = {
    get: async (props) =>{
        return(
            Request({
                url: `/email/template/template${props?.id ? "/" + props?.id : ''}`,
                method: "GET",
                data: props
            })
        )
    },
    add: async (props) =>{
        return(
            Request({
                url: "/email/template/add",
                method: "POST",
                data: props
            })
        )
    },
    delete: async (props) =>{
        return(
            Request({
                url: "/email/template/delete",
                method: "DELETE",
                data: props
            })
        )
    },
    edit: async (props) =>{
        return(
            Request({
                url: "/email/template/edit",
                method: "POST",
                data: props
            })
        )
    }
}

const templateTypes={
    get: async (props) =>{
        return(
            Request({
                url: `/email/template/type${props?.id ? "/" + props?.id : ""}`,
                method: "GET",
                data: props
            })
        )
    },
    add: async (props) =>{
        return(
            Request({
                url: "/email/template/type/add",
                method: "POST",
                data: props
            })
        )
    },
    delete: async (props)=>{
        return(
            Request({
                url: "/email/tempalte/type/delete",
                method: "DELETE",
                data: props
            })
        )
    },
    edit: async (props)=>{
        return(
            Request({
                url: "/email/template/type/edit",
                method: "POST",
                data: props
            })
        )
    }
}

const Email = {
    variables, templates, templateTypes
}

export default Email;

========================================
>>>File: Error.js

import Request from './Api';

const save = async (props) => {
    return (
        Request({
            url: "/create-log-error",
            method: "POST",
            data: {...props}
        })
    );
}


const Error = {
	save
}
  
export default Error;

========================================
>>>File: Events.js

import Request from './Api';


const publicGet = async (props) => {
    if (props?.event_types){
        if (props.event_types && typeof props.event_types === "string"){
            if (!(props.event_types.startsWith("[") && props.event_types.endsWith("]"))) props.event_types = [props.event_types];
            else JSON.parse(props.event_types);
        }
    }

    return(
        Request({
            url:"/public/event",
            data: props,
            method: "POST",
        })
    );
}

// Get event(s) - these are split into different endpoints because each [can] have different permissions attached

const getSingle = async (props) => {
    return (
        Request({
            url: "/event/" + props.id,
            method: "GET"
        })
    );
}

const getSimple = async (props) => {
    return (
        Request({
            url: "/event",
            method: "POST",
            data: props
        })
    );
}

const getDetail = async (props) => {
    return (
        Request({
            url: "/event/detail",
            method: "POST",
            data: props
        })
    );
}

const getForUser = async (props) => {
    return (
        Request({
            url: "/event/user",
            method: "POST",
            data: props
        })
    );
}

const getService = async (props) => {
    return (
        Request({
            url: "/service/event",
            method: "POST",
            data: props
        })
    );
}

const getServiceForUser = async (props) => {
    return (
        Request({
            url: "/service/event/user",
            method: "POST",
            data: props
        })
    );
}

// Create new event
const create = async (props) => {
    return (
        Request({
            url: "/event/add",
            method: "POST",
            data: props
        })
    );
}

// Edit event
const edit = async (props) => {
    return (
        Request({
            url: "/event/edit",
            method: "POST",
            data: props
        })
    );
}

// Create new events from wizard
const addWizard = async (props) => {
    return (
        Request({
            url: "/event/add_wiz",
            method: "POST",
            data: props
        })
    );
}


// Event types
const Types = {

    get: async(props)=>{
        return (
            Request({
                url: "/event/type",
                method: "POST",
                data: props
            })
        );
    },
    
    create: async(props)=>{
        return (
            Request({
                url: "/event/type/create",
                method: "POST",
                data: props
            })
        );
    },
    
    edit: async(props)=>{
        return (
            Request({
                url: "/event/type/edit",
                method: "PUT",
                data: props
            })
        );
    },
    
    delete: async(props)=>{
        return (
            Request({
                url: "/event/type/delete",
                method: "DELETE",
                data: props
            })
        );
    },
    
    order: async(props)=>{
        return (
            Request({
                url: "/event/type/order",
                method: "POST",
                data: props
            })
        );
    },
    
}

// Edit event role of user
const edit_role = async (props) => {
    return (
        Request({
            url: "/event/edit_role",
            method: "POST",
            data: props
        })
    );
}

const get_user_responses = async (props) => {
    return (
        Request({
            url: "/event/user_responses",
            method: "POST",
            data: props
        })
    )
}

const get_custom_fields = async (props) => {
    return (
        Request({
            url: "/event/custom_fields/" + props.event_id,
            method: "GET",
            data: props
        })
    )
}

const set_custom_fields = async (props) => {
    return (
        Request({
            url: "/event/custom_fields/" + props.event_id + "/edit",
            method: "PUT",
            data: props
        })
    )
}

const invite = async (props) => {
    return (
        Request({
            url: "/event/invite",
            method: "POST",
            data: props
        })
    )
}

const remove_members = async (props) => {
    return (
        Request({
            url: "/event/remove_members",
            method: "DELETE",
            data: props
        })
    )
}

const export_attendees = async (props) => {
    return (
        Request({
            url: "/event/attendees/export",
            method: "POST",
            data: props
        })
    )
}

const locationsAvailable = async (props) => {
    return (
        Request({
            url: "/event/locations_available",
            method: "POST",
            data: props
        })
    )
}

// Get Event by Statuses
const getEventStatus = async (props)=>{
    return(
        Request ({
            url: "/event/statuses",
            method: "GET",
            data: props
        })
    )
}

const editEventStatus = async (props)=>{
    return(
        Request({
            url: "/event/status/"+props.event_id,
            method: "PUT",
            data: props
        })
    );
}

const eventPostFilter = async (props)=>{
    return(
        Request({
            url: "/event",
            method: "POST",
            data: props
        })
    )
}

const getByLocationAndDate = async (props)=>{
    return(
        Request({
            url: "/event/by_location_and_date_range",
            method: "POST",
            data: props
        })
    )
}

const Events = {
    publicGet, getSingle, getSimple, getDetail, getForUser, getService, getServiceForUser, create, edit, addWizard, Types, edit_role, get_user_responses, get_custom_fields, set_custom_fields, invite, remove_members, export_attendees, locationsAvailable, getEventStatus, editEventStatus, eventPostFilter, getByLocationAndDate
}
  
export default Events;

========================================
>>>File: GiftCards.js

import Request from './Api';

const get = async (props) => {
    return(
        Request({
            url:"/giftcard",
            data: props,
            method: "POST",
        })
    );
}


// create
const create = async (props) => {
    return (
        Request({
            url: "/giftcard/create",
            data: props,
            method: "POST"
        })
    );
}

// update
const edit = async (props) => {
    return (
        Request({
            url: "/giftcard/edit",
            method: "PUT",
            data: props
        })
    );
}

const remove = async (props) => {
    return(
        Request({
            url: "/giftcard/delete",
            data: props,
            method: "DELETE"
        })
    );
}

const transactions = async (props) => {
    return(
        Request({
            url: "/giftcard/transactions",
            data: props,
            method: "POST"
        })
    );
}


const GiftCards = {
    get, create, edit, remove, transactions
}
  
export default GiftCards;

========================================
>>>File: Groups.js

import Request from './Api';
//import {stringToUUID} from './Common';
// get group(s)

const publicGet = async (props) => {
    return(
        Request({
            url:"/public/group",
            data: props,
            method: "POST",
        })
    );
}


const get = async (props) => {

    // // use test data on localhost only

    // let group={};
    // //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

    // // gets mock data for testing - will be removed when api is ready
    // let mockdata = await test();
    // if (!props) group=mockdata;
    // else {
    //     mockdata.forEach((item,i,test) => {
    //         if (props.id) {
    //             if (item.id===parseInt(props.id)){
    //                 group=test[i];
    //                 return false;
    //             }
    //         }
    //     });
    // }

    return (
        Request({
            url: "/group/" + (!props ? "" : props.id),
            method: "GET",
            // test: {data:group} // send mock data to simulate an api call
        })
    );
}

// create
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    //let mockdata = {data:1,errors:null};
    return (
        Request({
            url: "/group/create",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update
const edit = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/group/edit",
            method: "POST",
            data: props
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// invite users
// takes a group id and the array of users to be invited
const invite = async (props) => {

    return (
        Request({
            url: "/group/invite",
            data: props,
            method: "POST"
        })
    );
}

const edit_member = async (props) => {

    return (
        Request({
            url: "/group/edit_member",
            data: props,
            method: "POST"
        })
    );
}

const accept_invitation = async (props) => {

    return(
        Request({
            url: "/group/accept_invitation",
            data: props,
            method: "POST"
        })
    );
}

const delete_member = async (props) => {

    return(
        Request({
            url: "/group/delete_member",
            data: props,
            method: "DELETE"
        })
    );
}

const groupFilter=async(props)=>{
    
    return(
        Request({
            url:"/group/list",
            data: props,
            method: "POST",
        })
    );
}

const getGroupTypes=async(props)=>{

    return(
        Request({
            url: "/group_type",
            method: "GET"
        })
    )
}

// sets up mock data
const test = async() => {
    return new Promise((resolve, reject) => {
        let dummymembers=[
            {
                id: 1,
                first_name: "Tom",
                last_name: "Huerter",
                role_id: 1,
                role: "Chimney Sweep",
                status_id: 1,
                status: "Invited"
            },
            {
                id: 2,
                first_name: "Joe",
                last_name: "Gero",
                role_id: 1,
                role: "Chimney Sweep",
                status_id: 2,
                status: "Confirmed"
            },
            {
                id: 3,
                first_name: "Will",
                last_name: "Pedicone",
                role_id: 1,
                role: "Chimney Sweep",
                status_id: 3,
                status: "Suspended"
            },
            {
                id: 4,
                first_name: "David",
                last_name: "Oppenheim",
                role_id: 1,
                role: "Chimney Sweep",
                status_id: 4,
                status: "Banned"
            },
            {
                id: 5,
                first_name: "Leo",
                last_name: "Castro",
                role_id: 1,
                role: "Chimney Sweep",
                status_id: 5,
                status: "Pending"
            },
        ];
        let dummyroles=[
            {
                id: 1,
                name: "Chimney Sweep",
                description: "Performs rooftop dance routines"
            },
            {
                id: 2,
                name: "Wicked Witch",
                description: "Gobbles small children"
            },
            {
                id: 3,
                name: "Starship Janitor",
                description: "Mops hyperdrive heat sink condensation"
            },
            {
                id: 4,
                name: "Chosen One",
                description: "Lacks the high ground"
            }
        ];
        let dummydata=[
            {
                id: 123,
                parent_id: 0,
                group_status_id: 1,
                group_status: 'Active',
                group_type_id: 4,
                group_type: 'Family',
                company_id: 0,
                name: "Family",
                description: "Making a mess",
                members: dummymembers,
                roles: dummyroles
            },
            {
                id: 33,
                parent_id: 0,
                group_status_id: 1,
                group_status: 'Active',
                group_type_id: 2,
                group_type: 'Team',
                company_id: 0,
                name: "The Hornets",
                description: "Over 50 basketball league",
                members: dummymembers,
                roles: dummyroles
            },
            {
                id: 633,
                parent_id: 0,
                group_status_id: 1,
                group_status: 'Active',
                group_type_id: 2,
                group_type: 'Team',
                company_id: 0,
                name: "The Cool",
                description: "Under 80 Shuffleboard",
                members: dummymembers,
                roles: dummyroles
            },
        ];

        resolve(dummydata);
    });
}

const Groups = {
    publicGet, get, create, edit, invite, edit_member, accept_invitation, delete_member, groupFilter, getGroupTypes //, delete, etc. ...
}
  
export default Groups;

========================================
>>>File: Locations.js

import Request from './Api';

const get = async(props) => {

    // remove this when the tables are mapped on the live server
    if (props?.tables){
        let mockdata = await rtest();
        return (
            Request({
                url: "/location" + (!props ? "": "/"+props.id),
                data: {...props},
                test: {data:mockdata} // send mock data to simulate an api call
            })
        );
    }

    return (
        Request({
            url: "/location",
            data: {...props},
            method: "POST"
        })
    );
}


const getStructured = async(props) => {
    let structuredArray = { errors: null, data: [] };
    let unplacedLocations = [];
    let availabilities = getGenericAvailabilities();

    const search = (location, parent) => {
        if(parent.id === location.parent_id) {
            //parent found, add to children and remove from unplacedLocations
            location.general_availabilities = availabilities;
            parent.children = [...parent.children, {...location, children: []}];
            unplacedLocations = unplacedLocations.filter( item => item.id !== location.id);
        } else if(parent.children.length > 1) {
            //no match, search deeper
            parent.children.map( child => search(location, child) );
        }
    }

    await get(props)
    .then(response => {
        let locations = response.data;
        let i = 0;
        unplacedLocations = locations;
        while(i < locations.length && unplacedLocations.length > 0) {
            for(let j = 0; j < [...unplacedLocations].length; j++) {
                let location = [...unplacedLocations][j];
                if( !location.parent_id || location.id === 1 ) {
                    //place top-level locations with no parents
                    //id check is temp fix for seed error: main building should have parent_id null
                    location.general_availabilities = availabilities;
                    structuredArray.data = [...structuredArray.data, {...location, children: []}];
                    unplacedLocations = unplacedLocations.filter( item => item.id !== location.id);
                } else {
                    //has a parent location, need to find
                    structuredArray.data.map( parent => search(location, parent) );
                }
                return null;
            }
            i += 1;
        }
    })
    .catch(e => console.error(e));

    return structuredArray;
}

const getWithAvailabilities = async (props) => {
    // Locations.getEvents({id: props.location, start_datetime: date_start, end_datetime: date_end, include_sublocations: 0})
    let availabilities = getGenericAvailabilities();
    let returnData = { errors: null, data: {} };

    try {
        await get(props)
        .then(response => {
            returnData.data = response.data[0];
            returnData.data.general_availabilities = availabilities;
        });
        await getEvents(props)
        .then(response => {
            returnData.data.events = response.data?.filter( event => //other events at this location
                event.type_id !== 5 //not meta
                && event.status_id !== 3 //not postponed
                && event.status_id !== 4 //not canceled
            );
        });
    } catch (e) {
        console.error(e);
    }

    return returnData;
}

// create location
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Location Name is required", "Category is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/location/create",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update location
const update = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Location Name is required", "Type is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/location/edit",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// Location Types
const Types = {

    // get status
    get: async(props)=>{
        // sets mock data for testing - will be removed when api is ready
        //let mockdata = {data:null,errors:["Location Type Id is required"]};
        let mockdata = {
            data: [
                {
                    "id": 1,
                    "name": "Primary",
                    "description": null,
                    "children":[]
                },
                {
                    "id": 2,
                    "name": "Gym",
                    "description": null,
                    "children":[
                        {
                            "id": 5,
                            "name": "Court",
                            "description": null,
                            "children":[]
                        },                                
                    ]
                },
                {
                    "id": 3,
                    "name": "Front Desk",
                    "description": null,
                    "children":[]
                },
                {
                    "id": 4,
                    "name": "Restaurant",
                    "description": null,
                    "children":[
                        {
                            "id": 6,
                            "name": "Kitchen",
                            "description": null,
                            "children":[]
                        },
                        {
                            "id": 7,
                            "name": "Register",
                            "description": null,
                            "children":[]
                        },
                        {
                            "id": 8,
                            "name": "Dining Area",
                            "description": null,
                            "children":[
                                {
                                    "id": 9,
                                    "name": "Table",
                                    "description": null,
                                    "children":[]
                                },
                            ]
                        },
                        {
                            "id": 10,
                            "name": "Bar",
                            "description": null,
                            "children":[]
                        }                
                    ]
                },
                {
                    "id": 11,
                    "name": "Printer",
                    "description": null,
                    "children":[]
                },
            ],
            errors:null
        };

        if (props?.id){
            const filtered_data=mockdata.data.filter(item => item.id===parseInt(props.id));
            mockdata.data=filtered_data;
        }
        
        return (
            Request({
                url: "/location/type",
                data: {props},
                test: mockdata // send mock data to simulate an api call
            })
        );
    },
}


// Location Shapes
const Shapes = {

    // ge status
    get: async(props)=>{
        // sets mock data for testing - will be removed when api is ready
        //let mockdata = {data:null,errors:["Location Type Id is required"]};

        const blob_circle = new Blob([`<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50"><circle cx="25" cy="25" r="20"/></svg>`], {type: 'image/svg+xml'});
        const blob_square = new Blob([`<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50"><rect cx="25" cy="25" r="20"/></svg>`], {type: 'image/svg+xml'});
        const blob_rectangle = new Blob([`<svg xmlns="http://www.w3.org/2000/svg" width="150" height="50"><rect cx="25" cy="25" r="20"/></svg>`], {type: 'image/svg+xml'});

        let mockdata = {
            data: [
                {
                    "id": 1,
                    "name": "Square",
                    "visual":blob_square
                },
                {
                    "id": 2,
                    "name": "Circle",
                    "visual":blob_circle
                },
                {
                    "id": 3,
                    "name": "Rectangle",
                    "visual":blob_rectangle
                },
            ],
            errors:null
        };

        if (props?.id){
            const filtered_data=mockdata.data.filter(item => item.id===parseInt(props.id));
            mockdata.data=filtered_data;
        }
        
        return (
            Request({
                url: "/location/shape",
                data: props,
                test: mockdata // send mock data to simulate an api call
            })
        );
    },
}


// Event Types 
const Events = {
    Types: {
        // ge status
        get: async(props)=>{
            // sets mock data for testing - will be removed when api is ready
            //let mockdata = {data:null,errors:["Location Id is required"]};

            /*let mockdata = {
                data: [
                    {
                        "id": 1,
                        "name": "Class",
                        "description":"",
                        "is_meta":null,
                        "has_product_fee":null,
                        "allow_reserved_location":null,
                        "group_invites":null,
                        "individual_invites":null,
                        "has_divisions":null,
                        "allowed_subevents":null,
                        "locations":[5,15,16,17,18,19,20]
                    },
                    {
                        "id": 2,
                        "name": "Practice",
                        "description":"",
                        "is_meta":null,
                        "has_product_fee":null,
                        "allow_reserved_location":null,
                        "group_invites":null,
                        "individual_invites":null,
                        "has_divisions":null,
                        "allowed_subevents":null,
                        "locations":[5,15,16,17,18,19,20]
                    },
                    {
                        "id": 3,
                        "name": "Reservation",
                        "description":"",
                        "is_meta":null,
                        "has_product_fee":null,
                        "allow_reserved_location":null,
                        "group_invites":null,
                        "individual_invites":null,
                        "has_divisions":null,
                        "allowed_subevents":null,
                        "locations":[26,22,14,13]
                    },
                    {
                        "id": 4,
                        "name": "Game",
                        "description":"",
                        "is_meta":null,
                        "has_product_fee":null,
                        "allow_reserved_location":null,
                        "group_invites":null,
                        "individual_invites":null,
                        "has_divisions":null,
                        "allowed_subevents":null,
                        "locations":[15,16,17,18,19,20]
                    },
                ],
                errors:null
            };

            if (props?.id){
                const filtered_data=mockdata.data.filter(item => item.id===parseInt(props.id));
                mockdata.data=filtered_data;
            }*/
            
            return (
                Request({
                    url: "/location/event/type",
                    data: {props},
                    //test: mockdata // send mock data to simulate an api call
                })
            );
        },
    }
}



const getGenericAvailabilities=()=>{
    //const d=new Date();

    let slots = [];
    let slotsPerHour = 4;

    for(let day = 2; day <= 6; day += 1) { // Monday through Friday
        for(let hour = 6; hour < 22; hour += 1) {  // open 06:00 until 22:00
            for(let slotNum = 1; slotNum <= slotsPerHour; slotNum += 1) {

                let slotLength = 60 / slotsPerHour;
                let timestamp_hour = hour < 10 ? "0" + hour : hour;
                let timestamp_start_minutes = slotLength * ( slotNum - 1 ); //end of previous slot
                if (timestamp_start_minutes < 10) timestamp_start_minutes = "0" + timestamp_start_minutes;
                let timestamp_end_minutes = slotLength * slotNum - 1; //one minute before next slot
                if (timestamp_end_minutes < 10) timestamp_end_minutes = "0" + timestamp_end_minutes;

                slots = [ ...slots, {
                    day_of_week: day,
                    start_time: timestamp_hour + ":" + timestamp_start_minutes,
                    end_time: timestamp_hour + ":" + timestamp_end_minutes
                } ]
            }
        }
    }

    for(let day = 1; day <= 7; day += 6) { // Saturday and Sunday
        for(let hour = 7; hour < 22; hour += 1) {  // open 07:00 until 22:00
            for(let slotNum = 1; slotNum <= slotsPerHour; slotNum += 1) {

                let slotLength = 60 / slotsPerHour;
                let timestamp_hour = hour < 10 ? "0" + hour : hour;
                let timestamp_start_minutes = slotLength * ( slotNum - 1 ); //end of previous slot
                if (timestamp_start_minutes < 10) timestamp_start_minutes = "0" + timestamp_start_minutes;
                let timestamp_end_minutes = slotLength * slotNum - 1; //one minute before next slot
                if (timestamp_end_minutes < 10) timestamp_end_minutes = "0" + timestamp_end_minutes;

                slots = [ ...slots, {
                    day_of_week: day,
                    start_time: timestamp_hour + ":" + timestamp_start_minutes,
                    end_time: timestamp_hour + ":" + timestamp_end_minutes
                } ]
            }
        }
    }

    /*let filled=[];
    let general_availabilities=[];    
    for (let j=0;j<7;j++){
        for (let k=0;k<24;k++){

            for (let l=0;l<4;l++){
                general_availabilities.push(
                    {
                        day_of_week:j,
                        start_time:`${k}:${l*15}`,
                        end_time:`${k}:${l*15+14}`
                    },        
                );
            }

            /*
            let item = slots[Math.floor(Math.random() * slots.length)];
            const ast=item["start_time"].split(":");
            const aet=item["end_time"].split(":");
            if (!filled[ast[0]]){
                availability.push({...item,day_of_week:j});
                for (let i=ast[0];i<=aet[0];i++){
                    filled[i]=i;
                }
            }
        }
    }

    general_availabilities.sort(function(a, b) {
        const time_a=a.start_time.split(":");
        const time_b=b.start_time.split(":");
        const key_a = new Date(d.getDate(),d.getMonth(),d.getFullYear(),time_a[0],time_a[1]);
        const key_b = new Date(d.getDate(),d.getMonth(),d.getFullYear(),time_b[0],time_b[1]);
        if (key_a < key_b) return -1;
        if (key_a > key_b) return 1;
        return 0;
    });*/

    return slots;
}

const getEvents = props => {
    return (
        Request({
            url: "/location/events",
            data: props,
            method: "POST"
        })
    );
}


// Print Location
const printLocation = {
    getNext: props =>{
        return (
            Request({
                url: "/print_location",
                method: "POST",
                data: props,
            })
        );    
    },
    complete: props =>{
        return (
            Request({
                url: "/print_location/complete",
                method: "POST",
                data: props,
            })
        );    
    },
    addToQueue: props =>{
        return (
            Request({
                url: "/print_location/create",
                method: "POST",
                data: props,
            })
        );
    },
    getLocations: props =>{
        return (
            Request({
                url: "/print_location/location",
                method: "POST",
                data: props,
            })
        );
    },
};



// sets up mock data for tables
const rtest = async () => {
    return new Promise((resolve, reject) => {
        let dummydata=[

            {
                id: 121,
                name: "Restaurant",
                parent_id:1,
                location_type_id: 1,
                location_shape_id: 1,
                location_shape_blob: null,
                height:100,
                width:100,
                left:210,
                top:30,
                selectable:1,
                selectMultiple:0,
                availability:null,
                children:[
                    {
                        id: 123,
                        name: "Table 1",
                        parent_id:1,
                        location_type_id: 1,
                        location_shape_id: 1,
                        location_shape_blob: null,
                        height:100,
                        width:100,
                        left:50,
                        top:50,
                        selectable:1,
                        selectMultiple:0,
                        availability:null,
                        children:[]
                    },
                    {
                        id: 124,
                        parent_id:1,
                        name: "Table 2",
                        location_type_id: 1,
                        location_shape_id: 1,
                        location_shape_blob: null,
                        height:100,
                        width:100,
                        left:155,
                        top:50,
                        selectable:1,
                        selectMultiple:0,
                        availability:null,
                        children:[]
                    },
                    {
                        id: 125,
                        parent_id:1,
                        name: "Table 3",
                        location_type_id: 1,
                        location_shape_id: 1,
                        location_shape_blob: null,
                        height:100,
                        width:100,
                        left:260,
                        top:50,
                        selectable:1,
                        selectMultiple:0,
                        availability:null,
                        children:[]
                    },
                    {
                        id: 126,
                        parent_id:1,
                        name: "Table 4",
                        location_type_id: 1,
                        location_shape_id: 1,
                        location_shape_blob: null,
                        height:100,
                        width:100,
                        left:155,
                        top:155,
                        selectable:1,
                        selectMultiple:0,
                        availability:null,
                        children:[]
                    }        
                ]
            },
        ]


        

        resolve(dummydata);
    })
}


const Locations = {
	get, getStructured, getWithAvailabilities, create, update, getEvents, Types, Shapes, Events, printLocation //, delete, etc. ...
}
  
export default Locations;

========================================
>>>File: Media.js

import Request from './Api';


const Media = {

}
  
export default Media;

========================================
>>>File: Memberships.js

import Request from './Api';

// get memberships
const get = async (props) => {

    // use test data on localhost only
    let membership={};
    //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

        // gets mock data for testing - will be removed when api is ready
        let mockdata = await test();

        if (!props) membership=mockdata;
        else {
            mockdata.forEach((item,i,test) => {
                if (props.id) {
                    if (item.id===parseInt(props.id)){
                        membership=test[i];
                        return false;
                    }
                }
            });
        }
    //}
    
    return (
        Request({
            url: "/membership/get",
            data: props,
            test: {data:membership}  // send mock data to simulate an api call
        })
    );
}


// sets up mock data
const test = async() => {
    return new Promise((resolve, reject) => {
        let dummydata=[
            {
                id: 1,
                name: "Basic Membership",
                description: `<ul>
                    <li>Access to open court (when available)</li>
                    <li>Access to walking track</li>
                    <li>Access to locker room</li>
                    <li>1x free access to recovery (cryotherapy or normatec leg sleeves)</li>
                    <li>1x free Fitness Center workout</li>
                    <li>25% off classes, services, golf simulator (does not apply to league fees)</li>
                    <li>10% off apparel in Impact Store</li>
                    <li>10% off restaurant for individual member</li>
                    <li>1x month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)</li>
                </ul>`,
                items:[
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                    {
                        id: 12,
                        name: "Access to walking track",
                        qty: 1
                    },
                    {
                        id: 13,
                        name: "Access to locker room",
                        qty: 1
                    },
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 1
                    },
                    {
                        id: 15,
                        name: "Free Fitness Center workout",
                        qty: 1
                    },
                    {
                        id: 16,
                        name: "25% off classes, services, golf simulator (does not apply to league fees)",
                        qty: 1
                    },
                    {
                        id: 17,
                        name: "10% off apparel in Impact Store",
                        qty: 1
                    },
                    {
                        id: 18,
                        name: "10% off restaurant for individual member",
                        qty: 1
                    },
                    {
                        id: 19,
                        name: "Month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)",
                        qty: 1
                    },
                ],
                billing_price: 39.00,
                billing_period: 0, // month
                users: 239
            },
            {
                id: 2,
                name: "Athlete with team/league/class",
                description: `<ul>
                    <li>1x free access to recovery (cryotherapy or normatec leg sleeves ($25 value)</li>
                    <li>1x free smoothie in Impact Restaurant ($8 value)</li>
                    <li>Upgrade to Individual membership</li>
                </ul>`,
                items:[
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 1
                    },
                    {
                        id: 20,
                        name: "Free smoothie in Impact Restaurant ($8 value)",
                        qty: 1
                    },
                    {
                        id: 21,
                        name: "Upgrade to Individual membership",
                        qty: 1
                    },
                ],
                billing_price: 39.00,
                billing_period: 1, //year
                users: 102
            },
            {
                id: 3,
                name: "Household ",
                description: `<ul>                
                    <li>2 adults living at same address. Up to 3 kids under 18 yrs old free. Additional children additional $10/month</li>
                    <li>Access to open court (when available)</li>
                    <li>Access to walking track</li>
                    <li>Access to locker room</li>
                    <li>2x free access to recovery (cryotherapy or normatec leg sleeves)</li>
                    <li>2x free Fitness Center workout</li>
                    <li>25% off classes, services, golf simulator (does not apply to league fees)</li>
                    <li>10% off apparel in Impact Store</li>
                    <li>10% off restaurant for household members</li>
                    <li>2x month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking rack, 1 free Fitness Center workout)</li>
                    <li>4x drop in 90-minute Child Care/month (2yrs – 7 yrs and potty trained)</li>
                </ul>`,
                items:[
                    {
                        id: 22,
                        name: "2 adults living at same address. Up to 3 kids under 18 yrs old free. Additional children additional $10/month",
                        qty: 1
                    },
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                    {
                        id: 12,
                        name: "Access to walking track",
                        qty: 1
                    },
                    {
                        id: 13,
                        name: "Access to locker room",
                        qty: 1
                    },
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 2
                    },
                    {
                        id: 15,
                        name: "Free Fitness Center workout",
                        qty: 2
                    },
                    {
                        id: 16,
                        name: "25% off classes, services, golf simulator (does not apply to league fees)",
                        qty: 1
                    },
                    {
                        id: 17,
                        name: "10% off apparel in Impact Store",
                        qty: 1
                    },
                    {
                        id: 18,
                        name: "10% off restaurant for individual member",
                        qty: 1
                    },
                    {
                        id: 19,
                        name: "Month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)",
                        qty: 2
                    },
                    {
                        id: 23,
                        name: "Drop in 90-minute Child Care/month (2yrs – 7 yrs and potty trained)",
                        qty: 4
                    },
                ],
                billing_price: 79.00,
                billing_period: 0,
                users: 49
            },
            {
                id: 4,
                name: "Student (HS and College)",
                description: `<ul>                
                    <li>Present current year school ID</li>
                    <li>Access to open court (when available)</li>
                </ul>`,
                items:[
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                ],
                billing_price: 19.99,
                billing_period: 0,
                users: 32
            },
            {
                id: 5,
                name: "Military & First Responders",
                description: `<ul>                
                    <li>Present current/valid ID annually</li>
                    <li>10% off Individual ($39) or Household ($79) membership levels</li>
                </ul>`,
                items:[],
                billing_price: 35,
                billing_period: 0,
                users: 17
            },
            {
                id: 6,
                name: "Senior Citizen",
                description: `<ul>                
                    <li>Present current/valid ID. Applies to 55 years and older</li>
                    <li>10% off Individual ($39) or Household ($79) membership levels</li>
                </ul>`,
                items:[],
                billing_price: 35,
                billing_period: 0,
                users: 21
            },
        ];

        resolve(dummydata);
    });
}


const Memberships = {
    get //, create, update, delete, etc. ...
}
  
export default Memberships;

========================================
>>>File: Menu.js

import Request from './Api';

const getSideBar = () => {
	let menu=[];
	let localuser = localStorage.getItem("user");
	if (localuser) {
		try{
			localuser=JSON.parse(localuser);
			menu=localuser.menus[0].sidebar;
		} catch(error){
		}
	}

	return menu;

}

const getProfileMenu = () => {
	let menu=[];
	let localuser = localStorage.getItem("user");
	if (localuser) {
		try{
			localuser=JSON.parse(localuser);
			menu=localuser.menus[0].profile_menu;
		} catch(error){
		}
	}
	return menu;
}


const getFullMenu = async(props) => {
	if (props?.token) {
		return (
			Request({
				headers: { 'Authorization': props.token },
				url: "/user/menu",
				method: "POST"
			})
		);
	} else {
		return (
			Request({
				data: {...props},
				url: "/user/menu",
                method: "POST"
			})
		);
	}
}

const Menu = {
	getSideBar, getProfileMenu, getFullMenu //, create, update, delete, etc. ...
}
  
export default Menu;

========================================
>>>File: Notes.js

import Request from './Api';


// let notes = {} //Turn on for test

//read/get
const get = async(props)=>{
    // if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){
    //     let mockData = await test();
    //     if (!props)notes=mockData;
    //     else{
    //         mockData.forEach((data, i, test)=>{
    //             if(props.id){
    //                 if (data.id ===parseInt(props.id)){
    //                     notes=test[i];
    //                     return false;
    //                 }
    //             }
    //         })
    //     }
    // } //Set-up for test data
    return(
        Request({
            url: "/user/notes/"+props.userId,
            method: "GET", 
            // test: {data:notes}  //Turn on for test
        })
    );
}

//create
const create = async(props)=>{

    // let mockdata= {data: null, errors:["note is required", "user_id is required."]}; //error-turn on for test
    // let mockdata= {"errors":null,"data":[{"id":4}]} //success-turn on for test

    return(
        Request({
            url: "user/notes/add",
            method: "POST",
            data: {...props},
            // test: mockdata //Turn on for test
        })
    )
}

//update
const edit = async(props)=>{

    //  let mockdata= {data: null, errors:["note is required", "user_id is required."]}; //error
    //  let mockdata= {"errors":null,"data":[{"id":4}]} //success
     
     return(
         Request({
             url: "user/notes/edit",
             method: "POST", 
             data: props,
            //  test: mockdata
            })
            )
        }
        
        //delete
const remove = async(props)=>{

        // let mockdata= {data: null, errors:["id is required."]}; //error -turn on for test 
        // let mockdata= {"errors":null,"data":[{"id":4}]} //success - turn on for test

    return(
        Request({
            url: "/notes/delete",
            method: "POST",
            data: {props},
            // test: mockdata //- turn on for test
        })
    )
}

const test =async()=>{
    return new Promise((resolve)=>{
        let dummyData=[
                {
                    "id":1, //note id
                    "author_user_id":1338,
                    "author_first_name":"Geralt",
                    "author_last_name":"of Rivia",
                    "note":"He heard singing.  He didn’t understand the words; he couldn’t even identify the language.  He didn’t need to – the witcher felt and understood the very nature, the essence, of this quiet, piercing singing which flowed through the veins in a wave of nauseous, overpowering menace.",
                    "status":1,
                    "created_at":"2022-01-06T19:52:33.000000Z"
                },
                {
                    "id":2, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Joe",
                    "author_last_name": "Bishop",
                    "note": "But you’re not that smart, I mean, your species is responsible for Windows Vista.” “Vist- that was a long time ago!” “It’s still an insult to computers across the galaxy.",
                    "status": 2,
                    "created_at": "2022-01-01T19:54:33.000000Z"
                },
                {
                    "id":3, //note id
                    "author_user_id": 1338,
                    "author_first_name": "Skippy",
                    "author_last_name": "The Magnificent",
                    "note": "Also, I had to remember that word ‘committee’ had two Ms, two Ts and two Es in only nine letters, which told me that a committee was not a good way to do anything efficiently",
                    "status": 3,
                    "created_at": "2022-01-03T19:54:33.000000Z"
                },
        
                {
                    "id":4, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Atticus",
                    "author_last_name": "O'Sullivan",
                    "note": "Like many silly codes of bravery and manliness, the meat of my father’s instruction on how to die well can be distilled to a simple slogan: Die angry at maximum volume. (Dying silently is out of the question; the world’s last Druid should not go gentle into that good night.)",
                    "status": 2,
                    "created_at": "2021-12-06T19:54:33.000000Z"
                },
                {
                    "id":5, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Locke",
                    "author_last_name": "Lamora",
                    "note": "We’re a different sort of thief here, Lamora. Deception and misdirection are our tools. We don’t believe in hard work when a false face and a good line of bull can do so much more.",
                    "status": 4,
                    "created_at": "2022-01-05T19:54:33.000000Z"
                },
                {
                    "id":6, //note id
                    "author_user_id": 1338,
                    "author_first_name": "Durian",
                    "author_last_name": "Millpot",
                    "note": "The toast always lands butter side down.",
                    "status": 2,
                    "created_at": "2021-12-12T19:54:33.000000Z"
                },
                {
                    "id":7, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Durian",
                    "author_last_name": "Millpot",
                    "note": "The toast always lands butter side down.",
                    "status": 3,
                    "created_at": "2022-01-04T19:54:33.000000Z"
                },
                {
                    "id":8, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Durian",
                    "author_last_name": "Millpot",
                    "note": "Different Note",
                    "status": 3,
                    "created_at": "2022-01-06T19:54:33.000000Z"
                },
                {
                    "id":9, //note id
                    "author_user_id": 1337,
                    "author_first_name": "Durian",
                    "author_last_name": "Millpot",
                    "note": "A note about toast",
                    "status": 3,
                    "created_at": "2022-01-02T19:54:33.000000Z"
                }

        ];
        resolve(dummyData);
    });
}

const NoteApi = {
    get, create, edit, remove //CRUD
}

export default NoteApi

========================================
>>>File: Notifications.js

import Request from './Api';

const get = async(props) => {
    // gets mock data for testing - will be removed when api is ready
    //let tasks = await test();

    return (
        Request({
            url: "/notification/list",
            data: {
                user_id: props?.user_id,
            },
            //test: {data:tasks} // send mock data to simulate an api call
        })
    );
}

const Channel = {
	getByUser: async props=>{
		return(
			Request({
				url: "/user/notification/channel/" + (props?.id),
				data: props,
				method: "GET"
			})
		)
	},
	delete: async props=>{
		return(
			Request({
				url: "/user/notification/channel/delete",
				data: props,
				method: "DELETE"
			})
		)
	},
	update: async props=>{
		return(
			Request({
				url: "/user/notification/channel/update",
				data: props,
				method: "PUT"
			})
		)
	},
	add: async props=>{
		return(
			Request({
				url: "/user/notification/channel/add",
				data: props,
				method: "POST"
			})
		)
	}
}


// sets up mock data
/*const test = async() => {
	
	const getRandomDay = () => {
		return Math.floor(Math.random() * (27 - 1 + 1)) + 1;
	}

    return new Promise((resolve, reject) => {
		const current_month=new Date().getMonth()+1;
		const current_year=new Date().getFullYear();		
		const notification=[
			{
				id: 1,
				date:`${current_year}/${("0"+current_month).slice(-2)}/${("0"+getRandomDay()).slice(-2)}`,
				title:"Have you signed up for HS Basketball Tournament yet? Deadline approaching.",
				description:"I'm baby readymade occupy af tousled, pitchfork ugh lyft food truck poke asymmetrical."
			},
			{
				id: 121,
				date:`${current_year}/${("0"+current_month).slice(-2)}/${("0"+getRandomDay()).slice(-2)}`,
				title:"Project Updates.",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
			{
				id: 11,
				date:`${current_year}/${("0"+current_month).slice(-2)}/${("0"+getRandomDay()).slice(-2)}`,
				title:"Johnny has invited you to a group.",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
			{
				id: 55,
				date:`${current_year}/${("0"+current_month).slice(-2)}/${("0"+getRandomDay()).slice(-2)}`,
				title:"Password reset successfully",
				description:"Paleo godard marfa mixtape butcher venmo ethical slow-carb sustainable taiyaki truffaut jean shorts single-origin coffee fanny pack four dollar toast."
			},
		];

        resolve(notification);
    });
}*/

const Notifications = {
	get, Channel //, create, update, delete, etc. ...
}
  
export default Notifications;

========================================
>>>File: OpenAI.js

import axios from 'axios';
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
    apiKey: process.env.REACT_APP_OPENAI_API_KEY,
});

const openai = new OpenAIApi(configuration);

const interact = async(params) => {
    let response={
        text:["..."],
        usage:{},
        error:null
    }

    const section = params?.type?.toLowerCase() || "about us";
    const company = params?.company || "SiteBoss";
    const keywords = params.keywords || "";

    let heading = "";
    let rules = "";
    switch (section){
        case "create_page":
            /*
            heading = `Create a section for a page in JSON format for a company called ${company}.`;
            rules = `${keywords?`- The output should contain the following sections in the body: ${keywords}`:``}.
            - The output should NOT be in HTML but in JSON format.
            - The output JSON should pass JSON LINT validations.
            - Every key / property name of the output JSON should be wrapped ONLY in double quotes (").
            - Every string value of the output JSON should be wrapped ONLY in double quotes (").
            - The output JSON should NOT contain any line breaks or indentation (\\n, \\s, \\t).
            - The output JSON should NOT contain any comments.
            - The output JSON should NOT contain any HTML tags.
            - The output JSON should NOT contain any custom css classes or styles.
            - The output JSON should NOT contain any custom javascript code.
            - The output JSON be similar to this structure:
            {
                "header": {
                    "logo": "https://www.siteboss.ai/logo.png",
                    "menu": [{
                        "name": "Home",
                        "url": "/"
                    }, {
                        "name": "About Us",
                        "url": "/about-us"
                    }, {
                        "name": "Contact Us",
                        "url": "/contact-us"
                    }]
                },
                "body": [{
                    "hero": {
                        "heading": "Welcome to SiteBoss",
                        "subheading": "We build websites that convert visitors into customers.",
                        "image": "https://www.siteboss.ai/hero.png",
                        "button": {
                            "text": "Get Started",
                            "url": "/get-started"
                        }
                    }
                }, {
                    "container": {
                        "row": [{
                            "column": [{
                                "h1": "Our Services"
                            }, {
                                "span": "We build websites that convert visitors into customers."
                            }]
                        }, {
                            "column": [{
                                "parragraph": "We build websites that convert visitors into customers."
                            }, {
                                "image": "https://www.siteboss.ai/services.png"
                            }]
                        }]
                    }
                }, {
                    "container": {
                        "row": [{
                            "column": [{
                                "h1": "Our work"
                            }, {
                                "span": "We build websites that convert visitors into customers."
                            }]
                        }, {
                            "column": [{
                                "parragraph": "We build websites that convert visitors into customers."
                            }]
                        }]
                    }
                }],
                "footer": {
                    "logo": "https://www.siteboss.ai/logo.png",
                    "copyright": "© 2024 SiteBoss. All rights reserved."
                }
            }`;
            */

            heading = `Build an About Us section given a company and description, but expand on the description to make it longer and more interesting. The response should be JSON in the following format:
            {
            "heading":"Welcome to Company",
            "about_us":"Company provides a location and space for kids of all ages to 'play' - to be social, interact with others, and be active"
            }
            Company: ${company}
            Description: ${keywords}`;
            break;
        case "meta_description":
            heading = `Write a meta description for a website. The company's name is ${company}.`;
            rules = `- The output should be SEO friendly and meet Google's guidelines.`;
            break;
        case "meta_keywords":
            heading = `Write meta keywords for a website, separated by commas. The company's name is ${company}.`;
            rules = `- The output should be SEO friendly and meet Google's guidelines.`;
            break;
        case "headline":
        case "heading":
        case "title":
            heading = `Write a headline for a website.`;
            rules = `- The output should be concise and to the point.
            - The output should be no more than 10 words.
            - The output should not be wrapped in quotes or single quotes.`;
            break;
        default:
            heading = `Write a piece of copy text for a ${section} section of a website. The company's name is ${company}.`;
            if (params.allowHTML){
                rules = `- The output may contain the following html tags: p, b, i, span, u, ul, li, ol, quote.
                - The output should NOT contain custom css classes or styles.`;
            }
            break;
    }
    
    let prompt=`${heading}
    ${keywords?`Use these special keywords: ${keywords}.`: ``}
    ${rules? `Rules: ${rules}`: ``}`.trim();

    try {
        const res = await openai.createChatCompletion({
            model: params?.model || "gpt-3.5-turbo",
            messages: [{role: "user", content: prompt}],
            max_tokens: params?.max_tokens || 2000,
            temperature: section==="create_page" ? 0: 0.8,
            top_p: 1,
            frequency_penalty: 1.9,
            presence_penalty: 1.9,
        });

        if (res?.data?.choices?.[0]?.message){
            //const moderated=await moderate(res.data.choices[0].message.content);
            //if (moderated.trim()==="...") response.text=["..."];
            //else {
                let result=res.data.choices[0].message.content.trim();
                
                if (section==="create_page") result=result.replace(/“/g, '"').replace(/”/g, '"').replace(/‘/g, '"').replace(/’/g, '"').replace(/\n/g, '');
                else {
                    // remove double and single quotes from the beginning and end of the text
                    result=result.replace(/^['"]+/g, '').replace(/['"]+$/g, '');
                }
                response.text=result;
            //}
            response.usage=res.data.usage;
        }
    } catch (error) {
        console.log(error.message)
        if (error.response) response.error=error.response;
        else response.error=error.message;
    }

    return response;
}

const moderate = async(text) => {    
    const res = await axios.post(`https://api.openai.com/v1/moderations`,{input: text,model:"text-moderation-latest"},{
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.REACT_APP_OPENAI_API_KEY}`,
        }
    });
    if (res?.data?.results?.[0]){
        if (res.data.results[0].flagged===false) return text;
    } 

    return "...";
}

const OpenAI = {
    interact, moderate
}
   
export default OpenAI;

========================================
>>>File: Order.js

import Request from './Api';

const Statuses={
    getStatuses: async (props) => {
        return(
            Request({
                url: "/order/status",
                method: "GET",
                data: props
            })
        )
    },
    //despite the name of the endpoint, this will not just "log" but update the status if allowed by associated parent status
    logStatus: async (props) => { 
        return(
            Request({
                url: "/order/log_status",
                method: "POST",
                data: props
            })
        )
    }
}

const Order = {
    Statuses
}

export default Order;

========================================
>>>File: Permissions.js

import Request from './Api';

// get module(s)
const Modules={
    get: async (props) => {
        return(
            Request({
                url: "/module",
                method: "POST",
                data: props
            })
        )
    },
    create: async (props) => {
        return (
            Request({
                url: "/module/create",
                data: props,
                method: "POST"
            })
        );
    },
    update: async (props) => {
        return (
            Request({
                url: "/module/edit",
                data: props,
                method: "PUT"
            })
        );
    },
    assign: async (props) => {
        return (
            Request({
                url: "/module/assign",
                data: props,
                method: "POST"
            })
        );
    },
    assignMany: async (props) => {
        return (
            Request({
                url: "/module/assign_many",
                data: props,
                method: "POST"
            })
        );
    },
    sort: async(props)=>{
        return(
            Request({
                url: "/module/sort",
                data: props,
                method: "POST"
            })
        )
    }
}

const getModuleTypes = async(props)=>{
    return (
        Request({
            url: "/module/type",
            data: props,
            method: "POST"
        })
    )
}

const ModuleTypes={
    get: async (props) => {
        return(
            Request({
                url: "/module/type",
                method: "POST",
                data: props
            })
        )
    },
    add: async (props) => {
        return (
            Request({
                url: "/module/type/add",
                method: "POST",
                data: props
            })
        )
    },
    edit: async (props) => {
        return(
            Request({
                url: "/module/type/edit",
                method: "PUT",
                data: props
            })
        )
    },
    delete: async (props) => {
        return(
            Request({
                url: "/module/type/delete",
                method: "DELETE",
                data: props
            })
        )
    }
}

const Features = {
    getAll: async props =>{
        return (
            Request({
                url: "/feature",
                data: props,
                method: "POST"
            })
        )
    }, 
    create: async props =>{
        return (
            Request({
                url: "/feature/create",
                data: props,
                method: "POST"
            })
        )
    },
    edit: async props =>{
        return (
            Request({
                url: "/feature/edit",
                data: props,
                method: "PUT"
            })
        )
    },
    delete: async props =>{
        return (
            Request({
                url: "/feature/delete",
                data: props,
                method: "DELETE"
            })
        )
    },
    sort: async props =>{
        return (
            Request({
                url: "/feature/sort",
                data: props,
                method: "POST"
            })
        )
    },
    permissions: async props =>{
        return(
            Request({
                url: "/feature/permission",
                data: props,
                method: "POST"
            })
        )
    },
    assign: async props =>{
        return(
            Request({
                url: "/feature/assign",
                data: props,
                method: "POST"
            })
        )
    }
}

const Endpoints = {
    getAll: async props =>{
        return (
            Request({
                url: "/endpoint",
                data: props,
                method: "POST"
            })
        )
    },
    create: async props =>{
        return (
            Request({
                url: "/endpoint/create",
                data: props,
                method: "POST"
            })
        )
    },
    createMany: async props =>{
        return (
            Request({
                url: "/endpoint/create_many",
                data: props,
                method: "POST"
            })
        )
    },
    edit: async props =>{
        return (
            Request({
                url: "/endpoint/edit",
                data: props,
                method: "PUT"
            })
        )
    },
    delete: async props =>{
        return (
            Request({
                url: "/endpoint/delete",
                data: props,
                method: "DELETE"
            })
        )
    }
}

const PermissionLevels = {
    get: async props =>{
        return (
            Request({
                url: "/module/permission_level",
                data: props,
                method: "POST"
            })
        )
    },
    create: async props =>{
        return (
            Request({
                url: "/module/permission_level/create",
                data: props,
                method: "POST"
            })
        )
    },
    edit: async props =>{
        return (
            Request({
                url: "/module/permission_level/edit",
                data: props,
                method: "PUT"
            })
        )
    },
    delete: async props =>{
        return (
            Request({
                url: "/module/permission_level/delete",
                data: props,
                method: "DELETE"
            })
        )
    }
}

const MenuItems = {
    get: async props =>{
        return (
            Request({
                url: "/menu_item",
                data: props,
                method: "POST"
            })
        )
    },
    create: async props =>{
        return (
            Request({
                url: "/menu_item/create",
                data: props,
                method: "POST"
            })
        )
    },
    edit: async props =>{
        return (
            Request({
                url: "/menu_item/set",
                data: props,
                method: "POST"
            })
        )
    },
    editMany: async props=>{
        return(
            Request({
                url: "/menu_item/set_many",
                data: props,
                method: "POST"
            })
        )
    },
    sort: async props =>{
        return(
            Request({
                url: "/menu_item/sort",
                data: props,
                method: "POST"
            })
        )
    },
    delete: async props =>{
        return (
            Request({
                url: "/menu_item/delete",
                data: props,
                method: "DELETE"
            })
        )
    }
}

const UserModulePermission = {
    get: async(props) => {
        return (
            Request({
                url: `/user/module_permission`,
                data: props,
                method: "POST"
            })
        )
    }
}

const Permissions = {
    Modules, getModuleTypes, ModuleTypes, PermissionLevels, Features, Endpoints, MenuItems, UserModulePermission
}
  
export default Permissions;


========================================
>>>File: Pos.js

import Request from './Api';

// get registers
const register = {

    get: props => {
        return Request({
            url: "/register" + (props.is_patron_register ? "/patron" : ""),
            method: "POST",
            data: props,
        });
    },

    edit: props => {
        return (
            Request({
                url: "/register/edit",
                method: "POST",
                data: props
            })
        );
    }
}


// Cart
const order = {
    // saves the order
    save: props =>{
        return (
            Request({
                url: "/order/add",
                method: "POST",
                data: props,
            })
        );
    },
    // update the order
    update: props =>{
        return (
            Request({
                url: "/order/update",
                method: "PUT",
                data: props,
            })
        );
    },    
    get: props =>{
        return (
            Request({
                url: "/order" + (props.id ? "/order/"+props.id : ""),
                method: props.method || "GET",
                data:props,
            })
        );
    },
    latestOpen: props =>{
        return (
            Request({
                url: "/order/latest_open",
                method: "POST",
                data: props,
            })
        );
    },
    // loads order lines
    getItems: async props =>{
        const order=await Request({
            url: "/order/order/"+props.id,
            method: "GET",
            data: props,
        });
        if (order.data?.items) return Promise.resolve({data:order.data?.items});
        else return order;
    },
    // save order lines
    addItems: props =>{
        return (
            Request({
                url: "/order/add",
                method: "POST",
                data: props,
            })
        );
    },
    // saves an order's new custom status
    logStatus: props =>{
        return (
            Request({
                url: "/order/log_status",
                method: "POST",
                data: props,
            })
        );
    }
}

// Local storage
const local = {
    get: (key) =>{
        return new Promise((resolve, reject) => {
            resolve(JSON.parse(localStorage.getItem(key)));
        });
    },
    save: (key,value,type=0) =>{
        return new Promise((resolve, reject) => {
            let item=localStorage.getItem(key);
            switch(type){
                case 1: // push to key
                    if (item){
                        item=JSON.parse(item);
                        let new_item=[];
                        if (!Array.isArray(item)) new_item.push(item);
                        else new_item=item;
                        new_item.push(value);
                        value=new_item;
                    }
                    break;
                case 2: // update value
                    if (item){
                        item=JSON.parse(item);
                        let new_item;
                        if (!Array.isArray(item)) new_item=value;
                        else{
                            new_item=[];
                            item.forEach(i=>{
                                if (i.hash===value.hash) new_item.push(value);
                                else new_item.push(i);
                            });
                            value=new_item;
                        }
                    }
                    break;
                default:
                    break;
            }
            localStorage.setItem(key,JSON.stringify(value));
            resolve(Array.isArray(value)?value:[value]);
        });
    },
    update: (key,value) =>{
        return local.save(key,value,2);
    },
    append: (key,value) =>{
        return local.save(key,value,1);
    },
    remove: (key,value) =>{
        return new Promise((resolve, reject) => {
            if (!value) resolve(localStorage.removeItem(key));
            else {
                let item=localStorage.getItem(key);
                if (item){
                    item=JSON.parse(item);
                    if (!Array.isArray(item) && JSON.stringify(item)===JSON.stringify(value)) resolve(localStorage.setItem(key,"[]"));
                    else {
                        const new_item=item.filter(i=>JSON.stringify(i)!==JSON.stringify(value));
                        resolve(localStorage.setItem(key,JSON.stringify(new_item)));
                    }
                } else reject("Item not found!");
            }
        });
    }
}

const addRefund=async(props)=>{
    return(
        Request({
            url: "payment/refund/add",
            method: "POST",
            data: {...props}
        })
    )
}

const orderRefund=async(props)=>{
    return(
        Request({
            url: "payment/process/refund",
            method: "POST",
            data: {...props}
        })
    )
}

// Payment
const payment = {
    process: props =>{
        let header_data=null;
        if (props.hash){ // this is used to encrypt/decrypt the data (for manager discounts)
            // re-do the header to include the hash
            let token="";
            let localuser = localStorage.getItem("user");
            if (localuser) {
                try{
                    localuser=JSON.parse(localuser);
                    token=localuser.token;
                }catch(error){}
            }
            header_data = {'Authorization': token, 'X-Anti-Spam-Hash': props.hash};
            delete props.hash;
        }

        return (
            Request({
                url: "/payment/process",
                method: "POST",
                data: props,
                headers: header_data || undefined,
            })
        );
    },
    processRefund: props =>{ // refund a transaction
        return (
            Request({
                url: "/payment/refund",
                method: "POST",
                data: props,
            })
        );
    },
    processVoid: props =>{ // void a transaction
        return (
            Request({
                url: "/payment/void",
                method: "POST",
                data: props,
            })
        );
    },
    sendReceipt: props =>{
        return (
            Request({
                url: "/payment/email",
                method: "POST",
                data: props,
            })
        );
    },
    card: props =>{
        return (
            Request({
                url: "/payment/nmi_process_credit_card",
                method: "POST",
                data: props,
            })
        );    
    },
    terminal: props => {
        return (
            Request({
                url: "/payment/nmi_terminal_transaction",
                method: "POST",
                data: props,
            })
        );
    },
    terminalStatus: props => {
        return (
            Request({
                url: "/payment/nmi_async_status/"+props.transaction_id,
                method: "GET",
                data: props,
            })
        );
    },
    profile: props => {
        return (
            Request({
                url: "/payment/nmi_charge_payment_profile",
                method: "POST",
                data: props,
            })
        );
    },
    cash: props => {
        return (
            Request({
                url: "/payment/record_cash_payment",
                method: "POST",
                data: props
            })
        );
    },
    token: props => {
        return (
            Request({
                url: "/payment/redeem_token",
                method: "POST",
                data: props
            })
        );
    },
    service_tokens: props => {
        return (
            Request({
                url: "/payment/redeem_service_tokens",
                method: "POST",
                data: props
            })
        );
    },
    refund: props=>{
        return(
            Request({
                url: 'payment/nmi_refund_transaction',
                method: 'POST',
                data: props
            })
        )
    },
    split_payment: props=>{
        return(
            Request({
                url: "/order/split",
                method: "POST",
                data: props,
            })
        )
    },
    cancel_split: props=>{
        return(
            Request({
                url: "/order/cancel_split",
                method: "POST",
                data: props
            })
        )
    }
}

// Print Location
const printLocation = {
    getNext: props =>{
        return (
            Request({
                url: "/print_location",
                method: "POST",
                data: props,
            })
        );    
    },
    complete: props =>{
        return (
            Request({
                url: "/print_location/complete",
                method: "POST",
                data: props,
            })
        );    
    },
};

// Print Location
const registerGroups = {
    get: props =>{
        return (
            Request({
                url: "/register/group",
                method: "POST",
                data: props,
            })
        );    
    },
};


const Pos = {
    register, local, payment, order, orderRefund, addRefund, printLocation, registerGroups
}
  
export default Pos;


========================================
>>>File: Products.js

import Request from './Api';

// get product(s)
const get = async (props) => {
    return (
        Request({
            url: "/product",
            data: {...props, "max_records": props.max_records || 999},
            method: "POST",
        })
    );
}

const getSingle = async (props) =>{
    return (
        Request({
            url: `/product/product/${props.id}`,
            data: props,
            method: "GET"
        })
    )
}

const getFiltered = async (props) => {
    return (
        Request({
            url: "/product",
            data: props,
            method: "POST"
        })
    );
}

const getSubscriptions = async (props) => {
    return (
        Request({
            url: "/product",
            data: {...props, "product_type_id": 1},
            method: "POST",
        })
    );
}

const addImage = async(props)=>{
    return(
        Request({
            url: "/product/upload",
            data: props,
            method: "POST"
        })
    )
}

// create product
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Product Name is required", "Category is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/product/add",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update product
const update = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Product Name is required", "Category is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/product/edit",
            data: props,
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update products with bulk-edit
const updateBulk = async (props) => {
    return (
        Request({
            url: "/product/bulk-edit",
            data: props,
            method: "POST"
        })
    );
}


// Categories
const Categories = {

    get: async(props)=>{
        return (
            Request({
                url: "/category/" + (props?.id || ""),
                data: props,
            })
        );
    },

    // create categories
    create: async(props)=>{
        return (
            Request({
                url: "/category/add",
                data: props,
                method: "POST"
            })
        );
    },

    // update categories
    update: async(props)=>{
        return (
            Request({
                url: "/category/edit",
                data: props,
                method: "POST"
            })
        );
    },

    filter: async(props)=>{
        return(
            Request({
                url: "/category",
                data: props,
                method: "POST"
            })
        )
    }
}


// Types
const Types = {

    // ge types
    get: async(props)=>{
        return (
            Request({
                url: "/product/type/" + (props?.id || ""),
                data: props
                //test: mockdata // send mock data to simulate an api call
            })
        );
    },

    /*// create types
    create: async(props)=>{
        // sets mock data for testing - will be removed when api is ready
        //let mockdata = {data:null,errors:["Product Type Name is required"]};
        //let mockdata = {data:1,errors:null};
        
        return (
            Request({
                url: "/product/type/create",
                data: props,
                //test: mockdata // send mock data to simulate an api call
            })
        );
    },

    // update types
    update: async(props)=>{
        // sets mock data for testing - will be removed when api is ready
        //let mockdata = {data:null,errors:["Product Type Name is required"]};
        let mockdata = {data:1,errors:null};
        
        return (
            Request({
                url: "/product/type/update",
                data: props,
                test: mockdata // send mock data to simulate an api call
            })
        );
    },*/
}


// Status
const Status = {

    // ge status
    get: async(props)=>{
        // sets mock data for testing - will be removed when api is ready
        //let mockdata = {data:null,errors:["Status Name is required"]};
        let mockdata = {
            data:[
                {
                    id: 1,
                    name: "Active",
                },
                {
                    id: 2,
                    name: "Pending",
                },
                {
                    id: 3,
                    name: "Not Available",
                },
                {
                    id: 4,
                    name: "Removed",
                },
            ],
            errors:null
        };

        if (props?.id){
            const filtered_data=mockdata.data.filter(item => item.id===parseInt(props.id));
            mockdata.data=filtered_data;
        }
        
        return (
            Request({
                url: "/product/status",
                data: props,
                test: mockdata // send mock data to simulate an api call
            })
        );
    },

}

const Variants = {
    get: async (props) => {
        return (
            Request({
                url: "/product/variant/" + (!props ? "" : props.id),
                data: props,
                method: "GET",
            })
        )
    },

    create: async (props) => {
        return (
            Request({
                url: "/product/variant/add",
                data: props,
                method: "POST"
            })
        )
    },

    edit: async (props) => {
        return (
            Request({
                url: "/product/variant/edit",
                data: props,
                method: "POST"
            })
        )
    },

    delete: async (props) => {
        return (
            Request({
                url: "/product/variant/delete",
                data: props,
                method: "DELETE"
            })
        )
    },

    Addons: {
        get: async (props) => {
            const mockdata={
                "errors" : [],
                "data" : [
                  {
                    "category_id":55,
                    "category_name":"Meats",
                    "add_ons":[
                      {
                          "id": 22,
                          "product_id": 2,
                          "name": "Pepperoni",
                          "price": 0.99,
                          "sale_price": 0.98,
                          "token_price": 0,
                          "bill_interval": 0,
                          "interval_quantity": 0,
                          "activation_fee": 0,
                          "sku": "CDY-GMB-05",
                          "upc": "294058202513",
                          "is_taxable": 1,
                          "is_shippable": 0,
                          "handling_fee": 0,
                          "fulfillment_fee": 0,
                          "weight": null,
                          "height": null,
                          "width": null,
                          "length": null,
                          "expires_in_days": 0,
                          "date_available": "",
                          "quantity_units": 1,
                          "minimum_qty": 1,
                      },
                      {
                          "id": 56,
                          "product_id": 2,
                          "name": "Ham",
                          "price": 0.99,
                          "sale_price": 0.98,
                          "token_price": 0,
                          "bill_interval": 0,
                          "interval_quantity": 0,
                          "activation_fee": 0,
                          "sku": "CDY-GMB-05",
                          "upc": "294058202513",
                          "is_taxable": 1,
                          "is_shippable": 0,
                          "handling_fee": 0,
                          "fulfillment_fee": 0,
                          "weight": null,
                          "height": null,
                          "width": null,
                          "length": null,
                          "expires_in_days": 0,
                          "date_available": "",
                          "quantity_units": 1,
                          "minimum_qty": 1,
                      },
                    ]
                  },
                  {
                    "category_id":56,
                    "category_name":"Veggies",
                    "add_ons":[
                      {
                          "id": 22,
                          "product_id": 2,
                          "name": "Mushrooms",
                          "price": 0.99,
                          "sale_price": 0.98,
                          "token_price": 0,
                          "bill_interval": 0,
                          "interval_quantity": 0,
                          "activation_fee": 0,
                          "sku": "CDY-GMB-05",
                          "upc": "294058202513",
                          "is_taxable": 1,
                          "is_shippable": 0,
                          "handling_fee": 0,
                          "fulfillment_fee": 0,
                          "weight": null,
                          "height": null,
                          "width": null,
                          "length": null,
                          "expires_in_days": 0,
                          "date_available": "",
                          "quantity_units": 1,
                          "minimum_qty": 1,
                      },
                      {
                          "id": 125,
                          "product_id": 2,
                          "name": "Onions",
                          "price": 0.99,
                          "sale_price": 0.98,
                          "token_price": 0,
                          "bill_interval": 0,
                          "interval_quantity": 0,
                          "activation_fee": 0,
                          "sku": "CDY-GMB-05",
                          "upc": "294058202513",
                          "is_taxable": 1,
                          "is_shippable": 0,
                          "handling_fee": 0,
                          "fulfillment_fee": 0,
                          "weight": null,
                          "height": null,
                          "width": null,
                          "length": null,
                          "expires_in_days": 0,
                          "date_available": "",
                          "quantity_units": 1,
                          "minimum_qty": 1,
                      },
                    ]
                  }      
                ]
              };            
            return (
                Request({
                    url: "/product/variant/" +props.id+"/addons",
                    data: props,
                    method: "GET"
                })
            )
        },
    },

    byProduct: async (props) => {
        return (
            Request({
                url: "/product/"+props.id+"/variants",
                data: props,
                method: "GET"
            })
        )
    },    
}

const setProductCategory = async (props) => {    
    return (
        Request({
            url: "/product/category/add",
            data: props,
            method: "POST"
        })
    );
}

const removeProductCategory = async (props) => {
    return (
        Request({
            url: "/product/category/delete",
            data: props,
            method: "DELETE"
        })
    );
}


const Products = {
    get, getSingle, getFiltered, getSubscriptions, addImage, create, update, updateBulk, setProductCategory, removeProductCategory, Categories, Types, Status, Variants //, delete, etc. ...
}
  
export default Products;


========================================
>>>File: Provinces.js

const get = () => {
    let data = test();

    return data || []
}

const test = () => {
    return [
        {
            id: 1,
            name: "Alberta",
            short_name:"AB",
        },
        {
            id: 2,
            name: "British Columbia",
            short_name:"BC",
        },
        {
            id: 3,
            name: "Manitoba",
            short_name:"MB",
        },
        {
            id: 4,
            name: "New Brunswick",
            short_name:"NB",
        },
        {
            id: 5,
            name: "Newfoundland and Labrador",
            short_name:"NL",
        },
        {
            id: 6,
            name: "Northwest Territories",
            short_name:"NT",
        },
        {
            id: 7,
            name: "Nova Scotia",
            short_name:"NS",
        },
        {
            id: 8,
            name: "Nunavut",
            short_name:"NU",
        },
        {
            id: 9,
            name: "Ontario",
            short_name:"ON",
        },
        {
            id: 10,
            name: "Prince Edward Island",
            short_name:"PE",
        },
        {
            id: 11,
            name: "Quebec",
            short_name:"QC",
        },
        {
            id: 12,
            name: "Saskatchewan",
            short_name:"SK",
        },
        {
            id: 13,
            name: "Yukon",
            short_name:"YT",
        }
    ];
}

const Provinces = {
    get
}
  
export default Provinces;

========================================
>>>File: Registers.js

import Request from "./Api";

// Get all registers
const get = async (props) => {
  return Request({
    url: "/register",
    method: "POST",
    data: props,
  });
};

// Create new register
const create = async (props) => {
  return Request({
    url: "/register/create",
    method: "POST",
    data: props,
  });
};

// Edit event
const edit = async (props) => {
  return Request({
    url: "/register/edit",
    method: "PUT",
    data: props,
  });
};

const delete_register = async (props) => {
  return Request({
    url: "/register/" + props.id,
    method: "DELETE",
    data: props,
  });
};


const Groups = {
    get: async (props) => {
        return Request({
            url: "/register/group",
            method: "POST",
            data: props,
        });
    },

    create: async (props) => {
        return Request({
            url: "/register/group/create",
            method: "POST",
            data: props,
        });
    },

    edit: async (props) => {
        return Request({
            url: "/register/group/edit",
            method: "PUT",
            data: props,
        });
    },

    delete: async (props) => {
        return Request({
            url: "/register/group/" + props.id,
            method: "DELETE",
            data: props,
        });
    },
};

const Registers = {
  get,
  create,
  edit,
  delete_register,
  Groups
};

export default Registers;


========================================
>>>File: Roles.js

import Request from './Api';

const getAllRoles = async() => {
	//let mockdata = test();

    return (
        Request({
            url: "/user/roles",
            //data: {get_all:true},
            //test: {data:mockdata} // send mock data to simulate an api call
        })
    );
}

// sets up mock data
/*const test = () => {
    return [
		{
			id: 1,
			name: "Admin",
			description: "Super-powered user"
		},
		{
			id: 51,
			name: "Store Manager",
			description: "Manages the store"
		},
		{
			id: 52,
			name: "Store Clerk",
			description: "Works at the store"
		},
		{
			id: 99,
			name: "Dev-Ops",
			description: "Nobody knows what they do but it sounds cool"
		},
	];
}*/


const Roles = {
	getAllRoles //, create, update, delete, etc. ...
}
  
export default Roles;

========================================
>>>File: Routes.js

import Request from './Api';

const get = async (props) => {
    return (
        Request({
            url: "/routes",
            data: props,
            method: "GET",
        })
    );
}

const Routes = {
    get
}
  
export default Routes;


========================================
>>>File: Services.js

import Request from './Api';

// Get service(s)
const get = async (props) => {
    return (
        Request({
            url: "/service",
            method: "POST",
            data: props
        })
    );
}

// Create new service
const create = async (props) => {
    return (
        Request({
            url: "/service/add",
            method: "POST",
            data: props
        })
    );
}

// Edit service
const edit = async (props) => {
    return (
        Request({
            url: "/service/edit",
            method: "PUT",
            data: props
        })
    );
}

// Delete service
const remove = async (props) => {
    return (
        Request({
            url: "/service/delete",
            method: "DELETE",
            data: props
        })
    );
}

// Create booking event
const createBooking = async (props) => {
    return (
        Request({
            url: "/service/add_booking",
            method: "POST",
            data: props
        })
    );
}

// Cancel booking event
const cancelBooking = async (props) => {
    return (
        Request({
            url: "/service/cancel_booking",
            method: "POST",
            data: props
        })
    );
}

const Services = {
    get, create, edit, remove, createBooking, cancelBooking
}

export default Services;

========================================
>>>File: States.js

const get = () => {
    let data = test();

    return data || []
}

const test = () => {
    return [
        {
            id: 1,
            name: "Alabama",
            short_name:"AL",
        },
        {
            id: 2,
            name: "Alaska",
            short_name:"AK",
        },
        {
            id: 3,
            name: "Arizona",
            short_name:"AZ",
        },
        {
            id: 4,
            name: "Arkansas",
            short_name:"AR",
        },
        {
            id: 5,
            name: "California",
            short_name:"CA",
        },
        {
            id: 6,
            name: "Colorado",
            short_name:"CO",
        },
        {
            id: 7,
            name: "Connecticut",
            short_name:"CT",
        },
        {
            id: 8,
            name: "Delaware",
            short_name:"DE",
        },
        {
            id: 9,
            name: "District Of Columbia",
            short_name:"DC",
        },
        {
            id: 10,
            name: "Florida",
            short_name:"FL",
        },
        {
            id: 11,
            name: "Georgia",
            short_name:"GA",
        },
        {
            id: 12,
            name: "Hawaii",
            short_name:"HI",
        },
        {
            id: 13,
            name: "Idaho",
            short_name:"ID",
        },
        {
            id: 14,
            name: "Illinois",
            short_name:"IL",
        },
        {
            id: 15,
            name: "Indiana",
            short_name:"IN",
        },
        {
            id: 16,
            name: "Iowa",
            short_name:"IA",
        },
        {
            id: 17,
            name: "Kansas",
            short_name:"KS",
        },
        {
            id: 18,
            name: "Kentucky",
            short_name:"KY",
        },
        {
            id: 19,
            name: "Louisiana",
            short_name:"LA",
        },
        {
            id: 20,
            name: "Maine",
            short_name:"ME",
        },
        {
            id: 21,
            name: "Maryland",
            short_name:"MD",
        },
        {
            id: 22,
            name: "Massachusetts",
            short_name:"MA",
        },
        {
            id: 23,
            name: "Michigan",
            short_name:"MI",
        },
        {
            id: 24,
            name: "Minnesota",
            short_name:"MN",
        },
        {
            id: 25,
            name: "Mississippi",
            short_name:"MS",
        },
        {
            id: 26,
            name: "Missouri",
            short_name:"MO",
        },
        {
            id: 27,
            name: "Montana",
            short_name:"MT",
        },
        {
            id: 28,
            name: "Nebraska",
            short_name:"NE",
        },
        {
            id: 29,
            name: "Nevada",
            short_name:"NV",
        },
        {
            id: 51,
            name: "New Hampshire",
            short_name:"NH",
        },
        {
            id: 30,
            name: "New Jersey",
            short_name:"NJ",
        },
        {
            id: 31,
            name: "New Mexico",
            short_name:"NM",
        },
        {
            id: 32,
            name: "New York",
            short_name:"NY",
        },
        {
            id: 33,
            name: "North Carolina",
            short_name:"NC",
        },
        {
            id: 34,
            name: "North Dakota",
            short_name:"ND",
        },
        {
            id: 35,
            name: "Ohio",
            short_name:"OH",
        },
        {
            id: 36,
            name: "Oklahoma",
            short_name:"OK",
        },
        {
            id: 37,
            name: "Oregon",
            short_name:"OR",
        },
        {
            id: 38,
            name: "Pennsylvania",
            short_name:"PA",
        },
        {
            id: 39,
            name: "Rhode Island",
            short_name:"RI",
        },
        {
            id: 40,
            name: "South Carolina",
            short_name:"SC",
        },
        {
            id: 41,
            name: "South Dakota",
            short_name:"SD",
        },
        {
            id: 42,
            name: "Tennessee",
            short_name:"TN",
        },
        {
            id: 43,
            name: "Texas",
            short_name:"TX",
        },
        {
            id: 44,
            name: "Utah",
            short_name:"UT",
        },
        {
            id: 45,
            name: "Vermont",
            short_name:"VT",
        },
        {
            id: 46,
            name: "Virginia",
            short_name:"VA",
        },
        {
            id: 47,
            name: "Washington",
            short_name:"WA",
        },
        {
            id: 48,
            name: "West Virginia",
            short_name:"WV",
        },
        {
            id: 49,
            name: "Wisconsin",
            short_name:"WI",
        },
        {
            id: 50,
            name: "Wyoming",
            short_name:"WY",
        },
    ];
}

const States = {
    get
}
  
export default States;

========================================
>>>File: Subscriptions.js

import Request from './Api';

const setFinal =(props)=>{
    return(
        Request({
            url: '/subscriptions/set_final_bill_date',
            method: "POST",
            data: props
        })
    )
}

const setRestart=(props)=>{
    return(
        Request({
            url:"/subscriptions/set_restart_bill_date",
            method: "POST",
            data: props
        })
    )
}

const SubscriptionApi = {
    setFinal, setRestart,
}

export default SubscriptionApi;

========================================
>>>File: Tags.js

import Request from './Api';

const get = async (props) => {
    return (
        Request({
            url: "/tag",
            method: "POST",
            data: props
        })
    );
}

const create = async (props) => {
    return (
        Request({
            url: "/tag/add",
            method: "POST",
            data: props
        })
    );
}

const edit = async (props) => {
    return (
        Request({
            url: "/tag/edit",
            method: "PUT",
            data: props
        })
    );
}

const remove = async (props) => {
    return (
        Request({
            url: "/tag/delete",
            method: "DELETE",
            data: props
        })
    );
}

const Tags = {
    get, create, edit, remove
}

export default Tags;

========================================
>>>File: Tasks.js

import Request from './Api';

const get = async (props) => {
    // gets mock data for testing - will be removed when api is ready
    //let tasks = await test();

    return (
        Request({
            url: "/task/list",
            data: {
                user_id: props?.user_id,
				date_start: props?.date_start,
				date_end: props?.date_end,
            },
            //test: tasks // send mock data to simulate an api call
        })
    );
}


// create task
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Title is required"]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/task/create",
            data: {
                props
            },
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update task
const update = async(props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Title is required"]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/user/update",
            data: {
                props
            },
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

/*
// sets up mock data
const test = async() => {
	
	const getRandomDay = () => {
		return Math.floor(Math.random() * 27) + 1;
	}

    return new Promise((resolve, reject) => {
		const current_month=new Date().getMonth()+1;
		const current_year=new Date().getFullYear();
		
		let tasks=[];

		tasks["everyday"]=[
			{
				id: 1,
				type:1,
				time_start:"07:00:00",
				time_end:"08:00:00",
				title:"Workout",
				description:"I'm baby readymade occupy af tousled, pitchfork ugh lyft food truck poke asymmetrical."
			},
		];

		tasks[`${current_year}/${("0"+current_month).slice(-2)}/15`]=[
			{
				id: 121,
				type:0,
				day:15,
				month:current_month,
				year:current_year,
				time_start:"07:25:00",
				time_end:"12:00:00",
				title:"TEST",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
		];	

		let day=getRandomDay();
		tasks[`${current_year}/${("0"+current_month).slice(-2)}/${("0"+day).slice(-2)}`]=[
			{
				id: 11,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"11:25:00",
				time_end:"12:00:00",
				title:"Lunch with family",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
			{
				id: 55,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"20:25:44",
				time_end:"20:40:00",
				title:"Read a physics book",
				description:"Paleo godard marfa mixtape butcher venmo ethical slow-carb sustainable taiyaki truffaut jean shorts single-origin coffee fanny pack four dollar toast."
			},
		];

		day=getRandomDay();
		tasks[`${current_year}/${("0"+current_month).slice(-2)}/${("0"+day).slice(-2)}`]=[
			{
				id: 96,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"09:00:00",
				time_end:"20:00:00",
				title:"Work on personal project",
				description:"Man braid cred ramps listicle lyft fixie."
			},
		];

        resolve(tasks);
    });
}/**/

const Tasks = {
	get, create, update //, delete, etc. ...
}
  
export default Tasks;

========================================
>>>File: Themes.js

import Request from './Api';

const get = (props) => {
    return (
        Request({
            url: props?.my?"/cms/my_theme":`/cms/theme${props?.id?"/"+props.id:""}`,
            data:props,
            method: "GET",
        })
    );
}

const parse = (content) => {
    let variables=[];
    let styles="";

    if (content?.variables){
        content.variables.forEach((v,i)=>{
            variables.push({"name":v.name,"value":v.compiledValue || v.value});
        });
    }
    if (content?.styles) styles=content.styles;

    return {variables:[...variables],styles:styles};
}

const Themes = {
    get, parse
}
  
export default Themes;

========================================
>>>File: Transactions.js

import React from 'react';
import Request from './Api';

// get transactions
const getAll = async (props) => {
    return (
        Request({
            url: "/transactions",
            data:props,
            method: "POST"
        })
    );
}

// get transactions for a user
const get = async (props) => {
    return (
        Request({
            url: "user/transactions",
            data:props,
            method: "POST"
        })
    );
}

const getReports =async (props)=>{
    let url;
    if(props && props.url){url = props.url}
    delete props.url;
    return(
        Request({
            url: "/transaction/reports" + (url ? "/" + url : ""),
            method: "POST",
            data: props
        })
    )
}

const Reports = {
    getProducts: async props =>{
        return (
            Request({
                url: "/transactions/reports/products",
                method: "POST",
                data: props
            })
        )
    },
    getCategories: async props=>{
        return(
            Request({
                url: "transactions/reports/categories",
                method: "POST",
                data: props
            })
        )
    },
    getPaymentMethods: async props=>{
        return(
            Request({
                url: "transactions/reports/payment_methods",
                method: "POST",
                data: props
            })
        )
    },
    getCash: async props=>{
        return(
            Request({
                url: "transactions/reports/cash",
                method: "POST",
                data: props
            })
        )
    },
    getUsers: async props=>{
        return(
            Request({
                url: "reports/users",
                method: "POST",
                data: props
            })
        );
    },
    getSettlements: async props=>{
        return(
            Request({
                url: "/transactions/reports/settlement",
                method: "POST",
                data: props
            })
        )
    },
    getSubscriptions: async props=>{
        return(
            Request({
                url: "/transactions/reports/subscriptions",
                method: "POST",
                data: props
            })
        );
    },
    getCoupons: async props =>{
        return(
            Request({
                url: "/transactions/reports/coupons",
                method: "POST",
                data: props
            })
        )
    },
    getTips: async props =>{
        return(
            Request({
                url: "/transactions/reports/tips",
                method: "POST",
                data: props
            })
        )
    },
    getFormSubmissions: async props =>{
        return(
            Request({
                url: "/reports/form_submissions",
                method: "POST",
                data: props
            })
        )
    },
    getCheckins: async props =>{
        return(
            Request({
                url: "/reports/checkin_history",
                method: "POST",
                data: props
            })
        )
    },
    getEventPayments: async props =>{
        return(
            Request({
                url: "/transactions/reports/event_payments",
                method: "POST",
                data: props
            })
        )
    },
    getProductPurchaser: async props =>{
        return(
            Request({
                url: "/transactions/reports/product_purchasers",
                method: "POST",
                data: props
            })
        )
    }
}

const Transactions = {
    get, Reports, getReports, getAll
}

  
export default Transactions;


========================================
>>>File: Upload.js

import Request from './Api';

const send = (props) => {
    return Request({
        url: '/upload',
        method:'POST',
        headers: {'Content-Type': 'multipart/form-data'},
        data: props.data,
        test: {id:1,file_url:"/static/media/sports-pattern.8e9c523e.jpg"} // simulate api call, sends the expected return
    })
}

const Upload = {
    send //, create, update, delete, etc. ...
}
  
export default Upload;

========================================
>>>File: Users.js

import Request from './Api';
import Menu from './Menu';

// get user(s)
const get = async (props) => {

    let user={};

    // if the user exists in local storage, return that
    if (props && (!props.username && !props.id && !props.all)){
        user=localStorage.getItem("user");
        if (user) return Promise.resolve({data:JSON.parse(user)});
    }

    // if id is provided, retrieve that single user; otherwise send request to login
    const users = await Request({
        ...props,
        url: "/user" + (props && props.id ? "/user/" + props.id : props.all ? "" : "/login"),
        method: props && (props.id || props.all) ? "GET" : "POST",
        data: {...props},
    });

    if (users?.data && !props.id && !props.all){
        let menus=await Menu.getFullMenu({ token: users.data.token });
        users.data.menus=menus.data;

        /* uncomment when endpoint is ready
        let socials=await getSocials({id:props.id});
        users.data.social=socials.data;*/
    }

    return Promise.resolve(users);
}

const login = async (props) => {
    return(
        Request({
            url: "/user/login",
            method: "POST",
            data: { ...props }
        })
    )
}

const setUsername = async (props) => {
    return(
        Request({
            url: "/user/user/" + props.id,
            method: "PUT",
            headers: props.token ? { "Authorization": props.token } : null,
            data: { "username": props.username }
        })
    )
}

const getList = async (props) => {
    return (
        Request({
            url: "/user/list",
            method: "POST",
            data: {...props}
        })
    )
}

const address = async (props) => {
    return (
        Request({
            url: "/user/user/" + props.id + "/address",
            method: "GET"
        })
    );
}

// not currently working
const orders = async (props) => {
    return (
        Request({
            url: "/user/orders/" + props.id,
            method: "GET"
        })
    );
}

const purchasedProduct = async (props) => {
    return (
        Request({
            url: "/user/product_purchased",
            data: {...props},
            method: "POST"
        })
    );
}

// create user
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["First Name is required","Last Name is required", "Email is required."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/user/register",
            method: "POST",
            data: {...props},
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update user
const update = async (props) => {
    
    return (
        Request({
            url: "/user/user/"+props.id,
            data: {...props},
            method: "PUT"
        })
    );
}

// delete user
const remove = async (props) => {
    
    return (
        Request({
            url: "/user/user/"+props.id,
            data: {...props},
            method: "DELETE"
        })
    );
}


// list of users
const list = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["First Name is required","Last Name is required", "Email is required."]};
    /*let mockdata = {data:{this_page:2,page_record_count:23,total_record_count:48,users:[
            {id: 1,username: "tom",first_name: "Tom",last_name: "Huerter",email: "<EMAIL>"},
            {id: 2,username: "joe",first_name: "Joe",last_name: "Gero",email: "<EMAIL>"}
        ],
    },errors:null};*/
    
    return (
        Request({
            url: "/user/list",
            method: "POST",
            data: {...props},
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

const getSocials=async(props)=>{
    return Request({
        ...props,
        url: "/user/social/"+props.id,
        method: "GET",
        data: {
            ...props
        },
    });
}

//get purchased gift cards
const getGiftCards=async(props)=>{
    return Request({
        ...props,
        url: "/user/giftcards",
        method: "POST",
        data: {
            ...props
        },
    });
}


// get user memberships
const memberships = async (props) => {

//     /** */
//     // sets mock data for testing - will be removed when api is ready
//     //let mockdata = {data:null,errors:["User not found."]};
//     /*let mockdata = {data:{
//         "subscriptions": [
//             {
//               "id": 2,
//               "product_id": 20,
//               "product_variant_id": 150,
//               "subscription_status_id": 1,
//               "subscription_status": "Active",
//               "bill_interval": "M",
//               "interval_quantity": 1,
//               "product_name": "Access Pass",
//               "product_variant_name": "Yearly"
//             }
//         ],
//         "outstanding_charges": [
//             {
//               "event_id": 12,
//               "requires_membership": 1,
//               "event_name": "Fall Basketball",
//               "product_id": 155,
//               "product_name": "Admission Fee: Fall Basketball",
//               "product_variants": [
//                 {
//                   "product_variant_id": 255,
//                   "product_variant_name": "Default",
//                   "price": 25.50
//                 }
//               ]
//             }
//         ]
//     }, errors:null};
//     /**/

    let data={data:null,errors:["User not found."]};
    const auser=await  Request({
        url: "/user/user/"+props.user_id,
        method: "GET",
        data: {props},
        //test: mockdata // send mock data to simulate an api call
    });

    if (auser.data[0]) {
        data.data={"subscriptions":[...auser.data[0].subscriptions]};
        data.errors=null;
    }
    
    return Promise.resolve(data);
}

// get user events
const events = async (props) => {

    /** */
    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["User not found."]};
    let mockdata = {"errors": null, "data": [
        {
            "id": 139,
            "parent_id": null,
            "name": "HS Basketball League",
            "description": "All team members must be wearing their uniforms.",
            "image": "",
            "location_id": 17,
            "start_datetime": "2021/07/27 14:00:00",
            "end_datetime": "2021/07/30 18:00:00",
            "event_type_id": 4,
            "event_type_name": "Games",
            "event_status_id": 1,
            "event_status_name": "Pending",
        }
    ]}
    /**/

    return (
        Request({
            url: "event",
            method: props.method || "POST",
            data: {props},
            //test: mockdata // send mock data to simulate an api call
        })
    );
}


// get user groups
const groups = async (props) => {

    /** */
    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["User not found."]};
    /*let mockdata = {data:[
        {
            id: 123,
            type_id: 4, // Family
            status_id: 1, // Active
            name: "Family",
            description: "Making a mess",
            is_admin: 1,
            group_member_role_id: 6, // Parent/Guardian
            group_member_role: "Parent/Guardian",
            group_member_status_id: 2 // confirmed
        }, 
        {
            id: 33,
            type_id: 2, // Team
            status_id: 1, // Active
            name: "The Hornets",
            description: "Over 50 basketball league",
            is_admin:0,
            group_member_role_id: 2, 
            group_member_role: "Player",
            group_member_status_id:1 // invited
        }, 
        {
            id: 633,
            type_id: 2, // Team
            status_id: 1, // Active
            name: "The Cool",
            description: "Under 80 Shuffleboard",
            is_admin:1,
            group_member_role_id: 1, 
            group_member_role: "Coach",
            group_member_status_id:2 // confirmed
        }
    ], errors:null};
    /**/

    return (
        Request({
            url: "/user/groups" + (props.id ? "/" + props.id : ""),
            method: "GET"
        })
    );
}


// reset password link
const forgotPassword = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["User not found."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/user/passreset",
            data: {
                username: props.username,
                token: props.token
            },
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// forgot user link
const forgotUser = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["User not found."]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/user/usernamereminder",
            data: {
                email: props.email,
                token: props.token
            },
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// reset password
const resetPassword = async (props) => {
    
    return (
        Request({
            url: "/user/passresetconfirm",
            data: {...props},
            method: "POST"
        })
    );
}

// reset password email
const resetPasswordEmail = async (props) => {
    
    return (
        Request({
            url: "/user/passreset",
            data: {...props},
            method: "POST"
        })
    );
}

// registration verification code
const verify = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Code is invalid."]};
    //let mockdata = {data:"D17F25ECFBCC7857F7BEBEA469308BE0B2580943E96D13A3AD98A13675C4BFC2",errors:null};
    
    return (
        Request({
            url: "/user/verify",
            data: {code: props.code},
            method: "POST"
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

const multiregister = async (props) => {
    return (
        Request({
            url: "/user/multiregister",
            data: {...props},
            method: "POST"
        })
    );
}



/**/
// get a photo from a person generated by ai, only 50 requests a month so use carefully
/*const fakePhoto = async(id) => {
    Promise.resolve("https://picsum.photos/seed/"+(id+1)+"/240?random="+(id+1));
    try {
        const response = await Request({
            url: 'https://api.generated.photos/api/v1/faces?per_page=1&gender=male&order_by=random',
            method: 'GET',
            headers: { 'Authorization': 'API-Key WudeqJRiCo5TT_ovZhv6-w' }
        });
        return response.faces[0].urls[4]["512"];
    } catch(error) {
        return Promise.resolve("https://picsum.photos/seed/"+(id+1)+"/240?random="+(id+1)); // fallback to placeholder image
    }
}*/


const upload = async (props, uploadProgressCallback) => {
    return (
        Request({
            url: "/user/upload",
            data: props,
            method: "POST",
            config: {
                onUploadProgress: progressEvent => {
                    let percent = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    if (percent >= 100) uploadProgressCallback(100);
                    else uploadProgressCallback(percent);
                }
            }
        })
    );
}

// get media by id
const getMedia = async (props) => {
    return(
        Request({
            url: "/user/media",
            method: "POST",
            data: { ...props }
        })
    )

}

// get all media from a user
const getAllMedia = async (props) => {
    return(
        Request({
            url: "/user/media/user",
            method: "POST",
            data: { ...props }
        })
    )
}

// update user media
const updateMedia = async (props) => {
    return (
        Request({
            url: `user/media/edit`,
            method: "POST",
            data: props
        })
    );
}

// delete user media
const removeMedia = async (props) => {
    return (
        Request({
            url: `user/media/${props.id}`,
            method: "DELETE",
            data: props
        })
    );
}

const checkEmail = async (props) => {

    return (
        Request({
            url: "user/check-email-availability",
            data: {...props},
            method: "POST"
        })
    );
}

const checkUsername = async (props) => {

    return (
        Request({
            url: "user/check-username-availability",
            data: {...props},
            method: "POST"
        })
    );
}

const tokens = async (props) => {
    return (
        Request({
            url: "/user/tokens",
            data: {...props},
            method: "POST",
        })
    );
}

const wallet = async (props) => {
    return (
        Request({
            url: "/user/wallet",
            method: "GET",
            data: props
        })
    );
}

const eventRegister = async (props) => {
    return (
        Request({
            url: "/user/event/register",
            method: "POST",
            data: props
        })
    );
}

const eventRemove = async (props) => {
    return (
        Request({
            url: "/user/event/remove",
            method: "DELETE",
            data: props
        })
    );
}

const userEvent = async (props) => {
    return (
        Request({
            url: "/user/event/" + props.id,
            method: "GET"
        })
    );
}

const userEventResponse = async (props) => {
    return (
        Request({
            url: "/user/event/request_response",
            method: "POST",
            data: props
        })
    );
}

const getOutstandingCharges = async (props) => {
    return (
        Request({
            url: "/user/outstanding_charges",
            method: "POST",
            data: {...props}
        })
    );
}


const Roles = {
    get: async props => {
        return (
            Request ({
                url: "/user/roles/" + props.user_id,
                data: {},
                method: "GET"
            })
        );
    },
    getAll: async props => {
        return (
            Request ({
                url: "/user/roles",
                data: {},
                method: "GET"
            })
        );
    },
    edit: async props => {
        return (
            Request ({
                url: "/user/" + props.user_id + "/roles",
                data: {role_ids: props.roles},
                method: "PUT"
            })
        );
    }
}

// const Family = {
//     get: async props => {
//         let data={data:null,errors:["User not found."]};
//         const auser=await  Request({
//             url: "/user/user/"+props.user_id,
//             method: "GET",
//             data: {props},
//             //test: mockdata // send mock data to simulate an api call
//         });
    
//         if (auser.data[0].family) {
//             data.data=[...auser.data[0].family];
//             data.errors=null;
//         }

//         return Promise.resolve(data);

//         /*return (
//             Request ({
//                 url: "/user/children/",
//                 data: {user_id:props.user_id},
//                 method: "GET",
//                 test: mockdata
//             })
//         );*/
//     },
// }

const Checkin = {
    getAll: async props => {
        return (
            Request ({
                url: "checkins/",
                data: props,
                method: "POST"
            })
        );
    },
    get: async props => {
        return (
            Request ({
                url: "user/checkin_history/",
                data: props,
                method: "POST"
            })
        );
    },
    create: async props => {
        return (
            Request ({
                url: "/user/checkin/",
                data: props,
                method: "POST"
            })
        );
    }
}

const Merge = {
    merge: async props=>{
        return(
            Request({
                url: "/user/merge",
                data: props,
                method: "POST"
            })
        )
    },
    undo_merge: async props=>{
        return(
            Request ({
                url:"/user/undo_merge",
                data: props,
                method: "POST"
            })
        )
    },
    get_user: async props=>{
        return(
            Request({
                url:`/user/merge/${props.user_id}`,
                data: props,
                method: "GET"
            })
        );
    },
    get_all_merges: async props=>{
        return(
            Request({
                url:'/user/merge',
                data: props,
                method: "GET"
            })
        );
    },
    get_merges_post: async props=>{
        return(
            Request({
                url:'user/merge/search',
                data: props,
                method: "POST"
            })
        );
    }
}

const PaymentProfile = {
    create: async props=>{
        return(
            Request({
                url: "payment/nmi_create_profile",
                data: props,
                method: "POST"
            })
        )
    },

    createFromTransaction: async props=>{
        return(
            Request({
                url: "payment/nmi_create_profile_from_transaction",
                data: props,
                method: "POST"
            })
        )
    }
}

const Users = {
    get, 
    getList, 
    getOutstandingCharges, 
    getGiftCards, 
    address, 
    orders, 
    purchasedProduct, 
    create, 
    update, 
    remove, 
    list, 
    forgotUser, 
    forgotPassword, 
    resetPassword, 
    resetPasswordEmail, 
    verify, 
    multiregister, 
    groups, 
    events, 
    upload, 
    getMedia, 
    getAllMedia, 
    updateMedia,
    removeMedia, 
    tokens, 
    wallet, 
    checkUsername, 
    login, 
    checkEmail, 
    setUsername, 
    eventRegister, 
    eventRemove, 
    userEvent, 
    userEventResponse, 
    memberships, 
    Roles, 
    Checkin, 
    Merge, 
    PaymentProfile, 
    getSocials
}

export default Users;

========================================
