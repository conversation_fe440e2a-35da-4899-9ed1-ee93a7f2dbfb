import { useEffect, useState, useCallback } from 'react';

export const useProductEffects = ({
    containerRef, 
    thresholdRef, 
    allProducts, 
    itemsToLoad, 
    setFilteredProducts, 
    loading, 
    setLoading, 
    isBuilder,
    ...props
}) => {
    const [hasMore, setHasMore] = useState(itemsToLoad > 0);

    const fetchMore = useCallback(() => {
        if (!loading && hasMore && allProducts?.length > 0) {
            setLoading(true);
            try {
                setFilteredProducts(prev => {
                    const nextBatch = allProducts.slice(prev.length, prev.length + itemsToLoad);
                    if (nextBatch.length === 0) {
                        setHasMore(false);
                        return prev;
                    }
                    return [...prev, ...nextBatch];
                });
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        }
    }, [loading, hasMore, allProducts, itemsToLoad, setFilteredProducts, setLoading]);
 
    useEffect(() => {
        if (itemsToLoad && allProducts.length && hasMore && !isBuilder) {
            //setFilteredProducts(allProducts.slice(0, itemsToLoad));
            if (allProducts.length <= itemsToLoad) setHasMore(false);
            else setHasMore(true);
        }
        if (isBuilder || !itemsToLoad) setHasMore(false);
    }, [allProducts, itemsToLoad, hasMore, isBuilder]);

   // infinite scroll (TODO: make this better so it loads products from the server based on itemsToLoad to make rendering faster)
    useEffect(() => {
        if (!itemsToLoad || loading || !hasMore || !allProducts.length || isBuilder) return;

        const handleObserver = entities => {
            const target = entities[0];
            if (target.isIntersecting && !loading && hasMore) {
                fetchMore();
            }
        };

        const observer = new IntersectionObserver(handleObserver, {
            root: containerRef?.current || null,
            rootMargin: '20px',
            threshold: 1,
        });
        
        if (thresholdRef?.current) observer.observe(thresholdRef.current);

        return () => {
            if (thresholdRef.current) {
                observer.unobserve(thresholdRef.current);
                observer.disconnect();
            }            
        }
    }, [loading, hasMore, allProducts, itemsToLoad, isBuilder, thresholdRef?.current, containerRef?.current, fetchMore]);

    return {
        hasMore,
        setHasMore,
        fetchMore,
    }
};
