import { useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';

import { setInfo, resetProduct } from '../../store/reducers/currentShopItemSlice';
import { useProducts } from './useProducts';

export const useProduct = ({
    onAddToCart,
    cartItemId,
    productId,
    variantId,
    isBuilder,
    ...props
}) => {
    const dispatch = useDispatch();
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);
    const cart = useSelector(state => state.cart, shallowEqual);

    const [errors, setErrors] = useState(null);
    const [openProductDetail, setOpenProductDetail] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);

    const [media, setMedia] = useState([]);
    const [variant, setVariant] = useState(null);
    const [addons, setAddons] = useState(null);
    const [forUserIds, setForUserIds] = useState(null);
    const [giftCardRecipient, setGiftCardRecipient] = useState(null);

    const {
        products: allProducts,
        loading,
        errors: productsErrors,
        fetchData: fetchProducts,
    } = useProducts({});

    const handleSelectProduct = useCallback(async (value, cartItem = {}) => {
        let product = allProducts.find(a => a.id === value.id || value);
        if (!product || !allProducts?.length) {
            try {
                const res = await fetchProducts({max_records: 1, id: +value.id || +value});
                if (res) product = res?.[0] || res;
            } catch (error) {
                console.error(error);
            }
        }

        if (product){
            const item = {
                productId: product?.id, 
                productName: product?.name,
                qty: 1, 
                eventId: product?.events?.[0]?.id || null, 
                eventName: product?.events?.[0]?.name || null,
                hasEvent: product?.events?.length > 0 || false,
                tmpId: cartItem?.id || null,
                ...cartItem,
                metadata: {...product, ...cartItem?.metadata},
                productCustomPrice: cartItem?.metadata?.price || null,
            };
            dispatch(setInfo(item));
            setSelectedProduct(item);

            //if (item.addons) setAddons(item.addons);
            //if (item.forUserIds) setForUserIds(item.forUserIds);
            //if (item.giftCardRecipient)setGiftCardRecipient(item.giftCardRecipient);


            // DEFAULT VARIANT
            if (!variant && product?.product_variants?.length){
                let _variant = null;
                if (variantId || item?.productVariantId) _variant = product?.product_variants.find(v => +v.id === +item?.productVariantId || +variantId) || null;
                else _variant = product?.product_variants?.[0] || null;

                if (_variant){
                    setVariant(_variant);
                    dispatch(setInfo({productVariantId: _variant.id}));
                }
            }

            // MEDIA
            const _media = [];
            // transform product media data so it matches the gallery component
            product?.media?.forEach(m => {
                if (m.preview_url) _media.push(m);
                else {
                    _media.push({
                        id: m.id,
                        description: m.description,
                        preview_url: m.path,
                        type: m.media_type,
                        metadata: m.metadata,
                    });
                }
            });
            // transform product variant media data so it matches the gallery component
            product?.product_variants?.forEach(v => {
                if (v?.media){
                    v.media.forEach(m => {
                        if (m.preview_url) _media.push(m);
                        else {
                            _media.push({
                                id: m.id,
                                description: m.description,
                                preview_url: m.path,
                                type: m.media_type,
                                metadata: m.metadata,
                            });
                        }
                    });
                }
            });
            product?.events?.forEach(e => {
                if (e.images) _media.push(...e.images);
            });
            setMedia(_media);
        }
        setOpenProductDetail(true);
    }, [dispatch, fetchProducts, allProducts, variant, variantId, cartItemId]);

    const handleDetailsClose = useCallback(() => {
        dispatch(resetProduct());
        setSelectedProduct(null);
        setOpenProductDetail(false);
    }, [dispatch]);

    const handleAddToCart = useCallback((product, reduxState) => {
        if (!onAddToCart || !product) return;

        const res = onAddToCart(product, reduxState);
        switch (res){
            case 0:
                return;
            case 1:
                handleDetailsClose();
                break;
            case 2:
                handleSelectProduct(product);
                break;
            default:
                break;
        }
    }, [handleDetailsClose, handleSelectProduct, onAddToCart]);


    const handleMemoSave = useCallback(text => {
        dispatch(setInfo({memo: text}));
    }, [dispatch]);

    const handleGiftCardChange = useCallback(info => {
        if (!info) return;
        dispatch(setInfo({giftCardRecipient: info}));
        setGiftCardRecipient(info);
    }, [dispatch]);    

    const handleVariantChange = useCallback(variant => {
        const _variantId = variant?.id || variant || null;
        if (!_variantId) return;

        const _variant = selectedProduct?.metadata?.product_variants.find(v => +v.id === +_variantId) || null;
        if (!_variant) return;

        dispatch(setInfo({productVariantId: _variantId, productCustomPrice: null}));
        setVariant(_variant);        
    }, [selectedProduct, dispatch]);

    const handleAddonChange = useCallback(addon => {
        const _addons = addon?.length ? addon : [];
        dispatch(setInfo({addons: _addons.map(a => ({
            id: a.id, 
            name: a.name, 
            variantId: a.variantId || a.variant_id, 
            productId: a.productId || a.product_id, 
            price: +a.price || 0, 
            salePrice: +a.sale_price || 0, 
            tokenPrice: +a.token_price || 0, 
            metadata: a.metadata || {...a}
        }))}));
        setAddons(_addons); 
    }, [dispatch]);

    const handleForUserChange = useCallback(user => {
        const _userIds = user?.length ? user : [];
        dispatch(setInfo({forUserIds: _userIds.map(a => ({
            id: a.id, 
            firstName: a.first_name, 
            lastName: a.last_name, 
            role: a.role_name, dob: a.dob
        })), qty: _userIds.length}));
        setForUserIds(_userIds); 
    }, [dispatch]);

    useEffect(() => {
        if (productsErrors) setErrors(productsErrors);
    }, [productsErrors]);

    useEffect(() => {
        if (productId) handleSelectProduct(productId);
        if (cartItemId && cart?.cart?.length){
            const _cartItem = cart?.cart?.find(i => i.id === cartItemId) || null;
            if (_cartItem) handleSelectProduct(_cartItem.productId, _cartItem);
        }
    }, [productId, cartItemId, cart?.cart, handleSelectProduct]);

    useEffect(() => {
        return () => {
            setOpenProductDetail(false);
        }
    }, []);

    return {
        products: allProducts,
        loading,
        errors,
        setErrors,
        handleSelectProduct,
        handleAddToCart,
        handleDetailsClose,
        openProductDetail,
        setOpenProductDetail,
        selectedProduct,
        setSelectedProduct,
        currentShopItem,
        product: selectedProduct?.metadata || null,
        media,
        variant,
        setVariant,
        handleVariantChange,
        forUserIds,
        setForUserIds,
        handleForUserChange,
        giftCardRecipient,
        setGiftCardRecipient,
        handleGiftCardChange,
        addons,
        setAddons,
        handleAddonChange,
        handleMemoSave,
    };
};
