import React, { useMemo } from 'react';
import { useMatches, Link as RouterLink } from 'react-router-dom';
import { Stack, Breadcrumbs, Link, Typography } from '@mui/material';

import { formatSlug } from '../../../utils/cms';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

const useSafeMatches = ({isBuilder, t}) => {
    if (isBuilder) {
        return [
            { handle: { title: t('module:siteBoss.menu.home') }, pathname: '/'},
            { handle: { title: t('builder:component.menu.url') }, pathname: '/'},
            { handle: { title: t('builder:component.menu.url') }, pathname: '/'}
        ];
    }
    return useMatches();
};

export const Breadcrumb = ({
    id,
    separator,
    maxItems = 5,
    underline,
    variant,
    alignItems,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        breadcrumbs: {},    // MUI breadcrumbs props
        link: {},           // MUI link props
        text: {},           // MUI typography props
    },
    condition = null,
    isBuilder = false,
    builderProps,
    children,
    ...props
}) => {
    const { slotProps: updatedSlotProps, t, canRender, customCss } = prepareComponent({name: "breadcrumb", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const matches = useSafeMatches({isBuilder, t});

    const breadcrumbItems = useMemo(() => {
        const parents = matches.filter(a => a.handle?.title).map(match => {
            let title = match.handle.title.split(' - ')[0]; // get just the page title, not the full title
            if (title.startsWith('{') && title.endsWith('}')) {
                title = "";
            }
            return ({
                title,
                path: `/${formatSlug(match.pathname)}`
            });
        });
        if (!parents.find(a => a.path === '/' || a.path === '')) {
            parents.unshift({title: t("module:siteBoss.menu.home"), path: '/'});
        }
        return parents.filter(a => a.title && a.path);
    }, [matches, isBuilder, t]);

    if (!canRender || (breadcrumbItems.length === 0 && !isBuilder)) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                <Breadcrumbs 
                    separator={separator} 
                    maxItems={+maxItems || 5} 
                    {...slotProps?.breadcrumbs}
                    sx={{
                        '& .MuiBreadcrumbs-ol':{
                            justifyContent: alignItems || "flex-start", 
                        },
                        ...slotProps?.breadcrumbs?.sx
                    }}
                >
                    {breadcrumbItems.map((item, index) => {
                        const isLast = index === breadcrumbItems.length - 1;
                        
                        return (
                            <Link 
                                component={isBuilder ? "span" : RouterLink}
                                key={item.path}
                                to={isBuilder? undefined : item.path}
                                disabled={isBuilder}
                                underline={underline}
                                variant={variant}
                                color="inherit"
                                {...slotProps?.link}
                            >
                                {item.title}
                            </Link>
                        );
                    })}
                </Breadcrumbs>
            </Stack>
            {children}
        </CmsContainer>
    );
};