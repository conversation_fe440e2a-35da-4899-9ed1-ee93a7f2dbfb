import { Meta } from '@@storybook/addon-docs';

<Meta title='Docs/Storybook/Details and "How To"' />

# Contents

[Links](#links)  
[Addons](#addons)  
[Mocking Redux](#mocking-redux)

## Helpful Links  

[Storybook Blocks](https://storybook.js.org/docs/writing-docs/doc-blocks)\
[MDX Combined Stories](https://storybook.js.org/docs/writing-docs/mdx)\
[Markdown Refresher](https://www.markdownguide.org/basic-syntax/)

---

## Storybook Utilized Addons

Addons are powerful tools we can utilize.  Some of the addons we have are as follows:

***

**Design**
Design allows us to implement figma designs straight in storybook to work from

[*Docs*](https://storybookjs.github.io/addon-designs/?path=/docs/docs-quick-start--docs) \
[*Addon link*](https://storybook.js.org/addons/storybook-addon-designs)

***

**Accessibility**
This addon automatically runs tests when clicked to check the general accesibility of a component.

[*Docs/Addon Link*](https://storybook.js.org/addons/@storybook/addon-a11y)

***

**Chromatic Visual Testing**
Chromatic works with both Storybook AND Cypress and Storybook openly supports Chromatic.  Therefore I've chosen it for our visualy testing and begun integrating it into Storybook.

***

**Addon Actions (Part of Essentials base Install)**\
[*The Docs*](https://storybook.js.org/docs/essentials/actions)
 
 ---

## Mocking Redux

Storybook is set up to automatically take in the stores of the projects currently in this repo (Siteboss, Shared, and CMS).  


To add to this list in the future and to add mock data, you go from root > assets > Mocks > redux > mockStore.js \


This is where, if we add any other stores, we can add their mocked selves.  If a file has "cms", "shared", or "siteboss" in their title
they will put together the proper stores.  In order to mock more specific data for your story, you need to create the initial data. 
This needs to be passed in with the 'export default' on your story.  

```
export default{
    title: "CMS/something/something",
    component: Something,
    tags:['autodocs'],
    parameters:{
        init:{
            Your mocked state here
        }
    },
    argTypes:{

    }

}
```

The parameters are used for many things in storybook, "init" is something I added for us and is unique (i.e. no docs on it.  It's just part of the context passed to the stories).
You can declare your init object as above or declare it at the top of your doc and pass it in - they both get the job done.  

---
