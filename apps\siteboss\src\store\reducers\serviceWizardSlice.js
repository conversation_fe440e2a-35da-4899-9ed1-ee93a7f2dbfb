import { createSlice } from '@reduxjs/toolkit';

export const serviceWizardSlice = createSlice({
    name: 'serviceWizard',
    initialState: {
        id: null,
        serviceData: null,
        selectedLocations: [],
        selectedManagers: [],
        formData: null
    },
    reducers: {
        setInfo: (state, action) => {
            if (action.payload.hasOwnProperty('id')) state.id = action.payload.id;
            if (action.payload.hasOwnProperty('serviceData')) state.serviceData = action.payload.serviceData;
            if (action.payload.hasOwnProperty('formData')) state.formData = action.payload.formData;
        },
        setSelectedLocations: (state, action) => {
            state.selectedLocations = action.payload;
        },
        addLocation: (state, action) => {
            const exists = state.selectedLocations.some(location => location.id === action.payload.id);
            if (!exists) {
                state.selectedLocations.push(action.payload);
            }
        },
        removeLocation: (state, action) => {
            state.selectedLocations = state.selectedLocations.filter(
                location => location.id !== action.payload
            );
        },
        setSelectedManagers: (state, action) => {
            state.selectedManagers = action.payload;
        },
        addManager: (state, action) => {
            const exists = state.selectedManagers.some(manager => manager.id === action.payload.id);
            if (!exists) {
                state.selectedManagers.push(action.payload);
            }
        },
        removeManager: (state, action) => {
            state.selectedManagers = state.selectedManagers.filter(
                manager => manager.id !== action.payload
            );
        },
        updateFormData: (state, action) => {
            state.formData = action.payload;
        },
        resetInfo: (state) => {
            state.id = null;
            state.serviceData = null;
            state.selectedLocations = [];
            state.selectedManagers = [];
            state.formData = null;
        }
    }
});

export const {
    setInfo,
    setSelectedLocations,
    addLocation,
    removeLocation,
    setSelectedManagers,
    addManager,
    removeManager,
    updateFormData,
    resetInfo
} = serviceWizardSlice.actions;

export default serviceWizardSlice.reducer;
