import React from 'react';
import { 
    FormControl, 
    FormLabel, 
    FormHelperText, 
    RadioGroup, 
    FormControlLabel, 
    Radio,
    Box,
    styled
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

// Styled components for the radio buttons
const StyledFormControlLabel = styled(FormControlLabel)(({ theme, checked }) => ({
    margin: theme.spacing(0.5),
    '& .MuiRadio-root': {
        display: 'none',
    },
    '& .MuiTypography-root': {
        fontWeight: checked ? 600 : 400,
        color: checked ? theme.palette.primary.main : theme.palette.text.primary,
    },
}));

const StyledRadioButton = styled(Box)(({ theme, checked }) => ({
    padding: theme.spacing(1, 2),
    border: `1px solid ${checked ? theme.palette.primary.main : theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
    backgroundColor: checked ? theme.palette.primary.light : 'transparent',
    transition: theme.transitions.create(['background-color', 'border-color']),
    '&:hover': {
        backgroundColor: checked ? theme.palette.primary.light : theme.palette.action.hover,
        borderColor: checked ? theme.palette.primary.main : theme.palette.action.active,
    },
}));

/**
 * StyledRadioGroup component that looks like outlined buttons/tabs
 *
 * @param {Object} props - Component props
 * @param {string} props.label - Label for the radio group
 * @param {string} props.name - Name of the radio group
 * @param {boolean} props.required - Whether the field is required
 * @param {string} props.helperText - Helper text for the radio group
 * @param {Array} props.options - Options for the radio group
 * @param {Function} props.onChange - Function to call when the value changes
 * @param {string|number} props.value - Current value of the radio group
 * @returns {JSX.Element}
 */
export const StyledRadioGroup = ({
    label,
    name,
    required,
    helperText,
    options,
    onChange,
    value,
    error,
    ...props
}) => {
    const handleChange = (event) => {
        onChange(event.target.value);
    };

    return (
        <FormControl component="fieldset" required={required} error={error} fullWidth>
            {label && <FormLabel component="legend">{label}</FormLabel>}
            <RadioGroup
                aria-label={name}
                name={name}
                value={value}
                onChange={handleChange}
                row
                {...props}
            >
                {options.map((option) => {
                    const isChecked = String(value) === String(option.value);
                    return (
                        <StyledFormControlLabel
                            key={option.value}
                            value={option.value}
                            checked={isChecked}
                            control={<Radio />}
                            label={
                                <StyledRadioButton checked={isChecked}>
                                    {option.label}
                                    {isChecked && <CheckCircleIcon fontSize="small" color="primary" />}
                                </StyledRadioButton>
                            }
                        />
                    );
                })}
            </RadioGroup>
            {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </FormControl>
    );
};

export default StyledRadioGroup;
