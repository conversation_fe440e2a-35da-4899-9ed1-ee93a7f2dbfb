import { useState, useMemo } from 'react';
import {
  Container,
  Grid2 as Grid,
  Card,
  CardContent,
  CardActionArea,
  Typography,
  Box,
  Tab,
  Chip
} from '@mui/material';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import {
  LocalShipping as ShippingIcon,
  Receipt as OrderIcon,
  People as CustomerIcon,
  AccountBalance as FinancialIcon,
  Assessment as OverviewIcon,
  Schedule as TimeIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { Title } from '@siteboss-frontend/shared/components';

// Import report components
import OrderReports from './OrderReports/OrderReports';
import ShippingReports from './ShippingReports/ShippingReports';
import CustomerReports from './CustomerReports/CustomerReports';
import FinancialReports from './FinancialReports/FinancialReports';

export const Reports = () => {
  const [selectedTab, setSelectedTab] = useState('overview');

  // Define report categories
  const reportCategories = useMemo(() => [
    {
      id: 'overview',
      name: 'Overview',
      description: 'View all available reports and key metrics',
      icon: <OverviewIcon />,
      color: '#2196F3',
      reports: []
    },
    {
      id: 'orders',
      name: 'Order Reports',
      description: 'Track order volume, status, and processing metrics',
      icon: <OrderIcon />,
      color: '#4CAF50',
      reports: [
        { name: 'Order Volume', description: 'Daily/weekly/monthly order counts' },
        { name: 'Order Status', description: 'Distribution of order statuses' },
        { name: 'Processing Times', description: 'Average order processing duration' },
        { name: 'Order Value', description: 'Revenue and average order value trends' }
      ]
    },
    {
      id: 'shipping',
      name: 'Shipping Reports',
      description: 'Monitor carrier performance and shipping costs',
      icon: <ShippingIcon />,
      color: '#FF9800',
      reports: [
        { name: 'Carrier Performance', description: 'Delivery times and success rates' },
        { name: 'Shipping Costs', description: 'Cost analysis by carrier and region' },
        { name: 'Delivery Analytics', description: 'On-time delivery and tracking metrics' },
        { name: 'Zone Analysis', description: 'Shipping performance by geographic zone' }
      ]
    },
    {
      id: 'customers',
      name: 'Customer Reports',
      description: 'Analyze customer activity and billing patterns',
      icon: <CustomerIcon />,
      color: '#9C27B0',
      reports: [
        { name: 'Customer Activity', description: 'Order frequency and patterns' },
        { name: 'Billing Summary', description: 'Customer billing and payment status' },
        { name: 'Customer Growth', description: 'New vs returning customer metrics' },
        { name: 'Account Status', description: 'Active, inactive, and overdue accounts' }
      ]
    },
    {
      id: 'financial',
      name: 'Financial Reports',
      description: 'Track revenue, payments, and financial performance',
      icon: <FinancialIcon />,
      color: '#F44336',
      reports: [
        { name: 'Revenue Trends', description: 'Monthly and quarterly revenue analysis' },
        { name: 'Payment Methods', description: 'Payment method usage and success rates' },
        { name: 'Outstanding Balances', description: 'Overdue accounts and collections' },
        { name: 'Profit Margins', description: 'Service profitability analysis' }
      ]
    }
  ], []);

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  // Quick stats for overview
  const quickStats = [
    { label: 'Total Orders Today', value: '127', icon: <OrderIcon />, color: '#4CAF50' },
    { label: 'Active Shipments', value: '89', icon: <ShippingIcon />, color: '#FF9800' },
    { label: 'Revenue This Month', value: '$45,230', icon: <MoneyIcon />, color: '#2196F3' },
    { label: 'Avg Processing Time', value: '2.3h', icon: <TimeIcon />, color: '#9C27B0' }
  ];

  return (
    <Container>
      <Title
        title="Reports Dashboard"
        breadcrumbs={[{ title: 'Reports Dashboard' }]}
      />

      <TabContext value={selectedTab}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <TabList onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
            {reportCategories.map((category) => (
              <Tab
                key={category.id}
                label={category.name}
                value={category.id}
                icon={category.icon}
                iconPosition="start"
                sx={{ minHeight: 48 }}
              />
            ))}
          </TabList>
        </Box>

        {/* Overview Tab */}
        <TabPanel value="overview" sx={{ p: 0 }}>
          {/* Quick Stats */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {quickStats.map((stat, index) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                <Card sx={{
                  height: '100%',
                  background: `linear-gradient(135deg, ${stat.color} 0%, ${stat.color}CC 100%)`,
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ opacity: 0.9 }}>
                        {stat.label}
                      </Typography>
                      <Box sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.2)',
                        borderRadius: '50%',
                        p: 1,
                        display: 'flex'
                      }}>
                        {stat.icon}
                      </Box>
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                      {stat.value}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Report Categories Grid */}
          <Grid container spacing={3}>
            {reportCategories.slice(1).map((category) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={category.id}>
                <Card sx={{ height: '100%', transition: 'transform 0.2s', '&:hover': { transform: 'translateY(-4px)' } }}>
                  <CardActionArea onClick={() => setSelectedTab(category.id)} sx={{ height: '100%' }}>
                    <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box sx={{
                          bgcolor: category.color,
                          color: 'white',
                          borderRadius: '50%',
                          p: 1,
                          mr: 2,
                          display: 'flex'
                        }}>
                          {category.icon}
                        </Box>
                        <Typography variant="h6" component="div">
                          {category.name}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
                        {category.description}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {category.reports.slice(0, 3).map((report, index) => (
                          <Chip
                            key={index}
                            label={report.name}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        ))}
                        {category.reports.length > 3 && (
                          <Chip
                            label={`+${category.reports.length - 3} more`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.75rem', opacity: 0.7 }}
                          />
                        )}
                      </Box>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Individual Report Tabs */}
        <TabPanel value="orders" sx={{ p: 0 }}>
          <OrderReports />
        </TabPanel>

        <TabPanel value="shipping" sx={{ p: 0 }}>
          <ShippingReports />
        </TabPanel>

        <TabPanel value="customers" sx={{ p: 0 }}>
          <CustomerReports />
        </TabPanel>

        <TabPanel value="financial" sx={{ p: 0 }}>
          <FinancialReports />
        </TabPanel>
      </TabContext>
    </Container>
  );
};

export default Reports;
