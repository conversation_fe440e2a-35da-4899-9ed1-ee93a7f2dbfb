import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Dialog, DialogContent, useMediaQuery } from '@mui/material';
import { CloudUploadOutlined as UploadIcon } from '@mui/icons-material';

import ImageCropper from '../../FileUpload/ImageCropper';
import FilePreview from '../../FileUpload/Preview';

export const  UploadButton = ({
    label, // the label for the button
    name, // the name of the button
    icon, // the icon for the button
    variant, // the variant of the button
    accept = "image/*", // the file types to accept
    fileSize = 2097152, // the file size
    errors, // the errors for the button
    loading, // the loading status of the form so we can disable the button
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onRemove, // the function to be called when the file is removed
    multiple = false, // if the button should accept multiple files
    size, // the size of the button
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [files , setFiles] = useState({processed:[], originals:[]});
    const [filePreviews, setFilePreviews] = useState([]);
    const [sendFile, setSendFile] = useState(false);
    const [error, setError] = useState(null);
    const [showImageCropper, setShowImageCropper] = useState(null);


    // validate that the file is of the correct type and size
    const validateFile = f => {
        if (!f || !f.length) return false;
        let valid = true;
        if (accept) valid = Array.from(f).every(file => accept.split(',').some(type => file.file.type.match(type.trim())));
        if (fileSize && valid) valid = Array.from(f).every(file => file.file.size <= fileSize) ? true : -1;
        return valid;
    }

    // process the files to show previews
    const processFiles = useCallback(f => {
        if (!f.length) return;
        const _previews = Array.from(f).map(file => {
            const id = file?.id;
            if (file.file.type.startsWith('image/')) {
                return { name: file?.description || file.file.name, url: URL.createObjectURL(file.file), type: file.file?.type || 'image', id };
            } else {
                return { name: file?.description || file.file.name, type: file.file?.type || 'other', id };
            }
        });
        return _previews;
    }, [multiple]);

    const handleFiles = (f, send = true) => {
        if (f.length){
            const valid = validateFile(f);
            if (valid === true) {
                setError(null);
                const _previews = processFiles(f);
                setFilePreviews(prev => [...(multiple ? prev : []), ..._previews]);
                setFiles(prev => ({
                    processed: [...(multiple ? prev.processed : []), ...f], 
                    originals: [...(multiple ? prev.originals : []), ...f]
                }));
                if (send) setSendFile(true);
            } else {
                if (valid === -1) setError(t('error:invalidFileSize'));
                else setError(t('error:invalidFileType'));
            }
        }
    }

    // this shows the image cropper
    const handleFileCrop = f => e => {
        e.stopPropagation();
        if (!f) return;

        setError(null);
        const preview = processFiles([f]);
        setShowImageCropper(preview[0]);
    }

    // this removes a file from the list
    const handleFileRemove = file => e => {
        e.stopPropagation();
        if (!file) return;
        setError(null);
        setFilePreviews(prev => prev.filter(f => {
            if (f.id &&  file.id) return f.id !== file.id;
            return f.name !== file.name
        }));
        setFiles(prev => ({
            processed: prev.processed.filter(f => {
                if (f.id &&  file.id) return f.id !== file.id;
                return f.name !== file.name
            }), 
            originals: prev.originals.filter(f => {
                if (f.id &&  file.id) return f.id !== file.id;
                return f.name !== file.name
            })
        }));
        if (onRemove) onRemove(file);
    }

    // this deals with the final cropped image
    const handleCrop = file => (blob, imageUrl) => {
        if (!blob || !imageUrl) return;
        const croppedFile = new File([blob], file.name, { type: blob.type });
        setFiles(prev => ({
            ...prev, 
            processed: prev.processed.map(f => f.name === file.name ? croppedFile : f)
        }));
        setFilePreviews(prev => prev.map(f => f.name === file.name ? { name: file.name, url: imageUrl, type: 'image' } : f));
        setSendFile(true);
    }    

    const handleChange = e => handleFiles(Array.from(e.target.files).map(f => ({file: f})));

    // cleaup
    useEffect(() => {
        return () => {
            filePreviews.forEach((file) => {
                if (file.type === 'image') URL.revokeObjectURL(file.url);
            });
        }
    }, [filePreviews]);    

    return (
        <>
            <Button
                component="label"
                role={undefined}
                variant={variant || "contained"}
                tabIndex={-1}
                startIcon={icon || <UploadIcon />}
                disabled={loading}
                size={size || undefined}
            >
                {t(label || "file:upload")}
                <input 
                    accept={accept} 
                    hidden 
                    onChange={handleChange}
                    type="file" 
                    style={{
                        clip: 'rect(0 0 0 0)',
                        clipPath: 'inset(50%)',
                        height: 1,
                        width: 1,
                        overflow: 'hidden',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        whiteSpace: 'nowrap',
                    }}
                />
            </Button>
            {filePreviews.length > 0 &&
                <FilePreview files={files} previews={filePreviews} multiple={multiple} onFileRemove={handleFileRemove} onFileCrop={handleFileCrop} />
            }
            {/* image cropper */}
            {showImageCropper &&
                <Dialog open={Boolean(showImageCropper)} onClose={()=>setShowImageCropper(null)} fullWidth maxWidth="sm" fullScreen={isMobile} closeAfterTransition={false} sx={{overflow: 'hidden'}}>
                    <DialogContent sx={{p: 0, overflow: 'hidden'}}>
                        <ImageCropper 
                            src={showImageCropper?.url} 
                            onCrop={handleCrop(showImageCropper)} 
                            onClose={()=>setShowImageCropper(null)} 
                            cropAreaProps={props?.sx || undefined} 
                        />
                    </DialogContent>
                </Dialog>
            }
        </>
    );
}