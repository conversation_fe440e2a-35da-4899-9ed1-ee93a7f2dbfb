import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography } from '@mui/material';
import { createCurrencyFormatter } from '@siteboss-frontend/shared/utils';

export const Footer = (props) => {
    const { data } = props;
    const { t, isMobile } = useOutletContext();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company?.currency);
    
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    if (!data) return null;

    const fields = [
        { key: 'subtotal_price', slug: 'order:subtotal', valueFormatter: (value) => currencyFormatter.format(value || 0)},
        { key: 'tax_total', slug: 'order:tax', valueFormatter: (value) => currencyFormatter.format(value || 0)},
        { key: 'tip', slug: 'order:tip', valueFormatter: (value) => currencyFormatter.format(value || 0)},
        { key: 'total_price', slug: 'order:total', variant: 'h6', valueFormatter: (value) => currencyFormatter.format(value || 0)},
    ];

    return (
        <Box sx={{width: "100%", whiteSpace: "nowrap", order: isMobile ? 0 : 2, textAlign:'right'}}>
            {fields.map((field, index) => {
                const value = data[field.key];
                if (value === undefined || value === null) return null;
                
                return (
                    <Typography 
                        key={`footer-field-${field.key}`}
                        variant={field.variant || "subtitle2"}
                    >
                        {t(field.slug)}: {field.valueFormatter ? field.valueFormatter(value) : value}
                    </Typography>
                );
            })}
        </Box>
    );
}
