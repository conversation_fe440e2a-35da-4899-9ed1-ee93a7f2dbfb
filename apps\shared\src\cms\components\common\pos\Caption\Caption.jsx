import React from 'react';
import { Typography, useTheme } from '@mui/material';

export const Caption = React.forwardRef(({variant, component, text, bold = false, ...props}, ref) => {
    const theme = useTheme();
    return (
        <Typography 
            ref={ref}
            variant={variant} 
            component={component} 
            sx={{
                wordBreak: "break-word",
                fontWeight: bold ? theme.typography.fontWeightMedium : undefined,
                ...props?.sx
            }}
            {...props}
        >
            {text}
        </Typography>
    );
});