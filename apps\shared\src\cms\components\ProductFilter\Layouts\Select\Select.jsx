import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { TextField, MenuItem, Box } from '@mui/material';
import { flattenTree } from '../.././../../../utils';

export const Select = ({ 
    variant = "outlined",
    color = "primary",
    size = "medium",
    fullWidth = true,
    options = [], 
    loading,
    disabled,
    option = {},  // MUI MenuItem props
    children, 
    onSelect,
    type = "types",
    label = "product:productType",
    allSlug = "product:productTypes.all",
    reset = false,
    isBuilder,
    selectedId,
    ...props 
}) => {
    const {t} = useTranslation();

    const allOptions = useMemo(() => {
        if (!options || reset) return [];
        return flattenTree(JSON.parse(JSON.stringify(options)));
    }, [options, reset]);

    const [value, setValue] = useState(selectedId || "");

    const handleChange = useCallback(e => {
        setValue(e.target.value);
        if (onSelect) onSelect(e.target.value);
    }, [onSelect]);

    useEffect(() => {
        if (options.length === 1) onSelect(options[0].id);
    }, [options, onSelect]);

    useEffect(() => {
        if (reset) setValue("");
    }, [reset]);

    if (options?.length <= 1) return null;

    return (
        <>
            <TextField
                id={`product-${type}-select`}
                select
                label={t(label)}
                aria-label={t(label)}
                variant={variant}
                size={size}
                color={color}
                fullWidth={fullWidth === false ? false : true}
                disabled={loading || disabled}
                onChange={handleChange}
                value={value}
                {...props}
                slotProps={{
                    ...props?.slotProps,
                    input: {
                        ...props?.slotProps?.input,
                        sx: {
                            '.MuiSelect-select': {
                                '>.MuiBox-root': {
                                    ml: 0, // remove the depth padding from the displayed value
                                },
                            },
                        },    
                    }
                }}
            >
                {allOptions.length > 1 && <MenuItem value={""} {...option}>{t(allSlug)}</MenuItem>}
                {allOptions.map((option, i) => (
                    <MenuItem key={option.id || i} value={option.id} disabled={disabled || loading}>
                        <Box component="span" sx={{ml: option.depth * 2, fontWeight: option?.children?.length > 0 ? theme => theme.typography.fontWeightBold : undefined}}>
                            {t(option?.slug) || option?.label || option?.name || option}
                        </Box>
                    </MenuItem>
                ))}
            </TextField>
            {children}
        </>
    );
}