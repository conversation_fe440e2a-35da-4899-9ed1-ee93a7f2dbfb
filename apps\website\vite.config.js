import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// this is temporary
function handleMuiInternalsPlugin() {
  return {
    name: 'handle-mui-internals',
    transform(code, id) {
      if (id.includes('@mui/x-internals/reactMajor')) {
        return `
          import * as React from 'react';
          export default parseInt(React.version, 10);        
        `;
      }
    }
  }
}

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const BASE_PATH = `${env.VITE_BASE_PATH ?? ''}/`;

  return {
    base: BASE_PATH,
    plugins: [react(), handleMuiInternalsPlugin()],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern'
        }
      }
    },
    build: {
      manifest: true,
      minify: true,
      commonjsOptions: {
        include: [/node_modules/],
        transformMixedEsModules: true,
      },      
    },
    ssr: {
      noExternal: [/^d3.*$/, /^@nivo.*$/, /^@mui.*$/, /^@emotion.*$/],
    },
    optimizeDeps: {
      include: [
        '@mui/material',
        '@mui/lab',
        '@mui/icons-material',
        '@emotion/react',
        '@emotion/styled',
      ],
      force: true,
    }, 
    server: {
      watch: {
        ignored: ['**/node_modules/**', '**/dist/**'],
      },
    },
  }
});
