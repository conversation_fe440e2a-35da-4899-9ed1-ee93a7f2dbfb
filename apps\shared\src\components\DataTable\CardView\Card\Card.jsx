import React, { useCallback } from 'react';
import { Card as MuiCard, CardActionArea, CardContent, CardMedia, CardActions, Typography, Checkbox, Stack, Box } from '@mui/material';

export const Card = ({ 
	row,
    columns,
    onDelete,
    onRowSelectionModelChange,
    onClick,
	...props 
}) => {

	console.log(row)
	
	const handleCheckboxClick = useCallback(e => {
		e.stopPropagation();
		//if (onSelect) onSelect(e);
	}, []);
	
	return (
		<MuiCard 
			sx={{ 
				height: '100%', 
				display: 'flex', 
				flexDirection: 'column',
				position: 'relative',
				//border: selected ? '2px solid' : '1px solid',
				//borderColor: selected ? 'primary.main' : 'divider',
			}}
			data-cy={`data-table-card-${row.id}`}
		>
			<Box sx={{ position: 'absolute', top: 8, left: 8}}>
			</Box>
			
			<CardActionArea 
				onClick={onClick}
				sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
			>
				<CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
					{columns?.filter(a => a.field !== 'id')?.map((column, j) => (
						<Stack direction="column" spacing={0} useFlexGap flexWrap="wrap" key={`datagrid-card-field-${j}`} sx={{my: 1}}>
							{column.renderCard ? column.renderCard({row, column, value: row?.[column.field] || row?.[column.id]}) 
							:
								<>
									<Typography variant="subtitle3">{column.headerName}</Typography>
									{column?.renderCell 
										? <div>{column.renderCell({row, column, value: row?.[column.field] || row?.[column.id]})}</div> 
										:
										<Typography variant="body1" component="div" sx={{mb: j < columns.length - 1 ? 1 : 0}}>
											{column.valueFormatter ? column.valueFormatter(row?.[column.field] !== undefined ? row[column.field] : row) : row[column.field]}
										</Typography>
									}
								</>
							}
						</Stack>
					))}
				</CardContent>
			</CardActionArea>
			
			<CardActions sx={{ justifyContent: 'space-between', borderTop: '1px solid', borderColor: 'divider' }}>
				{row?.id &&
					<Typography variant="caption" color="text.secondary">
						ID: {row.id}
					</Typography>
				}
				<Checkbox 
					//checked={selected}
					//onClick={handleCheckboxClick}
					color="inherit"
					size="small"
				/>
			</CardActions>
		</MuiCard>
	);
};