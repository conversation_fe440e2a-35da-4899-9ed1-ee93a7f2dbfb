import React from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonWrapper, Caption } from '../../../../../common/pos';

export const Button = ({ items, onSelect, selected, disabled, slots, slotProps, ...props }) => {
    const { t } = useTranslation();

    return (
        <ButtonWrapper items={items} selected={selected} disabled={disabled} onClick={onSelect} slotProps={slotProps} slots={slots}>
            {items.map(item => (
                <React.Fragment key={item.id}>
                    <Caption variant="subtitle1" component="span" text={t(item.slug, item.slug)} bold />
                </React.Fragment>
            ))}
        </ButtonWrapper>
    );
}
