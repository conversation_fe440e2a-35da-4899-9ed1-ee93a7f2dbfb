import React, { Suspense, lazy } from 'react';
import { Routes as RrdRoutes, Route } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import { Box, Backdrop } from '@mui/material';

import { infiniteSpinner } from '@siteboss-frontend/shared/images';
import { Layout, Fzf } from '@siteboss-frontend/shared/components';
const Login = lazy(() => import('@siteboss-frontend/shared/components').then(module => ({ default: module.Login })));
const Logout = lazy(() => import('@siteboss-frontend/shared/components').then(module => ({ default: module.Logout })));

const Dashboard = lazy(() => import('./containers/Dashboard'));
const Users = lazy(() => import('./containers/Users'));
const Orders = lazy(() => import('./containers/Orders'));
const Events = lazy(() => import('./containers/Events'));
const Programs = lazy(() => import('./containers/Programs'));
const EventsWizard = lazy(() => import('./containers/Events/EventWizard'));
const Discounts = lazy(() => import('./containers/Discounts'));
const DiscountWizard = lazy(() => import('./containers/Discounts/DiscountWizard'));
const Services = lazy(() => import('./containers/Services'));
const ServiceWizard = lazy(() => import('./containers/Services/ServiceWizard'));
const Canvas = lazy(() => import('./containers/Canvas'));
const AiChat = lazy(() => import('./containers/AiChat'));

// Reports
const Reports = lazy(() => import('./containers/Reports/Reports').then(module => ({ default: module.default })));

// transition wrapper using framer-motion
const PageWrapper = ({ id, children }) => (
    <motion.div
        key={id}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
    >
        {children}
    </motion.div>
);

const routes = [
    { path: '/logout', component: Logout },
    { path: '/login', component: Layout, props: {basic: true}, children: [
        { path: null, component: Login},
        { path: 'forgot-password', component: () => <div/> },
        { path: 'reset-password', component: () => <div/> },
        { path: 'register', component: () => <div/> }
    ]},
    { path: '/', component: Layout, children: [
        { path: null, component: Dashboard },
        { path: 'dashboard', component: Dashboard },
        { path: 'orders/:id?', component: Orders },
        { path: 'users/:id?', component: Users },
        { path: 'events/wizard/t/:typeId?', component: EventsWizard },
        { path: 'events/wizard/:eventId?', component: EventsWizard },
        { path: 'events/:id?', component: Events },
        { path: 'events/wizard/:eventId?', component: EventsWizard },
        { path: 'programs/:id?', component: Programs },
        { path: 'programs/t/:typeId?', component: Programs },
        { path: 'discounts/:id?', component: Discounts },
        { path: 'discounts/wizard/:discountId?', component: DiscountWizard },
        { path: 'services/:id?', component: Services },
        { path: 'services/wizard/:serviceId?', component: ServiceWizard },
        { path: 'canvas', component: Canvas },
        { path: 'chat', component: AiChat },
        { path: 'reports', component: Reports },
    ]},
    { path: '*', component: Fzf, props: {basic: true}, children: [
        { path: null, component: Fzf },
    ]},
];

// recursirve render routes and children
const renderRoutes = (arr, index = 0) => arr.map((route, i) => (
    <Route
        key={`route-${index}-${i}`}
        path={route.path || undefined}
        index={!route.path || route.index}
        element={
            <PageWrapper id={`motion-${index}-${i}`}>
                <route.component {...(route?.props || {})} />
            </PageWrapper>
        }>
            {route.children && renderRoutes(route.children, i)}
    </Route>
));

export const Routes = () => (
    <Suspense fallback={
        <Backdrop open sx={{ zIndex: theme => theme.zIndex.drawer + 1 }}>
            <Box sx={{
                width: '100vh',
                height: '100vh',
                backgroundImage: `url(${infiniteSpinner})`,
                backgroundSize: '300px',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                filter: theme => theme.palette.mode === 'light' ? 'invert(1)' : undefined
            }} />
        </Backdrop>
    }>
        <AnimatePresence mode='wait'>
            <RrdRoutes>
                {renderRoutes(routes)}
            </RrdRoutes>
        </AnimatePresence>
    </Suspense>
);