import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { apiSlice, languageReducer, userReducer, colorModeReducer, companyReducer, permissionReducer, apiCacheReducer, currentShopItemReducer, cartReducer } from '@siteboss-frontend/shared/store';
import eventWizardReducer from './reducers/eventWizardSlice';
import discountWizardReducer from './reducers/discountWizardSlice';
import serviceWizardReducer from './reducers/serviceWizardSlice';

//exporting the rootReducer too so that it can be imported for things like Storybook
export const rootReducer = combineReducers({
    api: apiSlice.reducer,
    colorMode: colorModeReducer,
    language: languageReducer,
    user: userReducer,
    company: companyReducer,
    permission: permissionReducer,
    apiCache: apiCacheReducer,
    currentShopItem: currentShopItemReducer,
    cart: cartReducer,

    // local reducers
    eventWizard: eventWizardReducer,
    discountWizard: discountWizardReducer,
    serviceWizard: serviceWizardReducer,
});

export default configureStore({
reducer: rootReducer,
middleware: getDefaultMiddleware =>
    getDefaultMiddleware().concat(apiSlice.middleware),
});