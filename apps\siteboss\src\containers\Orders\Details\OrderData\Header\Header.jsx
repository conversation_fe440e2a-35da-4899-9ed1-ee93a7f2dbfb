import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography, Stack, Divider } from '@mui/material';
import { formatDate } from '@siteboss-frontend/shared/utils';
import { Logo } from '@siteboss-frontend/shared/components';

export const Header = (props) => {
    const { data } = props;
    const { t, isMobile } = useOutletContext();
    const language = useSelector(state => state.language);
    const company = useSelector(state => state.company);

    if (!data || data.length <= 0) return null;

    return (
        <Stack
            useFlexGap
            direction={{xs: "column", lg: "row"}}
            spacing={{xs: 2, md: 4}}
            divider={<Divider orientation="vertical" flexItem display={{xs: "none", lg: "block"}} sx={{order: 2}} />}
        >
            <Box sx={{width: "100%", order: isMobile ? 3 : 1}}>
                <Typography variant="h6">{t("order:order")} #{data.id}</Typography>
                <Typography variant="subtitle2" sx={{fontWeight: 500}}>{data.location_name}</Typography>
                <Typography variant="subtitle2">{t("order:date")}: {formatDate(new Date(data.created_at), language.code)}</Typography>
                <Typography variant="subtitle2">{t("order:customer")}: {data.user.first_name} {data.user.last_name}</Typography>
                <Typography variant="subtitle2">{t("general:email")}: {data.user.email}</Typography>
                <Typography variant="subtitle2">{t("general:phone")}: {data.user.mobile_phone}</Typography>
            </Box>
            <Box sx={{width: "100%", whiteSpace: "nowrap", order: isMobile ? 1 : 3}}>
                <Logo size="sm" noLink />
                <Typography variant="h6">{company.name ? company.name.toUpperCase() : ''}</Typography>
                <Typography variant="subtitle3" component="p">
                    {company.address && <>{company.address}<br /></>}
                    {company.address2 && <>{company.address2}<br /></>}
                    {company.city && <>{company.city}, </>}
                    {company.state && <>{company.state} </>}
                    {company.postalCode && <>{company.postalCode}</>}
                    {company.email && <><br/>{company.email}</>}
                    {company.phone && <><br/>{company.phone}</>}
                </Typography>
            </Box>
        </Stack>
    );
}