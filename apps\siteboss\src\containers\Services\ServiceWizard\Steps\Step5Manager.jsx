import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, Box, Chip, Button, Paper, TextField, Autocomplete, CircularProgress, Avatar } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import PersonIcon from '@mui/icons-material/Person';
import { useApi } from '@siteboss-frontend/shared';
import { setSelectedManagers, addManager, removeManager } from '../../../../store/reducers/serviceWizardSlice';
import { step5 } from './stepList';

const Step5Manager = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const selectedManagers = useSelector(state => state.serviceWizard.selectedManagers);
    const [searchValue, setSearchValue] = useState('');

    // API call to get users
    const { data: usersData, loading: usersLoading, error: usersError, call: fetchUsers } =
        useApi({
            fetchOnMount: false, // Only fetch when search value changes
            params: {
                endpoint: '/user',
                method: 'POST',
                data: {
                    search: searchValue,
                    max_records: 10
                }
            }
        });

    // Fetch users when search value changes
    useEffect(() => {
        if (searchValue) {
            fetchUsers();
        }
    }, [searchValue, fetchUsers]);

    // Handle adding a manager
    const handleAddManager = useCallback((manager) => {
        dispatch(addManager(manager));
        setSearchValue('');
    }, [dispatch]);

    // Handle removing a manager
    const handleRemoveManager = useCallback((managerId) => {
        dispatch(removeManager(managerId));
    }, [dispatch]);

    // Get user's full name
    const getUserFullName = (user) => {
        return `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email || 'Unknown User';
    };

    // Get user's initials for avatar
    const getUserInitials = (user) => {
        const firstName = user.first_name || '';
        const lastName = user.last_name || '';
        return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    };

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Service Managers
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                    Select the staff who will manage this service
                </Typography>

                <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        Search and add managers
                    </Typography>
                    <Autocomplete
                        options={usersData?.users || []}
                        getOptionLabel={(option) => getUserFullName(option)}
                        loading={usersLoading}
                        inputValue={searchValue}
                        onInputChange={(event, newValue) => setSearchValue(newValue)}
                        onChange={(event, newValue) => {
                            if (newValue) {
                                handleAddManager(newValue);
                            }
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label="Search users"
                                fullWidth
                                variant="outlined"
                                InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                        <>
                                            {usersLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                            {params.InputProps.endAdornment}
                                        </>
                                    ),
                                }}
                            />
                        )}
                        renderOption={(props, option) => (
                            <li {...props}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                                        {getUserInitials(option)}
                                    </Avatar>
                                    <Box>
                                        <Typography variant="body1">{getUserFullName(option)}</Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {option.email}
                                        </Typography>
                                    </Box>
                                </Box>
                            </li>
                        )}
                    />
                </Paper>

                <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        Selected Managers
                    </Typography>
                    {selectedManagers && selectedManagers.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                            {selectedManagers.map((manager) => (
                                <Chip
                                    key={manager.id}
                                    label={getUserFullName(manager)}
                                    onDelete={() => handleRemoveManager(manager.id)}
                                    color="primary"
                                    variant="outlined"
                                    avatar={
                                        <Avatar>
                                            {getUserInitials(manager)}
                                        </Avatar>
                                    }
                                />
                            ))}
                        </Box>
                    ) : (
                        <Typography variant="body2" color="text.secondary">
                            No managers selected. Please search and add managers above.
                        </Typography>
                    )}
                </Paper>
            </Grid>
        </Grid>
    );
};

export default Step5Manager;
