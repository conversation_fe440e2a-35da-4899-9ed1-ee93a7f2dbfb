import React from 'react';
import { Divider as <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts } from './layouts';
import { properties } from './properties';

export const Divider = ({
    id,
    flexItem,
    textAlign,
    variant,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        divider: {},        // MUI divider props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, isMobile, canRender, customCss } = prepareComponent({name: "divider", layoutId, layouts, slotProps, isBuilder, condition});
    slotProps = updatedSlotProps;

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap sx={isBuilder ? {minHeight: 40, justifyContent: "center"} : undefined} {...slotProps?.cmsStack}>
                <MuiDivider flexItem={Boolean(flexItem)} variant={variant || "fullWidth"} textAlign={textAlign || "center"} {...slotProps?.divider}>
                    {children}
                </MuiDivider>
            </Stack>
        </CmsContainer>
    );
};