import { useEffect, useMemo } from 'react';
import { Grid2, Stack, Divider } from '@mui/material';
import { LoadingBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import Transactions from './Transactions';
import Items from './Items';
import Header from './Header';
import Footer from './Footer';
import OrderHistory from './OrderHistory';

export const OrderData = ({id, ...props}) => {
    
    const apiParams = useMemo(() => ({
        params: {
            endpoint: `/order/order/${id}`,
            method: 'GET'
        }
    }), [id]);

    const { fetchData, data, loading, ErrorBar } = useApi(apiParams);

    useEffect(() => {
        if (id) {
            fetchData();
        }
    }, [id, fetchData]);

    if (!id) return null;

    return (
        <Grid2 container spacing={2}>
            <ErrorBar />
            {loading && <LoadingBar type="linear" sx={{height: "2px", position: "absolute", top: 0}} />}
            {data &&
                <Grid2 size={{xs: 12}}>
                    <Header data={data} />
                    <Items data={data} />
                    <OrderHistory data={data} />
                    <Stack
                        useFlexGap
                        direction={{xs: "column", lg: "row"}}
                        spacing={{xs: 2, md: 4}}
                    >
                        {data?.transactions?.length > 0 && 
                            <>
                                <Transactions data={data} />
                                <Divider 
                                    orientation="vertical" 
                                    sx={{ order: 1 }} 
                                    flexItem  
                                    display={{xs: "none", lg: "block"}} 
                                />
                            </>
                        }
                        <Footer data={data} />
                    </Stack>
                </Grid2>
            }
        </Grid2>
    );
}
