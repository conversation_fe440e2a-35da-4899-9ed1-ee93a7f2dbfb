import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import { DataTable } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import MerchantSelector from '../../../components/MerchantSelector';
import { tenantConfig } from '../../../components/KeycloakProvider/tenantConfig';

const apiParams = [
    {params: {endpoint: '/products', method: 'GET', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'DESC',
    }, config: {headers: { 'X-Tenant': tenantConfig()?.name || import.meta.env.VITE_TENANT }}}},
];

export const List = ({
    onExpand, 
    onDelete, 
    setSelected, 
    selected, 
    loading:parentLoading 
}) => {
    const { t, isMobile } = useOutletContext();

    const params = useParams();
    
    const [rows, setRows] = useState();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'DESC'});
    const [searchText, setSearchText] = useState('');
    const [merchantId, setMerchantId] = useState(null);

    const { fetchData, data, loading, ErrorBar, LoadingBar } = useApi(apiParams[0]);

    const columns = useMemo(() => [
        { field: 'sku', headerName: `${t('product:sku')}`, width: 90 },
        { field: 'product_type_name', headerName: t('product:productType'), minWidth: 100, valueGetter: (value, row) => {
            console.log(row)
        } },
        { field: 'product_brand_name', headerName: t('product:brand'), minWidth: 100, valueGetter: (value, row) => row?.metadata?.brand_code || "-" },
        { field: 'name', headerName: t('product:name'), minWidth: 150, flex: 1 },
    ], [t]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        rows?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [rows, setSelected]);

    const handleMerchantChange = useCallback(e => {
        const value = e.target.value;
        setMerchantId(value?.id || value);
    }, []);

    useEffect(() => {
        const _loadData = async () => {
            const result = await fetchData({
                page_no: Math.round(page) || 1,
                max_records: pageSize > 10 ? pageSize : 10,
                sort_col: order?.column || 'id',
                sort_direction: order.direction || 'DESC',
                filters:{
                    product_ids: params?.id ? [params.id] : undefined,
                    search_words: searchText,
                    merchant_id: merchantId ? merchantId : undefined,
                },
            });
            if (result?.data?.items) {
                const _rows = result.data.items.map(item => ({
                    id: item.id,
                    sku: item.id,
                    name: item.name,
                    product_type_name: item.product_type_name,
                    product_brand_name: item.product_brand_name,
                    metadata: item,
                }));
                setRows(_rows);
                setPageSize(result.data.page_record_count);
                setPage(result.data.this_page);
                setTotalRows(result.data.total_record_count);
                setTotalPages(Math.min(result.data.total_record_count / result.data.page_record_count));
            }
        };
        _loadData();
    }, [page, pageSize, order, fetchData, params.id, searchText, merchantId]);

    useEffect(() => {
        if (params.id && rows && (!selected || selected.length === 0 || !selected.find(r => +r.id === +params.id))) {
            setSelected(rows.filter(r => +r.id === +params.id));
        }
    }, [rows, params.id, setSelected, selected]);

    useEffect(() => {
        if (selected.length > 0 && params.id && onExpand) {
            onExpand();
        }
    }, [selected, params, onExpand]);

    return (
        <>
            <ErrorBar />
            {!data && <LoadingBar />}
            {data && rows &&
                <DataTable
                    checkboxSelection
                    hideFooterSelectedRowCount
                    rows={rows}
                    columns={columns}
                    onRowSelectionModelChange={model => handleRowSelection(model)}
                    rowSelectionModel={selected.map(s => s.id)}
                    onExpand={onExpand}
                    onDelete={onDelete}
                    order={order}
                    page={page}
                    pageSize={pageSize}
                    totalPages={totalPages}
                    totalRows={totalRows}
                    setPage={setPage}
                    setPageSize={setPageSize}
                    setOrder={setOrder}
                    setSearchText={setSearchText}
                    setSelected={setSelected}
                    loading={loading || parentLoading}
                    slots={{
                        filters: <MerchantSelector onChange={handleMerchantChange} />,
                    }}
                />
            }
        </>
    );
}