/** @type { import('@storybook/react-vite').Preview } */
import React, { useMemo } from 'react';
import {MemoryRouter, Routes, Route, Outlet} from 'react-router-dom';
import { Provider } from 'react-redux';
import { CssBaseline, useMediaQuery  } from '@mui/material';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';

import { theme } from '../apps/shared/src/theme.jsx';
import { ApiProvider } from '../apps/shared/src/api/ApiContext.jsx';
import '../apps/shared/src/languages/i18n.jsx';
import customViewports from './customViewports.json';
import { mockStore } from '../assets/Mocks/mockStore.js';

import StoryDescription from './components/StoryDescription.jsx';

const mockUser = {
    name: "A User", 
    token: 8675309,
    date: new Date()
}

const preview = {
    parameters: {
        actions: { argTypesRegex: "^on[A-Z]." },
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/,
            },
            expanded: true
        },
        //can add custom device sizes here
        viewport:{
            viewports:{
                ...customViewports
            }
        },
        storyComponents: {
            description: StoryDescription
        },
        a11y: {
            options: {
                runOnly: {
                type: 'tag',
                values: ['wcag2a', 'wcag2aa'],
                },
            },
            // Manual accessibility tests will be included in the Vitest tab
            manual: true,
        },
        
        vitest: {
            // Enable accessibility tests in the Vitest tab
            testTypes: ['interaction', 'accessibility'],
        },
    },
    globalTypes:{
        theme:{
            description: 'Global theme for components',
            defaultValue: 'light',
            toolbar:{
                title: 'Theme',
                icon: 'paintbrush',
                items: ['Light', 'Dark'],
                dynamicTitle: true
            }
        },
        user:{
            description: "Taking a user out of the local storage",
            defaultValue: "true",
            toolbar:{
                title: "User",
                icon: 'user',
                items: [ "true", "false"]
            }
        }
    },
    decorators:[
        (Story, context) => {
            
            const isMobile = useMediaQuery('(max-width:600px)');
            const { t } = useTranslation();
            let mode = context.globals.theme;
            let user = context.globals.user;
            const storybookTheme = useMemo(() => theme(mode), [mode]);
            
            //this means we want to have a user in local storage
            if(user === "true"){
                localStorage.setItem("user", JSON.stringify(mockUser))
            } else localStorage.removeItem("user")
            const mockedStore = mockStore(context.title, context?.parameters?.init);

            return(
                <div style={{margin: "20px", display: "flex", justifyContent:"center"}}>
                    <React.StrictMode>
                        <StyledEngineProvider injectFirst>
                            <Provider store={mockedStore} reactRouterOutlet>
                                <ApiProvider>
                                    <ThemeProvider theme={storybookTheme}>
                                        <CssBaseline />
                                            <MemoryRouter initialEntries={["/"]}>
                                                <Routes>
                                                    <Route path="/" element={<Outlet context={{t:t, isMobile: isMobile}}/> }>
                                                        <Route index element={<Story test="Rawr" storyDescription={StoryDescription} />} />
                                                    </Route>
                                                </Routes>
                                            </MemoryRouter>
                                    </ThemeProvider>
                                </ApiProvider>
                            </Provider>
                        </StyledEngineProvider>
                    </React.StrictMode>
                </div>
            )
        }
    ]
};

export default preview;
