import React from 'react';
import { Stack, Typography } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

export const Heading = ({
    id,
    title,
    titleVariant,
    subtitle,
    subtitleVariant,
    body,
    bodyVariant,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        title: {},          // MUI typography props
        subtitle: {},       // MUI typography props
        body: {},           // MUI typography props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, customCss, canRender, noContent } = prepareComponent({name: "heading", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    if (body && !Array.isArray(body)) body = [body]; // makes sure body is an array, in case the user wants to add multiple paragraphs

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap {...slotProps?.cmsStack}>
                {!title && !subtitle && !body?.length > 0 ? noContent :
                    <>
                    {title && 
                        <Typography variant={titleVariant || "h1"} {...slotProps?.title}>
                            {title}
                        </Typography>
                    }
                    {subtitle &&
                        <Typography variant={subtitleVariant || "subtitle2"} {...slotProps?.subtitle}>
                            {subtitle}
                        </Typography>
                    }
                    {body && body?.map((content, i) => (
                        <Typography 
                            key={`body-${id}-${i}`} 
                            variant={bodyVariant || "body1"} 
                            {...slotProps?.body} 
                            style={{whiteSpace: 'pre-line'}}
                        >
                            {content}
                        </Typography>
                    ))}
                    </>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};