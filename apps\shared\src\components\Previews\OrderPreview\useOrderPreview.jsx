import { useState, useEffect, useCallback, useMemo } from 'react';
import { Stack, Divider } from '@mui/material';

import useApi from '../../../api';
import { usePreviews } from '../usePreviews';
import { Header, Footer } from '../common';
import Transactions from './Transactions';
import Items from './Items';

import styles from './OrderPreview.module.scss';

const orderFormats = [
    {id: 'order', slug: 'order:formats.fullPage'},
    {id: 'ticket', slug: 'order:formats.ticket'},
    {id: 'kitchen', slug: 'order:formats.kitchenTicket'},
];

export const useOrderPreview = ({id, data: orderData, ...props}) => {
    const [data, setData] = useState(orderData || null);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);

    const apiParams = useMemo(()=> ({params: {endpoint: `/order/order/${id}`, method: "GET"}}), [id]);
    const { fetchData } = useApi(apiParams);

    // slots to assemble the screen preview and print preview
    const printSlots = useMemo(() => ({
        slots:{
            header: ({data, children}) => (
                <Header data={data}>
                    {children}
                </Header>
            ),
            body: ({data, children}) => (
                <Items data={data}>
                    {children}
                </Items>
            ),
            footer: ({data, children}) => (
                <Stack useFlexGap direction={{xs: "column", lg: "row"}}>
                    {data?.transactions?.length > 0 && 
                        <>
                            <Transactions data={data} />
                            <Divider 
                                flexItem  
                                orientation="vertical" 
                                sx={{ order: 1 }} 
                                display={{xs: "none", lg: "block"}} 
                            />
                        </>
                    }
                    <Footer data={data}>
                        {children}
                    </Footer>
                </Stack>
            ),
        },
        slotProps: {
            header : {data},
            body: {data},
            footer: {data},
        }
    }), [data]);

    // get the info to be printed
    const { 
        printRef, 
        handlePrint, 
        format, 
        setFormat,
        printCount, 
        handleChangePrintFormat, 
        printData,
    } = usePreviews({data, formats: props?.formats || orderFormats, styles, slots: printSlots.slots});


    const getData = useCallback(async () => {
        setLoading(true);
        try{
            const res = await fetchData();
            if (res?.errors) setErrors(res.errors);
            else if (res?.data) setData(res.data);
        } catch(e) {
            setErrors(e);
        } finally {
            setLoading(false);
        }
    }, [fetchData]);


    useEffect(() => {
        if (id && !data) getData();
    }, [id, data, getData]);

    return {
        loading,
        errors,
        setErrors,
        data,
        printRef,
        handlePrint,
        formats: orderFormats,
        format,
        setFormat,
        printCount,
        handleChangePrintFormat,
        printData,
        previewSlots: printSlots,        
    }
}