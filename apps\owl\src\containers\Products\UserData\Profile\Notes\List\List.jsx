import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { List as MuiList, ListItem, ListItemAvatar, ListItemText } from '@mui/material';
import { formatDateTime } from '@siteboss-frontend/shared/utils';
import { ToolbarUserAvatar as Avatar } from '@siteboss-frontend/shared/components';

export const List = ({ notes }) => {
    const { language } = useOutletContext();    
    const user = useSelector(state => state.user);

    return (
        <MuiList disablePadding sx={{ width: '100%' }}>
            {notes?.map(note => { 
                if (+user.profile.id !== +note.author_user_id) {
                    if (note.status===2){ //viewable by admin+
                        if (user.roles.filter(a => a.id > 0 && a.id < 5).length === 0) return null;
                    }
                    if (note.status===3){ //viewable by author only
                        if (user?.profile?.id !== note.author_user_id) return null;
                    }
                }

                return (
                    <ListItem key={`user-note-${note.id}`} disableGutters alignItems="flex-start">
                        <ListItemAvatar>
                            <Avatar hideStatus hideOptions size="md" userData={note.author_user}/>
                        </ListItemAvatar>
                        <ListItemText 
                            primary={note.note} 
                            primaryTypographyProps={{ variant: 'body1' }}
                            secondary={formatDateTime(new Date(note.created_at), language)} 
                            secondaryTypographyProps={{ variant: 'caption' }}
                        />
                    </ListItem>
                );
            })}
        </MuiList>
    );
}