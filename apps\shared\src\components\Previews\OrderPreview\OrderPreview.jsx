import React from 'react';
import { Grid2 } from '@mui/material';
import { LoadingBar, ErrorBar } from '../../Snacks';

import Actions from './Actions';
import { useOrderPreview } from './useOrderPreview';

export const OrderPreview = ({id, ...props}) => {
    const {
        loading,
        errors,
        setErrors,
        data,
        printRef,
        handleChangePrintFormat,
        printData, 
        previewSlots, 
        formats,
    } = useOrderPreview({id, ...props});

    if (!id) return null;

    return (
        <Grid2 container spacing={2}>
            <ErrorBar message={errors} open={Bo<PERSON>an(errors)} onClose={_ => setErrors(false)} />
            {loading && <LoadingBar type="linear" sx={{height: "2px", position: "absolute", top: 0}} />}            
            {data &&
                <>
                    {/* this is a hidden element that holds whats to be printed */}
                    {printData}

                    {/* what we're rendering on the screen */}
                    <Grid2 size={{xs: 12}}>
                        {previewSlots.slots.header && 
                            <previewSlots.slots.header {...previewSlots?.slotProps?.header}>
                                <Actions 
                                    id={id} 
                                    printRef={printRef} 
                                    onPrint={handleChangePrintFormat} 
                                    userEmails={data?.user?.email ? [data.user.email] : null}
                                    formats={props.formats || formats}
                                    showPayNow={props.showPayNow}
                                />
                            </previewSlots.slots.header>
                        }
                        {previewSlots.slots.body && <previewSlots.slots.body {...previewSlots?.slotProps?.body} />}
                        {previewSlots.slots.footer && <previewSlots.slots.footer {...previewSlots?.slotProps?.footer} />}
                    </Grid2>
                </>
            }
        </Grid2>
    );
}