import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    countries: [],
    states: [],
    cities: [],
    merchantBillingMethods: [],
    merchantBillingFrequencies: [],
    merchantBillingTerms: [],
    merchantSendToOptions: [],
    merchantGroupByOptions: [],
    contactTypes: [],
    companyTypes: [],
    fulfillmentTypes: [],
    uploadFormats: [],
    orderTypes: [],
    loaded: {
        countries: false,
        states: false,
        cities: false,
        merchantBillingMethods: false,
        merchantBillingFrequencies: false,
        merchantBillingTerms: false,
        merchantSendToOptions: false,
        merchantGroupByOptions: false,
        contactTypes: false,
        companyTypes: false,
        fulfillmentTypes: false,
        uploadFormats: false,
        orderTypes: false,
    },
};

const fixedDataSlice = createSlice({
    name: 'fixedData',
    initialState,
    reducers: {
        setInfo(state, action) {
            for (const key in action.payload) {
                state[key] = action.payload[key];
                state.loaded[key] = true;
            }
        },
        clearFixedData: () => initialState,
    },
});

export const {
    setInfo,
    clearFixedData,
} = fixedDataSlice.actions;

export default fixedDataSlice.reducer;
