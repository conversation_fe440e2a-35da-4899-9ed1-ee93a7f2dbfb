import store from '../../../store/store';
const company = store.getState().company;

export const properties = [
    {
        name: 'title',
        label: 'builder:component.footer.title',
        component: "TextField",
        value: company.name || "",
        size: "small",
        required: true,
        margin: "normal",
    },
    {
        name: 'copyRight',
        label: 'builder:component.footer.copyRight',
        component: "TextField",
        value: `© ${new Date().getFullYear()} ${company.name || ""}. All rights reserved.`,
        size: "small",
        margin: "normal",
    },
    {
        name: 'showMenu',
        label: 'builder:component.header.showMenu',
        component: "Switch",
        value: 1,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
];
