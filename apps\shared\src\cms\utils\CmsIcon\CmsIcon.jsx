import React from 'react';
import { Box, Paper, Skeleton, Stack } from '@mui/material';

/*
elements is an array of objects like this:
[
    { type: 'circle', size: 40, my: 'auto' },
    {
        type: 'group',
        direction: 'row',
        spacing: 2,
        children: [
            { type: 'text', width: 30 },
            { type: 'text', width: 40 }
        ]
    }
]
or
[
    {type: 'title'},
    {type: 'text'},
    {type: 'spacing'},
    {type: 'text', width: 30},
    {type: 'text', width: 20},
]
*/
export const CmsIcon = ({
    variant = "rounded",
    solid = false,
    animation = false,
    iconProps = {height: 48, width: 48, padding: 0, direction: 'column', spacing: 2},
    elements = [], 
}) => {
    const renderElement = (el, i) => {
        let props = {
            position: 'relative',
            width: iconProps.width,
            height: iconProps.spacing,
        };
        if (solid) {
            props = {
                ...props,
                spacing: iconProps.spacing,
                animation,
                variant,
            }
        }

        let Component = solid ? Skeleton : Box;

        const {type, size, children, ...elementProps} = el;
    
        let bgColor = solid ? 'text.secondary' : 'transparent', border = solid ? 'none' : '3px solid';
        switch (type) {
            case 'title':
                props.height = '4px';
                bgColor = 'text.secondary';
                border = 'none';
                break;
            case 'text':
                props.height = '2px';
                bgColor = 'text.secondary';
                border = 'none';
                break;
            case 'spacing':
                props = {height: `${props.spacing}px`, width: `${props.spacing}px`};
                Component = Box;
                bgColor = 'transparent';
                border = 'none';
                break;
            case 'circle':
                props.width = size;
                props.height = size;
                if (solid) props.variant = "circular";
                break;
            default:
                break;
        }
    
        if (elementProps.width) props.width = elementProps.width;
        if (elementProps.height) props.height = elementProps.height;
        if (size) {
            props.width = size;
            props.height = size;
        }
        props.sx = {borderRadius: type === 'circle' ? '50%' : '2px', bgcolor: bgColor, border, borderColor: 'text.secondary', ...elementProps};
    
        if (children) {
            return (
                <Stack 
                    key={i} 
                    direction={elementProps.direction || 'column'} 
                    flexGrow={1} 
                    useFlexGap 
                    spacing={`${elementProps.spacing || props.spacing}px`} 
                    sx={{justifyContent: 'center', ...elementProps.sx}}
                >
                    {children.map((child, j) => renderElement(child, j))}
                </Stack>
            );
        }
    
        return <Component key={i} {...props} />;
    };

    return (
        <Stack 
            spacing={`${iconProps.spacing}px`} 
            direction={iconProps.direction} 
            useFlexGap
            sx={{
                width:`${iconProps.width}px`, 
                height:`${iconProps.height}px`, 
                overflow: 'hidden', 
                justifyContent: 'center', 
                pointerEvents: 'none',
                position: 'relative',
                ...iconProps.sx
            }}
        >
            {elements.map((el, i) => {
                return renderElement(el, i);
            })}
        </Stack>
    );
};