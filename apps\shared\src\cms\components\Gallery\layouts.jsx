import { ViewCarouselOutlined as CarouselIcon, ViewModuleOutlined as <PERSON>Icon, ViewQuiltOutlined as MasonryIcon  } from '@mui/icons-material';
/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/

export const widgetIcon = CarouselIcon;
export const layouts = [
    {
        id: 1,
        name: 'Carousel',
        icon: <CarouselIcon fontSize="large" sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
    {
        id: 2,
        name: 'List',
        icon: <ListIcon fontSize="large" sx={{color: theme => theme.palette.text.secondary}} />,
        slotProps: {
        }
    },
    {
        id: 3,
        name: 'Masonry',
        icon: <MasonryIcon fontSize="large" sx={{color: theme => theme.palette.text.secondary}} />,
        slotProps: {
        }
    }
];