import { useEffect, useRef, useContext } from 'react';
import { usePageRouter } from '../utils/usePageRouter';
import { PosContext, usePos } from '.';

export const useCart = ({ shopId, isBuilder = false }) => {
    const { cart: contextCart } = useContext(PosContext) || {};
    const { router } = usePageRouter({pageRouterId: shopId});

    // change this to use the api directly
    const { loadLatestOrder } = usePos({isBuilder: true, shopId, registerId: router?.registerId?.value || null});  // flag it as builder because we only need to query

    const cartInfo = useRef(null);
    
    useEffect(() => {
        if (!cartInfo.current && !isBuilder){
            if (contextCart) {
                // tries to get the cart from the context first
                cartInfo.current = contextCart;
            } else {
                // tries to get the latest order from the pos hook if there is no context
                loadLatestOrder().then(res => {
                    if (res){
                        cartInfo.current = {...res, cart: res?.items || []};
                    }
                });
            }
        }
        return () => {
            cartInfo.current = null;
        }
    }, [contextCart, loadLatestOrder, isBuilder]);

    return {
        cart: cartInfo.current,
    }
}