import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Box, Tab, Chip } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';

import List from './List';

const orderStatuses = [
    { id: 0, slug: 'all', color: 'default' },
    { id: 1, slug: 'outstanding', color: 'error'},
    { id: 2, slug: 'paid', color: 'success' },
]

export const Orders = ({ userData, ...props }) => {
    const { t, isMobile } = useOutletContext();

    const [selectedTab, setSelectedTab] = useState('0');
    const [statusCount, setStatusCount] = useState(orderStatuses.map(status => ({ id: status.id, count: null })));

    const handleChangeTab = (e, tab) => setSelectedTab(`${tab}`);
    const updateStatusCount = (statusId, count) => setStatusCount(prev => prev.map(a => +a.id === +statusId ? { ...a, count } : a));

    return (
        <TabContext value={selectedTab}> 
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: isMobile ? 'column' : 'row'}}>
                <TabList 
                    orientation={isMobile ? 'horizontal' : 'vertical'}
                    variant="scrollable"
                    aria-label="orders and transactions"
                    sx={{ 
                        position: 'sticky',
                        borderRight: isMobile ? undefined : 1, 
                        borderBottom: isMobile ? 1 : undefined,
                        borderColor: 'divider',
                        width: isMobile ? '100%' : '170px',
                        px: 0,
                        '.MuiTab-root': {
                            alignItems: isMobile ? undefined : 'flex-end',
                            textAlign: isMobile ? undefined : 'right',
                        }
                    }}
                    onChange={handleChangeTab}
                >
                    {orderStatuses.map(status => {
                        const count = statusCount.find(a => a.id === status.id);
                        return (
                            <Tab key={`order-status-tab-${status.id}`} value={`${status.id}`} label={
                                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                    {t(`status:${status.slug}`)} 
                                    <Chip label={count.count !==null ? count.count : "..."} size="small" color={status.color} />
                                </div>
                            }/>
                        );
                    })}
                </TabList>

                {orderStatuses.map(status => (
                    <TabPanel 
                        key={`order-status-panel-${status.id}`} 
                        value={`${selectedTab}`} 
                        index={`${status.id}`} 
                        sx={{ // lil trick to prevent re-rendering the tab content
                            display: +selectedTab === +status.id ? 'block' : 'none', 
                        }}
                    >
                        <List 
                            userId={userData.id}
                            statusId={status.id || null}
                            onUpdateCount={updateStatusCount}
                            displayList={selectedTab === `${status.id}`}
                        />
                    </TabPanel>
                ))}
            </Box>
        </TabContext>
    );
}