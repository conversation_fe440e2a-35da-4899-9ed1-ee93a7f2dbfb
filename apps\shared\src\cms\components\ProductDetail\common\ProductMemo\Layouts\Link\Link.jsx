import React from 'react';
import { useTranslation } from 'react-i18next';
import { LinkWrapper, Caption } from '../../../../../common/pos';

export const Link = ({ items, disabled, selected, onSelect, slots, slotProps, ...props }) => {
    const { t } = useTranslation();

    return (
        <LinkWrapper items={items} selected={selected} disabled={disabled} onClick={onSelect} slotProps={slotProps} slots={slots}>
            {items.map(item => (
                <React.Fragment key={item.id}>
                    <Caption variant="body1" component="div" text={t(item.slug, item.slug)} />
                </React.Fragment>
            ))}
        </LinkWrapper>
    );
}