import store from "../../../store/store";
import { toCamelCase } from '@siteboss-frontend/shared/utils';

const getLocationData = ({countryField = 'country', stateField = 'state_province_code', cityField = 'city'}) => {
    const state = store.getState()?.fixedData;
    if (!state) return {};
    return {
        [countryField]: state?.countries,
        [stateField]: state?.states,
        [cityField]: state?.cities,
    };
};

const getBillingData = () => {
    const state = store.getState()?.fixedData;
    if (!state) return {};
    return {
        billingMethods: state?.merchantBillingMethods,
        billingFrequencies: state?.merchantBillingFrequencies,
        billingTerms: state?.merchantBillingTerms,
        billingGroupBy: state?.merchantGroupByOptions,
        uploadFormats: state?.uploadFormats,
        companyTypes: state?.companyTypes,
    };
};

// Organize fields by tabs
export const fieldsByTab = {
    info: [
        {name: 'name', type: 'text', label: 'merchant:name', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 8}},
        {name: 'client_code', type: 'text', label: 'merchant:code', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 4}, inputProps: { maxLength: 5 }},
        /*{name: 'contact', type: 'text', label: 'merchant:contact', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 4}},*/
        {name: 'company_type', label: 'merchant:type', required: true, value: 0, component: "Select", 
            options: getBillingData().companyTypes?.map(a => ({
                id: a.id, slug: `merchant:types.${toCamelCase(a.name)}`
            })), margin: "normal", rowSize: {xs: 12, md: 6, lg: 4}},
        {name: 'email', type: 'email', label: 'merchant:email', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 4}},
        {name: 'phone', type: 'tel', label: 'merchant:phone', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 4}},

        {name: 'address_line1', type: 'text', label: 'merchant:addressLine1', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'address_line2', type: 'text', label: 'merchant:addressLine2', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'location', label: null, required: false, value: {}, component: "HierarchySelector", margin: "normal", rowSize: {xs: 12, lg: 9},
            levels: [
                { id: 'country', label: 'merchant:country', parent: null, parentField: 'country_id' },
                { id: 'state_province_code', label: 'merchant:state', parent: 'country', parentField: 'country_id', parentIdField: 'country_id' },
                { id: 'city', label: 'merchant:city', parent: 'state_province_code', parentField: 'state_id', parentIdField: 'state_id' }
            ],
            isAsync: false,
            data: getLocationData({}),
            /*getOptionLabel: {
                country: option => option ? (option?.name || option) : '',
                state_province_code: option => option ? (option?.name || option) : '',
                city: option => option ? (option?.name || option) : '',
            },
            renderOption: {
                country: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
                state_province_code: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
                city: (props, option) => <li {...props} key={JSON.stringify(option)}>{option.name}</li>,
            },*/
            slotProps: {
                city: { freeSolo: true }
            },

            /*isAsync: true,
            fetchParams: {
                country: {endpoint: '/countries', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}, 
                state_province_code: {endpoint: '/states', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}, 
                city: {endpoint: '/cities', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}
            },
            getSearchParams: (searchText) => ({ search_words: searchText }),*/
        },
        {name: 'postal_code', type: 'text', label: 'merchant:zipcode', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6, lg: 3}},
        {name: 'client_notes', type: 'text', label: 'merchant:notes', required: false, value: '', component: "TextField", margin: "normal", multiline: true, rows: 3, rowSize: {xs: 12}},
    ],
    setup: [
        {name: 'allow_shipper_manual_upload', label: 'merchant:allowShipperManualUpload', required: false, value: false, component: "Switch", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'qa_weight_enabled', label: 'merchant:enableWeightBasedQA', required: false, value: false, component: "Switch", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'rename_on_order_cancel', label: 'merchant:renameOrderOnCancel', required: false, value: false, component: "Switch", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'reject_unknown_sku', label: 'merchant:rejectUnknownSKUs', required: false, value: false, component: "Switch", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'upload_format', label: 'merchant:orderFileFormat', required: false, value: '', component: "Select", 
            options: getBillingData().uploadFormats?.map(a => ({
                id: a.name, name: a.name, upload_format_id: a.id
            })),margin: "normal", rowSize: {xs: 12, md: 6}
        },
    ],
    billing: [
        {name: 'billing_method', label: 'merchant:billingMethod', required: false, value: '', component: "Select", 
            options: getBillingData().billingMethods?.map(a => ({
                id: a.id, slug: `merchant:billingMethods.${toCamelCase(a.name)}`
            })), margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'billing_frequency', label: 'merchant:billingFrequency', required: false, value: '', component: "Select", 
            options: getBillingData().billingFrequencies?.map(a => ({
                id: a.id, slug: `merchant:billingFrequencies.${toCamelCase(a.name)}`
            })), margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'billing_default_terms', label: 'merchant:billingTerms.title', required: false, value: '', component: "Select", 
            options: getBillingData().billingTerms?.map(a => ({
                id: a.id, slug: `merchant:billingTerms.${toCamelCase(a.name.replaceAll(/\\s+/g, '').toLowerCase())}`
            })), margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'send_to', label: 'merchant:sendTo', required: false, value: '', component: "Select", options: [
            {id: 'business', slug: 'merchant:sendToOptions.business'},
            {id: 'billing', slug: 'merchant:sendToOptions.billing'},
            {id: 'other', slug: 'merchant:sendToOptions.other'},
        ], margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'billing_group_by', label: 'merchant:groupBy', required: false, value: '', component: "Select", options: [
            {id: 'manifest_location', slug: 'merchant:groupByOptions.manifestLocation'},
            {id: 'brand_name', slug: 'merchant:groupByOptions.brandName'},
            {id: 'fulfillment_center', slug: 'merchant:groupByOptions.fulfillmentCenter'},
            {id: 'order_type', slug: 'merchant:groupByOptions.orderType'},
        ], margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'credit_limit', type: 'number', label: 'merchant:creditLimit', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}, inputProps: { min: 0, step: 0.01 }},
    ],
    brands: [
        {name: 'brands', value: [], component: "BrandsList"}
    ],
    shipping: [
        // Placeholder for future shipping-related fields
    ],
    packaging: [
        // Placeholder for future packaging-related fields
    ],
    email: [
        {name: 'smtp_server', type: 'text', label: 'merchant:smtpServer', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_port', type: 'number', label: 'merchant:smtpPort', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_username', type: 'text', label: 'merchant:smtpUsername', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_password', type: 'password', label: 'merchant:smtpPassword', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'from_email_address', type: 'email', label: 'merchant:fromEmailAddress', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'from_name', type: 'text', label: 'merchant:fromName', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'cc_recipient', type: 'email', label: 'merchant:ccRecipient', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'bcc_recipient', type: 'email', label: 'merchant:bccRecipient', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'email_templates', value: [], component: "EmailTemplatesList"}
    ],
    contacts: [
        {name: 'contacts', value: [], component: "ContactsList"}
    ],
    licenses: [
        {name: 'licenses', value: [], component: "LicensesList"}
    ],
};