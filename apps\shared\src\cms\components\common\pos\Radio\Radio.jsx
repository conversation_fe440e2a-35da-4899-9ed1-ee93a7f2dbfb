import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, Divider, FormControlLabel, RadioGroup, Radio as MuiRadio, FormControl, FormHelperText, IconButton, useMediaQuery } from '@mui/material';
import { ChevronRightOutlined as MoreIcon } from '@mui/icons-material';

import Modal from '../../../../../components/Modal';

/*
Radio buttons to be used throughout the POS. It can handle replacing modal content, or loading extra content in a collapsible section via slots and slotProps.
*/
export const Radio = ({ item, checked, disabled, slotProps, children, ...props }) => (
    <FormControlLabel 
        value={item.id} 
        checked={checked}
        control={<MuiRadio color="secondary" {...slotProps} />}
        slotProps={{typography: {component: 'div', sx: {width: '100%'}}}}
        disabled={disabled}
        label={children}
    />
);

export const RadioWrapper = ({ items, selected, disabled, onChange, slotProps, slots, changeView, children, ...props }) => {
    if (Array.isArray(selected)) selected = selected[0];
    if (selected && !selected?.id) selected = {id: selected};
    
    const [openModalId, setOpenModalId] = useState(null);
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const handleExpand = useCallback(item => e => {
        // replaces the modal content with whatever component is passed in the extraInfo slot
        if (slots?.extraInfo && !slotProps?.fullPage && slotProps?.extraInfo?.show && slotProps?.changeView && item) {
            slotProps.changeView(slots?.extraInfo({item}));
        }
    }, [slotProps, slots]);
    
    const handleChange = useCallback(e => {
        e.preventDefault();
        const id = e.currentTarget.value;
        if (id) {
            const item = items.find(a => +a.id === +id);
            if (onChange) onChange(item, 1);
            if (slotProps?.fullPage) setOpenModalId(item.id);
            else handleExpand(item)(e);
        }
    }, [onChange, items, handleExpand, slotProps?.fullPage]);

    return (
        <FormControl sx={{p: 2, width: '100%'}}>
            <RadioGroup value={selected?.id || 0} onChange={handleChange}>
                {items.map((item, i) => {
                    let checked = Boolean(+selected?.id === +item.id), _disabled = disabled, helperText;
                    if (slots?.validate && !disabled) {
                        let valid = slots.validate(item);
                        if (typeof valid === 'string') {
                            helperText = valid;
                            valid = false;
                        }
                        valid = Boolean(valid);
                        checked = valid ? checked : false;
                        _disabled = !valid;
                    }                    
                    return (
                        <React.Fragment key={item.id}>
                            <Radio item={item} disabled={_disabled} slotProps={slotProps?.radio} checked={checked}>
                                <Stack spacing={2} flexDirection="row" useFlexGap flexWrap="wrap" justifyContent="space-between" alignItems="center" sx={{ml: 1}} {...slotProps?.stack}>
                                    {children?.[i]}
                                    {!_disabled && slots?.extraInfo && slotProps?.extraInfo?.show && /*!slotProps?.fullPage &&*/
                                        <>
                                            {checked ?
                                                <IconButton size="small" onClick={handleExpand(item)}>
                                                    <MoreIcon fontSize='small'/>
                                                </IconButton>
                                            : <MoreIcon fontSize='small'/> }
                                        </>
                                    }
                                    {helperText && <FormHelperText>{helperText}</FormHelperText>}
                                </Stack>
                            </Radio>
                            {slots?.extraInfo && slotProps?.fullPage && slotProps?.extraInfo?.show &&
                                <Modal 
                                    open={openModalId === item.id} 
                                    onClose={() => setOpenModalId(null)}
                                    maxWidth={"sm"}
                                    fullScreen={isMobile}
                                >
                                    {slots?.extraInfo({item, onClose: () => setOpenModalId(null)})}
                                </Modal>
                            }
                            <Divider />
                        </React.Fragment>
                    );
                })}
            </RadioGroup>
        </FormControl>
    );
};