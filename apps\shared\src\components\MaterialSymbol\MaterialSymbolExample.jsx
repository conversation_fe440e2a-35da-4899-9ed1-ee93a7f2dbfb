import React from 'react';
import { Stack, Typography, Paper, Divider, Box } from '@mui/material';
import { MaterialSymbol } from './index';

// Note: The MaterialSymbolsLoader should be included in your app's head section
// or in your main layout component, not in individual components.
// It's included here just for demonstration purposes.

export const MaterialSymbolExample = () => {
  return (
    <Box>
      <Paper sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
        <Typography variant="h4" gutterBottom>
          Material Symbols Examples
        </Typography>

        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" gutterBottom>
          Basic Icons
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="home" />
          <MaterialSymbol icon="settings" />
          <MaterialSymbol icon="favorite" />
          <MaterialSymbol icon="delete" />
          <MaterialSymbol icon="search" />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Different Variants
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="star" variant="outlined" />
          <MaterialSymbol icon="star" variant="rounded" />
          <MaterialSymbol icon="star" variant="sharp" />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Fill Variations
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="favorite" fill={0} />
          <MaterialSymbol icon="favorite" fill={1} />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Weight Variations
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="bolt" weight={100} />
          <MaterialSymbol icon="bolt" weight={200} />
          <MaterialSymbol icon="bolt" weight={300} />
          <MaterialSymbol icon="bolt" weight={400} />
          <MaterialSymbol icon="bolt" weight={500} />
          <MaterialSymbol icon="bolt" weight={600} />
          <MaterialSymbol icon="bolt" weight={700} />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Grade Variations
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="star" grade={-25} />
          <MaterialSymbol icon="star" grade={0} />
          <MaterialSymbol icon="star" grade={100} />
          <MaterialSymbol icon="star" grade={200} />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Size Variations
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 4 }}>
          <MaterialSymbol icon="cloud" size="xs" />
          <MaterialSymbol icon="cloud" size="sm" />
          <MaterialSymbol icon="cloud" size="md" />
          <MaterialSymbol icon="cloud" size="lg" />
          <MaterialSymbol icon="cloud" size="xl" />
          <MaterialSymbol icon="cloud" size="48px" />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Color Variations
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol icon="warning" color="primary.main" />
          <MaterialSymbol icon="warning" color="secondary.main" />
          <MaterialSymbol icon="warning" color="error.main" />
          <MaterialSymbol icon="warning" color="warning.main" />
          <MaterialSymbol icon="warning" color="info.main" />
          <MaterialSymbol icon="warning" color="success.main" />
        </Stack>

        <Typography variant="h6" gutterBottom>
          Combined Variations
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
          <MaterialSymbol
            icon="favorite"
            variant="rounded"
            fill={1}
            weight={700}
            grade={200}
            size="xl"
            color="error.main"
          />
          <MaterialSymbol
            icon="check_circle"
            variant="rounded"
            fill={1}
            weight={500}
            grade={0}
            size="xl"
            color="success.main"
          />
          <MaterialSymbol
            icon="info"
            variant="rounded"
            fill={1}
            weight={500}
            grade={0}
            size="xl"
            color="info.main"
          />
        </Stack>
      </Paper>
    </Box>
  );
};

export default MaterialSymbolExample;
