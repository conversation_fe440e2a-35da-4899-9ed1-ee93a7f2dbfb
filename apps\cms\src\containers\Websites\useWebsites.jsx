import { useState, useEffect, useCallback } from 'react';
//import { useDispatch } from 'react-redux';
import { useOutletContext, useParams, useNavigate } from 'react-router-dom';
import { useApi } from '@siteboss-frontend/shared';
import { ErrorBar, usePath } from '@siteboss-frontend/shared/components';

//import { setInfo } from '../../store/reducers/websiteSlice';

const apiParams = [
    {enableCache: true, params: {endpoint: '/cms/theme'}},
    {enableCache: false, params: {endpoint: "/cms/site/delete"}},
    {enableCache: false, params: {endpoint: "/cms/site/edit"}},
];

export const useWebsites = () => {
    const { t } = useOutletContext();
    const params = useParams();
    const path = usePath();

    //const dispatch = useDispatch();
    const navigate = useNavigate();

    const [open, setOpen] = useState(false);
    const [isNew, setIsNew] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedSites, setSelectedSites] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [loading, setLoading] = useState(false);
    const [errorBar, setErrorBar] = useState(null);
    const [websiteId, setWebsiteId] = useState(params?.id || null);

    // api calls
    const { fetchData: fetchThemes, data: themes, loading: themesLoading, ErrorBar: ThemesErrorBar } = useApi(apiParams[0]);
    const { fetchData: processDelete } = useApi(apiParams[1]);
    const { fetchData: updateWebsite } = useApi(apiParams[2]);
    
    /*const { permissions } = usePermission({moduleIds: [74, 204]});*/

    const toggleDrawer = useCallback(open => e => {
        if (e?.type === 'keydown' && (e?.key === 'Tab' || e?.key === 'Shift')) return;
        if (!open && websiteId) {
            setSelectedSites([]);
            setWebsiteId(null);
        }
        setIsNew(false);
        setIsEdit(false);
        setOpen(open);

        if (!open) navigate(`${path.base}`, { replace: false });

    }, [isNew, isEdit, websiteId, params, path.base, navigate]);

    const handleDelete = useCallback((e, website) => {
        if (website) setDeleteParams([website]);
        else setDeleteParams(selectedSites);
        if (website || selectedSites.length > 0) setShowConfirm(true);
    }, [selectedSites]);

    const addErrorBar = ({message, httpCode, retry}) => {
        setErrorBar(_ => (()=> (
            <ErrorBar 
                open
                message={message} 
                httpCode={httpCode || undefined} 
                onClose={() => setErrorBar(null)}
                retry={retry || undefined} 
            />
        )));
    }

    // update the website
    const handleUpdate = useCallback(async (data, callback) => {
        setLoading(true);
        try {
            const result = await updateWebsite(data);
            if (result){
                if (result?.errors) {
                    addErrorBar({message: result.errors, httpCode: result?.httpCode || undefined, retry: () => handleUpdate});
                } else if (result?.data) {
                    if (callback) await callback();
                }
            } else {
                addErrorBar({message: t("error:default"), retry: () => handleUpdate});
            }
        } catch (error) {
            addErrorBar({message: t("error:default"), retry: () => handleUpdate});
        } finally {
            setLoading(false);
        }
    }, [updateWebsite, addErrorBar]);

    const handleEdit = useCallback((e, website) => {
        if (website) {
            setIsEdit(true);
            setIsNew(false);
            setOpen(true);
            setSelectedSites([website]);
            //navigate(`/websites/${website.id}`);
        }
    }, []);

    const handleNewClick = () => {
        setIsNew(true);
        setIsEdit(false);
        setOpen(true);
    }    

    /*
    // when the event type is selected, navigate to the wizard
    const handleEventTypeSelect = useCallback(id => {
        dispatch(setInfo({id}));
        navigate(`/wizard/${id}`); // we send the id to the wizard so it doesn't lose the selected event type when the user refreshes
    }, [dispatch, navigate]);
    */

    // when the user confirms a delete
    const handleConfirmDelete = useCallback(async () => {
        if (deleteParams.length > 0) {
            const _ids = deleteParams.map(s => s.id);
            setLoading(true);
            try {
                const result = await processDelete({ id: _ids });
                if (result?.errors){
                    addErrorBar({message: result.errors, httpCode: result?.httpCode || undefined, retry: () => handleConfirmDelete});
                } else if (result?.data) {
                    setDeleteParams([]);
                    setShowConfirm(false);
                }
            } catch (error) {
                addErrorBar({message: t("error:default"), retry: () => handleConfirmDelete});
            } finally {
                setLoading(false);
            }
        }
    }, [deleteParams, processDelete, addErrorBar]);

    // when the user declines to delete
    const handleDeclineDelete = useCallback(() => {
        setShowConfirm(false);
        setDeleteParams([]);
    }, [setDeleteParams, setShowConfirm]);

    // set the delete params when the selected events change
    useEffect(() => {
        if (selectedSites.length <= 0) {
            setOpen(false);
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedSites);
            setShowConfirm(false);
        }
    }, [selectedSites]);

    // open the drawer if the website id is set
    useEffect(() => {
        if (websiteId) {
            setIsNew(false);
            setIsEdit(false);
            setOpen(true);
        }
    }, [websiteId]);
    
    // get the themes on load
    useEffect(() => {
        fetchThemes();
    }, [fetchThemes]); 

    return {
        open,
        isNew,
        isEdit,
        showConfirm,
        selectedSites,
        deleteParams,
        websiteId,
        params,
        themes,
        loading: loading || themesLoading,
        errorBars: [ThemesErrorBar, errorBar],
        setOpen,
        setIsNew,
        setIsEdit,
        setShowConfirm,
        setSelectedSites,
        setDeleteParams,
        toggleDrawer,
        handleDelete,
        handleUpdate,
        handleNewClick,
        processDelete,
        handleEdit,
        handleConfirmDelete,
        handleDeclineDelete,
    };
}