import { useCallback, useContext, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { PosContext } from '../../hooks';

export const useProductDetail = ({
    onAddToCart,
    layouts,
    layoutId,
    ...props
}) => {
    const { id: productDetailId, slug: productDetailSlug, extra: cartItemId } = useParams();
    const { reduxStore, handleAddToCart:posAddToCart } = useContext(PosContext) || {};

    const Layout = useMemo(() => layouts.find(layout => layout.id === layoutId)?.component, [layoutId]);

    const handleAddToCart = useCallback((product, reduxState) => {
        const addFunction = onAddToCart || posAddToCart;
        if (addFunction) addFunction(product, reduxState);
    }, [onAddToCart, posAddToCart]);

    return {
        productDetailId,
        productDetailSlug,
        cartItemId,
        reduxStore,
        handleAddToCart,
        Layout,
    }
};