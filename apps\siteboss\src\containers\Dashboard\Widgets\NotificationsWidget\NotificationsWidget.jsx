import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { 
    Box, 
    Typography, 
    List, 
    ListItem, 
    ListItemText, 
    ListItemAvatar, 
    Avatar, 
    IconButton,
    Badge,
    Divider,
    Chip
} from '@mui/material';
import { 
    NotificationsOutlined as NotificationIcon,
    DeleteOutlineOutlined as DeleteIcon,
    CheckCircleOutlineOutlined as ReadIcon,
    MailOutlineOutlined as UnreadIcon
} from '@mui/icons-material';

// Mock data - in a real app, this would come from an API
const mockNotifications = [
    { 
        id: 1, 
        title: 'New user registration', 
        message: '<PERSON> has registered as a new user.', 
        timestamp: '2023-10-16T09:30:00Z', 
        read: false, 
        type: 'user' 
    },
    { 
        id: 2, 
        title: 'Payment received', 
        message: 'Payment of $250.00 received from Invoice #1234.', 
        timestamp: '2023-10-16T08:15:00Z', 
        read: false, 
        type: 'payment' 
    },
    { 
        id: 3, 
        title: 'System update', 
        message: 'System will be updated tonight at 2:00 AM. Expect brief downtime.', 
        timestamp: '2023-10-15T14:45:00Z', 
        read: true, 
        type: 'system' 
    },
    { 
        id: 4, 
        title: 'New comment', 
        message: '<PERSON> commented on your post.', 
        timestamp: '2023-10-15T11:20:00Z', 
        read: true, 
        type: 'comment' 
    },
    { 
        id: 5, 
        title: 'Task assigned', 
        message: 'You have been assigned a new task: "Update product descriptions".', 
        timestamp: '2023-10-14T16:10:00Z', 
        read: true, 
        type: 'task' 
    }
];

export const NotificationsWidget = ({ settings = {}, isEditing, ...props }) => {
    const { t } = useOutletContext();
    const [notifications, setNotifications] = useState(mockNotifications);
    const [showUnread, setShowUnread] = useState(settings.showUnread !== undefined ? settings.showUnread : true);
    
    // Filter notifications based on settings
    const filteredNotifications = showUnread 
        ? notifications 
        : notifications.filter(notification => !notification.read);
    
    // Count unread notifications
    const unreadCount = notifications.filter(notification => !notification.read).length;
    
    // Format date for display
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 60) {
            return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
        } else if (diffHours < 24) {
            return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
        } else {
            return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
        }
    };
    
    // Mark notification as read
    const handleMarkAsRead = (id) => {
        setNotifications(prev => 
            prev.map(notification => 
                notification.id === id 
                    ? { ...notification, read: true } 
                    : notification
            )
        );
    };
    
    // Delete notification
    const handleDelete = (id) => {
        setNotifications(prev => 
            prev.filter(notification => notification.id !== id)
        );
    };
    
    // Get avatar based on notification type
    const getAvatar = (type) => {
        switch (type) {
            case 'user':
                return <Avatar sx={{ bgcolor: 'primary.main' }}>U</Avatar>;
            case 'payment':
                return <Avatar sx={{ bgcolor: 'success.main' }}>P</Avatar>;
            case 'system':
                return <Avatar sx={{ bgcolor: 'warning.main' }}>S</Avatar>;
            case 'comment':
                return <Avatar sx={{ bgcolor: 'info.main' }}>C</Avatar>;
            case 'task':
                return <Avatar sx={{ bgcolor: 'secondary.main' }}>T</Avatar>;
            default:
                return <Avatar><NotificationIcon /></Avatar>;
        }
    };
    
    return (
        <Box data-cy="notifications-widget" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                    {t('widget:notifications.title')}
                    {unreadCount > 0 && (
                        <Badge 
                            badgeContent={unreadCount} 
                            color="error" 
                            sx={{ ml: 1 }}
                        />
                    )}
                </Typography>
            </Box>
            
            <Box sx={{ flex: 1, overflow: 'auto' }}>
                {filteredNotifications.length > 0 ? (
                    <List sx={{ width: '100%' }}>
                        {filteredNotifications.map((notification) => (
                            <React.Fragment key={notification.id}>
                                <ListItem 
                                    alignItems="flex-start"
                                    secondaryAction={
                                        <Box>
                                            {!notification.read && (
                                                <IconButton 
                                                    edge="end" 
                                                    aria-label="mark as read"
                                                    onClick={() => handleMarkAsRead(notification.id)}
                                                    size="small"
                                                >
                                                    <ReadIcon fontSize="small" />
                                                </IconButton>
                                            )}
                                            <IconButton 
                                                edge="end" 
                                                aria-label="delete"
                                                onClick={() => handleDelete(notification.id)}
                                                size="small"
                                            >
                                                <DeleteIcon fontSize="small" />
                                            </IconButton>
                                        </Box>
                                    }
                                    sx={{
                                        bgcolor: notification.read ? 'transparent' : 'action.hover',
                                        borderRadius: 1
                                    }}
                                >
                                    <ListItemAvatar>
                                        {getAvatar(notification.type)}
                                    </ListItemAvatar>
                                    <ListItemText
                                        primary={
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <Typography 
                                                    variant="subtitle2" 
                                                    sx={{ 
                                                        mr: 1,
                                                        fontWeight: notification.read ? 'normal' : 'bold'
                                                    }}
                                                >
                                                    {notification.title}
                                                </Typography>
                                                {!notification.read && (
                                                    <Chip 
                                                        label="New" 
                                                        size="small" 
                                                        color="primary"
                                                        sx={{ height: 20 }}
                                                    />
                                                )}
                                            </Box>
                                        }
                                        secondary={
                                            <>
                                                <Typography 
                                                    component="span" 
                                                    variant="body2" 
                                                    color="text.primary"
                                                    sx={{ display: 'block' }}
                                                >
                                                    {notification.message}
                                                </Typography>
                                                <Typography 
                                                    component="span" 
                                                    variant="caption" 
                                                    color="text.secondary"
                                                >
                                                    {formatDate(notification.timestamp)}
                                                </Typography>
                                            </>
                                        }
                                    />
                                </ListItem>
                                <Divider variant="inset" component="li" />
                            </React.Fragment>
                        ))}
                    </List>
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography variant="body2" color="text.secondary">
                            {t('widget:notifications.noNotifications')}
                        </Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default NotificationsWidget;
