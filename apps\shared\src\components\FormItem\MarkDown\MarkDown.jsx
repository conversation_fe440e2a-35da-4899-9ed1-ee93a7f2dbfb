import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Paper, Tab, FormHelperText, useMediaQuery, useTheme } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { EditNoteOutlined as EditorIcon, ManageSearchOutlined as PreviewIcon, HelpOutlineOutlined as HelpIcon } from '@mui/icons-material';

import { Editor, MonacoEditor } from './Editor';
import Preview from './Preview';
import Help from './Help';

export const MarkDown = ({ 
    name = 'markdown', 
    language = "markdown", 
    showHelp = true,
    showPreview = true,
    height = '200px', 
    value, 
    errors,
    onChange, 
    onBlur,
    slotProps,
    ...props 
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const theme = useTheme();

    const [tabValue, setTabValue] = useState('editor');
    const [editorValue, setEditorValue] = useState(value);

    const sxs = useMemo(() => ({
        tab: {p: 1, minWidth: 56},
        tabPanel: { width: '100%', height: height || '100%', flexGrow: 1, p: 0},
    }), [height]);


    const handleEditorChange = useCallback((e, newValue) => {
        setEditorValue(newValue);
        if (onChange) onChange({target: {name, value: newValue, dataType: 'editor'}});
    }, [onChange, onBlur, name]);

    const handleEditorBlur = useCallback(editor => {
        const newValue = editor?.getValue?.() || editor;
        if (onBlur) onBlur({target: {name, value: newValue, dataType: 'editor'}});
    }, [onBlur, name]);

    const handleTabChange = (e, newValue) => {
        setTabValue(newValue);
    };

    const handleSampleClick = useCallback((e, newValue) => {
        handleEditorChange(e, newValue);
        setTabValue('editor');
    }, [handleEditorChange]);

    const EditorComponent = language === "markdown" ? Editor : MonacoEditor;

    return (
        <TabContext value={tabValue}>
            <Box sx={{
                display: 'flex', 
                flexDirection: (!isMobile && slotProps?.tabs?.orientation === "vertical") ? "row-reverse" : "column", 
                alignItems: 'stretch', 
                justifyContent: 'flex-start', 
                mt: 1,
                mb: 0.5,
                ...slotProps?.container?.sx
            }}>
                <Box sx={{display: 'flex', justifyContent: 'flex-end'}}>
                    {(showPreview || showHelp) && 
                        <TabList 
                            onChange={handleTabChange} 
                            indicatorColor="secondary" 
                            orientation={isMobile ? "horizontal" : (slotProps?.tabs?.orientation || "horizontal")}
                            sx={{
                                ...((!isMobile && slotProps?.tabs?.orientation === "vertical") ? {borderLeft: `1px solid ${theme.palette.divider}`, borderRight: 0} : {}), 
                                ...slotProps?.tabs?.sx
                            }}
                            slotProps={{
                                indicator: {
                                    sx: {left: 0, right: 'auto'}
                                }
                            }}
                        >
                            <Tab icon={<EditorIcon/>} aria-label={t("general:editor")} value="editor" sx={sxs.tab} />
                            {showPreview && <Tab icon={<PreviewIcon/>} aria-label={t("general:preview")} value="preview" sx={sxs.tab} />}
                            {showHelp && <Tab icon={<HelpIcon/>} aria-label={t("general:help")} value="help" sx={sxs.tab} />}
                        </TabList>
                    }
                </Box>
                <Box sx={{ display:'flex', width: '100%'}}>
                    <TabPanel value="editor" sx={sxs.tabPanel}>
                        <EditorComponent 
                            language={language} 
                            value={editorValue} 
                            errors={errors} 
                            onChange={handleEditorChange} 
                            onBlur={handleEditorBlur}
                        />
                    </TabPanel>
                    {showPreview &&
                        <TabPanel value="preview" sx={sxs.tabPanel}>
                            <Paper variant="outlined" sx={{ background: 'transparent', p: 1, width: '100%', height: '100%', overflow: 'auto' }}>
                                <Preview value={editorValue} />
                            </Paper>
                        </TabPanel>
                    }
                    {showHelp &&
                        <TabPanel value="help" sx={sxs.tabPanel}>
                            <Help onSampleClick={handleSampleClick} />
                        </TabPanel>
                    }
                </Box>
            </Box>
            {errors && <FormHelperText error required sx={{mx: 2}}>{errors}</FormHelperText>}
        </TabContext>
    );
}