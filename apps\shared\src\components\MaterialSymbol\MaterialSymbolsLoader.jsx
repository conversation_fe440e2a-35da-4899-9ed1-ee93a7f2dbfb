import React from 'react';

/**
 * Component to load the Material Symbols font from Google Fonts
 * This component should be included once in your application, typically near the root
 *
 * @param {Object} props - Component props
 * @param {Array} props.variants - The variants to load (outlined, rounded, sharp)
 */
export const MaterialSymbolsLoader = ({
	variants = ['outlined', 'rounded', 'sharp']
}) => {
	const fontUrl = `https://fonts.googleapis.com/css2?family=${variants.map(variant => `Material+Symbols+${variant.charAt(0).toUpperCase() + variant.slice(1)}`).join('&family=')}&display=block`;

	return (
		<link
			rel="stylesheet"
			href={fontUrl}
		/>
	);
};
