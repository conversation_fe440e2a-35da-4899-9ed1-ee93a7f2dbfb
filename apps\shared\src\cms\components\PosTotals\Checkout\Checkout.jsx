import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Button } from '@mui/material';

import { createCurrencyFormatter } from '../../../../utils/currency';

export const Checkout = ({amount = 0, onCheckout, disabled, slotProps, ...props}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    
    return (
        <Button size="xl" variant="contained" color="primary" fullWidth disabled={disabled} onClick={onCheckout} sx={{mt: 1}} {...slotProps}>
            {t('pos:checkout')}
            {amount > 0 && ` - ${currencyFormatter.format(amount, currency)}`}
        </Button>
    );
}