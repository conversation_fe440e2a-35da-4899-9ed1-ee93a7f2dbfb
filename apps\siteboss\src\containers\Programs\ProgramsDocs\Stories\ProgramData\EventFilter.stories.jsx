import { EventFilter } from '../../../ProgramData/EventFilter/EventFilter';
import eventsFixture from '../../../../../../../../assets/Mocks/fixtures/events.json';
import { fn } from 'storybook/test'

let programData = {children: eventsFixture.impact_qa_event_list.data.events}

export default{
    title: "Siteboss/Features/Programs/Program Data/Event Filter",
    component: EventFilter,
    tags: ['autodocs'],
    argTypes:{
        programData:{

        },
        loading:{

        },
        onChange:{
            description: "",
            control: {
                type: 'function'
            },
            action: fn()
        }
    }
}

export const Playground={
    args:{
        programData: programData,
        onChange:fn()
    }
}