import { Stack } from '@mui/material';
import { Campaign as CampaignIcon, CropLandscape as CropIcon } from '@mui/icons-material';

import CmsIcon from '../../utils/CmsIcon';

const Icons = {
    Centered: () => (
        <Stack direction="column" alignItems="center" justifyContent="center" spacing={0} height={48}>
            <CropIcon sx={{color: theme => theme.palette.text.secondary}}/>
            {CmsIcon({
                iconProps: {height: 'auto', width: 48, direction: 'column', spacing: 2, sx: {alignItems: 'center'}},
                elements: [
                    {type: 'text', width: '80%'},
                    {type: 'text', width: '40%'},
                ],
            })}
        </Stack>
    ),
    Left: () => (
        <Stack direction="row" alignItems="center" justifyContent="center" spacing={0} height={48}>
            <CropIcon sx={{color: theme => theme.palette.text.secondary}}/>
            {CmsIcon({
                iconProps: {height: 'auto', width: 24, direction: 'row', spacing: 3},
                elements: [
                    {type: 'group', direction: 'column', spacing: 2, sx: {alignItems: 'left'}, children: [
                        {type: 'text', width: '100%'},
                        {type: 'text', width: '80%'},
                        {type: 'text', width: '40%'},
                    ]},
                ],
            })}
        </Stack>
    ),
    Right: () => (
        <Stack direction="row" alignItems="center" justifyContent="center" spacing={0} height={48}>
            {CmsIcon({
                iconProps: {height: 'auto', width: 24, direction: 'row', spacing: 3},
                elements: [
                    {type: 'group', direction: 'column', spacing: 2, sx: {alignItems: 'left'}, children: [
                        {type: 'text', width: '100%'},
                        {type: 'text', width: '80%'},
                        {type: 'text', width: '40%'},
                    ]},
                ],
            })}
            <CropIcon sx={{color: theme => theme.palette.text.secondary}}/>
        </Stack>
    ),    
};

export const widgetIcon = Icons.Centered;

export const layouts = [
    {
        id: 1,
        name: 'Centered',
        icon: <Icons.Centered />,
        slotProps: {
            wrapper: {},
            container: {
                sx: {
                    textAlign: 'center',
                }
            },
            image: {},
            title: {},
            body: {},
            callToAction: {},
        }
    },
    {
        id: 2,
        name: 'Left',
        icon: <Icons.Left />,
        slotProps: {
            wrapper: {},
            container: {
                sx: {
                    textAlign: 'left',
                }
            },
            image: {},
            title: {},
            body: {},
            callToAction: {},
        }
    },
    {
        id: 3,
        name: 'Right',
        icon: <Icons.Right />,
        slotProps: {
            wrapper: {},
            container: {
                sx: {
                    textAlign: 'left',
                }
            },
            image: {},
            title: {},
            body: {},
            callToAction: {},
        }
    },
];