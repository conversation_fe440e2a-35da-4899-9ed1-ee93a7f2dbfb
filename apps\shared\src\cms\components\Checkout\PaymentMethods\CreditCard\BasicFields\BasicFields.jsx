import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid2 } from '@mui/material';
import FormItem from '../../../../../../components/FormItem';

export const BasicFields = ({fields, amount, onFieldChange, slotProps, ...props}) => {
    const { t } = useTranslation();
    const [values, setValues] = useState({amount});
    const [errors, setErrors] = useState({});

    // group by row id
    const rows = useMemo(() => (
        [...(fields || [])].reduce((acc, field) => { 
            const group = acc.find(g => g[0]?.rowId === field.rowId);
            if (group) group.push(field);
            else acc.push([field]);
            return acc;
        }, [])
    ), [fields]);

    const checkErrors = useCallback(_values => {
        let _errors = {};
        const field = fields.find(f => f.name === _values.name);
        if (!field) _errors = {[_values.name]: t('error:invalid')};
        else if (field.required){
            if (_values.name === "amount" && (+_values?.value > +amount || _values?.value < 0)) {
                _errors = {[field.name]: t('error:invalid')};
            }
            if (!_values?.value) {
                _errors = {[field.name]: t('error:required')};
            }
        }
        return _errors;
    }, [t, amount]);

    const handleChange = useCallback(e => {
        let _errors = checkErrors(e.target);
        setErrors(prev => ({...prev, [e.target.name]: null, ..._errors}));
        setValues(prev => ({...prev, [e.target.name]: e.target.value}));
    }, [checkErrors]);

    useEffect(() => {
        if (onFieldChange) onFieldChange(values);
    }, [values, onFieldChange]);

    return (
        <>
            {rows.map((_, i) => (
                <Grid2 key={i} container spacing={2}>
                    {fields?.filter(a => a.rowId === i+1).map(field => (
                        <Grid2 key={field.name} size={{xs: 12, lg: "grow"}}>
                            <FormItem 
                                key={field.name}
                                {...field}
                                options={field?.options?.length > 0 ? field.options : null}
                                value={values?.[field.name]}
                                onChange={handleChange}
                                errors={errors?.[field.name]}
                                //disabled={loading}
                            />
                        </Grid2>
                    ))}
                </Grid2>
            ))}
        </>
    );
}