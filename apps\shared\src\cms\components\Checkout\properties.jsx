export const properties = [
    {
        name: 'showTips',
        label: 'builder:component.checkout.showTips',
        component: "Switch",
        value: true,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'showTotals',
        label: 'builder:component.checkout.showTotals',
        component: "Switch",
        value: true,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'items',
        label: 'builder:component.checkout.totalItems',
        component: 'Autocomplete',
        multiple: true,
        filterSelectedOptions: true,
        options: [
            {id: 'subtotal', slug: 'pos:subtotal'},
            {id: 'shipping', slug: 'pos:shipping'},
            {id: 'tax', slug: 'pos:tax'},
            {id: 'tip', slug: 'pos:tip'},
            {id: 'payments', slug: 'pos:payments'},
            {id: 'priceAdjustments', slug: 'pos:priceAdjustments'},
        ],
        value: [
            {id: 'subtotal', slug: 'pos:subtotal'},
            {id: 'shipping', slug: 'pos:shipping'},
            {id: 'tax', slug: 'pos:tax'},
            {id: 'tip', slug: 'pos:tip'},
            {id: 'payments', slug: 'pos:payments'},
            {id: 'priceAdjustments', slug: 'pos:priceAdjustments'},
        ],
        size: "small",
        margin: "normal",
        condition: { field: 'showTotals', value: true},
    },
    {
        name: 'paymentMethods',
        label: 'builder:component.checkout.paymentMethods',
        component: "Autocomplete",
        multiple: true,
        filterSelectedOptions: true,
        options: [
            {id: 'scanCard', slug: 'pos:paymentMethods.scanCard'},
            {id: 'card', slug: 'pos:paymentMethods.creditCard'},
            {id: 'cash', slug: 'pos:paymentMethods.cash'},
            {id: 'check', slug: 'pos:paymentMethods.check'},
            {id: 'giftCard', slug: 'pos:paymentMethods.giftCard'},
            {id: 'managerDiscount', slug: 'pos:paymentMethods.managerDiscount'},
        ],
        value: [
            {id: 'scanCard', slug: 'pos:paymentMethods.scanCard'},
            {id: 'card', slug: 'pos:paymentMethods.creditCard'},
            {id: 'cash', slug: 'pos:paymentMethods.cash'},
            {id: 'check', slug: 'pos:paymentMethods.check'},
            {id: 'giftCard', slug: 'pos:paymentMethods.giftCard'},
            {id: 'managerDiscount', slug: 'pos:paymentMethods.managerDiscount'},
        ],
        size: "small",
        margin: "normal",
    },
    {
        name: 'redirectToSuccess',
        label: 'builder:component.checkout.redirectToSuccess',
        component: "Switch",
        value: true,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },

];