{"name": "siteboss-frontend", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "repository": {"type": "git", "url": "git+https://github.com/SiteBossInc/siteboss-frontend.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/SiteBossInc/siteboss-frontend/issues"}, "homepage": "https://github.com/SiteBossInc/siteboss-frontend#readme", "workspaces": ["apps/shared", "apps/siteboss", "apps/cms", "apps/pos", "apps/website", "apps/owl"], "dependencies": {"@dnd-kit/accessibility": "^3.1.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^6.4.11", "@mui/lab": "^6.0.0-beta.10", "@mui/material": "^6.0.0", "@mui/x-data-grid": "^7.23.6", "@mui/x-date-pickers": "^7.18.0", "@mui/x-date-pickers-pro": "^7.3.2", "@mui/x-tree-view": "^7.18.0", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/line": "^0.88.0", "@nivo/pie": "^0.88.0", "@reduxjs/toolkit": "^2.5.1", "@siteboss-frontend/shared": "*", "axios": "^1.7.7", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.2.6", "i18next": "^23.16.8", "immer": "^10.1.1", "keycloak-js": "^26.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-redux": "^9.1.0", "react-router-dom": "^6.28.0", "redux-undo": "^1.1.0", "sass": "^1.72.0", "vite": "^5.4.11", "webfontloader": "^1.6.28"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.12", "@storybook/addon-designs": "^10.0.1", "@storybook/addon-docs": "^9.0.12", "@storybook/addon-links": "^9.0.12", "@storybook/addon-onboarding": "^9.0.12", "@storybook/addon-vitest": "^9.0.12", "@storybook/builder-vite": "^9.0.12", "@storybook/react-vite": "^9.0.12", "cypress": "^13.13.1", "prop-types": "^15.8.1", "storybook": "^9.0.12", "vitest": "^3.2.4", "@vitest/browser": "^3.2.4", "playwright": "^1.53.1", "@vitest/coverage-v8": "^3.2.4"}}