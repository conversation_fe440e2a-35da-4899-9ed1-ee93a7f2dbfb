import { useEffect, useState, useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useApi } from '../../../api/useApi';

export const useBlogPost = ({
    postId,
    postType,
    websiteId,
}) => {
    const { id: blogPostId, slug: blogPostSlug } = useParams();
    const [post, setPost] = useState(null);

    const apiParams = useMemo(() =>[
        {enableCache: true, params: {endpoint: `/cms/site/page/${(+postType === 2 ? postId : blogPostId) || ""}`, method: 'POST', data: {page_type_id: 3, include_children: true, website_id: websiteId, slug: blogPostSlug || null}}},
    ], [websiteId, blogPostId, blogPostSlug, postId, postType] );
    
    const { fetchData, loading, ErrorBar } = useApi(apiParams[0]);

    const loadPost = useCallback(async () => {
        let _post = null;
        if (blogPostId || blogPostSlug || postId) {
            try {
                const res = await fetchData();
                if (res?.data?.[0]?.content) {
                    _post = {...res.data[0]?.content};
                }
            } catch (error) {
                console.error('Failed to load blog post:', error);
            }
        }
        setPost(_post);
    }, [blogPostId, blogPostSlug, postId, fetchData]);

    useEffect(() => {
        loadPost();
    }, [loadPost]);

    return { post, loading, ErrorBar, loadPost };
};
