import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Container, Grid2, Typography } from '@mui/material';
import { LocalizationProvider, DateCalendar, PickersDay } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { startOfMonth, endOfMonth } from 'date-fns';
import { enUS, es } from 'date-fns/locale';
import { formatDate } from '../../../utils';
import Modal from '../../Modal';

import Schedule from '../Schedule';

export const Year = ({
    currentDate, // the current date
    events, // the events to display (optional)
    metaEvents, // the meta events to display (optional)
    colors, // the colors to use for the events (optional)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props // additional props
}) => {
    const language = useSelector(state => state.language);

    const [currentYear, setCurrentYear] = useState(null);
    const [selectedDate, setSelectedDate] = useState(null);
    const [open, setOpen] = useState(false);
    
    const handleClose = () => setOpen(false);    

    const handleDateChange = (date) => {
        setSelectedDate(date);
        setOpen(true);
    }
    
    useEffect(() => {
        if (currentDate) setCurrentYear(currentDate.getFullYear());
    }, [currentDate]);

    if (!currentYear) return null;

    return (
        <Container disableGutters sx={{mt: 5}}>
            <Grid2 container spacing={2}>
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                    {Array.from({ length: 12 }).map((_, i) => (
                        <DateCalendar  
                            key={`calendar-year-${i}`}
                            minDate={startOfMonth(new Date(currentYear, i, 1))}
                            maxDate={endOfMonth(new Date(currentYear, i, 1))}
                            autoFocus={false}
                            value={new Date(currentYear, i, 1)}
                            sx={{height: 'fit-content'}}
                            disabled={loading || disabled}
                            slots={{
                                calendarHeader: ()=> (
                                    <Typography variant="subtitle2">
                                        {formatDate(new Date(currentYear, i, 1), language.code, 'MMMM')}
                                    </Typography>
                                ),
                                day: props => <PickersDay {...props} selected={props.today}  />
                            }}
                            onChange={handleDateChange}
                        />
                    ))}
                </LocalizationProvider>
            </Grid2>
            {selectedDate &&
                <Modal 
                    open={open} 
                    onClose={handleClose} 
                    maxWidth="sm" 
                    title={formatDate(selectedDate, language.code, 'MMMM d yyyy')}
                >
                        <Schedule 
                            currentDate={selectedDate} 
                            events={events} 
                            metaEvents={metaEvents} 
                            colors={colors} 
                            loading={loading} 
                            disabled={disabled}
                            onEventClick={onEventClick}
                        />
                </Modal>
            }
        </Container>
    );    
}