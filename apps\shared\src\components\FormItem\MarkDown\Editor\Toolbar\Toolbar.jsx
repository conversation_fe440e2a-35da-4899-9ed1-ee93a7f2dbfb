import React, { useRef, useState, useCallback, useMemo } from 'react';
import clsx from 'clsx';
import { Paper, useTheme } from '@mui/material';
import { MDXEditor,
	headingsPlugin,
	listsPlugin,
	quotePlugin,
	thematicBreakPlugin,
	toolbarPlugin,
	UndoRedo,
	BoldItalicUnderlineToggles,
	linkPlugin,
	linkDialogPlugin,
	imagePlugin,
	tablePlugin,
	frontmatterPlugin,
	codeBlockPlugin,
	sandpackPlugin,
	codeMirrorPlugin,
	directivesPlugin,
	AdmonitionDirectiveDescriptor,
	diffSourcePlugin,
	markdownShortcutPlugin,
	BlockTypeSelect,
	CodeToggle,
	CreateLink,
	DiffSourceToggleWrapper,
	InsertCodeBlock,
	ConditionalContents,
	ChangeCodeMirrorLanguage,
	ShowSandpackInfo,
	Separator,
	ListsToggle,
	ChangeAdmonitionType,
	InsertImage,
	InsertTable,
	InsertThematicBreak,
	InsertSandpack,
	InsertAdmonition,
	InsertFrontmatter,
	DirectiveNode,
} from '@mdxeditor/editor';


export const Toolbar = () => {

	const whenInAdmonition = (editorInFocus) => {
		const node = editorInFocus?.rootNode;
		if (!node || node.getType() !== "directive") {
			return false;
		}
	  
		return ["note", "tip", "danger", "info", "caution"].includes(
			node.getMdastNode().name
		);
	}
		  
	return (
		<ConditionalContents options={[
			{
				when: (editor) => editor?.editorType === "codeblock",
				contents: () => <ChangeCodeMirrorLanguage />,
			},
			{
				when: (editor) => editor?.editorType === "sandpack",
				contents: () => <ShowSandpackInfo />,
			},
			{
				fallback: () => (
					<>
						<UndoRedo />
						<Separator />
						<BoldItalicUnderlineToggles />
						<CodeToggle />
						<Separator />
						<ListsToggle />
						<Separator />
		
						<ConditionalContents
							options={[
							{
								when: whenInAdmonition,
								contents: () => <ChangeAdmonitionType />,
							},
							{ fallback: () => <BlockTypeSelect /> },
							]}
						/>

						{/*
						<Separator />  
						<CreateLink />
						<InsertImage />
						<Separator />
						*/}

						<InsertTable />
						<InsertThematicBreak />

						{/*
						<Separator />
						<InsertCodeBlock />
						<InsertSandpack />
						*/}


						{/*
						<ConditionalContents
							options={[
							{
								when: (editorInFocus) => !whenInAdmonition(editorInFocus),
								contents: () => (
								<>
									<Separator />
									<InsertAdmonition />
								</>
								),
							},
							]}
						/>
						*/}
						
						{/*
							<Separator />
							<InsertFrontmatter />
						*/}
					</>
				),
			},
		]} />
	);
};