import React, { Suspense, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { produce } from 'immer';
import { Container, Box, CircularProgress, useTheme, useMediaQuery } from '@mui/material';

import { usePortalIframe } from '../../../components';
import { deepObjectMerge, evaluateCondition } from '../../../utils';
import { useCustomCss } from '../CustomCssInjector';
import CmsWrapper from '../CmsWrapper';

const checkLocalCondition = (condition, localState) => {
    let _data = null, _condition = null;
    if (condition?.data && condition.condition){
        _condition = condition.condition;
        const _obj = Object.fromEntries(
            Object.entries(localState || {}).map(([key, value]) => [`${key.includes('.') ? "" : "local."}${key}`, value])
        );
        _data = {...condition.data, ..._obj};
    }
    return evaluateCondition(_condition, _data);
};

export const EmptyWidget = props => {
    if (!props?.isBuilder) return null;
    return (
        <Box
            sx={{
                position:'relative',
                width: '100%',
                height: props?.height || 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: theme => theme.palette.divider,
                backgroundImage: theme => `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill='%23${theme.palette.divider}' fill-opacity='${theme.palette.action[theme.palette.mode === 'dark' ? 'disabledOpacity' : 'focusOpacity']}'%3E%3Cpath fill-rule='evenodd' d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2l-2 2h-2z'/%3E%3C/g%3E%3C/svg%3E")`,
                ...props?.sx
            }}
        >        
            {props?.children || <CircularProgress color="inherit"/>}
        </Box>
    );
};

// All cms components are wrapped in a MUI container so that the user can define paddings, max-width, etc.
export const CmsContainer = React.forwardRef(({
    isBuilder = false,
    maxWidth = false,
    fixed = false,
    disableGutters = true,
    children,
    properties,
    layouts,
    layoutId,
    sectionId,
    handleSectionSelection = () => {}, 
    handleSectionDeletion = () => {},
    selectedSectionId = null, 
    handleLayoutSelection = () => {}, 
    setOpenPropertiesDrawer = () => {},
    ...props
}, ref) => {
    const Wrap = useMemo(() => isBuilder ? CmsWrapper : React.Fragment, [isBuilder]);
    
    const WrapContainer = useMemo(() => React.forwardRef(({children, ...props}, ref) => (
        (isBuilder || sectionId !== 'header') 
            ? <Container {...props} ref={ref}>{children}</Container> 
            : <React.Fragment>{children}</React.Fragment>
    )), [isBuilder, sectionId]);

    const wrapProps = useMemo(() => isBuilder ? {
        layouts,
        layoutId,
        sectionId,
        properties,
        handleSectionSelection,
        handleSectionDeletion,
        selectedSectionId,
        handleLayoutSelection,
        setOpenPropertiesDrawer,
    } : {}, [isBuilder, layouts, layoutId, sectionId, properties, handleSectionSelection, handleSectionDeletion, selectedSectionId, handleLayoutSelection, setOpenPropertiesDrawer]);

    return (
        <Wrap {...wrapProps}>
            <WrapContainer 
                maxWidth={maxWidth} 
                disableGutters={disableGutters} 
                fixed={fixed} 
                {...props} 
                ref={ref}
                id={sectionId} // Add this to ensure the ID is on the Container
                sx={{minHeight: 40, ...props?.sx, maxHeight: props?.sx?.maxHeight}}
            >
                {children}
            </WrapContainer>
        </Wrap>
    );
});

export const innerProps = slotProps => {
    const theme = useTheme();
    let maxWidth = slotProps?.maxWidth;
    let mWidth = slotProps?.maxWidth;
    if (mWidth !== undefined && mWidth !== false) {
        if (typeof mWidth === 'string') maxWidth = theme.breakpoints.values?.[mWidth];
        else maxWidth = mWidth;
    } else maxWidth = undefined;

    return {
        ...slotProps,
        maxWidth,
        mx: 'auto',
    };
}

export const prepareComponent = ({name, layoutId, layouts, slotProps, isBuilder, condition, localState, widgetIcon: WidgetIcon}) => {
    const { t } = useTranslation();
    const customCss = useCustomCss({component: name});

    const _slotProps = produce(slotProps, draft => {
        if (layoutId) {
            const layout = layouts.find(l => l.id === layoutId);
            if (layout) {
                Object.assign(draft, deepObjectMerge(layout.slotProps, draft));
            }
        }
        if (draft?.cmsStack?.sx) {
            draft.cmsStack = produce(draft.cmsStack, cmsDraft => {
                cmsDraft.sx = innerProps(cmsDraft.sx);
            });
        }
    });

    const contentWindow = isBuilder ? usePortalIframe() : null;
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined});
    const canRender = isBuilder ? true : (checkLocalCondition(condition, localState) || false);
    const noContent = (WidgetIcon && isBuilder) ? (
        <EmptyWidget isBuilder={isBuilder}>
            <Suspense fallback={"..."}>
                <WidgetIcon fontSize='large' sx={{color: theme => theme.palette.text.secondary}}/>
            </Suspense>
        </EmptyWidget>
    ) : <React.Fragment />;

    return {slotProps: _slotProps, isMobile, contentWindow, t, canRender, noContent, customCss: customCss ? <style>{customCss}</style> : null};
};

/*
export const prepareComponent = ({name, layoutId, layouts, slotProps, isBuilder, condition, localState, widgetIcon: WidgetIcon}) => {
    const { t } = useTranslation();
    const customCss = useCustomCss({component: name});

    let _slotProps = JSON.parse(JSON.stringify(slotProps)); // deep copy the slotProps object

    if (layoutId) {
        const layout = layouts.find(l => l.id === layoutId);
        if (layout) {
            _slotProps = deepObjectMerge(layout.slotProps, _slotProps); // overrides the layout slotProps with the component slotProps
        }
    }
    if (_slotProps?.cmsStack?.sx) _slotProps.cmsStack.sx = innerProps(_slotProps.cmsStack.sx);

    // if rendering inside the builder iframe, use the iframe's matchMedia instead of the main window's
    const contentWindow = isBuilder ? usePortalIframe() : null;
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined});
    const canRender = isBuilder ? true : (checkLocalCondition(condition, localState) || false);
    const noContent = (WidgetIcon && isBuilder) ? (
        <EmptyWidget isBuilder={isBuilder}>
            <Suspense fallback={"..."}>
                <WidgetIcon fontSize='large' sx={{color: theme => theme.palette.text.secondary}}/>
            </Suspense>
        </EmptyWidget>
    ) : <React.Fragment />

    return {slotProps: _slotProps, isMobile, contentWindow, t, canRender, noContent, customCss: customCss ? <style>{customCss}</style> : null};
}
*/