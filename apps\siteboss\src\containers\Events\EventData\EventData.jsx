import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { Container } from '@mui/material';
import { Tab<PERSON>anel, TabContext } from '@mui/lab';
import { InfoOutlined as InfoIcon, PersonAddAltOutlined as AttendeeIcon, AccountTreeOutlined as TreeIcon, GroupAddOutlined as GroupIcon, DonutLargeOutlined as ChartIcon, ImageOutlined as ImageIcon, FormatListNumberedOutlined as FieldsIcon } from '@mui/icons-material';
import { MediaUpload, LoadingBar, WithDetailsToolbar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import EventInfo from './EventInfo';
import Attendees from './Attendees';
import Groups from './Groups';
import CustomFields from './CustomFields';
import Tree from './Tree';
import Statistics from './Statistics';

/*
this component is used to display the event's information and tabs with event related data
props:
    id: the id of the event to display
    onDelete: function to be called when the delete button is clicked
    onEdit: function to be called when the edit button is clicked
    onUpdate: function to be called when the data is updated
    loading: loading state for the parent component
    parentRef: a ref to the parent container
*/
export const EventData = ({onDelete, onEdit, onUpdate, id, loading:parentLoading, parentRef, fetchCounter, setFetchCounter, selectedEvents, ...props}) => {    
    const { isMobile } = useOutletContext();
    const contentRef = parentRef || useRef(null);

    const apiParams = useMemo(() => [
        {enableCache: false, params: {endpoint: `/event/${id}`, method: 'GET', params: {id, include_custom_fields: 1, include_tags: 1, include_media: 1}}},
        {enableCache: true, params: {endpoint: `/event/group_types`, method: 'POST', data: {event_id: id, include_custom_fields: 1}}},        
    ], [id]);

    const { fetchData, data, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: fetchGroupTypes, data: groupTypesData, ErrorBar: GroupTypesErrorBar, loading: groupTypesLoading } = useApi(apiParams[1]);

    const [selectedTab, setSelectedTab] = useState(null);
    const [selectedTabProps, setSelectedTabProps] = useState(null);

    const tabs = useMemo(() => {
        const _tabs = [];
        let mobileIndex = 0;
        if (isMobile) {
            mobileIndex = 1;
            _tabs.push({ id: 'info', index: 0, slug: 'event:toolbar.basic', moduleId: 25, icon: <InfoIcon/>, component: props => <EventInfo onDelete={onDelete} onEdit={onEdit} {...props} />});
        }

        _tabs.push(
            { id: 'groups', index: 0 + mobileIndex, slug: 'event:toolbar.groups', moduleId: 25, icon: <GroupIcon/>, component: props => <Groups {...props} />},
            { id: 'attendees', index: 1 + mobileIndex, slug: 'event:toolbar.attendees', moduleId: 25, icon: <AttendeeIcon/>, component: props => <Attendees {...props} />},
            { id: 'images', index: 2 + mobileIndex, slug: 'event:toolbar.images', moduleId: 25, icon: <ImageIcon/>, component: props => <MediaUpload {...props} />},
            { id: 'fields', index: 3 + mobileIndex, slug: 'event:toolbar.customFields', moduleId: 25, icon: <FieldsIcon/>, component: props => <CustomFields {...props} />},
            { id: 'tree', index: 4 + mobileIndex, slug: 'event:toolbar.tree', moduleId: 25, icon: <TreeIcon/>, component: props => <Tree {...props} />},
            { id: 'statistics', index: 5 + mobileIndex, slug: 'event:toolbar.statistics', moduleId: 25, icon: <ChartIcon/>, component: props => <Statistics {...props} />},
        );

        setSelectedTab(prev => !prev ? _tabs[0].id : prev);

        return _tabs;
    }, [isMobile, onDelete, onEdit]);

    const { permissions } = usePermission({moduleIds: tabs.map(tab => tab.moduleId)});

    const visibleTabs = useMemo(() => tabs.filter(tab => {
        return permissions[tab.moduleId] && ((data?.[0]?.is_meta || data?.[0]?.parent_id || data?.[0]?.children?.length > 0) || tab.id !== 4)
    }), [permissions, data, tabs]);


    // do stuff when a file is dropped in the upload container
    const handleFileDrop = useCallback(files => {
        if (files.length && id){
            const _formdata = new FormData();
            _formdata.append('id', id);
            files.forEach(file => {
                _formdata.append('files[]', file.file);
                _formdata.append('file_ids[]', file.id || null);
                _formdata.append('file_descriptions[]', file.description || null);
            });
            if (onUpdate) onUpdate(_formdata);
        }
    }, [id, onUpdate]);

    // do stuff when a file is removed from the upload container
    const handleFileRemove = useCallback(file => {
        if (file && id){
            //onFileRemove("update", file);
        }
    }, [id, onUpdate]);

    // update the event
    const handleUpdate = useCallback(data => {
        if (!data) return;
        if (!data.id) data.id = id;
        if (onUpdate) onUpdate(data);
    }, [id, onUpdate]);

    // handle the toolbar click
    const handleToolbarClick = useCallback((e, action) => {
        if (!action) return;

        let _props = null;
        switch(action){
            case 'images': // images
                _props = {
                    onFileDrop: handleFileDrop, 
                    onFileRemove: handleFileRemove, 
                    url: data?.[0]?.images.map(a=> ({...a, url: a.preview_url})) || null, 
                    loading: loading || parentLoading || groupTypesLoading,
                };
                break;
            case 'groups': // groups
            case 'attendees': // attendees
            case 'fields': // custom fields
            case 'tree': // tree
            case 'statisctics': // statistics
            case 'info':
                _props = {
                    eventData: data?.[0], 
                    groupTypes: groupTypesData,
                    onUpdate: handleUpdate, 
                    fetchCounter, 
                    setFetchCounter,
                    loading: loading || parentLoading,
                };
                break;
            default:
                break;
        }        
        setSelectedTab(`${action}`);
        setSelectedTabProps(_props);
    }, [data, groupTypesData, handleFileDrop, handleFileRemove, handleUpdate, loading, groupTypesLoading, parentLoading, fetchCounter, setFetchCounter]);

    useEffect(() => {
        if (id) {
            fetchData();
            fetchGroupTypes();
        }
    }, [id, fetchGroupTypes, fetchData]);

    if (!id) return null;

    return (
        <Container disableGutters ref={contentRef}>
            <ErrorBar />
            <GroupTypesErrorBar />
            {(loading || parentLoading || !data) && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: 0}} />}
            {data?.[0] &&
                <TabContext value={selectedTab || tabs?.[0]?.id}> 
                    {!isMobile && 
                        <EventInfo eventData={data?.[0]} onDelete={onDelete} onEdit={onEdit} loading={loading || parentLoading} />
                    }
                    <WithDetailsToolbar 
                        tabs={visibleTabs}
                        eventData={data?.[0]} 
                        parentRef={contentRef} 
                        onSelection={handleToolbarClick} 
                        selectedTab={selectedTab}
                        setSelectedTab={setSelectedTab}
                        sx={{mt: 4, ml: 'auto'}} 
                        stickyTop={selectedEvents?.length > 1 ? 68 : 38}
                    />
                    <TabPanel value={`${selectedTab || tabs?.[0]?.id}`} sx={{ px: isMobile ? 1 : undefined, pb: isMobile ? theme => theme.sizes.headerHeight : undefined }} >
                        {tabs?.find(a => a.id === selectedTab)?.component({...{
                            eventData: data?.[0], 
                            groupTypes: groupTypesData,
                            onUpdate: handleUpdate, 
                            fetchCounter, 
                            setFetchCounter,
                            loading: loading || parentLoading,
                        }, ...selectedTabProps})}
                    </TabPanel>
                </TabContext>
            }
        </Container>
    );
}