import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { IconButton, Stack, Table as MuiTable, TableBody, TableCell, TableHead, TableRow, Typography, lighten, Tooltip } from '@mui/material';
import { CopyAllOutlined as DuplicateIcon, DeleteOutlineOutlined as DeleteIcon} from '@mui/icons-material';

import { createCurrencyFormatter, toCamelCase, formatDate, capitalize, formatRecurringItem } from '../../../../../utils';
import { utils } from '../../../common/pos';

export const Table = ({items = [], onDelete, onRowClick, onDuplicate, slotProps, isBuilder, ...props}) => {
    const { t } = useTranslation();
    //const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const columns = useMemo(() => [
        { id: 'name', label: 'item', align: 'left', flex: 1, valueFormatter: (value, row) => {
            let _value = [<Typography variant="bold" component="div">{value}</Typography>];
            let recurringInfo = formatRecurringItem(row?.metadata);
            
            if (row?.metadata?.variant_name) { // variant info
                _value.push(<Typography variant="caption">{row.metadata.variant_name}</Typography>);
            }
            if (recurringInfo.length > 0) {  // recurring info
                _value.push(
                    <Typography variant="caption">
                        +{+row?.metadata?.price > 0 && currencyFormatter.format(row.metadata.price, currency)}&nbsp;
                        {capitalize(recurringInfo?.map(info => t(info))?.join(' ').toLocaleLowerCase())}
                    </Typography>);
            }
            if (row?.addons?.length > 0) { // addons
                _value.push(<ul>{row.addons.map(a => <li key={a.id}>{a.name || a?.metadata?.name}</li>)}</ul>);
            }
            if (row?.forUserIds?.length > 0) { // for users
                _value.push(<ul>{row.forUserIds.map(a => <li key={a.id}>{a.firstName}</li>)}</ul>);
            }
            if (row?.giftCardRecipient?.full_name) { // gift card recipient
                _value.push(
                    <Typography variant="subtitle2" component="div">
                        {row.giftCardRecipient.full_name}<br/>
                        {row.giftCardRecipient?.email && 
                            <>
                                {row.giftCardRecipient?.email}
                                <br/>
                            </>
                        }
                        {row.giftCardRecipient?.delivery_date && 
                            <>
                                {t("giftCard:deliveryDate")}: {formatDate(row.giftCardRecipient?.delivery_date, language)}
                                <br/>
                            </>
                        }
                        {row.giftCardRecipient?.message && <Typography variant="caption">{row.giftCardRecipient?.message}</Typography>}
                    </Typography>
                );
            }
            // memo
            if (row?.memo) _value.push(<Typography variant="caption" component="i"><q>{row.memo}</q></Typography>);
            return (
                <Stack direction="column" useFlexGap flexWrap="wrap">
                    {_value.map((v, i) => <div key={i}>{v}</div>)}
                </Stack>
            );
        }},
        { id: 'price', label: 'price', width: 110, align: 'right', valueFormatter: (value, row) => {
            let _value = +value;
            if (+row?.metadata?.activation_fee > 0) _value = +row.metadata.activation_fee;
            row?.addons?.forEach(a => _value += +a.price);
            _value = _value * (+row?.qty || 1);
            return currencyFormatter.format(_value, currency);
        }},
        { id: 'qty', label: 'qty', width: 60, align: 'right' },
    ], [t, currencyFormatter, currency, language]);

    return (
        <MuiTable stickyHeader aria-label={t("general:cart")} {...slotProps}>
            <TableHead sx={{
                '.MuiTableCell-root':{
                    backgroundColor: theme => lighten(theme.palette.background.paper, theme.palette.action.selectedOpacity),
                }
            }}>
                <TableRow>
                    {columns.map((column) => (
                        <TableCell
                            key={column.id}
                            align={column.align}
                            style={{ minWidth: column.width, flex: column.flex }}
                        >
                            {t(`order:${toCamelCase(column.label)}`, column.label)}
                        </TableCell>
                    ))}
                    <TableCell align="right" />
                </TableRow>
            </TableHead>
            <TableBody>
                {items.map(row => (
                    <TableRow hover={!isBuilder} role="checkbox" tabIndex={-1} key={row.id} disabled={isBuilder} onClick={isBuilder ? undefined : onRowClick(row.id)}>
                        {columns.map(column => {
                            const value = row?.metadata?.[column.id] || row?.[column.id];
                            return (
                                <TableCell key={column.id} align={column.align} style={{ width: column.width, flex: column.flex }}>
                                    {column.valueFormatter ? column.valueFormatter(value, row) : value}
                                </TableCell>
                            );
                        })}
                        <TableCell align="right">
                            <Stack direction="row" spacing={0}>
                                <Tooltip title={t('general:duplicate')}>
                                    <span>
                                        <IconButton size="small" disabled={isBuilder} onClick={onDuplicate(row.id)}>
                                            <DuplicateIcon fontSize='inherit' />
                                        </IconButton>
                                    </span>
                                </Tooltip>
                                <Tooltip title={t('general:remove')}>
                                    <span>
                                        <IconButton size="small" disabled={isBuilder} onClick={onDelete(row.id)}>
                                            <DeleteIcon fontSize='inherit'/>
                                        </IconButton>
                                    </span>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </MuiTable>
    );
}