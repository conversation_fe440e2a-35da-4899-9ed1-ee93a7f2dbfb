import React from 'react';
import { Con<PERSON><PERSON>, Stack, Grid2, useMediaQuery } from '@mui/material';
import Title from '../Title';
import { FormProvider } from '../useForm/FormContext';
import { useWizard } from './useWizard';
import SideSteps from './SideSteps';
import Steps from './Steps';

/*
slots: {
	breadcrumbs: [{title, to}, ...],
	stepList: Module: {stepList: [{key, slug, label, short_description, description}, ...] step1: () => {}, step2: () => {}, ...}
	steps: [Component, ...]
}
slotProps: {
	steps: {data, ...anything else we want to send to the steps}
    sideSteps: {sx: {background: 'red}}
}
*/
export const Wizard = ({
    stepperPosition = 'right', // the position of the stepper thingy
    title, // the title of the wizard
    onSubmit, // the function to call when the form is submitted
    onError, // the function to call when there is an error
    onChangeStep, // the function to call when the step changes
    loading, // loading state in the parent component
    externalVisitedSteps, // array of steps that have been visited or completed (from parent)
    isEditing = false, // whether we're editing an existing item
    slots, // an object that contains all the slots {breadcrumbs, steps, stepList}
    slotProps, // an object that contains individual props for each slot (steps, sideSteps, etc.1)
    ...props
}) => {
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const {
        handleErrors,
        setActiveStep,
        activeStep,
        hasErrors,
        visitedSteps,
    } = useWizard(onChangeStep, onError);

    return (
        <Container disableGutters>
            <Stack spacing={2} direction="row" justifyContent="space-between" alignItems="center">
                <Title title={title} breadcrumbs={slots?.breadcrumbs}/>
            </Stack>

            <FormProvider onSubmit={onSubmit} {...props}>
                <Grid2 container spacing={2}>
                    <Grid2 size={{xs: 12, lg: "grow"}} sx={{mt:4}} order={stepperPosition === 'right' ? 0 : 1}>
                        <Steps
                            {...slotProps?.steps}
                            steps={slots?.steps}
                            stepList={slots?.stepList}
                            activeStep={activeStep}
                            loading={loading}
                            onError={handleErrors}
                            hasErrors={hasErrors}
                            onStepChange={setActiveStep}
                            showGroupDescriptionColumn={stepperPosition === 'right' && !isMobile}
                        />

                    </Grid2>
                    <Grid2 size={{xs: 12, lg: "auto"}} order={stepperPosition === 'right' ? 1 : 0} position={{xs: "sticky", lg: "relative"}} sx={{bottom: {xs: 0, lg: undefined}, zIndex: theme => theme.zIndex.appBar + 1}}>
                        <SideSteps
                            stepList={slots?.stepList.stepList}
                            activeStep={activeStep}
                            onStepChange={setActiveStep} // This will be intercepted by Steps component
                            loading={loading}
                            hasErrors={hasErrors}
                            visitedSteps={externalVisitedSteps || visitedSteps || []}
                            isEditing={isEditing}
                            {...slotProps.sideSteps}
                        />
                    </Grid2>
                </Grid2>
            </FormProvider>
        </Container>
    );
}