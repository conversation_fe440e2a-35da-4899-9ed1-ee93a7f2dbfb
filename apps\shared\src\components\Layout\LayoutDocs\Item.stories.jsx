import React from 'react';
import { Item } from '../Menu/Item/Item';
import BathtubIcon from '@mui/icons-material/Bathtub';
import BrunchDiningIcon from '@mui/icons-material/BrunchDining';
import SportsMartialArtsIcon from '@mui/icons-material/SportsMartialArts';

export default {
    title: 'Shared/Layout/Menu/Item',
    component: Item,
    tags: ['autodocs'],
    argTypes: {
        isDocked: {
            description: "Flag to indicate if the item is docked",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag indicating whether the item is docked."
            }
        },
        selectedModuleName: {
            description: "Name of the selected module",
            control: 'text',
            table: {
                type: { summary: "string" },
                defaultValue: { summary: "" },
                detail: "The name of the module that is currently selected."
            }
        },
        item: {
            description: "The item object containing path, slug, and icon",
            control: 'object',
            type: { required: true },
            table: {
                type: { summary: "object" },
                defaultValue: { summary: "{}" },
                detail: "An object representing the item, containing 'path', 'slug', and 'icon'."
            }
        },
        drawerWidth: {
            description: "Width of the drawer",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 240 },
                detail: "The width of the drawer in pixels."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        item: {
            path: '/home',
            slug: 'home',
            icon: <BathtubIcon />
        },
        drawerWidth: 240
    }
};

// Following stories to illustrate each significant prop
export const DockedItem = {
    args: {
        isDocked: true,
        item: {
            path: '/home',
            slug: 'home',
            icon: <BathtubIcon />
        },
        drawerWidth: 240
    }
};

export const WithSelectedModuleName = {
    args: {
        selectedModuleName: 'dashboard',
        item: {
            path: '/dashboard',
            slug: 'dashboard',
            icon: <BrunchDiningIcon />
        },
        drawerWidth: 240
    }
};

export const WithCustomDrawerWidth = {
    args: {
        item: {
            path: '/settings',
            slug: 'settings',
            icon: <SportsMartialArtsIcon />
        },
        drawerWidth: 300
    }
};
