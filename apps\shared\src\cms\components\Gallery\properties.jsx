export const properties = [
    {
        name: 'images',
        label: 'builder:component.gallery.images',
        component: "MediaManager",
        value: [],
        size: "large",
        margin: "normal",
        multiple: true,
        returnMultiple: true,
        mediaType: 1,
        accept: 'image/*',
        showThumbs: true,
    },
    {
        name: 'imageSize',
        label: 'builder:component.gallery.imageSize',
        component: "Measurement",
        value: {width: '100%', height: '100%'},
        measurements: ['width', 'height'],
        size: "small",
        margin: "normal",
    },
    {
        name: 'objectFit',
        label: 'builder:component.gallery.objectFit',
        component: "Select",
        value: 'cover',
        size: "small",
        margin: "normal",
        options: [
            {id: 'cover', slug: 'fit:cover'},
            {id: 'contain', slug: 'fit:contain'},
        ],
    },
    {
        name: 'objectPosition',
        label: 'builder:component.gallery.objectPosition',
        component: "Select",
        value: 'center',
        size: "small",
        margin: "normal",
        options: [
            {id: 'center', slug: 'position:center'},
            {id: 'top', slug: 'position:top'},
            {id: 'bottom', slug: 'position:bottom'},
            {id: 'left', slug: 'position:left'},
            {id: 'right', slug: 'position:right'},
            {id: 'top center', slug: 'position:topCenter'},
            {id: 'top left', slug: 'position:topLeft'},
            {id: 'top right', slug: 'position:topRight'},
            {id: 'bottom center', slug: 'position:bottomCenter'},
            {id: 'bottom left', slug: 'position:bottomLeft'},
            {id: 'bottom right', slug: 'position:bottomRight'},
            {id: 'center left', slug: 'position:centerLeft'},
            {id: 'center right', slug: 'position:centerRight'},
        ],
    },
];
