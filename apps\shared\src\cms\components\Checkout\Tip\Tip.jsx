import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Stack } from '@mui/material';

import { ButtonWrapper, Caption } from '../../common/pos';
import CustomAmount from './CustomAmount';

export const Tip = ({tips = [10, 15, 20], amount, loading: parentLoading, onTipChange, slotProps, ...props}) => {
    const { t } = useTranslation();
    const customRef = useRef(null);

    const [showCustom, setShowCustom] = useState(false);
    const [customWidth, setCustomWidth] = useState(0);
    const [cashValue, setCashValue] = useState(0);

    const tipAmounts = useMemo(() => {
        const _tips = tips.map(tip => ({id: +tip, label: +tip === 0 ? t("pos:noTip") : `${tip}%`}));
        if (!tips.includes(0)) _tips.push({id: 0, label: t("pos:noTip")});
        _tips.push({id: "custom", label: t("pos:customAmount")});
        return _tips;
    }, [tips, t]);

    const handleTipSelect = useCallback(item => {
        if (!item) setShowCustom(true);
        else {
            const tipAmount = item.id === "custom" ? item.value : item.id;
            const _cashAmount = item.id === "custom" ? tipAmount : amount * tipAmount / 100;
            setShowCustom(item.id === "custom");
            setCashValue(_cashAmount);
            if (onTipChange && tipAmount >= 0) onTipChange({amount: _cashAmount, type: item.id});
        }
    }, [onTipChange, amount]);

    useEffect(() => {
        if (customRef.current) setCustomWidth(customRef.current.offsetWidth);
    }, [customRef.current]);

    return (
        <Stack direction="row" spacing={1} useFlexGap flexWrap="wrap">
            <ButtonWrapper items={tipAmounts} disabled={parentLoading} onClick={handleTipSelect} slotProps={slotProps} className="auto">
                {tipAmounts.map(item => (
                    <React.Fragment key={item.id}>
                        {(item.id === "custom" && showCustom)
                            ? 
                                <CustomAmount 
                                    item={item} 
                                    onSelect={handleTipSelect} 
                                    label={null} 
                                    amount={cashValue}
                                    disabled={parentLoading} 
                                    slotProps={{
                                        ...slotProps, 
                                        customAmount: {
                                            size: "large", 
                                            variant: "filled"
                                        }, 
                                        container: {
                                            sx: {
                                                width: customWidth || "80px"
                                            }
                                        }
                                    }} 
                                />
                            : <Caption variant="subtitle2" component="div" text={item.label} bold ref={customRef} />
                        }
                    </React.Fragment>
                ))}
            </ButtonWrapper>
        </Stack>
    );
}