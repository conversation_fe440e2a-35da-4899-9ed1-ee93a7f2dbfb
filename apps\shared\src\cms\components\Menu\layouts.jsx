import { MenuOpenOutlined as MenuIcon, HighlightAltOutlined as ContextIcon } from '@mui/icons-material';
import CmsIcon from '../../utils/CmsIcon';

export const widgetIcon = MenuIcon;
export const layouts = [
    {
        id: 1,
        name: 'Responsive',
        icon: <MenuIcon sx={{color: theme => theme.palette.text.secondary}} />,
        slotProps: {
            menu: { type: 'responsive' },
        }        
    },
    {
        id: 2,
        name: 'Vertical',
        icon: CmsIcon({
            iconProps: {height: 16, width: 24, direction: 'column', spacing: 2},
            elements: [
                {type: 'text', width: '100%'},
                {type: 'text', width: '100%'},
                {type: 'text', width: '100%'},
            ]
        }),
        slotProps: {
            menu: { type: 'vertical' },
        }
    },
    {
        id: 3,
        name: 'Horizontal',
        icon: CmsIcon({
            iconProps: {height: 3, width: 24, direction: 'row', spacing: 2},
            elements: [
                {type: 'text', width: 6},
                {type: 'text', width: 6},
                {type: 'text', width: 6},
            ]
        }),
        slotProps: {
            menu: { type: 'horizontal' },
        }
    },
    {
        id: 4,
        name: 'Mega',
        icon: CmsIcon({
            iconProps: {height: 16, width: 24, direction: 'column', spacing: 1},
            elements: [
                {type: 'group', direction: 'row', spacing: 2, children: [
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                ]},
                {type: 'group', direction: 'row', spacing: 2, children: [
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                ]},
                {type: 'group', direction: 'row', spacing: 2, children: [
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                    {type: 'text', width: 6},
                ]},
            ]
        }),
        slotProps: {
            menu: { type: 'mega' },
        }
    },
    {
        id: 5,
        name: 'Context',
        icon: <ContextIcon sx={{color: theme => theme.palette.text.secondary}} />,
        slotProps: {
            menu: { type: 'context' },
        }
    },
];