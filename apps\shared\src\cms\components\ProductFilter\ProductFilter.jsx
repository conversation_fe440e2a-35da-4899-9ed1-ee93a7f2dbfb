import React, { useContext, memo } from 'react';
import { Stack } from '@mui/material';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';

import { PosContext } from '../../hooks/PosContext';
import { layouts } from './Layouts';
import { properties } from './properties';
import Types from './Types';
import Categories from './Categories';
import { useProductFilter } from './useProductFilter';

/*
Display a filter for products. 
The filters may be categories or product types.
*/
export const ProductFilter = memo(({
    id,
    layoutId,
    filterType = 'category',
    categoryIds = [],
    productTypeIds = [],
    options = [],
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        buttons: {},        // MUI button props
        select: {},         // MUI textfield props
        list: {}            // MUI menu list props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    builderProps,
    children,
    ...props
}) => {
    const { reduxStore } = useContext(PosContext) || {};
    const { data, loading, error, handleSelection, selectedId } = useProductFilter({isBuilder, options, ids: filterType === "category" ? categoryIds : productTypeIds, type: filterType});

    const { slotProps: updatedSlotProps, canRender, customCss } = prepareComponent({name: 'product-filter', layoutId, layouts, slotProps, isBuilder, condition, localState: {
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;

    const Component = filterType === "category" ? Categories : Types;

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={1} direction="column" useFlexGap {...slotProps?.cmsStack}>
                <Component 
                    isBuilder={isBuilder} 
                    layoutId={layoutId} 
                    slotProps={slotProps} 
                    options={data} 
                    disabled={isBuilder} 
                    loading={loading}
                    onSelect={handleSelection}
                    selectedId={selectedId}
                />
            </Stack>
            {children}
        </CmsContainer>
    );
});