import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../KeycloakProvider/tenantConfig";
import { setInfo } from '../../../store/reducers/fixedDataSlice';

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

const apiParams = {
    enableCache: true,
    params: {endpoint: '/luts/billing-frequency', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}
};

export const useMerchantBillingFrequenciesData = () => {
    const dispatch = useDispatch();
    const loaded = useSelector(state => state.fixedData.loaded.merchantBillingFrequencies);
    const token = useSelector(state => state.user.token);

    const { fetchData, loading } = useApi(apiParams);

    useEffect(() => {
        if (!loaded && token) {
            fetchData()
                .then(result => {
                    if (result?.data) {
                        dispatch(setInfo({ merchantBillingFrequencies: result.data }));
                    }
                })
                .catch(error => {
                    console.error('Error fetching merchant billing frequencies:', error);
                });
        }
    }, [loaded, fetchData, dispatch, token]);

    return { loading };
};
