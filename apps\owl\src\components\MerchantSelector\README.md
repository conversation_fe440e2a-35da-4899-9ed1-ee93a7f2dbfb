# MerchantSelector Component

A reusable autocomplete component for selecting merchants in the OWL application. This component uses the FormItem component from the shared repo and provides async data fetching with search functionality.

## Features

- **Async Data Fetching**: Automatically fetches merchants from the `/user/list` endpoint
- **Search Functionality**: Supports real-time search with debounced API calls
- **Rich Display**: Shows merchant code, contact, email, phone, and type information
- **Customizable**: Supports all standard FormItem and Autocomplete props
- **Multiple Selection**: Supports both single and multiple merchant selection
- **Consistent Styling**: Follows the existing design patterns in the codebase

## Usage

### Basic Usage

```jsx
import { MerchantSelector } from '../../../components/MerchantSelector';

// In your form field definition
{
  name: 'merchant_id',
  label: 'product:merchant',
  required: true,
  value: '',
  component: MerchantSelector,
  margin: "normal",
  rowSize: {xs: 12, md: 6}
}
```

### Direct Component Usage

```jsx
import { MerchantSelector } from './components/MerchantSelector';

function MyForm() {
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  const handleMerchantChange = (event) => {
    setSelectedMerchant(event.target.value);
  };

  return (
    <MerchantSelector
      label="Select Merchant"
      name="merchant_id"
      value={selectedMerchant}
      onChange={handleMerchantChange}
      required
    />
  );
}
```

### Multiple Selection

```jsx
<MerchantSelector
  label="Select Merchants"
  name="merchant_ids"
  multiple
  value={selectedMerchants}
  onChange={handleMerchantsChange}
/>
```

## Props

The MerchantSelector accepts all standard FormItem and Autocomplete props, plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | string | `'merchant:merchant'` | The label for the field |
| `name` | string | `'merchant_id'` | The name of the field |
| `required` | boolean | `false` | If the field is required |
| `multiple` | boolean | `false` | Allow multiple selections |
| `getOptionLabel` | function | Built-in formatter | Custom function to format option labels |
| `renderOption` | function | Built-in renderer | Custom function to render options |

## Data Format

The component expects merchant data in the following format:

```javascript
{
  id: 123,
  first_name: "John",
  last_name: "Doe",
  email: "<EMAIL>",
  mobile_phone: "+1234567890",
  contact: "John Doe",
  roles: [{ name: "producer" }]
}
```

## Display Format

- **Option Label**: "Code - Contact (Type)"
- **Option Details**: Shows email and phone in a secondary line
- **Search**: Searches across all merchant fields

## Integration

This component is already integrated into:
- **Products Form**: Used for selecting the merchant when creating/editing products
- **Available for**: Any form that needs merchant selection functionality

## API Endpoint

Uses the `/user/list` endpoint with the following parameters:
- `method`: POST
- `filters.search_words`: For search functionality
- `page_no`: 1
- `max_records`: 50
- `sort_col`: id
- `sort_direction`: DESC
