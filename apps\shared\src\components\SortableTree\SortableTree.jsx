import React from "react";
import { DndContext, closestCenter, DragOverlay, MeasuringStrategy, defaultDropAnimation } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { getChildCount } from '../../utils';
import Portal from "../Portal";
import { useTree } from "./useTree";
import TreeItem  from "./TreeItem";

const adjustTranslate = ({ transform }) => ({ ...transform, y: transform.y - 25});
const measuring = {droppable: {strategy: MeasuringStrategy.Always }};
const dropAnimation = {...defaultDropAnimation, dragSourceOpacity: 0.5};

export const SortableTree = ({
    collapsible = true, // if items can be collapsed
    defaultItems = [], // the default items to be displayed
    indicator = true, // if the indicator should be displayed
    indentationWidth = 32, // the width of the indentation
    removable = true, // if items can be removed
    details = false, // if the details should be displayed
    onUpdate, // the function to be called when the tree is updated
    onRemove, // the function to be called when an item is removed
    handleDetails = () => {}, // function that returns the rendered details of an item
}) => {
    const {
        items,
        sensors,
        activeId,
        activeItem,
        flattenedItems,
        sortedIds,
        projected,
        handleDragStart,
        handleDragMove,
        handleDragOver,
        handleDragEnd,
        handleDragCancel,
        handleRemove,
        handleCollapse,
        toggleDetails,
        announcements,       
    } = useTree({defaultItems, indentationWidth, onUpdate, onRemove});

    return (
        <DndContext
            announcements={announcements}
            sensors={sensors}
            collisionDetection={closestCenter}
            measuring={measuring}
            onDragStart={handleDragStart}
            onDragMove={handleDragMove}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            onDragCancel={handleDragCancel}
        >
            <SortableContext items={sortedIds} strategy={verticalListSortingStrategy}>
                {flattenedItems.map(({ id, children, collapsed, depth, ...item }) => (
                    <TreeItem
                        key={id}
                        id={id}
                        value={item?.name || id}
                        details={details ? handleDetails(item): undefined}
                        depth={id === activeId && projected ? projected.depth : depth}
                        indentationWidth={indentationWidth}
                        indicator={indicator}
                        collapsed={Boolean(collapsed && children.length)}
                        showDetails={Boolean(item?.showDetails && details)}
                        onCollapse={collapsible && children?.length > 0 ? () => handleCollapse(id) : undefined}
                        onToggleDetails={details ? () => toggleDetails(id) : undefined}
                        onRemove={removable ? () => handleRemove(id) : undefined}
                    />
                ))}
                <Portal>
                    <DragOverlay dropAnimation={dropAnimation} modifiers={indicator ? [adjustTranslate] : undefined}>
                        {activeId && activeItem &&
                            <TreeItem
                                id={activeId}
                                depth={activeItem.depth}
                                clone
                                childCount={getChildCount(items, activeId) + 1}
                                value={activeItem?.name || activeId}
								details={handleDetails(activeItem)}
                                indentationWidth={indentationWidth}
                            />
                        }
                    </DragOverlay>
                </Portal>
            </SortableContext>
        </DndContext>
    );
}