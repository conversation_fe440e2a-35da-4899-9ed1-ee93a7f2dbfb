import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Paper } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

export const OutstandingReports = () => {
    const { t } = useOutletContext();

    return (
        <Container>
            <Title
                title={t('reports:outstandingReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:outstandingReports')}
                ]}
            />

            <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h5" gutterBottom>
                    Outstanding Reports Content
                </Typography>
                <Typography variant="body1">
                    This page will contain outstanding reports including:
                </Typography>
                <ul>
                    <li>Outstanding Payments</li>
                    <li>Unpaid Invoices</li>
                    <li>Payment Plans</li>
                    <li>Aging Reports</li>
                    <li>Collection Status</li>
                </ul>
            </Paper>
        </Container>
    );
};

export default OutstandingReports;
