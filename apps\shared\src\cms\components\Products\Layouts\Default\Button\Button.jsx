import React, { useCallback } from 'react';
import { Caption, Button as CommonButton } from '../../../../common/pos';
import { Stack } from '@mui/material';

export const Button = ({item, content, disabled, selected, onSelect, onAddToCart, ...props}) => {

    const handleClick = useCallback(async e => {
        e.preventDefault();
        if (onAddToCart) {
            const res = await onAddToCart(item);
            if (res === 2 && onSelect) await onSelect(item);
        }
    }, [onAddToCart, onSelect, item]);

    return (
        <CommonButton 
            item={item}
            disabled={disabled} 
            selected={selected} 
            onClick={handleClick}
            value={item.id}
            {...props}
        >
            <Stack direction="column" spacing={1} useFlexGap>
                {item?.name && <Caption variant="subtitle1" component="span" text={item.name} bold />}
                {content?.price?.map((p, i) => (
                    <Caption key={i} variant={p?.amount ? "subtitle2" : "caption"} component="span" text={p?.amount || p}  bold={Boolean(p?.amount)} />
                ))}
                {content?.text && <Caption variant="caption" component="span" text={content.text} />}
            </Stack>
        </CommonButton>
    );
}