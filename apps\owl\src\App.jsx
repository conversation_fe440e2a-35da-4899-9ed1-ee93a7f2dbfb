import React, { useState } from 'react';
import { StyledEngineProvider } from '@mui/material/styles';
//import { LicenseInfo } from '@mui/x-license';
import { ApiProvider, PermissionProvider, ThemeProvider, Router } from '@siteboss-frontend/shared';
import { ErrorBoundary } from '@siteboss-frontend/shared/components';

import { Loader } from './Loader';
import { Routes } from './Routes';
import { KeycloakProvider } from './components/KeycloakProvider';

function App() {
	//LicenseInfo.setLicenseKey('YOUR_LICENSE_KEY');
	const [ loading, setLoading ] = useState(true);
	const [ savedTheme, setSavedTheme ] = useState(null);

	return (
		<StyledEngineProvider injectFirst>
				<ApiProvider>
					<ThemeProvider onLoading={setLoading} onThemeLoaded={setSavedTheme} overrides={{owl: 'owl'}}>
						<ErrorBoundary>
							<PermissionProvider /*modules={[1, 2, 3]}*/>
								<Loader onLoading={setLoading} />
								{savedTheme &&
									<Router
										basename={import.meta.env.VITE_BASE_PATH}
										themeData={savedTheme}
										loading={loading}
										noLogin
										endpointOverrides={{companyConfig: {endpoint: '/tenant/info', method: 'GET', data: {}}}}
									>
										<KeycloakProvider>											
											<Routes />
										</KeycloakProvider>
									</Router>
								}
							</PermissionProvider>
						</ErrorBoundary>
					</ThemeProvider>
				</ApiProvider>
		</StyledEngineProvider>
	);
}

export default App;
