import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Alert, CssBaseline } from '@mui/material';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';

import { useApi } from './api/useApi';
import { theme } from './theme';
import { MaterialSymbolsLoader } from './components/MaterialSymbol';

const apiParams = {enableCache: true, keepCache: true, params: {endpoint: "/cms/my_theme", params: {cms_version: 2}}};

export const ThemeProvider = ({
	onLoading, 				// function to be called when the theme is loading
	onThemeLoaded, 			// function to be called after the theme is loaded
	theme: defaultTheme, 	// an already loaded theme to use
	overrides, 				// theme overrides, if not provided, it will fetch from the server
	children
}) => {
	const [loading, setLoading] = useState(!Boolean(defaultTheme || overrides));
	const initialized = useRef(false);
	const colorMode = useSelector(state => state.colorMode);

	const { fetchData, data, errors } = useApi(apiParams);

	const _theme = useMemo(() => defaultTheme || theme(colorMode.mode, overrides || data?.[0]?.content?.v2), [colorMode, data, defaultTheme, overrides]);

	const loadTheme = useCallback(async () => {
		setLoading(true);
		if (overrides) {
			if (onThemeLoaded) onThemeLoaded(_theme);
			setLoading(false);
			initialized.current = true;
		} else {
			try {
				const res = await fetchData();
				if (res?.data && onThemeLoaded) onThemeLoaded(res.data);
			} catch (error) {
				console.error(error);
			} finally {
				setLoading(false);
				initialized.current = true;
			}
		}
	}, [fetchData, onThemeLoaded, overrides, _theme]);

	useEffect(() => {
		if (!initialized.current && (!defaultTheme)) loadTheme();
	}, [loadTheme, defaultTheme]);

	useEffect(() => {
		if (onLoading) onLoading(loading);
	}, [loading, onLoading]);

	if ((loading || !initialized.current) && (!defaultTheme)) return null;

	return (
		<MuiThemeProvider theme={_theme}>
			<MaterialSymbolsLoader />
			<CssBaseline enableColorScheme />
			{errors && <Alert severity="error">{errors}</Alert>}
			{children}
		</MuiThemeProvider>
	);
}