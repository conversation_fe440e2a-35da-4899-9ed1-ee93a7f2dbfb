import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Typography, Tooltip, IconButton } from '@mui/material';
import { AddOutlined as AddIcon } from '@mui/icons-material';

export const NewButton = ({ type, disabled, showEmptyText, onClick }) => {
    const { t } = useTranslation();

    if (type === "autocomplete") {
        return (
            <Button variant="text" color="inherit" size="small" onClick={onClick} disabled={disabled}>
                {t('category:newCategory')}
            </Button>
        );
    } else if (showEmptyText) {
        return (
            <>
                <Typography variant="subtitle2" component="div" sx={{width: '100%'}}>
                    {t('category:empty')}
                </Typography>
                <Button variant="text" color="inherit" size="small" onClick={onClick} disabled={disabled}>
                    {t('category:newCategory')}
                </Button>
            </>
        )
    } else {
        return (
            <div>
                <Tooltip title={t('category:newCategory')}>
                    <IconButton size="small" disabled={disabled} onClick={onClick}>
                        <AddIcon fontSize='inherit' />
                    </IconButton>
                </Tooltip>
            </div>
        );
    }
}
