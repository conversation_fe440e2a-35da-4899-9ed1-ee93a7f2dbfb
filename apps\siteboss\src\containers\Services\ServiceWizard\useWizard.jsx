import { useState, useEffect, useCallback, useRef } from 'react';
import { formatISO } from 'date-fns';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useOutletContext, useNavigate } from 'react-router-dom';
import { ErrorBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';
import { uuid } from '@siteboss-frontend/shared/utils';
import { setInfo, resetInfo, setSelectedLocations, setSelectedManagers, updateFormData } from '../../../store/reducers/serviceWizardSlice';

export const useWizard = () => {
    const { t } = useOutletContext();
    const { serviceId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    // Use a ref to store the serviceWizard data to prevent re-renders
    const serviceWizardRef = useRef(null);
    const serviceWizardFromStore = useSelector(state => state.serviceWizard);

    // We're intentionally not cleaning up the Redux store on unmount
    // to preserve form data when navigating away and coming back
    // If you need to reset the form, call resetInfo() explicitly
    const resetForm = useCallback(() => {
        dispatch(resetInfo());
        console.log('Resetting form data in Redux store');
    }, [dispatch]);

    // API calls
    const { data: serviceData, loading: serviceLoading, error: serviceError, call: fetchService } =
        useApi({
            fetchOnMount: !!serviceId,
            params: {
                endpoint: `/service/${serviceId}`,
                method: 'GET'
            }
        });

    const { data: createData, loading: createLoading, error: createError, call: createService } =
        useApi({
            params: {
                endpoint: '/service/add',
                method: 'POST'
            }
        });

    const { data: updateData, loading: updateLoading, error: updateError, call: updateService } =
        useApi({
            params: {
                endpoint: '/service/edit',
                method: 'PUT'
            }
        });

    // State for wizard
    const [activeStep, setActiveStep] = useState(0);
    const [completedSteps, setCompletedSteps] = useState([0]);
    const [success, setSuccess] = useState(false);
    const [hasErrors, setHasErrors] = useState(false);
    const [errorBar, setErrorBar] = useState(null);

    // Manual refresh function if needed
    const refreshServiceData = useCallback(() => {
        if (serviceId && fetchService) {
            fetchService();
        }
    }, [serviceId, fetchService]);

    // Update Redux store with service data
    useEffect(() => {
        if (serviceData) {
            dispatch(setInfo({
                id: serviceId,
                serviceData,
                formData: {
                    ...serviceData,
                    // Convert any specific fields as needed
                    has_start_date: !!serviceData.start_date,
                    has_end_date: !!serviceData.end_date,
                }
            }));

            // Set selected locations and managers
            if (serviceData.location_ids) {
                dispatch(setSelectedLocations(
                    serviceData.location_ids.map(id => ({ id, name: `Location ${id}` }))
                ));
            }

            if (serviceData.managers) {
                dispatch(setSelectedManagers(serviceData.managers));
            }

            // Mark all steps as completed when editing
            if (serviceId) {
                setCompletedSteps([0, 1, 2, 3, 4, 5, 6, 7]);
            }
        }
    }, [serviceData, serviceId, dispatch]);

    // Update the ref with the latest data from Redux
    useEffect(() => {
        serviceWizardRef.current = serviceWizardFromStore;
    }, [serviceWizardFromStore]);

    // Custom setActiveStep function that also tracks completed steps
    const setActiveStepWithTracking = useCallback((stepOrFn) => {
        setActiveStep(prevStep => {
            const newStep = typeof stepOrFn === 'function' ? stepOrFn(prevStep) : stepOrFn;

            // Update completed steps
            setCompletedSteps(prev => {
                if (!prev.includes(newStep)) {
                    return [...prev, newStep];
                }
                return prev;
            });

            return newStep;
        });
    }, []);

    // Update Redux store with form data as it's being filled out
    const updateReduxFormData = useCallback((formData) => {
        if (formData) {
            console.log('Updating Redux store with form data:', formData);
            dispatch(updateFormData(formData));
        }
    }, [dispatch]);

    // Handle form errors
    const handleErrors = useCallback((errors) => {
        console.error('Form errors:', errors);
        setHasErrors(true);
        setErrorBar(<ErrorBar error={errors} />);
    }, []);

    // Handle form submission
    const handleSubmit = useCallback(async (formData) => {
        try {
            console.log('Submitting form data:', formData);
            updateReduxFormData(formData);

            // Format data for API
            const apiData = {
                ...formData,
                // Add any specific formatting needed for the API
                location_ids: serviceWizardRef.current.selectedLocations.map(loc => loc.id),
                manager_ids: serviceWizardRef.current.selectedManagers.map(manager => manager.id),
            };

            // Remove any fields that shouldn't be sent to the API
            delete apiData.has_start_date;
            delete apiData.has_end_date;

            // If start/end dates are not enabled, set them to null
            if (!formData.has_start_date) apiData.start_date = null;
            if (!formData.has_end_date) apiData.end_date = null;

            let result;
            if (serviceId) {
                // Update existing service
                apiData.id = serviceId;
                result = await updateService(apiData);
            } else {
                // Create new service
                result = await createService(apiData);
            }

            if (result && !result.error) {
                setSuccess(true);
                // Navigate back to services list after successful submission
                setTimeout(() => {
                    navigate('/services');
                }, 1500);
            } else {
                throw new Error(result?.error || 'Failed to save service');
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            setHasErrors(true);
            setErrorBar(<ErrorBar error={error} />);
        }
    }, [serviceId, updateService, createService, navigate, updateReduxFormData]);

    return {
        handleErrors,
        handleSubmit,
        setActiveStep: setActiveStepWithTracking,
        activeStep,
        loading: serviceLoading || createLoading || updateLoading,
        success,
        setSuccess,
        hasErrors,
        errorBars: [
            serviceError && <ErrorBar error={serviceError} />,
            createError && <ErrorBar error={createError} />,
            updateError && <ErrorBar error={updateError} />,
            errorBar
        ],
        serviceId,
        serviceData,
        resetForm,
        refreshServiceData,
        completedSteps,
        serviceWizard: serviceWizardRef.current,
    };
};

export default useWizard;
