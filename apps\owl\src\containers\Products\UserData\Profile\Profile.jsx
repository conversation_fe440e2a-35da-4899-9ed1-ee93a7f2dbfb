import React, { useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Grid2 } from '@mui/material';
import { formatDate, toCamelCase } from '@siteboss-frontend/shared/utils';
import { InlineEditor } from '@siteboss-frontend/shared/components';

import Notes from './Notes';
import UserInfo from '../UserInfo';

export const Profile = ({ userData, roles, onDelete, onEdit, loading, ...props }) => {
    const { t, language, isMobile } = useOutletContext();

    const fields = useMemo(() => [
        {slug: 'firstName', value: 'first_name'},
        {slug: 'lastName', value: 'last_name'},
        {slug: 'email', value: 'email'},
        {slug: 'dateOfBirth', value: 'dob', valueFormatter: value => formatDate(new Date(value), language) },
        //{slug: 'phone', value: 'phone'},
        {slug: 'address1', value: 'address1'},
        {slug: 'address2', value: 'address2'},
        {slug: 'city', value: 'city'},
        {slug: 'state', value: 'state'},
        {slug: 'zip', value: 'zip'},
        {slug: 'role', value: 'roles', options: roles.map(a=>({id: a.id, label: a.name})), valueFormatter: ({value, field="name"}) => value?.[0]?.[field] ? (field === "name" ? t(`user:roles.${toCamelCase(value[0][field] || "")}`) : value[0][field]) : null},
    ], [roles, t, language]);

    const handleSave = (text, field) => {
        console.log("profile", field, text);
    }

    return (
        <Grid2 container spacing={2}>
            {isMobile && 
                <Grid2 size={{xs: 12}}>
                    <UserInfo userData={userData} loading={loading} onDelete={onDelete} onEdit={onEdit} />
                </Grid2>
            }
            <Grid2 size={{xs: 12, lg: 6}}>
                {fields.map((field, i) => (
                   userData?.[field.value] ?
                        <InlineEditor 
                            id={field.slug}
                            key={`data-key-${i}`} 
                            sx={{mt: i === 0 ? 0 : undefined}} 
                            title={t(`user:${field.slug}`)}
                            value={
                                field?.valueFormatter 
                                    ? field.valueFormatter({value: userData[field.value]})
                                    : userData[field.value]
                            }
                            optionValue={
                                field?.valueFormatter 
                                    ? field.valueFormatter({value: userData[field.value], field: 'id'})
                                    : userData[field.value]
                            }
                            options = {field?.options || null}
                            onSave = {(text, id) => handleSave(text, id)}
                        />
                    : null
                ))}
            </Grid2>
            <Grid2 size={{xs: 12, lg: 6}}>
                <Notes userData={userData} />
            </Grid2>
        </Grid2>
    );
}