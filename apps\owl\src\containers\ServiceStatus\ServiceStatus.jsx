import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Container,
  Paper,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Chip,
  Alert,
  AlertTitle
} from '@mui/material';
import {
  CheckCircleOutline as CheckIcon,
  ErrorOutline as ErrorIcon,
  WarningAmber as WarningIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { Title } from '@siteboss-frontend/shared/components';

// Sample data for service status
const services = [
  { id: 1, name: 'Order Processing API', status: 'operational', lastIncident: null, uptime: '99.99%' },
  { id: 2, name: 'Shipping Label Generation', status: 'operational', lastIncident: '2023-04-10', uptime: '99.95%' },
  { id: 3, name: 'Tracking Updates', status: 'degraded', lastIncident: '2023-05-01', uptime: '98.5%' },
  { id: 4, name: 'Inventory Sync', status: 'operational', lastIncident: '2023-03-15', uptime: '99.9%' },
  { id: 5, name: 'Customer Portal', status: 'operational', lastIncident: null, uptime: '100%' },
  { id: 6, name: 'Reporting Engine', status: 'operational', lastIncident: null, uptime: '99.98%' },
  { id: 7, name: 'Billing System', status: 'incident', lastIncident: '2023-05-02', uptime: '95.5%' },
];

// Sample data for recent incidents
const incidents = [
  { 
    id: 1, 
    service: 'Billing System', 
    status: 'investigating', 
    started: '2023-05-02 08:15:00', 
    description: 'We are investigating issues with the billing system. Some customers may experience delays in invoice generation.',
    updates: [
      { time: '2023-05-02 08:15:00', message: 'Issue identified with billing system. Investigation underway.' },
      { time: '2023-05-02 08:45:00', message: 'Root cause identified as database connection issue. Working on resolution.' }
    ]
  },
  { 
    id: 2, 
    service: 'Tracking Updates', 
    status: 'monitoring', 
    started: '2023-05-01 14:30:00', 
    description: 'Tracking updates are experiencing delays. We are monitoring the situation after implementing a fix.',
    updates: [
      { time: '2023-05-01 14:30:00', message: 'Delays detected in tracking updates.' },
      { time: '2023-05-01 15:20:00', message: 'Implemented fix to address the delay issue.' },
      { time: '2023-05-01 16:00:00', message: 'Monitoring system performance after fix implementation.' }
    ]
  },
];

export const ServiceStatus = (props) => {
  const { t } = useOutletContext();
  const [lastUpdated, setLastUpdated] = useState(new Date().toLocaleTimeString());

  const handleRefresh = () => {
    // In a real app, this would fetch the latest status data
    setLastUpdated(new Date().toLocaleTimeString());
  };

  // Count services by status
  const statusCounts = services.reduce((acc, service) => {
    acc[service.status] = (acc[service.status] || 0) + 1;
    return acc;
  }, {});

  return (
    <Container>
      <Title 
        title={t('services:serviceStatus')}
        breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('services:serviceStatus')}]}
      />
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="body2">
          Last updated: {lastUpdated}
        </Typography>
        <Button 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {/* System Status Overview */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>System Status</Typography>
        
        {incidents.length > 0 ? (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <AlertTitle>Active Incidents: {incidents.length}</AlertTitle>
            There are currently active incidents affecting some services. See details below.
          </Alert>
        ) : (
          <Alert severity="success" sx={{ mb: 2 }}>
            <AlertTitle>All Systems Operational</AlertTitle>
            All services are running normally.
          </Alert>
        )}
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h5" color="success.main">
                  {statusCounts.operational || 0}
                </Typography>
                <Typography variant="body2">Operational Services</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h5" color="warning.main">
                  {statusCounts.degraded || 0}
                </Typography>
                <Typography variant="body2">Degraded Services</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h5" color="error.main">
                  {statusCounts.incident || 0}
                </Typography>
                <Typography variant="body2">Services with Incidents</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Active Incidents */}
      {incidents.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>Active Incidents</Typography>
          
          {incidents.map((incident) => (
            <Card key={incident.id} variant="outlined" sx={{ mb: 2 }}>
              <CardHeader
                title={incident.service}
                subheader={`Started: ${incident.started}`}
                action={
                  <Chip 
                    label={incident.status === 'investigating' ? 'Investigating' : 'Monitoring'} 
                    color={incident.status === 'investigating' ? 'error' : 'warning'} 
                  />
                }
              />
              <Divider />
              <CardContent>
                <Typography variant="body1" paragraph>
                  {incident.description}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  Updates:
                </Typography>
                <List dense>
                  {incident.updates.map((update, index) => (
                    <ListItem key={index}>
                      <ListItemText 
                        primary={update.message} 
                        secondary={update.time} 
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          ))}
        </Paper>
      )}

      {/* Service Status List */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>Service Status</Typography>
        
        <List>
          {services.map((service) => (
            <React.Fragment key={service.id}>
              <ListItem>
                <ListItemIcon>
                  {service.status === 'operational' ? (
                    <CheckIcon color="success" />
                  ) : service.status === 'degraded' ? (
                    <WarningIcon color="warning" />
                  ) : (
                    <ErrorIcon color="error" />
                  )}
                </ListItemIcon>
                <ListItemText 
                  primary={service.name} 
                  secondary={`Uptime: ${service.uptime} | Last incident: ${service.lastIncident || 'None'}`} 
                />
                <Chip 
                  label={
                    service.status === 'operational' ? 'Operational' : 
                    service.status === 'degraded' ? 'Degraded Performance' : 
                    'Incident'
                  } 
                  color={
                    service.status === 'operational' ? 'success' : 
                    service.status === 'degraded' ? 'warning' : 
                    'error'
                  } 
                  size="small"
                />
              </ListItem>
              <Divider variant="inset" component="li" />
            </React.Fragment>
          ))}
        </List>
      </Paper>
    </Container>
  );
}
