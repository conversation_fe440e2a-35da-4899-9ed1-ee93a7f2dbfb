const typeText = [
    { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
    { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
    { id: 'body1', slug: 'builder:component.heading.types.body1' },
    { id: 'body2', slug: 'builder:component.heading.types.body2' },
    { id: 'caption', slug: 'builder:component.heading.types.caption' },
    { id: 'overline', slug: 'builder:component.heading.types.overline' },
    { id: 'p', slug: 'builder:component.heading.types.p' },
    { id: 'code', slug: 'builder:component.heading.types.code' },
];

export const properties = [
    {
        name: 'separator',
        label: 'builder:component.breadcrumb.separator',
        component: "TextField",
        value: '/',
        size: "small",
        margin: "normal",
    },
    {
        name: 'maxItems',
        label: 'builder:component.breadcrumb.maxItems',
        component: "NumberField",
        value: 5,
        size: "small",
        margin: "normal",
    },
    {
        name: 'variant',
        label: 'builder:component.breadcrumb.variant',
        component: "Select",
        value: 'caption',
        size: "small",
        margin: "normal",
        options: typeText,
    },
    {
        name: 'underline',
        label: 'builder:component.breadcrumb.underline',
        component: "Select",
        value: 'hover',
        size: "small",
        margin: "normal",
        options: [
            {id: 'none', slug: 'builder:component.breadcrumb.underlines.none'},
            {id: 'hover', slug: 'builder:component.breadcrumb.underlines.hover'},
            {id: 'always', slug: 'builder:component.breadcrumb.underlines.always'},
        ],
    },
    {
        name: 'alignItems',
        label: 'builder:component.breadcrumb.alignItems',
        component: "Select",
        value: 'flex-start',
        size: "small",
        margin: "normal",
        options: [
            {id: 'center', slug: 'builder:component.breadcrumb.aligns.center'},
            {id: 'flex-start', slug: 'builder:component.breadcrumb.aligns.left'},
            {id: 'flex-end', slug: 'builder:component.breadcrumb.aligns.right'},
        ],
    }
];
