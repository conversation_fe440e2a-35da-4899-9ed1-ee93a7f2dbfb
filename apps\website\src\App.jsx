import React, { StrictMode } from 'react';
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';
import { StyledEngineProvider } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import { i18n, A<PERSON><PERSON>rovider, PermissionProvider, ThemeProvider } from '@siteboss-frontend/shared';
import { ErrorBoundary } from '@siteboss-frontend/shared/components';

import store from './store/store';
import { Routes } from './Routes';

const App = ({ 
	cache,  				// the Emotion cache from createEmotionCache.js
	url = undefined,		// the url to render
	ssr = true,				// whether to render the app on the server or client
	pages = [],				// all the routes
	company = null,			// the company data
	indexPage = null,		// the index page data
	routes = null,			// the routes (if not provided, will be generated from Routes.jsx)
	themeOverrides = null,	// the theme overrides
}) => {
	//const _theme = theme(null, themeOverrides);
	return (
		<StrictMode>
			<I18nextProvider i18n={i18n}>
				<Provider store={store}>
					<CacheProvider value={cache}>
						<StyledEngineProvider injectFirst>
							<ApiProvider>
								<ThemeProvider overrides={themeOverrides}>
									<ErrorBoundary>
										<PermissionProvider /*modules={[1, 2, 3]}*/>
											<Routes 
												company={company} 
												url={url} 
												ssr={ssr} 
												pages={pages} 
												indexPage={indexPage}
												routes={routes}
											/>
										</PermissionProvider>
									</ErrorBoundary>
								</ThemeProvider>
							</ApiProvider>
						</StyledEngineProvider>
					</CacheProvider>
				</Provider>
			</I18nextProvider>
		</StrictMode>
	);
}

export default App;