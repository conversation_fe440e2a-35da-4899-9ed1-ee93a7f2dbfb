import React, { useRef, useMemo, useCallback, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useForm, LoadingBar } from '@siteboss-frontend/shared/components';
import { toCamelCase, toKebabCase } from '@siteboss-frontend/shared/utils';
import { Box, Button, Paper, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';

import Fields from '../Fields';

const prepareFields = (fields, slug, overrides = {}) => {
    if (!fields || !Array.isArray(fields)) return [];
    
    return fields.map(field => ({
        ...field,
        slug: `${slug}.${toCamelCase(field.name)}`,
        helperSlug: `${slug}.${toCamelCase(field.name)}Helper`,
        value: overrides?.[field.name] || field.value
    }));
};

export const Form = ({
    parentId, 
    pageTypeData, 
    selectedPageTypeId,
    selectedEntry,
    loading,
    fields,
    baseSlug = 'website:page.type.{name}.entries',
    buttonSlug = 'general:save',
    offsetTop,
    onSubmit,
    ...props
}) => {
    const { t, isMobile } = useOutletContext();
    const formRef = useRef(null);
    const [activeTab, setActiveTab] = useState(0);

    const slug = useMemo(() => baseSlug.replace('{name}', toCamelCase(pageTypeData?.name)), [baseSlug, pageTypeData]);
    const formFields = useMemo(() => prepareFields(fields, slug, selectedEntry), [fields, slug, selectedEntry]);

    const { values, errors, handleChange, handleBlur, handleSubmit, resetForm } = useForm(formFields, {
        onSubmit, 
        onFieldChange:({e, name, value, checked, setValues: updateValues}) => {
            if (name === "title") {
                const updateFields = [
                    { name: 'slug', formatter: toKebabCase },
                    { name: 'meta_title' }
                ];
                updateValues(prev => prev.map(field => {
                    const updateField = updateFields.find(uf => uf.name === field.name);
                    if (updateField) {
                        return {
                            ...field,
                            value: updateField.formatter ? updateField.formatter(value) : value
                        };
                    }
                    return field;
                }));
            }
        },
        onFieldBlur: ({e, setValues: updateValues}) => {
            updateValues(prev => prev.map(field => field.name === e.target.name ? {...field, value: e.target.value} : field));
        }
    });

    const tabs = useMemo(() => {
        if (!fields || !Array.isArray(fields)) return [];

        const groupedFields = fields.reduce((acc, field) => {
            const groupId = field.groupId || 'properties';
            if (!acc[groupId]) acc[groupId] = [];
            acc[groupId].push(field.name);
            return acc;
        }, {});

        return Object.entries(groupedFields)
            .map(([groupId, fieldNames]) => ({
                id: groupId,
                slug: `${slug}.toolbar.${toCamelCase(groupId)}`,
                fields: fieldNames
            }))
            .filter(tab => tab.fields.length > 0);
    }, [fields, slug]);

    const tabFields = useMemo(() => 
        tabs.reduce((acc, tab) => {
            acc[tab.id] = tab.fields.map(fieldName => 
                values.find(field => field.name === fieldName)
            ).filter(Boolean);
            return acc;
        }, {}),
    [tabs, values]);

    const handleSubmitForm = useCallback(async e => {
        const res = await handleSubmit(e);
        if (res?.data) resetForm(true);
    }, [handleSubmit, resetForm]);

    const handleTabChange = (e, newValue) => setActiveTab(newValue);

    return (
        <Box sx={{position: 'relative', mb: {xs: 6, lg: 0}, ...props?.sx}} ref={formRef}>
            {loading && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: 0}} />}
            
            <TabContext value={activeTab}>
                <Box component={Paper} elevation={16} square sx={{ 
                    display: 'flex',
                    flexDirection: isMobile ? 'column' : 'row',
                    justifyContent: 'space-between',
                    alignItems: isMobile ? 'stretch' : 'center',
                    position: offsetTop ? 'sticky' : undefined,
                    top: offsetTop || undefined,
                    boxShadow: 0,
                    width: '100%',
                    zIndex: theme => theme.zIndex.appBar
                }}>
                    {tabs?.length > 1 &&
                        <TabList 
                            value={activeTab} 
                            onChange={handleTabChange} 
                            aria-label="form tabs"
                            variant={isMobile ? "scrollable" : "standard"}
                            scrollButtons="auto"
                            allowScrollButtonsMobile
                            sx={{ width: '100%'}}
                        >
                            {tabs.map((tab, index) => <Tab key={tab.id} label={t(tab.slug)} value={index} />)}
                        </TabList>
                    }
                </Box>
                {tabs.map((tab, index) => (
                    <TabPanel key={tab.id} value={index} sx={{p: 0, pb: 3, pt: tabs?.length > 1 ? 3 : 0}}>
                        <Fields 
                            formRef={formRef} 
                            pageTypeId={selectedPageTypeId}
                            parentId={parentId}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            loading={loading}
                            fields={tabFields[tab.id]}
                            errors={errors}
                        />
                    </TabPanel>
                ))}
                <Button variant='contained' color='primary' size='large' onClick={handleSubmitForm} disabled={loading} fullWidth>
                    {t(buttonSlug)}
                </Button>
            </TabContext>
        </Box>
    );
}
