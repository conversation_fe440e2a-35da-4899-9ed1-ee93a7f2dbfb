import React, { useCallback, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Buttons, Select, List } from '../Layouts';

import { setInfo } from '../../../../store/reducers/currentShopItemSlice';

export const Categories = ({ 
    layoutId, 
    loading, 
    disabled, 
    isBuilder, 
    slotProps = {}, 
    options = [], 
    onSelect, 
    selectedId, 
}) => {
    const dispatch = useDispatch();
    const selectedCategory = useSelector(state => state.currentShopItem?.productCategoryId);

    const handleSelect = useCallback(value => {
        if (options.length && !value) dispatch(setInfo({productCategoryId: options.map(a => a.id)}));
        else dispatch(setInfo({productCategoryId: value}));
        if (onSelect) onSelect(value);
    }, [dispatch, onSelect, options]);

    const sharedProps = useMemo(() => ({
        options: options || [],
        allSlug: "product:productCategories.all",
        type: "category",
        label: "product:productCategories.title",
        onSelect: handleSelect,
        reset: false, //selectedCategory === null
        isBuilder,
        selectedId,
    }), [options, handleSelect, selectedCategory, isBuilder, selectedId]);

    useEffect(() => {
        if (options.length) {
            dispatch(setInfo({productCategoryId: options.map(a => a.id)}));
        }
    }, [options, dispatch]);

    useEffect(() => {
        if (selectedId) dispatch(setInfo({productCategoryId: selectedId}));
    }, [selectedId, dispatch]);

    switch (layoutId) {
        case 1:
            return <Buttons {...slotProps?.buttons} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        case 2:
            return <Select {...slotProps?.select} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        case 3:
        case 4:
            return <List {...slotProps?.list} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        default:
            return null;
    }
}