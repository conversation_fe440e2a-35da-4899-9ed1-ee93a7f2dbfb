import { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { usePermissionContext } from './PermissionContext';
import { addPermission } from '../store/reducers/permissionSlice';

import { ROLES } from './constants';

// Hook to get the permissions for a user
export const usePermission = ({moduleIds = [], userId = null, roles = [], ...props}) => {
    const dispatch = useDispatch();
    const alreadyLoaded = useRef(false);
    const [modulePermissions, setModulePermissions] = useState({});

    const { loadPermissions } = usePermissionContext();

    const user = useSelector(state => state.user.profile.id);
    const userRoles = useSelector(state => state.user.roles || []);
    const permissionCache = useSelector(state => state.permission.permissions); // cache of permissions already loaded so we don't query the server again

    // if userId is not provided, use the current user
    const userProfileId = useMemo(() => userId || user, [userId, user]);

    // Determine user's highest role
    const userRole = useMemo(() => {
        if (!userRoles || userRoles.length === 0) return ROLES.PATRON;

        // Find the lowest role ID (highest privilege)
        const lowestRoleId = Math.min(...userRoles.map(role => role.id));
        return lowestRoleId;
    }, [userRoles]);    
    
    // get the modules from the props and group them by module id
    const modulesArray = useMemo(() => {
        let _modules = Array.isArray(moduleIds) ? moduleIds : [moduleIds];
        // group by module id
        return _modules.reduce((acc, moduleId) => {
            if (!acc.includes(moduleId)) acc.push(moduleId);
            return acc;
        }, []);

    }, [moduleIds]);

    // get the permissions from the cache
    const permissionsInCache = useMemo(() => {
        let cache = null;
        if (permissionCache) {
            modulesArray.forEach(moduleId => {
                if (permissionCache?.[moduleId]) {
                    if (!cache) cache = {};
                    cache[moduleId] = permissionCache[moduleId];
                }
            });
        }
        return cache;
    }, [permissionCache, modulesArray]);

    // fetch the permissions (that are not in the cache) from the server
    const fetchPermissions = useCallback(async () => {
        // get the modules that are not in the cache
        const remainingModules = modulesArray.filter(moduleId => !permissionsInCache?.[moduleId]);
        try{
            const result = await loadPermissions(userProfileId, remainingModules);
            if (result) { // if there's a result, update the permissions
                setModulePermissions(result);
                dispatch(addPermission(result)); // add the permissions to the cache
            }
        } catch (e) {
            setModulePermissions({});
            //alreadyLoaded.current = false;
        }
    }, [userProfileId, modulesArray, loadPermissions, permissionsInCache, dispatch]);

    useEffect(() => {
        // only fetch permissions once
        if (!alreadyLoaded.current) {
            alreadyLoaded.current = true;
            fetchPermissions();
        }
    }, [fetchPermissions]);

    return { permissions: {...modulePermissions, ...permissionsInCache}, userRole: { id: userRole || ROLES.PATRON, name: ROLES[userRole] || 'PATRON'} };
}