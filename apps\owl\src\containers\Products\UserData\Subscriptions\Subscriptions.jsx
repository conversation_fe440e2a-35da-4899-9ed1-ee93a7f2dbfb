import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Box, Stack, Card, CardContent, CardHeader, CardActions, Button, Typography } from '@mui/material';
import { createCurrencyFormatter, formatDate, capitalize, toCamelCase } from '@siteboss-frontend/shared/utils';
import { DataTableNoRows } from '@siteboss-frontend/shared/components';

import styles from './Subscriptions.module.scss';

const _intervalNames = {
    adverb: {
        m: 'Monthly',
        w: 'Weekly',
        d: 'Daily',
        y: 'Yearly',
    },
    noun: {
        m: 'Month',
        w: 'Week',
        d: 'Day',
        y: 'Year',
    },
    nounPlural: {
        m: 'Months',
        w: 'Weeks',
        d: 'Days',
        y: 'Years',
    },
}

export const Subscriptions = ({ userData, loading, ...props }) => {
    const { t, language, currency } = useOutletContext();
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const rows = [];
    userData?.subscriptions.forEach(subscription => {
        let text = [currencyFormatter.format(subscription.price)]
        if (subscription.interval_quantity === 1){
            text = [...text,
                t('subscription:billed'),
                t("subscription:for"),
                subscription.interval_quantity,
                t(`subscription:${toCamelCase(_intervalNames.noun[subscription.bill_interval.toLowerCase()])}`),
            ];
        } else {
            text = [...text,
                t('subscription:billedEvery'),
                t(`subscription:${toCamelCase(_intervalNames.noun[subscription.bill_interval.toLowerCase()])}`),
                t('subscription:for'),
                subscription.interval_quantity,
                t(`subscription:${toCamelCase(_intervalNames.nounPlural[subscription.bill_interval.toLowerCase()])}`),
            ];
        }
        rows.push({
            id: subscription.id,
            title: subscription.product_name,
            subtitle: subscription.product_variant_name,
            interval: _intervalNames[subscription.bill_interval.toLowerCase()],
            color: subscription.subscription_status_id === 1 ? 'success' : 'error',
            status_id: subscription.subscription_status_id,
            status_name: subscription.subscription_status,
            text: capitalize(text.join(' ').toLocaleLowerCase()),
            next_bill_date: subscription.next_bill_date,
            metadata: subscription,
        });
    });

    return (
        <Container disableGutters>
            {!loading && (!rows || rows.length === 0) &&
                <Stack>
                    <DataTableNoRows title={t("subscription:empty")} />
                </Stack>
            }
            {!loading && rows && rows.length > 0 &&
                <Box className={styles.wrapper}>
                    {rows.map(subscription => (
                        <Card variant="groups" key={`subscription-${subscription.id}`}>
                            <CardHeader
                                title={subscription.title}
                                subheader={subscription.subtitle}
                            />
                            <CardContent>
                                <Typography variant='body1' color={subscription.color}>
                                    {t(`subscription:status.${subscription.status_name.toLowerCase()}`)}
                                </Typography>

                                <Typography variant='body2'>
                                    {subscription.text}
                                </Typography>
                                {subscription.next_bill_date &&
                                    <Typography variant='body2'>
                                        {t(`subscription:nextBillDate`)}: {formatDate(subscription.next_bill_date, language)}
                                    </Typography>
                                }
                            </CardContent>
                            <CardActions>
                                <Button variant="contained" color="primary">
                                    Manage
                                    {+subscription?.metadata?.subscription_status_id !==1 && " / Renew"}
                                </Button>
                            </CardActions>
                        </Card>
                    ))}
                </Box>
            }
        </Container>
    );
}