import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, InputAdornment, TextField } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step5 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';
import PercentIcon from '@mui/icons-material/Percent';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';

const Step5Type = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);

    // Initialize state for discount values
    const [discountType, setDiscountType] = useState(0);
    const [discountAmount, setDiscountAmount] = useState('');

    // Initialize local state from Redux store
    useEffect(() => {
        if (formData?.discount_type !== undefined) {
            setDiscountType(formData.discount_type);
        }
        if (formData?.discount_amount !== undefined) {
            setDiscountAmount(formData.discount_amount);
        }
    }, [formData]);

    // Initialize form data from API response - only run once when data changes
    useEffect(() => {

        // Check if we have data from the API and we need to initialize
        if (data && !formData?.initialized) {
            const updatedData = { ...formData, initialized: true };

            // Handle discount_type
            if (data.discount_type !== undefined) {
                const type = parseInt(data.discount_type);
                setDiscountType(type);
                updatedData.discount_type = type;
            }

            // Handle discount_amount
            if (data.discount_amount !== undefined) {
                const amount = data.discount_amount.toString();
                setDiscountAmount(amount);
                updatedData.discount_amount = amount;
            }

            // Dispatch a single update with all changes
            dispatch(updateFormData(updatedData));
        }
    }, [data, dispatch]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Choose the type of discount and its value
                </Typography>

                <StyledRadioGroup
                    name="discount_type"
                    label="Discount Type"
                    required
                    options={[
                        {
                            id: 0,
                            value: 0,
                            label: 'Percentage discount',
                            icon: <PercentIcon />
                        },
                        {
                            id: 1,
                            value: 1,
                            label: 'Fixed amount discount',
                            icon: <MonetizationOnIcon />
                        }
                    ]}
                    value={discountType}
                    onChange={(e) => {
                        const value = parseInt(e.target.value);
                        setDiscountType(value);
                        // Reset discount amount when changing type
                        setDiscountAmount('');
                        // Save the discount_type value and reset discount_amount
                        dispatch(updateFormData({
                            ...formData,
                            discount_type: value,
                            discount_amount: ''
                        }));
                    }}
                    helperText="Choose the type of discount to apply"
                />

                <TextField
                    name="discount_amount"
                    label={discountType === 0 ? 'Percentage' : 'Amount'}
                    type="number"
                    fullWidth
                    required
                    margin="normal"
                    helperText={discountType === 0
                        ? "Enter the percentage discount (1-100)"
                        : "Enter the fixed amount discount"}
                    inputProps={{
                        min: 0,
                        step: discountType === 0 ? 1 : 0.01,
                        max: discountType === 0 ? 100 : undefined
                    }}
                    InputProps={{
                        startAdornment: discountType === 1 ? (
                            <InputAdornment position="start">$</InputAdornment>
                        ) : undefined,
                        endAdornment: discountType === 0 ? (
                            <InputAdornment position="end">%</InputAdornment>
                        ) : undefined,
                    }}
                    value={discountAmount}
                    onChange={(e) => {
                        const value = e.target.value;
                        setDiscountAmount(value);
                        dispatch(updateFormData({
                            ...formData,
                            discount_amount: value
                        }));
                    }}
                />
            </Grid>
        </Grid>
    );
};

export default Step5Type;
