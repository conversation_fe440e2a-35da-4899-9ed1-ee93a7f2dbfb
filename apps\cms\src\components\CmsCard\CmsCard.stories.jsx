import { CmsCard } from "./CmsCard";
import { fn } from 'storybook/test'

export default {
    title: "Cms/Components/Cms Card",
    component: CmsCard,
    tags: ["autodocs"],
    argTypes:{
        name: {
            description: "The name or title of the card",
            control: { type: 'text' },
            type: { required: false },
            table: {
            type: { summary: "string" },
                defaultValue: { summary: null },
            }
          },
          description: {
            description: "The description text for the card",
            control: { type: 'text' },
            type: { required: false },
            table: {
                type: { summary: "string" },
                defaultValue: { summary: null},
            }
          },
          image: {
            description: "The URL of the image to be displayed",
            control: { type: 'text' },
            type: { required: false },
            table: {
                type: { summary: "string" },
                defaultValue: { summary: null },
            }
          },
          tags: {
            description: "An array of tags to be displayed as chips",
            control: { type: 'array' },
            type: { required: false },
            table: {
                type: { summary: "string[]" },
                defaultValue: { summary: "[]" },
            }
          },
          onClick: {
            description: "Function to be called when the card is clicked",
            control: { type: 'function' },
            action: fn(),
            type: { required: false },
            table: {
                type: { summary: "() => void" },
                defaultValue: { summary: "undefined" },
            }
          },
          sx: {
            description: "The system prop that allows defining system overrides as well as additional CSS styles",
            control: { type: 'object' },
            type: { required: false },
            table: {
                type: { summary: "object" },
                defaultValue: { summary: "{}" },
                detail: "This prop allows you to provide custom styles to the Card component using MUI's sx prop syntax."
            }
          },
    }
}

export const Playground = {
    args:{}
}

export const WithTags={
    args:{
        tags:[
            "Rawr",
            "A",
            "Tag"
        ]
    }
}

export const WithImageLink={
    args:{
        image: "https://siteboss.s3.amazonaws.com/media/1/ef6d051e-d528-4a3c-b7cc-ae67971ac9f9.svg"
    }
}

export const WithDescription={
    args:{
        description: "This is a description"
    }
}

export const WithName={
    args:{
        name: "This is a name"
    }
}

export const WithEverything={
    args:{
        name: "Name",
        description: "This is a description that is describing a thing and that thing is a nothing (or maybe it is a something and that something, of course, describes a thing), thereby making this a description...",
        image: "https://siteboss.s3.amazonaws.com/media/1/ef6d051e-d528-4a3c-b7cc-ae67971ac9f9.svg",
        tags:[
            "Rawr",
            "A",
            "Tag"
        ],
        onClick: fn()
    }
}