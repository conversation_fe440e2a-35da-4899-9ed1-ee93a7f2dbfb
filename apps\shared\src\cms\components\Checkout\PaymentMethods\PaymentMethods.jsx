import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, shallowEqual } from 'react-redux';
import { Container, Accordion, AccordionSummary, AccordionDetails, Typography, Paper } from '@mui/material';
import { ChevronRightOutlined as ExpandIcon, CreditCardOutlined as CreditCardIcon, ContactlessOutlined as TerminalIcon, LocalAtmOutlined as CashIcon, CardGiftcardOutlined as GiftCardIcon, AccountBalanceOutlined as CheckIcon, AdminPanelSettingsOutlined as ManagerDiscountIcon } from '@mui/icons-material';

import { ErrorBar, SuccessBar } from '../../../../components';
import ScanCard from './ScanCard';
import Cash from './Cash';
import Check from './Check';
import CreditCard from './CreditCard';
import GiftCard from './GiftCard';
import ManagerDiscount from './ManagerDiscount';

export const PaymentMethods = ({
    allowMultiplePayments = false,
    paymentMethods = ["scan", "card", "cash", "check", "giftCard", "managerDiscount"], 
    onPaymentProcess, 
    onPaymentChange,
    onPaymentReset, 
    loading: parentLoading = false,
    slotProps, 
    ...props
}) => {
    const { t } = useTranslation();
    
    const [expanded, setExpanded] = useState(false);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState(null);
    const [success, setSuccess] = useState(false);
    const [disabled, setDisabled] = useState(false);

    const cart = useSelector(state => state.cart, shallowEqual);
    const balance = (+cart?.totals?.total - +cart?.totals?.payments ) || 0;
    const justCash = cart?.transactions?.length > 0 ? cart.transactions.filter(a => a.transaction_payment_method_id === 2).length === cart.transactions.length : false;

    const methods = useMemo(() => {
        const cashDiscount = cart?.totals?.calculatedCashDiscount || 0;
        let amount = +balance;
        if (justCash && +cashDiscount > 0) amount += +cashDiscount;
        const _methods = [
            {
                id: "scanCard",
                paymentMethodId: 0,
                slug: 'pos:paymentMethods.scanCard',
                component: ScanCard,
                icon: TerminalIcon,
                removeCashDiscount: Boolean(justCash && +cashDiscount > 0),
                cashDiscount,
                amount,
            },
            {
                id: "card",
                paymentMethodId: 1,
                slug: 'pos:paymentMethods.creditCard',
                component: CreditCard,
                icon: CreditCardIcon,
                removeCashDiscount: Boolean(justCash && +cashDiscount > 0),
                cashDiscount,
                amount,
            },
            {
                id: "cash",
                paymentMethodId: 2,
                slug: 'pos:paymentMethods.cash',
                component: Cash,
                icon: CashIcon,
            },
            {
                id: "check",
                paymentMethodId: 3,
                slug: 'pos:paymentMethods.check',
                component: Check,
                icon: CheckIcon,
                removeCashDiscount: Boolean(justCash && +cashDiscount > 0),
                cashDiscount,
                amount,
            },
            {
                id: "giftCard",
                paymentMethodId: 4,
                slug: 'pos:paymentMethods.giftCard',
                component: GiftCard,
                icon: GiftCardIcon,
                removeCashDiscount: Boolean(justCash && +cashDiscount > 0),
                cashDiscount,
                amount,
            },
            {
                id: "managerDiscount",
                paymentMethodId: 5,
                slug: 'pos:paymentMethods.managerDiscount',
                component: ManagerDiscount,
                icon: ManagerDiscountIcon,
            },
        ];

        // filter out the methods that are not in the paymentMethods array and order them according to paymentMethods
        return paymentMethods.map(method => _methods.find(m => m.id === method)).filter(m => m);

    }, [paymentMethods, justCash, balance, cart?.totals?.calculatedCashDiscount]);

    const handleChange = useCallback(panel => (e, newExpanded) => {
        setExpanded(newExpanded ? panel : false);
        if (onPaymentReset) onPaymentReset();
    }, [onPaymentReset]);

    // when the payment updates we may want to update other components
    const handlePaymentChange = useCallback(fields => {
        if (onPaymentChange) onPaymentChange(fields);
    }, [onPaymentChange]);

    // process the payment
    const handlePay = useCallback(async fields => {
        try {
            setLoading(true);
            if (onPaymentProcess) {
                await onPaymentProcess(fields, res => {
                    if (res?.payments){
                        setExpanded(false);
                        setSuccess(true);
                    }
                });
            }
        } catch (error) {
            setErrors(error);
            console.log(error)
        } finally {
            setLoading(false);
        }
    }, [onPaymentProcess]);

    useEffect(() => {
        setLoading(parentLoading);
    }, [parentLoading]);

    return (
        <Container disableGutters sx={{position:"sticky", top: 0, m: 0, height: "fit-content"}} {...slotProps?.container}>
            <ErrorBar open={!!errors} message={errors} onClose={() => setErrors(null)} />
            <SuccessBar open={success} onClose={() => setSuccess(false)} />
            {methods.map(method => (
                <Accordion 
                    key={method.id} 
                    expanded={expanded === method.id} 
                    onChange={handleChange(method.id)}
                    slotProps={{transition: { unmountOnExit: true }}}
                    variant="outlined"
                    disabled={loading || disabled || balance <= 0}
                    disableGutters
                    {...slotProps?.accordion}
                    sx={{
                        ...slotProps?.accordion?.sx,
                        '&.MuiAccordion-root': {
                            mb: 1,
                        },
                        '&.MuiPaper-root.Mui-expanded': {
                            backgroundColor: theme => theme.palette.background.paper,
                            backgroundImage: 'none',
                        },
                    }}
                >
                    <AccordionSummary 
                        id={`panel${method.id}-header`} 
                        aria-controls={`panel${method.id}-content`} 
                        expandIcon={<ExpandIcon />}
                        {...slotProps?.accordionSummary}
                        sx={{
                            ...slotProps?.accordionSummary?.sx,
                            '.MuiAccordionSummary-content': {
                                py: 1,
                            },
                        }}
                    >
                        <>
                            <method.icon fontSize="small" sx={{mr: 1.5}} />
                            <Typography variant="h6" component="div" {...slotProps?.title}>{t(method.slug)}</Typography>
                        </>
                    </AccordionSummary>
                    <AccordionDetails sx={{bgcolor: 'background.paper'}} {...slotProps?.accordionDetails}>
                        <method.component 
                            paymentMethod={method.id}
                            paymentMethodId={method.paymentMethodId}
                            amount={method?.amount || balance} 
                            loading={loading} 
                            onPaymentProcess={handlePay} 
                            onPaymentChange={handlePaymentChange} 
                            removeCashDiscount={method?.removeCashDiscount || false}
                            cashDiscount={method?.cashDiscount || 0}
                            {...slotProps?.[method.id]} 
                        />
                    </AccordionDetails>
                </Accordion>
            ))}
        </Container>
    );
}