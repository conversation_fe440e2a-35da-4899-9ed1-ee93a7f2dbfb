import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Typography, Stack, ListItem, ListItemText, ListItemAvatar, Chip } from '@mui/material';
import { ToolbarUserAvatar as Avatar } from '@siteboss-frontend/shared/components';
import { formatDate } from '@siteboss-frontend/shared/utils';

export const Item = ({ user, ...props }) => {
    const { t, language } = useOutletContext();

    return (
        <ListItem alignItems="flex-start" component="li" divider>
            <ListItemAvatar component="div">
                <Avatar size='md' variant="circular" hideStatus hideOptions userData={user} src={user.profile_img_path} label={user.first_name}/>
            </ListItemAvatar>
            <ListItemText component="div" disableTypography
                primary={
                    <Stack direction="row" useFlexGap spacing={1} alignItems='center'>
                        <Typography component="div" variant="subtitle2" sx={{ mr: 1 }}>
                            {user.first_name} {user.last_name}
                        </Typography>
                        <Chip
                            label={user.status}
                            size="small"
                            color={user.status === 'active' ? 'success' : 'default'}
                        />
                    </Stack>
                }
                secondary={
                    <Stack direction="column" useFlexGap spacing={0}>
                        <Typography component="span" variant="caption" color="text.secondary">
                            {user.role}
                        </Typography>
                        <Typography component="span" variant="caption" color="text.primary">
                            {user.email}
                        </Typography>
                        <Typography component="span" variant="caption" color="text.secondary">
                            {t('user:lastLogin')}: {formatDate(user.last_login, language)}
                        </Typography>
                    </Stack>
                }
            />
        </ListItem>
    );
};