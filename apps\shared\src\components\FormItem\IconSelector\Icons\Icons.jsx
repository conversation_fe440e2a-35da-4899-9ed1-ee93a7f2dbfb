import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Stack, TextField, InputAdornment, Box } from '@mui/material';
import { SearchOutlined as SearchIcon } from '@mui/icons-material';

import IconType from './IconType';
import IconList from './IconList';

export const Icons = ({
    onIconSelection,
    ...props
}) => {
    const { t } = useTranslation();

    const [iconStyle, setIconStyle] = useState('outlined');
    const [searchText, setSearchText] = useState('');

    const handleStyleChange = value => {
        setIconStyle(value);
    }

    const handleSearchChange = e => {
        setSearchText(e.target.value);
    }

    return (

        <Container maxWidth={false}>
            <Stack direction="row" spacing={1} useFlexGap>
                <Box sx={{width: theme => theme.sizes.menuWidth}}>
                    <IconType onStyleChange={handleStyleChange} />
                </Box>
                <Stack direction="column" spacing={1} useFlexGap sx={{width: '100%', flexGrow: 1}}>
                    <TextField
                        label={t("general:search")}
                        id="icon-search"
                        sx={{ my: 1 }}
                        fullWidth
                        onChange={handleSearchChange}
                        value={searchText}
                        slotProps={{
                            input: {
                                endAdornment: <InputAdornment position="end"><SearchIcon /></InputAdornment>,
                            },
                        }}
                    />
                    <IconList 
                        iconStyle={iconStyle} 
                        searchText={searchText}
                        onIconSelection={onIconSelection} 
                    />
                </Stack>
            </Stack>
        </Container>
    );
}