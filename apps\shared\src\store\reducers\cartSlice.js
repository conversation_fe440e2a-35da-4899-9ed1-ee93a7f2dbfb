import { createSlice } from '@reduxjs/toolkit';
import { uuid } from '../../utils/string';

const initial = {
	userId: null,
	registerId: null,
	shopId: null,
	registerGroupId: null,
	fakeOrderId: null,
	orderId: null,
	cart: [
		/*{
			productId: null,
			productVariantId: null,
			productCustomPrice: null,
			addons: [],
			eventId: null,
			forUserIds: [],	
			customFields: [],
			giftCardRecipient: {},
			qty: 1,	
			status: 1, // 1 = active, 0 = inactive (don't show)
		}*/
	],
	coupons: [],
	couponsApplied: [],
	totals: {
		subtotal: 0,
		shipping: 0,
		tax: 0,
		tip: 0,
		total: 0,
		payments: 0,
		priceAdjustments: [],
		calculatedCashDiscount: 0,
	},
	transactions: [],
	memo: null,
};

export const cartSlice = createSlice({
	name: 'cart',
	initialState: {...initial},
	reducers: {
        setInfo: (state, action) => {
			if (action.payload.hasOwnProperty('userId')) state.userId = action.payload.userId;
			if (action.payload.hasOwnProperty('registerId')) state.registerId = action.payload.registerId;
			if (action.payload.hasOwnProperty('shopId')) state.shopId = action.payload.shopId;
			if (action.payload.hasOwnProperty('registerGroupId')) state.registerGroupId = action.payload.registerGroupId;
			if (action.payload.hasOwnProperty('orderId')) state.orderId = action.payload.orderId;
			if (action.payload.hasOwnProperty('memo')) state.memo = action.payload.memo;
			if (action.payload.hasOwnProperty('fakeOrderId')) state.fakeOrderId = action.payload.fakeOrderId;
			if (action.payload.hasOwnProperty('coupons')) state.coupons = action.payload.coupons;
			if (action.payload.hasOwnProperty('couponsApplied')) state.couponsApplied = action.payload.couponsApplied;
			if (action.payload.hasOwnProperty('transactions')) state.transactions = action.payload.transactions;
			if (action.payload.hasOwnProperty('subtotal')) state.totals.subtotal = action.payload.subtotal;
			if (action.payload.hasOwnProperty('shipping')) state.totals.shipping = action.payload.shipping;
			if (action.payload.hasOwnProperty('tax')) state.totals.tax = action.payload.tax;
			if (action.payload.hasOwnProperty('tip')) state.totals.tip = action.payload.tip;
			if (action.payload.hasOwnProperty('total')) state.totals.total = action.payload.total;
			if (action.payload.hasOwnProperty('payments')) state.totals.payments = action.payload.payments;
			if (action.payload.hasOwnProperty('priceAdjustments')) state.totals.priceAdjustments = action.payload.priceAdjustments;
			if (action.payload.hasOwnProperty('calculatedCashDiscount')) state.totals.calculatedCashDiscount = action.payload.calculatedCashDiscount;
        },
		resetInfo: (state) => {
			state.userId = null;
			state.orderId = null;
			//state.fakeOrderId = null;
			//state.registerId = null;
			//state.registerGroupId = null;
			state.cart = [];
			state.coupons = [];
			state.couponsApplied = [];
			state.totals = {
				subtotal: 0,
				shipping: 0,
				tax: 0,
				tip: 0,
				total: 0,
				payments: 0,
				priceAdjustments: [],
				calculatedCashDiscount: 0,
			};
			state.memo = null;
			state.transactions = [];
		},
		removeFromCart: (state, action) => {
			if (!action.payload) return;
			state.cart = state.cart.filter(c => c.id !== action.payload);
		},
		duplicateCartRow: (state, action) => {
			if (!action.payload) return;
			const row = state.cart.find(c => c.id === action.payload);
			if (row){
				state.cart.push({
					...row,
					id: uuid(),
				});
			}
		},
		addToCart: (state, action) => {
			if (!Array.isArray(action.payload)) action.payload = [action.payload];
			let productId = null, 
				productName = null,
				productVariantId = null, 
				productCustomPrice = null,
				productMedia = null,
				variantMedia = null,
				eventMedia = null,
				qty = 1, 
				addons = null, 
				eventId = null,
				eventName = null,
				forUserIds = null, 
				customFields = null, 
				customFieldDefinition = null,
				giftCardRecipient = null, 
				memo = null,
				metadata= null,
				status = 1,
				tmpId = uuid();
			
			action.payload.forEach(p => {
				productId = null;
				productName = null;
				productVariantId = null;
				productCustomPrice = null;
				productMedia = null,
				variantMedia = null,
				eventMedia = null,
				qty = 1;
				addons = null;
				eventId = null;
				eventName = null;
				forUserIds = null;
				customFields = null;
				customFieldDefinition = null;
				giftCardRecipient = null;
				memo = null;
				metadata= null;
				status = 1;
				tmpId = uuid();

				if ('tmp_id' in p) tmpId = p.tmp_id;
				if ('productId' in p) productId = p.productId;
				if ('productName' in p) productName = p.productName;
				if ('productVariantId' in p) productVariantId = p.productVariantId;
				if ('qty' in p) qty = p.qty;
				if ('addons' in p) addons = p.addons;
				if ('productCustomPrice' in p) productCustomPrice = p.productCustomPrice;
				if ('eventId' in p) eventId = p.eventId;
				if ('eventName' in p) eventName = p.eventName;
				if ('forUserIds' in p) forUserIds = p.forUserIds;
				if ('customFields' in p) customFields = p.customFields;
				if ('giftCardRecipient' in p) giftCardRecipient = p.giftCardRecipient;
				if ('memo' in p) memo = p.memo;
				if ('metadata' in p) metadata = p.metadata;
				if ('status' in p) status = p.status;
				if ('productMedia' in p) productMedia = p.productMedia;
				if ('variantMedia' in p) variantMedia = p.variantMedia;
				if ('eventMedia' in p) eventMedia = p.eventMedia;

				// sometimes we need to update the cart straight from the backend's response, so we deal with those cases here
				if (!productId){
					if ('product_id' in p) productId = p.product_id;
					if ('product_name' in p) productName = p.product_name;
					if ('quantity' in p) qty = p.quantity;
					if ('variant_id' in p) productVariantId = p.variant_id;
					if ('giftcard' in p) giftCardRecipient = p.giftcard;
					if ('product_media' in p) productMedia = p.product_media;
					if ('variant_media' in p) variantMedia = p.variant_media;
					if ('addons' in p) {
						addons = p.addons?.map(a => ({
							id: a.variant_id,
							name: a.product_name,
							productId: a.product_id,
							variantId: a.variant_id,
							qty: a?.quantity || 1,
							price: +a.price,
							metadata: {
								order_item_id: a.order_item_id || a.id,
								id: productId,
								price: +p?.price || 0,
								total_paid: +p?.total_paid || 0,
								bill_interval: p?.bill_interval,
								bill_num_times: p?.bill_num_times,
								interval_quantity: p?.interval_quantity,
								pro_rate: p?.pro_rate,
								activation_fee: +p?.activation_fee || 0,
								bill_on_day: p?.bill_on_day,
								first_bill_after_x_cycles: p?.first_bill_after_x_cycles,	
								tmp_id: uuid(),	
							}
						})) || null;
					}
					
					if ('event' in p) {
						if (p.event?.event_id) {
							eventId = p.event.event_id;
							eventName = p.event.event_name;
							eventMedia = p.event.event_media;
						}
						if (p.event?.for_user_id) {
							let _forUserIds = p.event.for_user_id;
							let _forUserNames = p.event.for_user_name;
							if (!Array.isArray(p.event.for_user_id)) {
								_forUserIds = [p.event.for_user_id];
								_forUserNames = [p.event.for_user_name];
							}
							forUserIds = _forUserIds.map((u, i) => {
								const [firstName, lastName ] = _forUserNames?.[i]?.split(' ');
								return ({id: u, firstName, lastName});
							});
						}
						if (p.event?.custom_fields) customFields = p.event.custom_fields;
						if (p.event?.custom_field_definition) customFieldDefinition = p.event.custom_field_definition;
					}

					metadata = {
						order_item_id: p.order_item_id || p.id,
						id: productId,
						name: p?.product_name || 'ERROR',
						price: +p?.price || 0,
						total_paid: +p?.total_paid || 0,
						bill_interval: p?.bill_interval,
						bill_num_times: p?.bill_num_times,
						interval_quantity: p?.interval_quantity,
						pro_rate: p?.pro_rate,
						activation_fee: +p?.activation_fee || 0,
						bill_on_day: p?.bill_on_day,
						first_bill_after_x_cycles: p?.first_bill_after_x_cycles,
						tmp_id: tmpId,
						product_media: productMedia,
						variant_media: variantMedia,
						event_media: eventMedia,
						custom_fields: customFieldDefinition,
					}
				}
				
				if (productId && productVariantId && qty){
					state.cart.push({
						id: tmpId,
						productId,
						productName,
						productVariantId,
						productCustomPrice,
						qty,
						addons,
						eventId,
						eventName,
						forUserIds,
						customFields,
						giftCardRecipient,
						memo,
						metadata,
						status,
						productMedia,
						variantMedia,
						eventMedia,
					});
				}
			});
		},
		resetCart: (state) => {
			state.cart = [];
		},
		removeForUsers: (state) => {
			if (state.cart.length > 0){
				state.cart = state.cart.filter(c => !c.forUserIds || c.forUserIds.length === 0);
			}
		},
	},
});

export const { setInfo, resetInfo, addToCart, removeFromCart, duplicateCartRow, resetCart, removeForUsers } = cartSlice.actions;
export default cartSlice.reducer;