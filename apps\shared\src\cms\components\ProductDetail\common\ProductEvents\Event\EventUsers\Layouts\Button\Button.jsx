import React from 'react';
import { differenceInYears } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { Stack } from '@mui/material';

import UserAvatar from '../../../../../../../../../components/ToolbarButtons/UserAvatar';
import { ButtonWrapper, Caption } from '../../../../../../../common/pos';

export const Button = ({ items, selected, disabled, onSelect, slotProps, slots, ...props }) => {
    const { t } = useTranslation();
    
    return (
        <ButtonWrapper items={items} selected={selected} disabled={disabled} onClick={onSelect} slotProps={slotProps} slots={slots}>
            {items.map(item => (
                <Stack key={item.id} direction="row" spacing={2} useFlexGap justifyContent="flex-start">
                    <UserAvatar size='lg' variant="circular" hideStatus hideOptions userData={item} src={item.profile_img_path} label={item.first_name} component="div" />
                    <Stack direction="column" spacing={0} useFlexGap justifyContent="flex-start">
                        {item?.first_name && <Caption variant="subtitle1" component="span" text={item.first_name} bold sx={{alignSelf: 'flex-start'}} />}
                        {item?.role_name && <Caption variant="subtitle3" component="span" text={t(`user:relationships.${item.role_name.toLowerCase()}`, item.role_name)} sx={{alignSelf: 'flex-start'}} />}
                        {item?.dob && <Caption variant="subtitle2" component="span"  sx={{alignSelf: 'flex-start'}} text={
                            `${differenceInYears(new Date(), new Date(item.dob))} ${t("calendar:yearsOld")}`
                        } />}
                    </Stack>
                </Stack>
            ))}
        </ButtonWrapper>
    );
};