import { useCallback } from 'react';
import { useMediaQuery, useTheme } from '@mui/material';

// This hook is used to get the current breakpoint from MUI based on the contentWindow
export const useBreakpoint = ({contentWindow}) => {
    const theme = useTheme();
    const breakpoints = theme.breakpoints.keys;
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined});
    const [breakpoint] = breakpoints.filter(breakpoint => useMediaQuery(theme.breakpoints.only(breakpoint), {matchMedia: contentWindow ? contentWindow.matchMedia : undefined}));

    // get the closest breakpoint to the current one that has a value in the sizes object
    // sizes object should be an object like: {xs: 1, lg: 4, xl: 5}
    // if a breakpoint is not found, it will return the next closest breakpoint or undefined
    const getClosestBreakpoint = useCallback(sizes => {
        let idx = breakpoints.indexOf(breakpoint);
        while (idx >= 0) {
            const bp = breakpoints[idx];
            if (sizes?.[bp] !== undefined) return bp;
            idx--;
        }        
        return undefined;
    }, [breakpoints, breakpoint]);

    return {
        isMobile,
        breakpoints,
        currentBreakpoint: breakpoint,
        getClosestBreakpoint,
    };
}