import React from 'react';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './Layouts';
import { properties } from './properties';
import { useCore } from './useCore';

export const Core = ({
    id,
    title,
    titleVariant = 'h1',
    subtitle,
    subtitleVariant = 'subtitle2',
    body, // Changed from description
    bodyVariant = 'body1', // Added this
    videoUrl,
    mediaType,
    images,
    imageSize = {width: '100%', height: 200},
    autoplay = false,
    muted = false,
    controls = true,
    ctas = [],
    layoutId,
    slotProps = {
        cmsContainer: {},
        cmsStack: {},
        title: {},
        subtitle: {},
        body: {}, // Changed from description
        video: {},
        ctaContainer: {},
        cta: {},
        placeholder: {},
        gallery: {},
    },
    isBuilder = false,
    condition = null,
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, customCss, canRender, noContent, contentWindow } = prepareComponent({name: "core", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const { mainContent, slotProps: hookSlotProps, slots: hookSlots, Layout } = useCore({
        title,
        titleVariant,
        subtitle,
        subtitleVariant,
        body,
        bodyVariant,
        videoUrl,
        mediaType,
        images,
        imageSize,
        autoplay,
        muted,
        controls,
        ctas,
        layoutId,
        layouts,
        slotProps,
        isBuilder,
        contentWindow,
    });

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            {Object.keys(mainContent).length === 0 && !title && !subtitle && !body ? noContent :
                <Layout 
                    layoutId={layoutId}
                    type={mediaType}
                    slotProps={hookSlotProps}
                    slots={hookSlots}
                />
            }
            {children}
        </CmsContainer>
    );
}