
import { useOutletContext } from 'react-router-dom';
import { Box, Skeleton } from '@mui/material';
import { MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';
import {
    Error as ErrorIcon,
    FiberNew as NewIcon,
    LocalShipping as ShippingIcon,
    Done as DoneIcon,
    KeyboardReturn as ReturnIcon
} from '@mui/icons-material';

export const StatusTiles = ({
    statuses = [],
    counts = {},
    selectedStatus,
    onStatusSelect,
    loading = false
}) => {
    const { t } = useOutletContext();

    const getStatusIcon = (statusId) => {
        const iconMap = {
            invalid: <ErrorIcon />,
            new: <NewIcon />,
            fulfilling: <ShippingIcon />,
            shipped: <DoneIcon />,
            return: <ReturnIcon />
        };
        return iconMap[statusId] || <ErrorIcon />;
    };

    const handleCardClick = (statusId) => {
        onStatusSelect(statusId);
    };

    if (loading) {
        return (
            <Box sx={{ mb: 4 }}>
                <MetricCardGroup spacing={3}>
                    {[1, 2, 3, 4, 5].map((index) => (
                        <Skeleton
                            key={index}
                            variant="rectangular"
                            height={120}
                            sx={{ borderRadius: 2, width: '100%' }}
                        />
                    ))}
                </MetricCardGroup>
            </Box>
        );
    }

    return (
        <Box sx={{ mb: 4 }}>
            <MetricCardGroup spacing={3}>
                {statuses.map((status) => {
                    const count = counts[status.id] || 0;
                    const isSelected = selectedStatus === status.id;
                    return (
                        <MetricCard
                            key={status.id}
                            onClick={() => handleCardClick(status.id)}
                            title={t(status.slug)}
                            value={count.toLocaleString()}
                            icon={getStatusIcon(status.id)}
                            color={status.color}
                            sx={{
                                border: isSelected ? '2px solid' : 'none',
                                borderColor: 'primary.main',
                            }}
                        />
                    );
                })}
            </MetricCardGroup>
        </Box>
    );
};
