import React, { useState, useContext, useCallback, useEffect } from 'react';
import { differenceInYears } from 'date-fns';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Typography, FormGroup } from '@mui/material';

import FormItem from '../../../../../../../../components/FormItem';
import { WithExtraInfo } from '../../../../../../common/pos';
import { PosProductDetailContext } from '../../../../../../../hooks/PosProductDetailContext';
import { removeForUser, setCustomFields } from '../../../../../../../../store/reducers/currentShopItemSlice';

export const CustomFields = ({user, event, onClose, ...props}) => {
    const dispatch = useDispatch();
    const { t } = useTranslation();

    const { fullPage, goToPreviousView } = useContext(PosProductDetailContext) || {};
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);

    const [fields, setFields] = useState({});
    const [errors, setErrors] = useState(null);

    // validate required fields
    const validateFields = useCallback(() => {
        let valid = true;
        const requiredFields = event?.custom_fields?.filter(field => field.required);
        if (!requiredFields || requiredFields.length === 0) return valid;

        for (let i = 0; i < requiredFields.length; i++) {
            if (!fields[requiredFields[i].name]){
                setErrors(prev => ({
                    ...(prev || {}),
                    [requiredFields[i].name]: t("error:required"),
                }));
                valid = false;
            }
        }
        return valid;
    }, [fields, event]);

    const handleSubmit = useCallback((getOut = true) => {
        setErrors(null);
        const valid = validateFields();
        if (valid) {
            dispatch(setCustomFields({...fields, userId: user.id, eventId: event.id}));
            if (getOut) {
                if (fullPage && onClose) onClose();
                else goToPreviousView();
            }
            return valid;
        }
        return valid;
    }, [validateFields, goToPreviousView, user, dispatch, fields, event.id, onClose, fullPage]);

    // back click only if in modal mode
    const handleBackClick = useCallback(() => {
        const valid = handleSubmit(false);
        if (!valid && user.id) { // back button is pressed but validation failed, so we  remove the selected user from forUserIds
            dispatch(removeForUser(user.id));
        } 
        if (fullPage && onClose) onClose();
        else goToPreviousView();
    }, [handleSubmit, goToPreviousView, user, dispatch, onClose, fullPage]);


    const handleChange = useCallback(e => {
        setFields(prev => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    }, []);


    useEffect(() => {
        const _customFields = currentShopItem?.customFields?.find(a => +a.userId === +user.id);
        if (_customFields) setFields(_customFields);
    }, [currentShopItem?.customFields, user]);

    useEffect(() => {
        if (!user && !fullPage) goToPreviousView();
    }, [user, fullPage, goToPreviousView]);

    if (!user || !event) return null;

    return (
        <WithExtraInfo 
            title={
                <>
                    <Typography variant="subtitle2" component="span" sx={{alignSelf: 'flex-start'}}>
                        {event?.title}
                    </Typography>
                    <Typography variant={props.variant || "h4"} component="h1">
                        {`${user.first_name} ${user.last_name}`}
                    </Typography>
                </>
            } 
            subtitle={`${differenceInYears(new Date(), new Date(user.dob))} ${t("calendar:yearsOld")}`} 
            fullPage={fullPage} 
            goToPreviousView={handleBackClick} 
            onSave={handleSubmit}
        >
            <FormGroup>
                {event?.custom_fields?.map(field => (
                    <FormItem 
                        key={field.id}
                        label={field.placeholder_text}
                        required={Boolean(field.required)}
                        component={(field.custom_field_type_id === 2 && field.options?.length > 0) ? 'Select' : 'TextField'}
                        name={field.name}
                        options={field?.options?.length > 0 ? field.options : null}
                        margin="normal"
                        value={fields?.[field.name] || field.default_value}
                        onChange={handleChange}
                        errors={errors?.[field.name]}
                    />
                ))}
            </FormGroup>
        </WithExtraInfo>
    );
}