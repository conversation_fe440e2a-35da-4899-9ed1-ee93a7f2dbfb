import { useState, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
    Box,
    Typography,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Chip,
    IconButton,
    Tooltip,
    Paper
} from '@mui/material';
import {
    ExpandMore as ExpandMoreIcon,
    Visibility as ViewIcon,
    Store as StoreIcon
} from '@mui/icons-material';
import { formatDate, createCurrencyFormatter } from '@siteboss-frontend/shared/utils';
import { DataTable } from '@siteboss-frontend/shared/components';

export const OrdersList = ({
    data = null,
    selectedStatus = 'all',
    loading = false,
    onRefresh,
    onViewOrder
}) => {
    const { t, language, currency } = useOutletContext();
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    const [expandedGroups, setExpandedGroups] = useState(new Set());

    // Process and filter data based on selected status
    const processedData = useMemo(() => {
        if (!data || !data.orders) return [];

        return [{
            id: 'kkk',
            name: 'kkk',
            orders: data.orders,
            orderCount: data.orders.length,
            totalValue: data.orders.reduce((sum, order) => sum + (order.total_price || 0), 0)
        }].filter(group => group.orderCount > 0); // Only show groups with orders
    }, [data]);

    const handleGroupToggle = (groupId) => {
        setExpandedGroups(prev => {
            const newSet = new Set(prev);
            if (newSet.has(groupId)) {
                newSet.delete(groupId);
            } else {
                newSet.add(groupId);
            }
            return newSet;
        });
    };

    const getStatusColor = (status) => {
        const colorMap = {
            invalid: 'error',
            new: 'info',
            fulfilling: 'warning',
            shipped: 'success',
            return: 'secondary'
        };
        return colorMap[status] || 'default';
    };

    const handleViewOrder = useMemo(() => (orderId) => {
        // TODO: Implement order detail view
        // For now, we'll add a prop to handle this
        if (onViewOrder) {
            onViewOrder(orderId);
        }
    }, [onViewOrder]);

    // Define columns for DataTable (no group column since it's in accordion)
    const columns = useMemo(() => [
        {
            field: 'orderNumber',
            headerName: t('order:orderNumber'),
            width: 120,
            renderCell: (params) => (
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    #{params.row.id}
                </Typography>
            )
        },
        {
            field: 'date',
            headerName: t('order:date'),
            width: 120,
            renderCell: (params) => formatDate(new Date(params.row.created_at), language)
        },
        {
            field: 'customer',
            headerName: t('order:customer'),
            width: 200,
            flex: 1,
            renderCell: (params) => (
                params.row.customer?.name ||
                `${params.row.customer?.first_name} ${params.row.customer?.last_name}` ||
                t('general:unknown')
            )
        },
        {
            field: 'status',
            headerName: t('order:status'),
            width: 120,
            renderCell: (params) => (
                <Chip
                    label={t(`order:statuses.${params.row.status}`)}
                    color={getStatusColor(params.row.status)}
                    size="small"
                />
            )
        },
        {
            field: 'total',
            headerName: t('order:total'),
            width: 120,
            align: 'right',
            renderCell: (params) => currencyFormatter.format(params.row.total_price || 0)
        },
        {
            field: 'actions',
            headerName: t('general:actions'),
            width: 100,
            align: 'center',
            sortable: false,
            renderCell: (params) => (
                <Tooltip title={t('general:view')}>
                    <IconButton
                        size="small"
                        onClick={() => handleViewOrder(params.row.id)}
                    >
                        <ViewIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            )
        }
    ], [t, language, currencyFormatter, getStatusColor, handleViewOrder]);

    if (loading) {
        return (
            <DataTable
                rows={[]}
                columns={columns}
                loading={true}
                page={1}
                pageSize={10}
                totalRows={0}
                disableSearch={true}
            />
        );
    }

    if (!processedData.length) {
        return (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary">
                    {selectedStatus === 'all'
                        ? t('order:noOrdersFound')
                        : t('order:noOrdersForStatus', { status: t(`order:statuses.${selectedStatus}`) })
                    }
                </Typography>
            </Paper>
        );
    }

    return (
        <Box>
            <Typography variant="h5" sx={{ mb: 3, fontWeight: 'medium' }}>
                {t('order:ordersByGroup')}
                <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    ({processedData.length} {t('order:groups')})
                </Typography>
            </Typography>

            {processedData.map((group) => {
                const isExpanded = expandedGroups.has(group.id);

                return (
                    <Accordion
                        key={group.id}
                        expanded={isExpanded}
                        onChange={() => handleGroupToggle(group.id)}
                        sx={{ mb: 2 }}
                    >
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', pr: 2 }}>
                                <StoreIcon sx={{ mr: 2, color: 'primary.main' }} />
                                <Box sx={{ flexGrow: 1 }}>
                                    <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
                                        {group.name}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {t('order:merchant')}: {group.merchant?.name || t('general:unknown')}
                                    </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                    <Chip
                                        label={`${group.orderCount} ${t('order:orders')}`}
                                        color="primary"
                                        size="small"
                                    />
                                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                        {currencyFormatter.format(group.totalValue)}
                                    </Typography>
                                </Box>
                            </Box>
                        </AccordionSummary>

                        <AccordionDetails sx={{ pt: 0 }}>
                            <DataTable
                                rows={group.orders}
                                columns={columns}
                                loading={false}
                                page={1}
                                pageSize={group.orders.length} // Show all orders in group
                                totalRows={group.orders.length}
                                disableSearch={true}
                                checkboxSelection={false}
                                disableRowSelectionOnClick={true}
                                sx={{ height: 'auto', minHeight: 300 }}
                                onRowClick={(params) => handleViewOrder(params.row.id)}
                            />
                        </AccordionDetails>
                    </Accordion>
                );
            })}
        </Box>
    );
};
