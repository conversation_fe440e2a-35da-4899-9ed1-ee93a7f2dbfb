import CmsIcon from '../../utils/CmsIcon';

/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/
const icons = [
    CmsIcon({elements: [
        {type: 'text', width: '100%'},
    ]}),
    CmsIcon({elements: [
        {type: 'text', width: '2px', m: 'auto', height: '100%'},
    ]}),
];

export const widgetIcon = () => icons[0];
export const layouts = [
    {
        id: 1,
        name: 'Horizontal',
        icon: icons[0],
        slotProps: {
            divider: {
                orientation: 'horizontal',
            }
        }
    },
    {
        id: 2,
        name: 'Vertical',
        icon: icons[1],
        slotProps: {
            divider: {
                orientation: 'vertical',
            }
        }
    },    
];