import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../KeycloakProvider/tenantConfig";
import { setInfo } from '../../../store/reducers/fixedDataSlice';

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

const apiParams = {
    enableCache: true,
    params: {endpoint: '/countries', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}
};

export const useCountriesData = () => {
    const dispatch = useDispatch();
    const loaded = useSelector(state => state.fixedData.loaded.countries);
    const token = useSelector(state => state.user.token);

    const { fetchData, loading } = useApi(apiParams);

    useEffect(() => {
        if (!loaded && token) {
            fetchData()
                .then(result => {
                    // TRANSFORM THE DATA: REMOVE THIS WHEN WE START STORING THINGS THE RIGHT WAY... this is slow, sloppy, and wrong.
                    let countries = [];
                    if (result?.data) {
                        countries = result.data.map(country => ({
                            country_id: country.id,
                            id: country.country_code2,
                            name: country.country_name,
                            code: country.country_code2,
                        }));
                    }
                    dispatch(setInfo({ countries }));
                })
                .catch(error => {
                    console.error('Error fetching countries:', error);
                });
        }
    }, [loaded, fetchData, dispatch, token]);

    return { loading };
};
