// Role IDs
export const ROLES = {
    MASTER_ADMIN: 1,     // SiteBoss Master Admin - Access to all sites and all functions
    SUPER_ADMIN: 2,      // SiteBoss Super Admin - Access to all sites and all functions except altering Master Admin
    COMPANY_OWNER: 3,    // Company Owner - Access to all modules for company
    COMPANY_ADMIN: 4,    // Company Admin - Access to all modules and info for a single company
    STAFF: 5,            // Staff - Access to assigned modules only
    NON_STAFF_MANAGER: 6, // Non-staff Manager - Access to assigned modules only
    PATRON: 7            // Patron - Access to backend and all modules granted by subscriptions
};

// Module IDs for permissions
export const MODULES = {
    DASHBOARD_ADMIN: 100,
    DASHBOARD_COMPANY: 101,
    DASHBOARD_STAFF: 102,
    DASHBOARD_PATRON: 103,
    SALES_REPORTS: 110,
    USER_MANAGEMENT: 120,
    CONTENT_MANAGEMENT: 130,
    INVENTORY_MANAGEMENT: 140,
    SITE_MANAGEMENT: 150,
    ANALYTICS: 160,
    <PERSON><PERSON><PERSON>AR: 170,
    WEATHER: 180,
    NOTIFICATIONS: 190,
    TASKS: 200
};

// Default widgets by role
export const DEFAULT_WIDGETS_BY_ROLE = {
    [ROLES.MASTER_ADMIN]: [
        { id: 'analytics', type: 'analytics', required: true },
        { id: 'users', type: 'users', required: true },
        { id: 'sites', type: 'sites', required: true },
        { id: 'sales', type: 'sales', required: false },
        { id: 'calendar', type: 'calendar', required: false },
        { id: 'weather', type: 'weather', required: false },
        { id: 'tasks', type: 'tasks', required: false },
        { id: 'notifications', type: 'notifications', required: false }
    ],
    [ROLES.SUPER_ADMIN]: [
        { id: 'analytics', type: 'analytics', required: true },
        { id: 'users', type: 'users', required: true },
        { id: 'sites', type: 'sites', required: true },
        { id: 'sales', type: 'sales', required: false },
        { id: 'calendar', type: 'calendar', required: false },
        { id: 'weather', type: 'weather', required: false },
        { id: 'tasks', type: 'tasks', required: false },
        { id: 'notifications', type: 'notifications', required: false }
    ],
    [ROLES.COMPANY_OWNER]: [
        { id: 'analytics', type: 'analytics', required: true },
        { id: 'sales', type: 'sales', required: true },
        { id: 'users', type: 'users', required: true },
        { id: 'inventory', type: 'inventory', required: false },
        { id: 'calendar', type: 'calendar', required: false },
        { id: 'weather', type: 'weather', required: false },
        { id: 'tasks', type: 'tasks', required: false },
        { id: 'notifications', type: 'notifications', required: false }
    ],
    [ROLES.COMPANY_ADMIN]: [
        { id: 'analytics', type: 'analytics', required: true },
        { id: 'sales', type: 'sales', required: true },
        { id: 'users', type: 'users', required: false },
        { id: 'inventory', type: 'inventory', required: false },
        { id: 'calendar', type: 'calendar', required: false },
        { id: 'weather', type: 'weather', required: false },
        { id: 'tasks', type: 'tasks', required: false },
        { id: 'notifications', type: 'notifications', required: false }
    ],
    [ROLES.STAFF]: [
        { id: 'tasks', type: 'tasks', required: true },
        { id: 'calendar', type: 'calendar', required: true },
        { id: 'notifications', type: 'notifications', required: true },
        { id: 'weather', type: 'weather', required: false }
    ],
    [ROLES.NON_STAFF_MANAGER]: [
        { id: 'tasks', type: 'tasks', required: true },
        { id: 'calendar', type: 'calendar', required: true },
        { id: 'notifications', type: 'notifications', required: true },
        { id: 'weather', type: 'weather', required: false }
    ],
    [ROLES.PATRON]: [
        { id: 'subscriptions', type: 'subscriptions', required: true },
        { id: 'calendar', type: 'calendar', required: false },
        { id: 'weather', type: 'weather', required: false },
        { id: 'notifications', type: 'notifications', required: false }
    ]
};

// Default grid layouts by role
export const DEFAULT_GRID_LAYOUTS = {
    [ROLES.MASTER_ADMIN]: { rows: 4, cols: 12 },
    [ROLES.SUPER_ADMIN]: { rows: 4, cols: 12 },
    [ROLES.COMPANY_OWNER]: { rows: 3, cols: 12 },
    [ROLES.COMPANY_ADMIN]: { rows: 3, cols: 12 },
    [ROLES.STAFF]: { rows: 2, cols: 12 },
    [ROLES.NON_STAFF_MANAGER]: { rows: 2, cols: 12 },
    [ROLES.PATRON]: { rows: 2, cols: 12 }
};
