import React from 'react';
import { UrlChips } from './UrlChips';
import { action } from 'storybook/actions';

export default {
  title: 'CMS/Components/Url Chips - Claude',
  component: UrlChips,
  tags: ['autodocs'],
  argTypes: {
    chips: {
        description: "An array of chip objects or strings",
        control: 'object',
        type: { required: true },
        table: {
            type: {
                summary: 'array'
            },
            defaultValue: {
                summary: '[]'
            },
            detail: `
                Array of objects: [{id, label, slug, color, chipProps}]
                or array of strings: ['tag1', 'tag2']
            `
        }
    },
    onClick: {
        description: "Function called when a chip is clicked",
        action: action('chip-click'),
        table: {
            type: {
                summary: 'function'
            }
        }
    },
    onDelete: {
        description: "Function called when a chip's delete button is clicked",
        action: action('chip-delete'),
        table: {
            type: {
                summary: 'function'
            }
        }
    }
  }
};

// Playground story with only required props
export const Playground = {
  args: {
    chips: ['React', 'Storybook', 'MUI']
  }
};

// Story to illustrate different chip types
export const MixedChipTypes = {
  args: {
    chips: [
      { id: 1, label: 'Object Chip', color: 'primary' },
      'String Chip',
      { id: 2, slug: 'translated.chip', color: 'secondary' }
    ],
    onClick: action('chip-click'),
    onDelete: action('chip-delete')
  }
};

// Story to showcase different colors
export const ColorfulChips = {
  render: () => (
    <UrlChips
      chips={[
        { id: 1, label: 'Default', color: 'default' },
        { id: 2, label: 'Primary', color: 'primary' },
        { id: 3, label: 'Secondary', color: 'secondary' },
        { id: 4, label: 'Error', color: 'error' },
        { id: 5, label: 'Info', color: 'info' },
        { id: 6, label: 'Success', color: 'success' },
        { id: 7, label: 'Warning', color: 'warning' }
      ]}
      onClick={action('chip-click')}
    />
  )
};

// Story to demonstrate chips with delete functionality
export const DeletableChips = {
  args: {
    chips: [
      { id: 1, label: 'Deletable Chip 1' },
      { id: 2, label: 'Deletable Chip 2' },
      { id: 3, label: 'Deletable Chip 3' }
    ],
    onDelete: action('chip-delete')
  }
};

// A whimsical story
export const EmojiChips = {
  args: {
    chips: [
      { id: 1, label: '😊 Happy', color: 'success' },
      { id: 2, label: '😢 Sad', color: 'info' },
      { id: 3, label: '😡 Angry', color: 'error' },
      { id: 4, label: '😎 Cool', color: 'primary' }
    ],
    onClick: action('emoji-chip-click')
  }
};