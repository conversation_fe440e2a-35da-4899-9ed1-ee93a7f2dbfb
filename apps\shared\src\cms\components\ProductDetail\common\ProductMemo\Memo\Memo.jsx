import React, { useCallback, useContext, useEffect, useState } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Grid2 } from '@mui/material';

import FormItem from '../../../../../../components/FormItem';
import { WithExtraInfo } from '../../../../common/pos';
import { PosProductDetailContext } from '../../../../../hooks/PosProductDetailContext';

export const Memo = ({product, onSave, onClose, ...props}) => {
    const { t } = useTranslation();
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);

    const { fullPage, goToPreviousView } = useContext(PosProductDetailContext) || {};
    const [memo, setMemo] = useState(currentShopItem?.memo || "");

    const handleSave = useCallback(_ => {
        if (onSave) {
            if (memo) onSave(memo);
            if (fullPage && onClose) onClose();
            else goToPreviousView();
        }
    }, [onSave, memo, fullPage, goToPreviousView, onClose]);


    useEffect(() => {
        if (!product && !fullPage) goToPreviousView();
    }, [product, fullPage, goToPreviousView]);

    if (!product) return null;

    return (
        <WithExtraInfo 
            title={`${product.name}`} 
            subtitle={t("pos:preferences")}
            fullPage={fullPage} 
            goToPreviousView={fullPage ? onClose : goToPreviousView} 
            onSave={handleSave}
        >
            <Grid2 container spacing={2}>
                <Grid2 size={{xs: 12, lg: "grow"}}>
                    <FormItem 
                        name='memo'
                        type='text'
                        label={t("pos:itemMemo")}
                        required
                        value={memo}
                        component="TextField"
                        margin="normal"
                        minRows={4}
                        onChange={e=>setMemo(e.target.value)}
                    />
                </Grid2>
            </Grid2>
        </WithExtraInfo>
    );
}