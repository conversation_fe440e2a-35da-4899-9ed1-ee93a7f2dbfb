import React, { useCallback } from 'react';
import { Box } from '@mui/material';
import { FormItem } from '../../../../../components/FormItem';

export const CustomAmount = React.forwardRef(({ label, item, amount, onSelect, slotProps, ...props }, ref) => {
    const handleBlur = useCallback(e => {
        if (+e.target.value) onSelect({...item, value: e.target.value});
    }, [onSelect, item]);
    
    return (
        <Box ref={ref} sx={{width:"80px", ...slotProps?.container?.sx}}>
            <FormItem 
                name="custom_amount"
                fullWidth
                required
                hiddenLabel={!label}
                size="large"
                variant="standard"
                color="secondary"
                component="MoneyField"
                aria-label="pos:amount"
                label={label}
                min={1}
                onBlur={handleBlur}
                value={(+amount).toFixed(2)}
                slotProps={{
                    inputLabel: { shrink: true },
                }}
                {...slotProps?.formItem}
            />
        </Box>
    );
});