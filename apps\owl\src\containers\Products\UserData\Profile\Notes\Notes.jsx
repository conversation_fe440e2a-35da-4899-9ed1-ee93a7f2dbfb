import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Box, IconButton, Menu, MenuItem, Tooltip } from '@mui/material';
import { CheckOutlined as CheckIcon } from '@mui/icons-material';
import { InlineEditor, SuccessBar } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import List from './List';

const options = [
    {id: 1, slug: 'public'},
    {id: 2, slug: 'adminOnly'},
    {id: 3, slug: 'author'},
];

export const Notes = ({userData, ...props}) => {
    const { t } = useOutletContext();

    const status = useRef(null);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/user/notes/${userData?.id}`, method: 'GET'}},
        {params: {endpoint: `/user/notes/add`, method: 'POST'}},
    ], [userData?.id]);

    const { fetchData, data, ErrorBar, LoadingBar } = useApi(apiParams[0]);
    const { fetchData:saveData, ErrorBar:saveErrorBar, LoadingBar:saveLoadingBar  } = useApi(apiParams[1]);
    
    const [success, setSuccess] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    
    const handleClick = e => setAnchorEl(e.currentTarget);
    
    const handleClose = useCallback(() => {
        setAnchorEl(null);
        setSuccess(false);
        status.current = null;
    }, []);

    const handleMenuItemClick = useCallback((st, onSave) => e => {
        status.current = st;
        onSave();
        handleClose();
    }, [handleClose]);

    const handleSave = useCallback(async (data) => {
        if (data.id && data.text && status.current) {
            const result = await saveData({user_id: userData.id, note: data.text, status: status.current});
            if (result.data) {
                fetchData();
                setSuccess(true);
            }
        }
    }, [saveData, userData.id, fetchData]);

    useEffect(() => { 
        fetchData() 
    }, [fetchData]);

    return (
        <Box>
            {success &&
                <SuccessBar message={t(`success:saved`)} onClose={()=>setSuccess(false)} />
            }
            <ErrorBar />
            <LoadingBar />
            <InlineEditor 
                id="notes"
                title={t('user:notes')}
                value={t('user:addNote')}
                textarea={1}
                sx={{mt: 0}} 
                onSave={(text, id) => handleSave({text, id})}
                slots={{
                    saveButton: onSave => (
                        <>
                            <Tooltip title={t("general:save")}>
                                <IconButton 
                                    id="save-button"
                                    aria-controls={open ? 'save-menu' : undefined}
                                    aria-haspopup="true"
                                    aria-expanded={open ? 'true' : undefined}
                                    onClick={handleClick}                    
                                    size="small"
                                    disabled={false /*loading*/}
                                >
                                    <CheckIcon fontSize='inherit' />
                                </IconButton>
                            </Tooltip>
                            <Menu
                                id="save-menu"
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                MenuListProps={{'aria-labelledby': 'save-button'}}
                            >
                                {options.map(option => (
                                    <MenuItem key={`save-menu-option-${option.id}`} onClick={handleMenuItemClick(option.id, onSave)}>
                                        {t(`user:noteStatus.${option.slug}`)}
                                    </MenuItem>
                                ))}
                            </Menu>
                        </>
                    )
                }}
            />
            <List notes={data} />
        </Box>
    );
}