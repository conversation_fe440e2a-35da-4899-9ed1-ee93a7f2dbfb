import React, { Suspense } from 'react';
import { Skeleton, Stack } from '@mui/material';
import { useFormItem } from './useFormItem';

/*
This is where we render form fields
*/
export const FormItem = ({
    component, // the component to use for the field, it could be a TextField, a select, or a custom component (TextField, PasswordField, Checkbox, TileButton, TileButtonGroup)
    type, // the type of the field, it could be text, email, password, or something custom if its a special component
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors, // the errors for the field
    loading, // the loading status of the form so we can disable the fields
    value, // the value of the field
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onBlur, // the function to be called when the field is blurred
    options, // the options for the select field
    parentRef, // the ref of the parent component
    InputProps, // additional props to pass to the input field
    ...props
}) => {
    const { Component, componentProps } = useFormItem({
        component,
        type,
        label,
        name,
        required,
        errors,
        loading,
        value,
        onChange,
        onBlur,
        options,
        parentRef,
        InputProps,
        ...props
    });

    if (!component) return null;

    return (
        <Suspense fallback={
            <Stack direction="column" spacing={0.5} useFlexGap sx={{width: '100%'}}>
                <Skeleton animation="wave" variant="text" width={60} height={4} />
                <Skeleton animation="wave" variant="rounded" height={45} width="100%" />
            </Stack>
        }>
            <Component {...componentProps} />
        </Suspense>
    );
}