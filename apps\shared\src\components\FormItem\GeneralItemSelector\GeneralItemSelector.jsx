import React, { useRef, useEffect } from 'react';
import { Box } from '@mui/material';

import ItemSelector from '../../ItemSelector';
import { useFetch } from './useFetch';

export const GeneralItemSelector = ({ 
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors: initialErrors, // the errors for the field
    disabled, // if the field is disabled
    loading: loadingState, // the loading status of the form so we can disable the fields
    value: initialValue, // the value of the field
    onChange, // the function to be called when the field value changes
    onBlur, // the function to be called when the field is blurred
    type = "chip", // the component type

    items, // optional array of items to display, if not provided, it will fetch from the server
    selectedColor,  // color of selected items
    color, // color of items
    variant, // variant of the component

    fetchParams, // object with parameters to be sent to the fetch function, for example: {endpoint: "/event", data: {event_id: 1, status_id: 2}}
    valueFormatter, // function to format the value to be displayed

    ...props 
}) => {
    const ref = useRef(null);

    const { 
        data,
        loading,
        fetchData,
        ErrorBar,
        handleChange,
        selectedItems,
    } = useFetch({ name, onChange, onBlur, initialValue, fetchParams, valueFormatter });

    useEffect(() => {
        if (!items) fetchData(/*{___: new Date().getTime()}*/);
    }, [items, fetchData]);
    return (
        <Box ref={ref} sx={{position: 'relative', my: 2, ...props?.sx}}>
            <ErrorBar />
            {/*loading && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: -16}} />*/}

            <ItemSelector
                type={type}
                items={items || data}
                selectedItems={selectedItems}
                onSelect={handleChange}
                selectedColor={selectedColor}
                color={color}
                variant={variant}
                disabled={loading || loadingState || disabled}
                size="small"
                errors={initialErrors}
                label={label}
                name={name}
            />
        </Box>
    );
};