import { useState, useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Stack, Button, Tab } from '@mui/material';
import { Add as AddIcon, SettingsOutlined as ConfigIcon, EmailOutlined as TemplatesIcon } from '@mui/icons-material';
import { DataTable, Modal } from '@siteboss-frontend/shared/components';
import { TabContext, TabPanel, TabList } from '@mui/lab';

import BasicInfo from '../BasicInfo';
import { useEmailTemplates } from './useEmailTemplates';

export const EmailTemplates = ({ value, errors, onChange, loading, merchantId }) => {
    const { t, isMobile } = useOutletContext();
    const [selectedTab, setSelectedTab] = useState('config');

    const {
        data,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: apiLoading,
        errorBars,
        fieldDefinitions,
        fieldsByTab,
        selected,
        setSelected,
        handleRowSelection,
        handleResetForm,
    } = useEmailTemplates({ merchantId, onMainFormChange: onChange });

    const handleTabChange = useCallback((_, newValue) => {
        setSelectedTab(newValue);
    }, []);



    return (
        <TabContext value={selectedTab}>
            <Stack spacing={2} direction="column" useFlexGap>
                <TabList onChange={handleTabChange} aria-label="email configuration tabs">
                    <Tab label={t('emailTemplates:configuration')} value="config" icon={<ConfigIcon />} />
                    <Tab label={t('emailTemplates:title')} value="templates" icon={<TemplatesIcon />} />
                </TabList>

                <TabPanel value="config" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.config}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="templates" sx={{ p: 0 }}>
                    <Stack spacing={2} direction="column" useFlexGap>
                        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                            <Button
                                variant="contained"
                                color="secondary"
                                startIcon={<AddIcon />}
                                loading={loading || apiLoading}
                                onClick={e => {
                                    handleResetForm();
                                    handleToggleModal(true)(e);
                                }}
                            >
                                {t('emailTemplates:addEmailTemplate')}
                            </Button>
                        </div>

                        <DataTable
                            rows={data || value || []}
                            columns={columns}
                            page={page}
                            pageSize={pageSize}
                            totalPages={totalPages}
                            setPage={setPage}
                            setPageSize={setPageSize}
                            onExpand={handleEditItem}
                            onDelete={handleDeleteItem}
                            loading={loading || apiLoading}
                            disableSearch
                            hideFooterSelectedRowCount
                            onRowSelectionModelChange={model => handleRowSelection(model)}
                            rowSelectionModel={selected.map(s => s.id)}
                            setSelected={setSelected}
                        />

                        <Modal
                            open={modalOpen}
                            onClose={handleToggleModal(false)}
                            title={formData?.id ? t('emailTemplates:editEmailTemplate') : t('emailTemplates:addEmailTemplate')}
                            maxWidth="md"
                            fullScreen={isMobile}
                        >
                            {errorBars.map((ErrorBar, i) => ErrorBar && <ErrorBar key={i} />)}

                            <BasicInfo
                                fields={fieldDefinitions}
                                values={formData}
                                errors={errors}
                                onChange={handleChange}
                                loading={loading || apiLoading}
                            />
                            <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mt: 2 }}>
                                <Button
                                    variant="outlined"
                                    onClick={handleToggleModal(false)}
                                    loading={loading || apiLoading}
                                >
                                    {t('general:cancel')}
                                </Button>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    onClick={handleSaveItem}
                                    loading={loading || apiLoading}
                                >
                                    {formData?.id ? t('general:update') : t('general:add')}
                                </Button>
                            </Stack>
                        </Modal>
                    </Stack>
                </TabPanel>
            </Stack>
        </TabContext>
    );
};
