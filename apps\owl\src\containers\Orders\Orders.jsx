import { useState, useEffect } from 'react';
import { useOutletContext, useNavigate, useParams } from 'react-router-dom';
import { Container, Box } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import StatusTiles from './StatusTiles';
import OrdersList from './OrdersList';
import OrderDetails from './OrderDetails';

// Order status constants
const ORDER_STATUSES = [
    { id: 'invalid', slug: 'order:statuses.invalid', color: 'error', icon: 'error' },
    { id: 'new', slug: 'order:statuses.new', color: 'info', icon: 'fiber_new' },
    { id: 'fulfilling', slug: 'order:statuses.fulfilling', color: 'warning', icon: 'local_shipping' },
    { id: 'shipped', slug: 'order:statuses.shipped', color: 'success', icon: 'done' },
    { id: 'return', slug: 'order:statuses.return', color: 'secondary', icon: 'keyboard_return' },
];

const apiParams = [
    {params: {endpoint: `/order`, method: 'POST', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'desc',
    }}},
];

export const Orders = () => {
    const { t } = useOutletContext();
    const navigate = useNavigate();
    const { id } = useParams();

    const [selectedStatus, setSelectedStatus] = useState('all');
    const [statusCounts, setStatusCounts] = useState({});
    const [refreshCounter, setRefreshCounter] = useState(0);

    // API hooks for fetching data
    const { fetchData: fetchOrders, data: ordersData, loading: ordersLoading, ErrorBar: OrdersErrorBar } = useApi(apiParams[0]);

    // Fetch initial data
    useEffect(() => {
        fetchOrders();
    }, [fetchOrders, refreshCounter]);


    const handleStatusSelect = (statusId) => {
        setSelectedStatus(statusId);
    };

    const handleRefresh = () => {
        setRefreshCounter(prev => prev + 1);
    };

    const handleViewOrder = (orderId) => {
        navigate(`/orders/${orderId}`);
    };

    // If we have an ID, render the order details page
    if (id) {
        return <OrderDetails id={id} />;
    }

    // Otherwise render the orders list page
    return (
        <Container>
            <Title
                title={t('order:ordersDashboard')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('order:ordersDashboard')}
                ]}
            />

            <OrdersErrorBar />

            {/* Status Tiles Section */}
            <StatusTiles
                statuses={ORDER_STATUSES}
                counts={statusCounts}
                selectedStatus={selectedStatus}
                onStatusSelect={handleStatusSelect}
            />

            {/* Orders List Section */}
            <Box>
                <OrdersList
                    data={ordersData}
                    selectedStatus={selectedStatus}
                    loading={ordersLoading}
                    onRefresh={handleRefresh}
                    onViewOrder={handleViewOrder}
                />
            </Box>
        </Container>
    );
};
