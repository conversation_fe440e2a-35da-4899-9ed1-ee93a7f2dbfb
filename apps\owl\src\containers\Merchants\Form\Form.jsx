import React, { useRef } from 'react';
import { useOutletContext, useParams, useNavigate } from 'react-router-dom';
import { Container, Button, Box, Paper } from '@mui/material';
import { SuccessBar, Title } from '@siteboss-frontend/shared/components';

import { useFormLogic } from './useFormLogic';
import Tabs from './Tabs';

export const Form = (props) => {
    const { t, isMobile } = useOutletContext();
    const { id } = useParams();
    const navigate = useNavigate();
    const ref = useRef(null);

    const isEdit = !!id;

    const handleSuccess = () => {
        navigate('/merchants');
    };

    const {
        errors,
        success,
        loading,
        merchantErrors,
        handleChange,
        handleSubmit,
        ErrorBar,
        LoadingBar,
        MerchantErrorBar,
        values,
    } = useFormLogic({loading: false, id});

    if (merchantErrors) {
        return (
            <Container>
                <MerchantErrorBar />
            </Container>
        );
    }

    return (
        <Container>
            <Title
                title={isEdit ? t('merchant:editMerchant') : t('merchant:newMerchant')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'}, 
                    {title: t('merchant:merchants'), to: '/merchants'}, 
                    {title: isEdit ? t('merchant:editMerchant') : t('merchant:newMerchant')}
                ]}
            />
            
            <LoadingBar />
            <ErrorBar />
            {success && <SuccessBar message={t(`merchant:${id ? 'updated' : 'saved'}`)} onClose={handleSuccess} />}

            <Tabs
                contentRef={ref}
                values={values}
                errors={errors}
                onChange={handleChange}
                loading={loading}
                merchantId={id}
            />

            <Box component={Paper} elevation={0} square sx={{
                display: 'flex',
                justifyContent: 'space-between',
                position: isMobile ? undefined : 'sticky',
                bottom: isMobile ? undefined : 0,
                boxShadow: 0,
                width: '100%',
                zIndex: theme => theme.zIndex.appBar,
                pb: isMobile ? theme => theme.sizes.headerHeight : undefined 
            }}>
                <Button variant='contained' color='primary' size='large' fullWidth sx={{mt:1}} loading={loading} onClick={handleSubmit}>
                    {t('general:save')}
                </Button>
            </Box>

        </Container>
    );
}
