import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

export const useShippingAccount = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();

    const fields = useMemo(() => [
        {
            name: 'carrier', 
            label: 'shipping:carrier', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'fedex', slug: 'shipping:carriers.fedex' },
                { id: 'ups', slug: 'shipping:carriers.ups' },
                { id: 'usps', slug: 'shipping:carriers.usps' },
                { id: 'dhl', slug: 'shipping:carriers.dhl' },
                { id: 'ontrac', slug: 'shipping:carriers.ontrac' },
                { id: 'lasership', slug: 'shipping:carriers.lasership' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'fulfillment_type', 
            label: 'shipping:fulfillmentType', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'warehouse', slug: 'shipping:fulfillmentTypes.warehouse' },
                { id: 'dropship', slug: 'shipping:fulfillmentTypes.dropship' },
                { id: 'thirdparty', slug: 'shipping:fulfillmentTypes.thirdParty' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'fulfillment_location', 
            label: 'shipping:fulfillmentLocation', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'main_warehouse', slug: 'shipping:fulfillmentLocations.mainWarehouse' },
                { id: 'secondary_warehouse', slug: 'shipping:fulfillmentLocations.secondaryWarehouse' },
                { id: 'vendor_location', slug: 'shipping:fulfillmentLocations.vendorLocation' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'manifest_location', 
            label: 'shipping:manifestLocation', 
            required: true, 
            value: '', 
            component: "Select", 
            options: [
                { id: 'warehouse_a', slug: 'shipping:manifestLocations.warehouseA' },
                { id: 'warehouse_b', slug: 'shipping:manifestLocations.warehouseB' },
                { id: 'distribution_center', slug: 'shipping:manifestLocations.distributionCenter' }
            ],
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'account_number', 
            type: 'text',
            label: 'shipping:accountNumber', 
            required: true, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'third_party_bill_to', 
            type: 'text',
            label: 'shipping:thirdPartyBillTo', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'allow_alcohol', 
            label: 'shipping:allowAlcohol', 
            required: false, 
            value: false, 
            component: "Switch", 
            margin: "normal", 
            rowSize: {xs: 12}
        },
        {
            name: 'api_key', 
            type: 'text',
            label: 'shipping:apiKey', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'secret_key', 
            label: 'shipping:secretKey', 
            required: false, 
            value: '', 
            component: "PasswordField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'tracking_api_key', 
            type: 'text',
            label: 'shipping:trackingApiKey', 
            required: false, 
            value: '', 
            component: "TextField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        },
        {
            name: 'tracking_api_secret_key', 
            label: 'shipping:trackingApiSecretKey', 
            required: false, 
            value: '', 
            component: "PasswordField", 
            margin: "normal", 
            rowSize: {xs: 12, md: 6}
        }
    ], []);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/shipping-accounts`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/shipping-accounts`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/shipping-accounts`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/shipping-accounts`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);

    const [modalOpen, setModalOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    }, []);

    const handleToggleModal = useCallback(open => e => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({page: page, per_page: pageSize});
            if (res?.data) return res.data;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);

    const handleEditItem = useCallback(() => {
        if (!selected?.length) return;

        const _data = {
            id: selected[0]?.id,
            carrier: selected[0]?.carrier,
            fulfillment_type: selected[0]?.fulfillment_type,
            fulfillment_location: selected[0]?.fulfillment_location,
            manifest_location: selected[0]?.manifest_location,
            account_number: selected[0]?.account_number,
            third_party_bill_to: selected[0]?.third_party_bill_to,
            allow_alcohol: selected[0]?.allow_alcohol,
            api_key: selected[0]?.api_key,
            secret_key: selected[0]?.secret_key,
            tracking_api_key: selected[0]?.tracking_api_key,
            tracking_api_secret_key: selected[0]?.tracking_api_secret_key,
        }
        
        setFormData(_data);
        setModalOpen(true);
    }, [selected]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                await deleteData({endpoint: `/clients/${merchantId}/shipping-accounts/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData();
        }
    }, [deleteData, fetchData, merchantId]);

    const handleSaveItem = useCallback(async () => {
        if (!formData) return;

        if (merchantId){
            // when a merchantId is defined, save directly
            const apiCall = formData.id ? updateData : createData;
            try {
                const res = await apiCall(formData);
                if (res?.data) {
                    fetchData();
                    setModalOpen(false);
                }
                return true;
            } catch(e){
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            onMainFormChange({
                target: {
                    name: 'shipping_accounts',
                    value: {...formData}
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [formData, updateData, createData, fetchData, merchantId, onMainFormChange]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        data?.items?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data]);

    const handleResetForm = useCallback(() => {
        setFormData({});
    }, []);

    // DataTable columns
    const columns = useMemo(() => [
        {
            field: 'carrier',
            headerName: t('shipping:carrier'),
            flex: 1,
            minWidth: 120,
            valueGetter: (_, row) => {
                const carrier = row.carrier;
                return carrier ? t(`shipping:carriers.${carrier}`) : '-';
            }
        },
        {
            field: 'fulfillment_type',
            headerName: t('shipping:fulfillmentType'),
            flex: 1,
            minWidth: 150,
            valueGetter: (_, row) => {
                const type = row.fulfillment_type;
                return type ? t(`shipping:fulfillmentTypes.${type}`) : '-';
            }
        },
        {
            field: 'account_number',
            headerName: t('shipping:accountNumber'),
            flex: 1,
            minWidth: 150
        },
        {
            field: 'allow_alcohol',
            headerName: t('shipping:allowAlcohol'),
            width: 120,
            valueGetter: (_, row) => {
                return row.allow_alcohol ? t('general:yes') : t('general:no');
            }
        }
    ], [t]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
    }, [handleLoadItems]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        setFormData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: fields || [],
    };  
};
