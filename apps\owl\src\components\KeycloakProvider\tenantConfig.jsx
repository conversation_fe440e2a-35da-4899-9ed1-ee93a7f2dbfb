export const tenantConfig = () => {
    let tenantConfig = null;

    if (typeof window !== 'undefined' && window.localStorage) {

        const companyData = localStorage.getItem('company');
        if (companyData) {
            const parsedCompany = JSON.parse(companyData);
            const config = parsedCompany?.config || null;
            if (config){
                tenantConfig = {...config};
            }
        }
    }

    return {
        tenant: tenantConfig?.tenant || null,
        authProvider: tenantConfig?.auth_provider || null,
    };
};