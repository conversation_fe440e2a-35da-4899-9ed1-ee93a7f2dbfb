import React, { useMemo } from "react";

const rowProps = {
    rowSize: {xs: 12, sm: 6, md: 4, lg: 3},
    row: 3,
    margin: 'normal',
    group: 'wine',
};

const originData = [
    {
        id: 1,
        name: 'United States',
        children: [
            {
                id: 101,
                name: 'California',
                children: [
                    { id: 1001, name: 'Los Angeles' },
                    { id: 1002, name: 'San Francisco' }
                ]
            },
            {
                id: 102,
                name: 'New York',
                children: [
                    { id: 1003, name: 'New York City' },
                    { id: 1004, name: 'Buffalo' }
                ]
            }
        ]
    },
    {
        id: 2,
        name: 'Canada',
        children: [
            {
                id: 201,
                name: 'Ontario',
                children: [
                    { id: 2001, name: 'Toronto' },
                    { id: 2002, name: 'Ottawa' }
                ]
            }
        ]
    }
];

const fields = [
    {name: 'wine_type', label: 'product:wine.type', required: true, value: '', component: "Select", options: [
        { id: 1, slug: 'product:wine.types.still' },
        { id: 2, slug: 'product:wine.types.nonAlcoholic' },
        { id: 3, slug: 'product:wine.types.sparkling' },
        { id: 4, slug: 'product:wine.types.fortified' },
    ], ...rowProps},
    {name: 'wine_vintage', label: 'product:wine.vintage', required: false, component: "TextField", value: '', ...rowProps},
    {name: 'wine_origin', label: 'product:wine.origin', required: false, value: '', component: "HierarchySelector",
        data: originData,
        levels: [
            { id: 'country', label: 'geographic:country' },
            { id: 'state', label: 'geographic:state', parent: 'country', parentField: 'id', parentIdField: 'country_id' },
        ], ...{...rowProps, rowSize: {xs: 12, margin: 'dense'}}},
    {name: 'wine_vineyard', label: 'product:wine.vineyard', required: false, value: '', component: "TextField", ...rowProps},
    {name: 'wine_alcohol_by_volume', label: 'product:wine.alcoholByVolume', required: false, value: 0, component: "NumberField", endAdornment: '%', ...rowProps},
    {name: 'wine_varietal', label: 'product:wine.varietal', required: false, value: '', component: "Select", options: [], ...rowProps},
    {name: 'wine_bottle_size', label: 'product:wine.bottleSize', required: false, value: '', component: "NumberField", endAdornment: 'ml', ...rowProps},
    {name: 'wine_cola', label: 'product:wine.cola', required: false, value: '', component: "TextField", ...rowProps},
    {name: 'slot_count', label: 'product:slotCount', required: false, value: 0, component: "NumberField", ...rowProps},
    {name: 'slot_equivalent', label: 'product:slotEquivalent', required: false, value: '', component: "Select", options: [
        {id: "0", slug: "0ml"},
        {id: 187, slug: "187ml"},
        {id: 375, slug: "375ml"},
        {id: 500, slug: "500ml"},
        {id: 750, slug: "750ml"},
        {id: 1500, slug: "1500ml"},
        {id: 1800, slug: "1800ml"},
    ], ...rowProps},
    {name: 'dimensions', type: 'number', label: 'product:dimensions', required: false, component: "Measurement", value: {width: 0, height: 0, length: 0, weight: 0}, measurements: ['width', 'height', 'length', 'weight'], unit: ['in', 'in', 'in', 'lbs'], gridSize: 3, ...{...rowProps, rowSize: {xs: 12}, margin: 'dense'}},
];

export const useWine = ({rowId = 3}) => {
    const _fields = useMemo(() => fields.map(field => ({...field, row: rowId})), [rowId]);
    
    return {
        fields: _fields,
    }
};