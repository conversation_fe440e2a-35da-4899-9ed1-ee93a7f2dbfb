import React from 'react';
import { Stack } from '@mui/material';

export const BasicCentered = ({ type, slots, slotProps }) => {
    return (
        <Stack 
            spacing={2} 
            direction="column" 
            className="BasicCentered"
            {...slotProps?.cmsStack}
            sx={{
                alignItems: 'center', 
                ...slotProps?.cmsStack?.sx
            }}
        >
            {slots?.title?.({ ...slotProps?.title })}
            {slots?.subtitle?.({ ...slotProps?.subtitle })}
            {+type === 1 
                ? slots?.images?.({ ...slotProps?.content?.images }) 
                : slots?.video?.({ ...slotProps?.content?.video })
            }
            {slots?.body?.({ ...slotProps?.body })}
            {slots?.ctas?.({ ...slotProps?.ctas })}
        </Stack>
    );
};