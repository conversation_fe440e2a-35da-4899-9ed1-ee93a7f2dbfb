import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { Box, Typography, Stack, Divider, useMediaQuery } from '@mui/material';
import { formatDate } from '@siteboss-frontend/shared/utils';
import { Logo } from '@siteboss-frontend/shared/components';

import { EmailTwoTone as EmailIcon, PhoneTwoTone as PhoneIcon, PlaceTwoTone as PlaceIcon } from '@mui/icons-material';

import styles from './Header.module.scss';

export const Header = ({
    data, // the data to display
    fields, // field definition ({key: the key in the data object, slug: the slug to send to the translator, valueFormatter: a function to format the value to be displayed, variant: the typography variant to use})
    className, // additional class name to apply to the wrapper (used for different print formats)
    children, // additional children to render under the fields
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const company = useSelector(state => state.company);

    if (!fields){
        fields = [
            { key: 'id', slug: '', variant: 'h6', className: clsx('always-visible', styles.document), valueFormatter: (value) => `${t('order:order')} #${value}`},
            { key: 'location_name', slug: '', sx: {fontWeight: 500}},
            { key: 'created_at', slug: 'order:date', valueFormatter: (value) => formatDate(new Date(value), language.code)},
            { key: 'user', slug: 'order:customer', className: clsx('always-visible', styles.user), valueFormatter: (value) => `${value.first_name} ${value.last_name}`},
            { key: 'user', slug: 'general:email', valueFormatter: (value) => `${value.email}` },
            { key: 'user', slug: 'general:phone', valueFormatter: (value) => `${value.mobile_phone}` },
        ];
    }

    const processField = (field, data) => {
        let processedField = {field: null, value: null};

        if (!field.key) return processedField;
        let value = data?.[field.key];

        if (!value) return processedField;
        if (field?.valueFormatter) value = field.valueFormatter(value);

        processedField = {field: field.slug ? t(field.slug) : null, value};
        return processedField;
    }

    if (!data || data.length <= 0) return null;

    return (
        <Stack
            useFlexGap
            direction={{xs: 'column', lg: 'row'}}
            spacing={{xs: 2, md: 4}}
            divider={<Divider orientation='vertical' flexItem display={{xs: 'none', lg: 'block'}} sx={{order: 2}} />}
            className={clsx(styles.wrapper, className)}
        >
            <Box sx={{width: '100%', order: isMobile ? 3 : 1}}>
                {fields.map(field => {
                    const {field:key, value} = processField(field, data);
                    if (!key && !value) return null;
                    return (
                        <Typography
                            key={`header-field-${field.key}-${field.slug}`}
                            variant={field.variant || 'subtitle2'}
                            sx={{...(field.sx || {})}}
                            className={field.className || undefined}
                        >
                            {key && <>{key}: </>}
                            {value}
                        </Typography>
                    );
                })}
                {children}
            </Box>
            <Box sx={{width: '100%', whiteSpace: 'nowrap', order: isMobile ? 1 : 3}}>
                <Logo size='sm' noLink />
                {company.name &&
                    <>
                        <Typography variant='h6' sx={{my: 1}}>{company.name ? company.name.toUpperCase() : ''}</Typography>
                        <Stack direction='column' spacing={1}>
                            <Stack direction='row' spacing={0} alignItems='flex-start' justifyContent='flex-start'>
                                <PlaceIcon fontSize='1rem' sx={{mr: 1}} />
                                <Stack direction='column'>
                                    {company.address && <Typography variant='subtitle3'>{company.address}</Typography>}
                                    {company.address2 && <Typography variant='subtitle3'>{company.address2}</Typography>}
                                    {company.city &&
                                        <Typography variant='subtitle3'>
                                            {company.city}, {company.state} {company.postalCode}
                                        </Typography>
                                    }
                                </Stack>
                            </Stack>
                            {company.email &&
                                <Stack direction='row' spacing={0} alignItems='center'>
                                    <EmailIcon fontSize='1rem' sx={{mr: 1}} />
                                    <Typography variant='subtitle3'>{company.email}</Typography>
                                </Stack>
                            }
                            {company.phone &&
                                <Stack direction='row' spacing={0} alignItems='center'>
                                    <PhoneIcon fontSize='1rem' sx={{mr: 1}} />
                                    <Typography variant='subtitle3'>{company.phone}</Typography>
                                </Stack>
                            }
                        </Stack>
                    </>
                }
            </Box>
        </Stack>
    );
}