import CmsIcon from '../../utils/CmsIcon';

/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/

export const layouts = [
    {
        id: 1,
        name: 'Basic',
        icon: CmsIcon({
            iconProps: {height: 48, width: 48, direction: 'row', spacing: 3},
            elements: [            
                {type: 'circle', size: 10, my: 'auto'},
                {type: 'title', width: '50%', my: 'auto'},
        ]}),
        slotProps: {
            toolbar: {
                sx: {
                    flexDirection: 'row',
                    alignItems: 'center',
                }
            },
            logo: {
                sx: {
                    mr: 2,
                    ml: 0,
                    maxHeight: 56,
                    maxWidth: 120,
                    minHeight: 56,
                    minWidth: 120,
                    width: 'auto',
                    height: '100%',
                    objectFit: 'contain',
                }
            },
            title: {
                variant: "h1",
                sx: {
                    m: 2,
                    ml: 0,
                }
            },
        },
    },
    {
        id: 2,
        name: 'Centered',
        icon: CmsIcon({
            iconProps: {width: 48, height: 48, spacing: 3},
            elements: [
                {type: 'circle', size: 15, mx: 'auto'},
                {type: 'title', width: '50%', mx: 'auto'},
            ]
        }),
        slotProps: {
            toolbar: {
                sx: {
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                },
            },
            logo: {
                sx: {
                    mr: 2,
                    ml: 0,
                    maxHeight: 56,
                    maxWidth: 120,
                    minHeight: 56,
                    minWidth: 120,
                    width: 'auto',
                    height: '100%',
                    objectFit: 'contain',
                }
            },
            title: {
                variant: "h1",
                sx: {
                    m: 2,
                    ml: 0,
                }
            },
        },
    },
    {
        id: 3,
        name: 'Logo',
        icon: CmsIcon({elements: [
            {type: 'circle', size: 32, m: 'auto'},
        ]}),
        slotProps: {
            toolbar: {
                sx: {
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                },
            },
            logo: {
                sx: {
                    mx: 'auto',
                    maxHeight: 56,
                    maxWidth: 120,
                    minHeight: 56,
                    minWidth: 120,
                    width: 'auto',
                    height: '100%',
                    objectFit: 'contain',
                }
            },
            title: {
                sx: {
                    display: 'none',
                    height: 0,
                    width: 0,
                }
            },
        }
    },
];

export const widgetIcon = () =>layouts[0].icon;