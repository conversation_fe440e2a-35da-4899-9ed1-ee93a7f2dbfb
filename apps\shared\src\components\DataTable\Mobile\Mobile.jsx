import React, { useEffect, useRef } from 'react';
import { Box, List, Divider } from '@mui/material';

import NoRows from '../NoRows';
import Row from './Row';

export const Mobile = ({onRowSelectionModelChange, onExpand, onDelete, setPage, page, pageSize, totalPages, loading, ...props}) => {
    const loaderRef = useRef();

    useEffect(() => {
        if (props?.rows?.length < pageSize || page >= totalPages) return;

        const handleObserver = (entities) => {
            const target = entities[0];
            if (target.isIntersecting && !loading) {
                setPage(prev => {
                    if (prev > pageSize) return prev;
                    return prev + 1;
                });
            }
        };

        const observer = new IntersectionObserver(handleObserver, {
            root: null,
            rootMargin: '20px',
            threshold: 1.0,
        });

        if (loaderRef.current) observer.observe(loaderRef.current);

        return () => {
            if (loaderRef.current) {
                observer.unobserve(loaderRef.current);
            }
        };
    }, [loading, totalPages, pageSize, setPage, page, props.rows]);

    if (props?.rows?.length === 0) {
        return (
            <Box sx={{my: 3}}>
                <NoRows />
            </Box>
        );
    }

    return (
        <List>
            {props?.rows?.map((row, i) => (
                <React.Fragment key={`datagrid-mobile-row-${i}`}>
                    <Row 
                        row={row} 
                        columns={props.columns} 
                        onExpand={onExpand} 
                        onDelete={onDelete} 
                        onRowSelectionModelChange={onRowSelectionModelChange}
                    />
                    {i < props.rows.length - 1 && <Divider />}
                </React.Fragment>
            ))}
            <div ref={loaderRef} />
        </List>
    );
}