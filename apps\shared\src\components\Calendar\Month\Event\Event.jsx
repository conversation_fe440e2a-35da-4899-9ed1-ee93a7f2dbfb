import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { differenceInCalendarDays, getDay, startOfDay, endOfDay } from 'date-fns';

import EventBase from '../../Event';

import styles from './Event.module.scss';

export const Event = ({
    currentDate, // the current date (Date object)
    event, // the event object
    color = null, // an array of colors (optional)
    disabled,
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props // additional props
}) => {
    const [eventProps, setEventProps] = useState(null);

    useEffect(() => {
        if (!currentDate || !event) return;

        const startDiff = differenceInCalendarDays(startOfDay(currentDate), event.startDate);
        const endDiff = differenceInCalendarDays(event.endDate, endOfDay(currentDate));
    
        if (startDiff >= 0 && endDiff >= 0) {
                    
            // calculate event width
            const totalDays = differenceInCalendarDays(event.endDate, event.startDate) + 1;
            const daysTillEndOfWeek = 6 - getDay(currentDate);
            const spanDays = Math.min(totalDays - startDiff, daysTillEndOfWeek + 1);
            const width = 100 * spanDays;

            const isEnd = (spanDays > 0 && getDay(currentDate) === 0) || spanDays === totalDays;

            // only render if it's the start date or the continuation in a new week
            if (startDiff === 0 || getDay(currentDate) === 0) {
                setEventProps({
                    width: width,
                    startDiff: startDiff,
                    isEnd: isEnd,
                    spanDays: spanDays,
                });
            }
        }
    }, [currentDate, event]);

    if (!eventProps) return null;

    return (
        <EventBase
            event={event}
            className={clsx(
                styles.event, 
                eventProps.startDiff > 0 && eventProps.isEnd && styles["just-prev"], 
                !eventProps.isEnd && eventProps.startDiff === 0 && styles["just-next"], 
                eventProps.spanDays === 7 && styles["both"]
            )}
            //{`${styles.event} ${eventProps.startDiff > 0 && eventProps.isEnd ? styles["just-prev"] : null} ${!eventProps.isEnd && eventProps.startDiff === 0 ? styles["just-next"] : null} ${eventProps.spanDays === 7 ? styles["both"] : null}`}
            sx={{
                width: `${eventProps.width}%`,
                background: theme => color || (theme.palette.mode === "light" ? theme.palette.primary.dark : theme.palette.primary.light),
                color: theme => color ? theme.palette.getContrastText(color) : theme.palette.primary.contrastText,
                zIndex: theme => theme.zIndex.mobileStepper + 1,
            }}
            disabled={disabled}
            onEventClick={onEventClick}
        />
    );
}