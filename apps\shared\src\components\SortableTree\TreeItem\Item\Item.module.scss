.wrapper {
    padding-left: var(--spacing);
    margin-bottom: -1px;
  
    &.clone {
      display: inline-block;
      pointer-events: none;
      padding: 0;
      margin-left: 8px;
      margin-top: 4px;
  
      .treeItem {
        --vertical-padding: 4px;
        padding-right: 32px;
      }
    }
  
    &.ghost {
      &.indicator {
        opacity: 1;
        position: relative;
        z-index: 1;
        margin-bottom: -1px;
  
        .treeItem {
          position: relative;
          padding: 0;
          height: 4px;
          border-radius: 0;
          background-color: var(--background-color);
  
          &:before {
            position: absolute;
            left: -8px;
            top: -4px;
            display: block;
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 3px solid var(--background-color);
            background-color: #fff;
          }
  
          > * {
            /* Items are hidden using height and opacity to retain focus */
            opacity: 0;
            height: 0;
          }
        }
      }
  
      &:not(.indicator) {
        opacity: 0.5;
      }
  
      .treeItem > * {
        box-shadow: none;
        //background-color: transparent;
      }
    }
  }
  
  .treeItem,
  .details {
    --vertical-padding: 8px;
  
    position: relative;
    display: flex;
    align-items: center;
    //padding: var(--vertical-padding) 8px;
    padding: var(--vertical-padding) 2px;
    box-sizing: border-box;
  }

  .details {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--vertical-padding) 16px;
    padding-bottom: 16px;
    margin-top: -8px;
    border-top: 0;
    margin-bottom: 4px;
  }
  
  .text {
    flex-grow: 1;
    padding-left: 0.5rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: move;
  }
  
  .count {
    position: absolute;
    top: -8px;
    right: -8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--background-color);
    font-size: 0.8rem;
    font-weight: 600;
    color: #fff;
  }
  
  .disableInteraction {
    pointer-events: none;
  }
  
  .disableSelection,
  .clone {
    .text,
    .count {
      user-select: none;
      -webkit-user-select: none;
    }
  }
  