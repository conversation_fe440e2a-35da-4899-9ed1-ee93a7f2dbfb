import React, { Suspense, lazy } from 'react';
import { Routes as RrdRoutes, Route } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import { Layout, Fzf } from '@siteboss-frontend/shared/components';
import { InfiniteLoader } from '@siteboss-frontend/shared';
import { ProtectedRoute } from './components/KeycloakProvider';

// Lazy load container components
//const Login = lazy(() => import('./containers/Login').then(module => ({ default: module.Login })));
const Logout = lazy(() => import('./containers/Login').then(module => ({ default: module.Logout })));

const Dashboard = lazy(() => import('./containers/Dashboard'));
const BillingInvoices = lazy(() => import('./containers/BillingInvoices'));
const CustomerManagement = lazy(() => import('./containers/CustomerManagement'));
const CarrierConfigurations = lazy(() => import('./containers/CarrierConfigurations'));
const MarketingTools = lazy(() => import('./containers/MarketingTools'));
const ServiceStatus = lazy(() => import('./containers/ServiceStatus'));


const Products = lazy(() => import('./containers/Products'));
const Merchants = lazy(() => import('./containers/Merchants'));
const MerchantForm = lazy(() => import('./containers/Merchants/Form'));
const Orders = lazy(() => import('./containers/Orders'));
const Reports = lazy(() => import('./containers/Reports'));

// Transition wrapper using framer-motion
const PageWrapper = ({ id, children }) => (
	<motion.div
		key={id}
		initial={{ opacity: 0 }}
		animate={{ opacity: 1 }}
		exit={{ opacity: 0 }}
		transition={{ duration: 0.3 }}
	>
		{children}
	</motion.div>
);

const ProtectedLayout = (props) => (
	<ProtectedRoute>
		<Layout {...props} />
	</ProtectedRoute>
);

const routes = [
	{ path: '/logout', component: Logout },
	/*{ path: '/login', component: Layout, props: { basic: true }, children: [
		{ path: null, component: Login },
		{ path: 'forgot-password', component: () => <div /> },
		{ path: 'reset-password', component: () => <div /> },
	]},*/
	{ path: '/', component: ProtectedLayout, props: { moduleId: 5 }, children: [
		{ path: null, component: Dashboard },
		{ path: 'dashboard', component: Dashboard },

		{ path: 'billing-invoices/:id?', component: BillingInvoices },
		{ path: 'customer-management/:id?', component: CustomerManagement },
		{ path: 'carrier-configurations/:id?', component: CarrierConfigurations },
		{ path: 'marketing-tools/:id?', component: MarketingTools },
		{ path: 'service-status/:id?', component: ServiceStatus },

		{ path: 'products/:id?', component: Products },
		{ path: 'merchants', component: Merchants },
		{ path: 'merchants/new', component: MerchantForm },
		{ path: 'merchants/edit/:id', component: MerchantForm },
		{ path: 'orders/:id?', component: Orders },
		{ path: 'reports', component: Reports },
	]},
	{ path: '*', component: Dashboard },
];

// Recursive render routes and children
const renderRoutes = (arr, index = 0) => arr.map((route, i) => (
	<Route
		key={`route-${index}-${i}`}
		path={route.path || undefined}
		index={!route.path || route.index}
		element={
			<PageWrapper id={`motion-${index}-${i}`}>
				<route.component {...(route?.props || {})} />
			</PageWrapper>
		}>
		{route.children && renderRoutes(route.children, i)}
	</Route>
));

export const Routes = () => (
	<Suspense fallback={
		<InfiniteLoader open={true} errors={null} setErrors={null} onRetry={null} message="Loading route..." />
	}>
		<AnimatePresence mode='wait'>
			<RrdRoutes>
				{renderRoutes(routes)}
			</RrdRoutes>
		</AnimatePresence>
	</Suspense>
);
