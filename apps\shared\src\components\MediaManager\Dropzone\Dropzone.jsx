import React, { useMemo } from "react";
import { useTranslation } from 'react-i18next';
import { Typography, Box } from "@mui/material";
import { ImageOutlined as ImageIcon, PlayCircleOutlineOutlined as VideoIcon, DescriptionOutlined as DocumentIcon, LocalFloristOutlined as LogoIcon} from '@mui/icons-material';

export const Dropzone = ({
    mediaType,
    isDragging,
    message,
    onClick,
    ...props
}) => {
    const { t } = useTranslation();

    const config = useMemo(() => {
        let Icon;
        let text = message || null;
        switch (mediaType) {
            case 1:
            case 2:
            case 3:
                Icon = ImageIcon;
                if (!text) text = t('file:dropImageOrClick');
                break;
            case 4:
                Icon = VideoIcon;
                if (!text) text = t('file:dropVideoOrClick');
                break;
            case 5:
                Icon = DocumentIcon;
                if (!text) text = t('file:dropDocumentOrClick');
                break;
            case 9:
                Icon = LogoIcon;
                if (!text) text = t('file:dropImageOrClick');
                break;
            default:
                Icon = DocumentIcon;
                if (!text) text = t('file:dropFileOrClick');
                break;
        }
        return { icon: Icon, text };
    }, [mediaType, message, t]);

    return (
        <Box 
            onClick={onClick}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                textAlign: 'center',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                height: '100%',
                flexGrow: 1,
                backgroundColor: theme => isDragging ? `${theme.palette.success.main}!important` : 'transparent',
                border: `2px dashed`,
                borderRadius: 4,
                color: theme => isDragging ? theme.palette.success.contrastText : undefined,
                cursor: onClick ? 'pointer' : undefined,
            }}
        >
            <config.icon size="large" sx={{mb: 1}} />
            <Typography variant='h5'>{config.text}</Typography>
        </Box>
    );
}