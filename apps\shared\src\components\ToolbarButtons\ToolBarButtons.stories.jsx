import React from 'react';
import * as Icons from './index.js';

export default {
    title: "Shared/Components/ToolbarButtons",
    component: Icons,
    tags: ["autodocs"],
    argTypes:{
        color:{
            description: "Color of the icon",
            control: { type: 'color' },
            table: {
                type: { summary: 'color' },
                defaultValue: { summary: "inherit" },
            }
        },
        size:{
            description: "Size of the icon",
            control: { type: 'number' },
            table: {
                type: { summary: 'number' },
                defaultValue: { summary: "24" },
            }
        },
    }
}

export const ToolbarButtons={
    render:()=>(
        <div>
            <p>
                <h3>
                    Cart
                </h3>
                <Icons.ToolbarCart />
            </p>
            <p>
                <h3>
                    ChangeColor
                </h3>
                <Icons.ToolbarChangeColor />
            </p>
            <p>
                <h3>
                    Change Language
                </h3>
                <Icons.ToolbarChangeLanguage />
            </p>
            <p>
                <h3>
                    Logout
                </h3>
                <Icons.ToolbarLogout />
            </p>
            <p>
                <h3>
                    User Avatar
                </h3>
                <Icons.ToolbarUserAvatar />
            </p>
        </div>
    )
}