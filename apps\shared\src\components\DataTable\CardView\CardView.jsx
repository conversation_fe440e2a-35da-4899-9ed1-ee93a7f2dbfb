import React, { useEffect, useCallback } from 'react';
import { Grid2, Box } from '@mui/material';

//import ProgramCard from './ProgramCard';

import NoRows from '../NoRows';
import Card from './Card';

export const CardView = ({ 
    onRowSelectionModelChange, 
    rowSelectionModel, 
    onPaginationModelChange, 
    onClick, 
    onExpand, 
    onDelete, 
    setPage, 
    page, 
    pageSize, 
    pageSizeOptions, 
    totalPages, 
    loading, 
    rows, 
    ...props 
}) => {

    if (rows?.length === 0) {
        return (
            <Box sx={{my: 3}}>
                <NoRows />
            </Box>
        );
    }    

    return (
        <>
            <Grid2 container spacing={3}>
                {rows?.map(row => (
                    <Grid2 size={{xs: 12, md: 6, lg: 4}} key={row.id}>
                        <Card 
                            row={row} 
                            columns={props.columns} 
                            onExpand={onExpand} 
                            onDelete={onDelete} 
                            onRowSelectionModelChange={onRowSelectionModelChange}
                            onClick={onClick}
                        />
                    </Grid2>
                ))}
            </Grid2>

{/*
                            <Stack
                                direction={isMobile ? "column" : "row"}
                                spacing={2}
                                justifyContent="space-between"
                                alignItems={isMobile ? "center" : "flex-end"}
                            >
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Typography variant="body2" color="text.secondary">
                                        {t('general:showing')} {rows.length} {t('general:of')} {totalRows} {t('program:programs')}
                                    </Typography>
                                    <FormControl variant="outlined" size="small" sx={{ minWidth: 80 }}>
                                        <InputLabel id="page-size-select-label">{t('general:perPage')}</InputLabel>
                                        <Select
                                            labelId="page-size-select-label"
                                            value={pageSize}
                                            onChange={handlePageSizeChange}
                                            label={t('general:perPage')}
                                        >
                                            {pageSizeOptions.map(option => (
                                                <MenuItem key={option} value={option}>{option}</MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Stack>

                                <Pagination
                                    count={totalPages}
                                    page={page}
                                    onChange={handlePageChange}
                                    color="primary"
                                    disabled={loading || parentLoading}
                                    siblingCount={isMobile ? 0 : 1}
                                />
                            </Stack>
*/}                            
        </>
    );
}