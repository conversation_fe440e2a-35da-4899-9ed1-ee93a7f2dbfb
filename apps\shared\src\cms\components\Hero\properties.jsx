import { getRandomWord, generateTitle, generateParagraph, capitalize } from "../../../utils";

const typeTitle = [
    { id: 'h1', slug: 'builder:component.heading.types.h1' },
    { id: 'h2', slug: 'builder:component.heading.types.h2' },
    { id: 'h3', slug: 'builder:component.heading.types.h3' },
    { id: 'h4', slug: 'builder:component.heading.types.h4' },
    { id: 'h5', slug: 'builder:component.heading.types.h5' },
    { id: 'h6', slug: 'builder:component.heading.types.h6' },
];

const typeText = [
    { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
    { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
    { id: 'body1', slug: 'builder:component.heading.types.body1' },
    { id: 'body2', slug: 'builder:component.heading.types.body2' },
    { id: 'caption', slug: 'builder:component.heading.types.caption' },
    { id: 'overline', slug: 'builder:component.heading.types.overline' },
    { id: 'p', slug: 'builder:component.heading.types.p' },
    { id: 'code', slug: 'builder:component.heading.types.code' },
];

export const properties = [
    {
        name: 'image',
        label: 'builder:component.hero.image',
        component: "MediaManager",
        value: '',
        size: "small",
        margin: "normal",
        mediaType: 1,
        accept: 'image/*',
    },
    {
        name: 'height',
        label: null,
        component: "Measurement",
        value: {height: '70vh'},
        measurements: ['height'],
        size: "small",
        margin: "normal",
    },
    {
        name: 'title',
        label: 'builder:component.hero.title',
        component: "TextField",
        value: generateTitle(),
        size: "small",
        margin: "normal",
    },
    {
        name: 'titleVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'h1',
        size: "small",
        margin: "normal",
        options: [...typeTitle, ...typeText]
    },
    {
        name: 'body',
        label: 'builder:component.hero.body',
        component: "TextField",
        minRows: 4,
        value: generateParagraph(5),
        size: "small",
        margin: "normal",
    },
    {
        name: 'bodyVariant',
        label: 'builder:component.heading.type',
        component: "Select",
        value: 'body1',
        size: "small",
        margin: "normal",
        options: [...typeText]
    },
    {
        name: 'callToActionUrl',
        label: 'builder:component.hero.callToActionUrl',
        component: "TextField",
        value: '',
        placeholder: 'https://hexagon.bicycle.rights.org',
        size: "small",
        margin: "normal",
    },
    {
        name: 'callToActionText',
        label: 'builder:component.hero.callToActionText',
        component: "TextField",
        value: capitalize(getRandomWord(1)),
        size: "small",
        margin: "normal",
    },
];