import React, { useEffect, useCallback, useState } from 'react';
import { Grid, Typography, TextField } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useFormContext } from '@siteboss-frontend/shared/components';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';

const Step1Name = ({ data, onError }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);
    const { saveData } = useFormContext();

    // Initialize local state with Redux values
    const [nameValue, setNameValue] = useState(formData?.name || '');
    const [descriptionValue, setDescriptionValue] = useState(formData?.description || '');

    // Sync local state with Redux when formData changes
    useEffect(() => {
        if (formData?.name !== undefined) {
            setNameValue(formData.name);
        }
        if (formData?.description !== undefined) {
            setDescriptionValue(formData.description);
        }
    }, [formData]);

    // Register fields with FormContext for validation and report errors
    useEffect(() => {
        // Only run this effect once on component mount to register fields
        // Register the name field as required
        saveData(0, { name: 'name', value: nameValue, required: true });
        // Register the description field (not required)
        saveData(0, { name: 'description', value: descriptionValue, required: false });
    }, []); // Empty dependency array means this only runs once on mount

    // Validation effect - only reports validation state without showing errors
    useEffect(() => {
        // Register validation state with the parent component
        // This doesn't show errors, but allows the parent to check if fields are valid
        if (onError) {
            // If name is empty, report an error object
            if (!nameValue) {
                onError({ name: 'Name is required' });
            } else {
                // Otherwise, report no errors
                onError(null);
            }
        }
    }, [nameValue, onError]);

    // Handle input changes
    const handleNameChange = useCallback((e) => {
        const value = e.target.value;
        setNameValue(value);

        // Update Redux state
        dispatch(updateFormData({
            ...formData,
            name: value
        }));

        // Update FormContext for validation - this will trigger the validation effect
        saveData(0, { name: 'name', value, required: true });
    }, [dispatch, formData, saveData]);

    const handleDescriptionChange = useCallback((e) => {
        const value = e.target.value;
        setDescriptionValue(value);

        // Update Redux state
        dispatch(updateFormData({
            ...formData,
            description: value
        }));

        // Update FormContext for validation
        saveData(0, { name: 'description', value, required: false });
    }, [dispatch, formData, saveData]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Enter the name and description for your discount
                </Typography>
                <TextField
                    name="name"
                    label="Discount Name"
                    fullWidth
                    required
                    margin="normal"
                    helperText="Enter a descriptive name for this discount"
                    onChange={handleNameChange}
                    value={nameValue}
                />
                <TextField
                    name="description"
                    label="Description"
                    fullWidth
                    multiline
                    rows={4}
                    margin="normal"
                    helperText="Provide additional details about this discount (optional)"
                    onChange={handleDescriptionChange}
                    value={descriptionValue}
                />
            </Grid>
        </Grid>
    );
};

export default Step1Name;
