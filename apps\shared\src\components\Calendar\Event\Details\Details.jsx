import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { AppBar, Box, Button, Grid2, Typography, ImageList, ImageListItem, useMediaQuery } from '@mui/material';
import { formatDate, formatDateTime, createCurrencyFormatter } from '../../../../utils';

import Title from '../../../Title';
import Line from './Line';
import { format, isSameDay } from 'date-fns';

export const Details = ({ event }) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    let date;
    if (isSameDay(event?.startDate, event?.endDate)) {
        date = `${formatDate(event?.startDate, language.code)}<br/>${format(event.startDate, "hh:mmaa")} - ${format(event.endDate, "hh:mmaa")}`;
    } else {
        date = `${formatDateTime(event.startDate, language.code)}<br/>${t("calendar:to")}<br/>${formatDateTime(event.endDate, language.code)}`;
    }

    const lines = [
        {
            caption: t(`calendar:where`),
            value: event.metadata.location_name || null,
        },
        {
            caption: t(`calendar:when`),
            value: date
        },
        {
            caption: t(`calendar:ageRequirement`),
            value: (event.metadata.min_age || event.metadata.max_age) 
                ? (event.metadata.min_age > 0 && !event.metadata.max_age ? `${event.metadata.min_age} ${t('calendar:yearsOld')} ${t('calendar:andUp')}` : '') +
                    (!event.metadata.min_age && event.metadata.max_age > 0 ? `${t('calendar:upTo')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '') + 
                    (event.metadata.min_age > 0 && event.metadata.max_age > 0 ? `${event.metadata.min_age} ${t('calendar:to')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '')
                : null
        },
        {
            caption: t(`calendar:eventFee`),
            value: event.metadata.product_price > 0 ? currencyFormatter.format(event.metadata.product_price) : null
        },
        {
            caption: null,
            value: !event.metadata?.requires_registration && !event.metadata?.children?.length ? `<b>${t(`calendar:noRegistration`)}</b>` : null
        },
    ];

    return (
        <Box sx={{ flexGrow: 1, position: 'relative' }}>
            <AppBar position='sticky' component='div' color='default' sx={{zIndex: theme => theme.zIndex.appBar - 2}} elevation={24}>
                <Title title={event.title} subtitle={event.short_description} sx={{mb: 4}} />
            </AppBar>
            <Grid2 container spacing={2}>
                <Grid2 size={{xs: 12, lg: "auto"}} sx={{
                    pr: isMobile ? undefined : 3,
                    position: isMobile ? undefined : 'fixed',
                    borderRight: theme => ({xs: 0, sm: `1px solid ${theme.palette.divider}`}),
                    width: theme => isMobile ? undefined : `${theme.sizes.menuWidth}!important`,
                }}>
                    {lines.filter(a=>a.value)?.map((line, i) => (
                        <Line key={`event-line-${i}`} caption={line.caption} text={line.value} />
                    ))}
                    {event.metadata?.requires_registration === 1 &&
                        <Button variant='contained' color='primary' size="large" fullWidth sx={{mt: 2}}>{t('calendar:signUp')}</Button>
                    }
                </Grid2>
                <Grid2 size={{xs: 12, lg: "grow"}} sx={{
                    mt: isMobile ? 2 : undefined,
                    ml: theme => isMobile ? undefined : `calc(${theme.sizes.menuWidth} + ${theme.spacing(2)})`, 
                    minHeight: isMobile ? undefined : 400
                }}>
                    <Typography 
                        variant='body1' 
                        component='div' 
                        sx={{
                            whiteSpace: 'pre-wrap',
                            wordWrap: 'break-word',
                            overflowWrap: 'break-word',
                            hyphens: 'auto',
                        }}
                        dangerouslySetInnerHTML={{__html: event.metadata.description}} 
                    />
                    {event.metadata?.images?.length > 0 &&
                        <ImageList variant='masonry' cols={isMobile ? 2 : 4} gap={8}>
                            {event.metadata?.images?.map(image => (
                                <ImageListItem key={`event-${event.id}-image-${image.preview_url}`}>
                                    <img
                                        srcSet={image.preview_url}
                                        src={image.preview_url}
                                        alt={image.description || event.title}
                                        loading='lazy'
                                    />
                                </ImageListItem>
                            ))}
                        </ImageList>
                    }
                </Grid2>
            </Grid2>
        </Box>
    );
}