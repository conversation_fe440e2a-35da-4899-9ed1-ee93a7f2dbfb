import { getRandomWord, capitalize, generateTitle, generateParagraph } from "../../../utils";

export const defaultValue = () => (
`<div class="content">
    <h1 style="margin: 0;">${capitalize(generateTitle())}</h1>
    <p style="margin-top: 0;">${generateParagraph(5)}</p>
    <div style='display: flex; flex-direction: row; align-items: center; justify-content: center; gap: 8px;'>
        <button class="button animated">${capitalize(getRandomWord(1))}</button>
        <button class="button">${capitalize(getRandomWord(1))}</button>
    </div>
</div>
<style>
    .content {
        font-family: 'Roboto', sans-serif;
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        justify-content: center; 
        padding: 16px; gap: 1px;
    }
    .button {
        font-family: 'Roboto', sans-serif;
        padding: 8px 16px; 
        border: 1px solid #757575; 
        border-radius: 4px;
        cursor: pointer;
        margin: 8px;
        background-color: #EC407A;
        color: #fff;
    }
    @keyframes animated {
        from { transform: scale3d(1, 1, 1) }
        10%, 20% { transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg) }
        30%, 50%, 70%, 90% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg) }
        40%, 60%, 80% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg) }
        to { transform: scale3d(1, 1, 1) }
    }
    .animated {
        animation-name: animated;
        animation-timing-function: ease-in-out;
        animation-duration: 1s;
        animation-fill-mode: both;
        animation-iteration-count: 1;
        animation-delay: 1s;
    }
</style>`);