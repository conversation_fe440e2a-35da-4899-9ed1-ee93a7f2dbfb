import React from 'react';
import { useSelector } from 'react-redux';
import { createCurrencyFormatter } from '../../../../../../../utils/currency';
import { Caption, CheckboxWrapper } from '../../../../../common/pos';

export const Checkbox = ({ items, selected, disabled, onSelect, slotProps, slots, ...props }) => {
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    return (
        <CheckboxWrapper items={items} selected={selected} disabled={disabled} onChange={onSelect} slots={slots} slotProps={slotProps}>
            {items.map(item => { 
                let _price = +item?.price || 0;
                if (_price > 0 && selected.find(a => a.id === item.id)) _price = -_price;
                return (
                    <React.Fragment key={item.id}>
                        {item?.name && <Caption variant="body1" component="span" text={item.name} />}
                        {item?.price > 0 && <Caption variant="body1" component="span" text={`${_price < 0 ? "-" : "+"}${currencyFormatter.format(item.price, currency)}`} />}
                    </React.Fragment>
                );
            })}
        </CheckboxWrapper>
    );
};