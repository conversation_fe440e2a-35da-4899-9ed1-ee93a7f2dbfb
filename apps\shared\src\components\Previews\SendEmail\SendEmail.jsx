import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Autocomplete, TextField, DialogContentText } from '@mui/material';
import { SuccessBar } from '../../Snacks';
import Modal from '../../Modal';

export const SendEmail = ({id, emails = null, open = false, onClose, ...props}) => {
    const { t, isMobile } = useTranslation();
    const emailRef = useRef(null);

    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    const sendHandler = () => {
        setError(null);
        setSuccess(null);
        const email = emailRef.current.value;
        if (!email) {
            setError(t("error:required"));
            return;
        }

        // check if email is valid
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            setError(t("error:invalid"));
            return;
        }

        // send email
        console.log(email)
        setSuccess(t("success:sent"));        
    }

    const closeHandler = () => {
        setError(null);
        setSuccess(null);
        onClose();
    }

    return (
        <Modal open={open} onClose={closeHandler} title={t("order:sendEmail")} maxWidth="sm" fullScreen={isMobile} slots={{
            actions: (
                <>
                    <Button onClick={closeHandler}>{t("general:cancel")}</Button>
                    <Button variant="primary" onClick={sendHandler}>{t("general:send")}</Button>
                </>
            )
        }}>
            {success && <SuccessBar message={success} onClose={e=>setSuccess(null)} />}
            <DialogContentText>{t("order:sendEmailText")}</DialogContentText>
            <Autocomplete
                freeSolo
                options={emails || undefined}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        inputRef={emailRef}
                        required
                        margin="dense"
                        name="email"
                        type="email"
                        label={t("general:email")}
                        fullWidth
                        variant="standard"
                        autoComplete='nope'
                        slotProps={{
                            ...params?.slotProps,
                            input: {
                                ...params?.slotProps?.input,
                                ...params?.InputProps,
                            },
                        }}
                        error={Boolean(error)}
                        helperText={error}
                    />
                )}
            />
            {/*<Dialog open={open} onClose={closeHandler}>
                <DialogContent>
                    <DialogContentText>{t("order:sendEmailText")}</DialogContentText>
                    <Autocomplete
                        freeSolo
                        options={emails || undefined}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                inputRef={emailRef}
                                required
                                margin="dense"
                                name="email"
                                type="email"
                                label={t("general:email")}
                                fullWidth
                                variant="standard"
                                autoComplete='nope'
                                slotProps={{
                                    ...params?.slotProps,
                                    input: {
                                        ...params?.slotProps?.input,
                                        ...params?.InputProps,
                                    },
                                }}
                                error={Boolean(error)}
                                helperText={error}
                            />
                        )}
                    />
                    {success && <SuccessBar message={success} onClose={e=>setSuccess(null)} />}
                </DialogContent>
                <DialogActions>
                </DialogActions>
            </Dialog>*/}
        </Modal>
    );
}