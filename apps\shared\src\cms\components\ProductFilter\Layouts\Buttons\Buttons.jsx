import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Stack } from '@mui/material';
import ButtonGroup from './ButtonGroup';

export const Buttons = ({ 
    variant= "outlined",
    color = "primary",
    size = "medium",
    orientation = "horizontal",
    options = [], 
    loading,
    disabled,
    button = {}, // MUI ToggleButton props
    allSlug = "product:productTypes.all",
    onSelect,
    reset = false,
    isBuilder,
    selectedId,
    ...props 
}) => {
    const parentButtonRefs = useRef([]);
    const [value, setValue] = useState([]);
    const [allOptions, setAllOptions] = useState([{level: 0, values: options, offset: {x: 0, y: 0}, ref: null}]);

    const handleClick = useCallback((newValue, parentLevel = 0) => e => {
        let _prev = [];
        setValue(prev => {
            let insert = false;
            _prev = [...prev];
            if (!newValue) _prev = [];
            else {
                if (!_prev.includes(newValue)) insert = true;
                _prev.splice(parentLevel, prev.length - parentLevel);
                if (insert && newValue) _prev = [..._prev, newValue];
                else newValue = _prev?.[_prev.length - 1] || null;
            }
            return _prev;
        });
        if (onSelect) onSelect(newValue);

        if (_prev.length > 0) {
            let newOptions = [{ level: 0, values: options, offset: {x: 0, y: 0}, ref: null }];
            _prev.forEach((val, i) => {
                const parentOption = newOptions[i].values.find(a => a.id === val);
                if (parentOption && parentOption.children) {
                    const parentButton = parentButtonRefs.current?.[i]?.querySelector(`#button-${val}`);
                    const newOffset = {x: parentButton?.offsetLeft || 0, y: parentButton?.offsetTop || 0};
                    newOptions = [...newOptions, { level: i + 1, values: parentOption.children, offset: newOffset, ref: parentButton }];
                }
            });
            setAllOptions(newOptions);
        } else setAllOptions([{ level: 0, values: options, offset: {x: 0, y: 0}, ref: null }]);
    }, [options, onSelect]);

    useEffect(() => {
        setAllOptions([{level: 0, values: options, offset: {x: 0, y: 0}, ref: null}]);
        if (options.length === 1) onSelect(options[0].id);
    }, [options, onSelect]);

    useEffect(() => {
        if (reset) {
            setValue([]);
            setAllOptions([{ level: 0, values: options, offset: {x: 0, y: 0}, ref: null }]);
        }
    }, [reset, options]);

    if (options?.length <= 1) return null;

    return (
        <Stack sx={{width: '100%', position: 'relative', mb: 1}} direction="column" useFlexGap flexWrap="wrap">
            {allOptions.map(((option, i) => (
                <ButtonGroup 
                    key={i}
                    ref={el => parentButtonRefs.current[i] = el}
                    variant={variant}
                    color={color}
                    size={size}
                    orientation={orientation}
                    options={option.values}
                    loading={loading}
                    disabled={disabled}
                    button={button}
                    allSlug={allSlug}
                    onClick={handleClick}
                    value={value}
                    parentLevel={i}
                    offset={option.offset}
                    elementRef={option.ref}
                    {...props}
                />
            )))}
        </Stack>
    );
}