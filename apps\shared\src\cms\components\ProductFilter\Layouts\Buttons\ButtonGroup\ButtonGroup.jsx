import React from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Box, Button, Popper } from '@mui/material';

export const ButtonGroup = React.forwardRef(({
    variant= "outlined",
    color = "primary",
    size = "medium",
    orientation = "horizontal",
    options = [], 
    loading,
    disabled,
    button = {}, // MUI ToggleButton props
    allSlug = "product:productTypes.all",
    onClick,
    parentLevel = 0,
    value,
    offset= {x: 0, y: 0},
    elementRef,
    ...props
}, ref) => {
    const {t} = useTranslation();

    /*
    const Wrapper = parentLevel === 0 ? React.Fragment : Popper;
    const wrapperProps = parentLevel === 0 ? {} : {
        open: true, 
        anchorEl: elementRef, 
        placement: orientation === "vertical" ? "right" : "bottom-start", 
        ml: orientation === "vertical" ? undefined : `${offset.x}px`
    };
    */

    return (
        <Box sx={{
            mb: 0.5,
            ml: orientation === "vertical" ? undefined : `${offset.x}px`,
        }}>
            <Stack ref={ref} useFlexGap direction={orientation === "vertical" ? "column" : "row"} flexWrap={"wrap"} {...props}>
                {options.length > 1 && !parentLevel &&
                    <Button 
                        variant="text" 
                        color={color} 
                        size={size} 
                        disabled={disabled} 
                        loading={loading}
                        {...button} 
                        onClick={onClick(null)}
                    >
                        {t(allSlug)}
                    </Button>
                }
                {options.map((option, index) => { 
                    const selected = value?.findIndex(a => a === (option?.id || (index + 1))) > -1;
                    return (
                        <Button 
                            id={`button-${option.id}`}
                            key={`button-${index}`}
                            disabled={disabled}
                            loading={loading}
                            color={selected ? color : "inherit"} 
                            size={size} 
                            variant={selected ? "contained" : variant}
                            onClick={onClick(option.id || (index + 1), parentLevel)}
                            {...button}
                            sx={{
                                bgcolor: (!selected && variant === "outlined") ? "background.paper" : undefined,
                                ...button?.sx
                            }}
                        >
                            {t(option?.slug) || option?.label || option?.name || option}
                        </Button>
                    );
                })}
            </Stack>
        </Box>
    );
});