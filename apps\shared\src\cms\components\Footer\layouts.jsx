import CmsIcon from '../../utils/CmsIcon';


export const layouts = [
    {
        id: 1,
        name: 'Basic',
        icon: CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'column', spacing: 3},
            elements: [            
                {type: 'title'},
                {type: 'spacing'},
                {type: 'text', width: 35},
        ]}),
        slotProps: {
            title: {
                order: 1,
                sx: {
                    m: 2,
                    mb: 0,
                }
            },
            copyRight: {
                variant: "body3",
                order: 2,
                sx: {
                    m: 2,
                    mt: 0,
                }
            },
        },
    },
];

export const widgetIcon = () =>layouts[0].icon;