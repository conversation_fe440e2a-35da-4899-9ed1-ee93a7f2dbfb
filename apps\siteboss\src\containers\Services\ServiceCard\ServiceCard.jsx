import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { 
  Card, 
  CardActionArea, 
  CardContent, 
  CardMedia, 
  CardActions,
  Typography, 
  Checkbox,
  Chip,
  Stack,
  Box
} from '@mui/material';

export const ServiceCard = ({ 
  service, 
  selected, 
  onSelect, 
  onExpand, 
  onDelete,
  ...props 
}) => {
  const { t } = useOutletContext();
  
  const handleCardClick = (e) => {
    if (onExpand) onExpand(e, service);
  };
  
  const handleCheckboxClick = (e) => {
    e.stopPropagation();
    if (onSelect) onSelect(e, service);
  };
  
  const handleDeleteClick = (e) => {
    e.stopPropagation();
    if (onDelete) onDelete(e, service);
  };

  // Get image URL or use placeholder
  const imageUrl = service.image_url || 
    `https://placehold.co/300?font=source-sans-pro&text=${encodeURIComponent(service.name)}`;

  return (
    <Card 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        position: 'relative',
        border: selected ? '2px solid' : '1px solid',
        borderColor: selected ? 'primary.main' : 'divider',
      }}
      data-cy={`service-card-${service.id}`}
    >
      <Box sx={{ position: 'absolute', top: 8, left: 8, zIndex: 1 }}>
        <Checkbox 
          checked={selected}
          onClick={handleCheckboxClick}
          color="primary"
          sx={{ 
            bgcolor: 'rgba(255, 255, 255, 0.7)', 
            borderRadius: '50%',
            '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' } 
          }}
        />
      </Box>
      
      <CardActionArea 
        onClick={handleCardClick}
        sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
      >
        <CardMedia
          component="img"
          height="140"
          image={imageUrl}
          alt={service.name}
          sx={{ objectFit: 'cover' }}
        />
        <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          <Typography gutterBottom variant="h6" component="div" noWrap>
            {service.name}
          </Typography>
          
          {service.description && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {service.description}
            </Typography>
          )}
          
          <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 'auto' }}>
            <Chip 
              label={`${service.block_minutes} ${t('service:minutes')}`} 
              size="small" 
              variant="outlined"
              sx={{ mb: 1 }}
            />
            <Chip 
              label={`$${service.default_price}`} 
              size="small" 
              variant="outlined"
              sx={{ mb: 1 }}
            />
          </Stack>
        </CardContent>
      </CardActionArea>
      
      <CardActions sx={{ justifyContent: 'space-between', borderTop: '1px solid', borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary">
          ID: {service.id}
        </Typography>
        <Chip
          label={service.status === 1 ? t('service:active') : t('service:inactive')}
          color={service.status === 1 ? 'success' : 'default'}
          size="small"
        />
      </CardActions>
    </Card>
  );
};

export default ServiceCard;
