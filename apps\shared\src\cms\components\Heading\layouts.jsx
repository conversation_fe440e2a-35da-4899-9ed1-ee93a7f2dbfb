import CmsIcon from '../../utils/CmsIcon';

/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/

export const layouts = [
    {
        id: 1,
        name: 'Default',
        icon: CmsIcon({
            iconProps: {height: 36, width: 36, direction: 'column', spacing: 3, sx: {alignItems: 'center'}},
            elements: [
                {type: 'title', width: '80%'},
                {type: 'group', direction: 'column', spacing: 2, sx: {height: 'auto', width: '80%', flexGrow: 0}, children: [
                    {type: 'text', width: '70%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '30%'},
                ]},
            ]
        }),
        slotProps: {
            title: {
                //variant: "h1",
                order: 1,
                sx: {
                    mb: 0,
                }
            },
            subtitle: {
                //variant: "subtitle2",
                order: 2,
                sx: {
                    mb: 2,
                }
            },
            body: {
                //variant: "body1",
                order: 3,
            },
            bodyType: "body1",
        }
    },
    {
        id: 2,
        name: 'Caption',
        icon: CmsIcon({
            iconProps: {height: 36, width: 36, direction: 'column', spacing: 3, sx: {alignItems: 'center'}},
            elements: [
                {type: 'text', width: '60%', alignSelf: 'flex-start', height: '1.5px'},                
                {type: 'group', direction: 'column', spacing: 2, sx: {height: 'auto', width: '100%', flexGrow: 0}, children: [
                    {type: 'title', width: '80%'},
                    {type: 'text', width: '40%'},
                    {type: 'text', width: '25%'},
                ]},
            ]
        }),
        slotProps: {
            title: {
                //variant: "h1",
                order: 2,
                sx: {
                    mb: 2,
                }
            },
            subtitle: {
                //variant: "subtitle3",
                order: 1,
                sx: {
                    mb: 0,
                }
            },
            body: {
                //variant: "body1",
                order: 3,
            },
        }
    },    
];

export const widgetIcon = () =>layouts[0].icon;