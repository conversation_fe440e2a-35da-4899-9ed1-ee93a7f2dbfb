import { useState } from 'react';
import {
  Grid2 as Grid,
  Card,
  CardContent,
  Typography,
  <PERSON>,
  Button
} from '@mui/material';
import {
  AccountBalance as FinancialIcon,
  TrendingUp as RevenueIcon,
  Payment as PaymentIcon,
  Warning as OverdueIcon,
  Assessment as ProfitIcon,
  MonetizationOn as MoneyIcon
} from '@mui/icons-material';
import { LineChart, PieChart, DataTable, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';
import { ReportCard, ReportFilters } from '../components';

export const FinancialReports = () => {
  const [filters, setFilters] = useState({});

  // Mock data for charts
  const revenueData = {
    data: [
      {
        id: 'Revenue',
        data: [
          { x: 'Jan', y: 42500 },
          { x: 'Feb', y: 38200 },
          { x: 'Mar', y: 45800 },
          { x: 'Apr', y: 52100 },
          { x: 'May', y: 48900 },
          { x: 'Jun', y: 55300 },
          { x: 'Jul', y: 61200 }
        ]
      },
      {
        id: 'Profit',
        data: [
          { x: 'Jan', y: 12750 },
          { x: 'Feb', y: 11460 },
          { x: 'Mar', y: 13740 },
          { x: 'Apr', y: 15630 },
          { x: 'May', y: 14670 },
          { x: 'Jun', y: 16590 },
          { x: 'Jul', y: 18360 }
        ]
      }
    ]
  };

  const paymentMethodsData = {
    data: [
      { id: 'Credit Card', value: 65, color: '#4CAF50' },
      { id: 'Bank Transfer', value: 25, color: '#2196F3' },
      { id: 'Check', value: 8, color: '#FF9800' },
      { id: 'Cash', value: 2, color: '#9C27B0' }
    ]
  };

  const financialTableData = {
    columns: [
      { field: 'id', headerName: 'Invoice ID', width: 120 },
      { field: 'customer', headerName: 'Customer', width: 200 },
      { field: 'amount', headerName: 'Amount', width: 120 },
      { field: 'status', headerName: 'Status', width: 120 },
      { field: 'dueDate', headerName: 'Due Date', width: 150 },
      { field: 'paidDate', headerName: 'Paid Date', width: 150 }
    ],
    rows: [
      { id: 'INV-001', customer: 'Acme Corporation', amount: '$12,500.00', status: 'Paid', dueDate: '2024-01-10', paidDate: '2024-01-08' },
      { id: 'INV-002', customer: 'Tech Solutions Inc', amount: '$8,900.50', status: 'Paid', dueDate: '2024-01-15', paidDate: '2024-01-14' },
      { id: 'INV-003', customer: 'Global Industries', amount: '$21,000.00', status: 'Overdue', dueDate: '2024-01-05', paidDate: '-' },
      { id: 'INV-004', customer: 'StartUp Logistics', amount: '$4,500.75', status: 'Pending', dueDate: '2024-01-20', paidDate: '-' },
      { id: 'INV-005', customer: 'Enterprise Ltd', amount: '$32,000.00', status: 'Paid', dueDate: '2024-01-12', paidDate: '2024-01-11' }
    ]
  };

  const reports = [
    {
      title: 'Revenue Trends',
      description: 'Monthly and quarterly revenue analysis with growth metrics',
      icon: <RevenueIcon />,
      color: '#4CAF50',
      metrics: [
        { label: 'This Month', value: '$61,200' },
        { label: 'Growth', value: '+10.7%' },
        { label: 'YTD Revenue', value: '$343,700' }
      ],
      tags: ['Revenue', 'Growth', 'Trends'],
      lastUpdated: '1 hour ago'
    },
    {
      title: 'Payment Methods Analysis',
      description: 'Breakdown of payment methods and success rates',
      icon: <PaymentIcon />,
      color: '#2196F3',
      metrics: [
        { label: 'Success Rate', value: '97.8%' },
        { label: 'Primary Method', value: 'Credit Card' },
        { label: 'Failed Payments', value: '2.2%' }
      ],
      tags: ['Payments', 'Methods', 'Success'],
      lastUpdated: '2 hours ago'
    },
    {
      title: 'Outstanding Balances',
      description: 'Track overdue accounts and collection metrics',
      icon: <OverdueIcon />,
      color: '#FF9800',
      metrics: [
        { label: 'Total Outstanding', value: '$45,230' },
        { label: 'Overdue Accounts', value: '8' },
        { label: 'Avg Days Overdue', value: '23' }
      ],
      tags: ['Collections', 'Overdue', 'Risk'],
      lastUpdated: '30 minutes ago'
    },
    {
      title: 'Profit Margin Analysis',
      description: 'Service profitability and margin trends',
      icon: <ProfitIcon />,
      color: '#9C27B0',
      metrics: [
        { label: 'Gross Margin', value: '30.2%' },
        { label: 'Net Profit', value: '$18,360' },
        { label: 'Margin Trend', value: '+2.1%' }
      ],
      tags: ['Profit', 'Margins', 'Profitability'],
      lastUpdated: '1 hour ago'
    }
  ];

  const customFilters = [
    {
      key: 'paymentMethod',
      label: 'Payment Method',
      type: 'select',
      options: [
        { value: 'credit-card', label: 'Credit Card' },
        { value: 'bank-transfer', label: 'Bank Transfer' },
        { value: 'check', label: 'Check' },
        { value: 'cash', label: 'Cash' }
      ]
    },
    {
      key: 'invoiceStatus',
      label: 'Invoice Status',
      type: 'select',
      options: [
        { value: 'paid', label: 'Paid' },
        { value: 'pending', label: 'Pending' },
        { value: 'overdue', label: 'Overdue' },
        { value: 'cancelled', label: 'Cancelled' }
      ]
    }
  ];

  const handleViewReport = (reportTitle) => {
    console.log('Viewing report:', reportTitle);
  };

  const handleDownloadReport = (reportTitle) => {
    console.log('Downloading report:', reportTitle);
  };

  const handlePrintReport = (reportTitle) => {
    console.log('Printing report:', reportTitle);
  };

  return (
    <Box>
      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={() => setFilters({})}
        dateRange={true}
        customerFilter={true}
        customFilters={customFilters}
      />

      {/* Key Metrics */}
      <MetricCardGroup sx={{ mb: 4 }}>
        <MetricCard
          title="Total Revenue"
          value="$343,700"
          change="+10.7%"
          changeType="positive"
          icon={<MoneyIcon />}
          color="primary"
        />
        <MetricCard
          title="Net Profit"
          value="$103,110"
          change="****%"
          changeType="positive"
          icon={<ProfitIcon />}
          color="success"
        />
        <MetricCard
          title="Outstanding"
          value="$45,230"
          change="-$5,200"
          changeType="positive"
          icon={<OverdueIcon />}
          color="warning"
        />
        <MetricCard
          title="Profit Margin"
          value="30.2%"
          change="+2.1%"
          changeType="positive"
          icon={<RevenueIcon />}
          color="info"
        />
      </MetricCardGroup>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Revenue & Profit Trends
              </Typography>
              <Box sx={{ height: 300 }}>
                <LineChart data={revenueData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Payment Methods Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <PieChart data={paymentMethodsData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Report Cards */}
      <Typography variant="h5" sx={{ mb: 3 }}>
        Available Reports
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reports.map((report, index) => (
          <Grid size={{ xs: 12, md: 6, lg: 3 }} key={index}>
            <ReportCard
              {...report}
              onView={() => handleViewReport(report.title)}
              onDownload={() => handleDownloadReport(report.title)}
              onPrint={() => handlePrintReport(report.title)}
            />
          </Grid>
        ))}
      </Grid>

      {/* Recent Invoices Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Recent Invoices
            </Typography>
            <Button variant="outlined" size="small">
              View All Invoices
            </Button>
          </Box>
          <DataTable
            columns={financialTableData.columns}
            rows={financialTableData.rows}
            pageSize={5}
            disableSelectionOnClick
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default FinancialReports;
