import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { produce } from 'immer';

// Helper function to create a deep copy of an array
const deepCopy = (arr) => {
    try {
        // Try to use structuredClone first
        if (typeof structuredClone === 'function') return structuredClone(arr);
        // Fall back to JSON.parse/stringify, but this will lose functions
        return JSON.parse(JSON.stringify(arr));
    } catch (error) {
        console.warn('Failed to deep copy with structuredClone, falling back to manual copy', error);
        // If structuredClone fails (e.g., due to functions), use a manual deep copy
        return manualDeepCopy(arr);
    }
};

// Manual deep copy function that preserves functions
const manualDeepCopy = (obj) => {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    // Handle Date
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }

    // Handle Array
    if (Array.isArray(obj)) {
        return obj.map(item => manualDeepCopy(item));
    }

    // Handle Object
    if (obj instanceof Object) {
        const copy = {};
        Object.keys(obj).forEach(key => {
            copy[key] = manualDeepCopy(obj[key]);
        });
        return copy;
    }

    throw new Error(`Unable to copy obj! Its type isn't supported.`);
};

/*
Hook to manage form logic
props:
    initialValues: array with the initial values of the form fields
    options: object with the following properties:
        onFieldChange: optional function to be called when a field changes
        onFieldBlur: optional function to be called when a field is blurred
        validate: optional function to validate the form fields
        onSubmit: function to be called when the form is submitted
        onBeforeSubmit: optional function to be called before the form is submitted
        onAfterSubmit: optional function to be called after the form is submitted
*/
export const useForm = (initialValues, options = {}) => {
    const { t } = useTranslation();
    const [values, setValues] = useState(() =>
        produce(initialValues || [], draft => draft)
    );
    const [errors, setErrors] = useState({});

    const handleChange = useCallback((e, append = false) => {
        const { name, value } = e.target;
        let checked;
        if (e.target?.type === 'checkbox') checked = e.target.checked;

        setValues(produce(draft => {
            const idx = draft.findIndex(a => a.name === name);
            if (idx === -1) {
                draft.push({ name, value, checked: !!checked });
            } else {
                if (append) {
                    if (!Array.isArray(draft[idx].value)) draft[idx].value = [draft[idx].value];
                    draft[idx].value.push(value);
                } else draft[idx].value = value;
                if (checked !== undefined) draft[idx].checked = !!checked;
            }
        }));

        if (options?.onFieldChange) {
            options.onFieldChange({
                e,
                name,
                value,
                values,
                checked,
                setErrors,
                setValues,
            });
        }
    }, [options?.onFieldChange, values]);

    const handleBlur = useCallback(e => {
        if (options?.onFieldBlur) {
            options.onFieldBlur({ e, values, setErrors, setValues });
        }
    }, [options?.onFieldBlur, values]);

    const resetForm = useCallback((forceClear = false) => {
        if (forceClear) setValues([]);
        else setValues(produce(initialValues || [], draft => draft));
        setErrors({});
    }, [initialValues]);

    const handleSubmit = useCallback(async e => {
        if (e.preventDefault) e.preventDefault();

        try {
            // create a deep copy of values and filter unchecked checkboxes
            let updatedValues = deepCopy(values);
            if (updatedValues.length === 0) return;
            updatedValues = updatedValues.filter(a => a.checked === undefined || a.checked);

            // call onBeforeSubmit with a deep copy if it exists
            if (options.onBeforeSubmit) {
                updatedValues = options.onBeforeSubmit(deepCopy(updatedValues));
            }

            // validate the updated values
            let validationErrors = {};
            if (options.validate) {
                validationErrors = options.validate(updatedValues);
            } else {
                const requiredFields = updatedValues.filter(a => a.required && (!a.value || (Array.isArray(a.value) && a.value?.length === 0) || (typeof a.value === 'object' && Object.keys(a.value).length === 0)));
                requiredFields.forEach(field => {
                    validationErrors[field.name] = t('error:required');
                });
            }

            if (Object.keys(validationErrors).length > 0) {
                setErrors(validationErrors);
                return;
            }

            // submit the form
            if (options.onSubmit) {
                const _values = {};
                updatedValues.forEach(field => {
                    _values[field.name] = field.value;
                });
                const res = await options.onSubmit(_values, setErrors);
                if (options.onAfterSubmit) {
                    options.onAfterSubmit(updatedValues, resetForm);
                }
                return res;
            }
        } catch (error) {
            console.error('Error in form submission:', error);
            setErrors({form: t('error:default')});
            return null;
        }
    }, [values, options, resetForm, t]);

    // Manually update the form values.
    // newValues can be an object (for single field updates) or an array of objects (for bulk updates).
    // newValues can have two formats: {name: value} to update a simple field value, or {name, value, checked, ...} to update the value and other states like checked or disabled
    const setFormValues = useCallback(newValues => {
        if (newValues) {
            setValues(
                produce(draft => {
                    if (Array.isArray(newValues)) {
                        newValues.forEach(newValue => {
                            const idx = draft.findIndex(a => a.name === newValue.name);
                            if (idx !== -1) {
                                Object.assign(draft[idx], newValue);
                            } else {
                                draft.push(newValue);
                            }
                        });
                    } else {
                        for (const key in newValues) {
                            const value = newValues[key];
                            const idx = draft.findIndex(a => a.name === key);
                            if (idx !== -1) {
                                draft[idx].value = value;
                            } else {
                                draft.push({ name: key, value });
                            }
                        }
                    }
                })
            );
        }
    }, []);

    useEffect(() => {
        if (initialValues) setValues(produce(initialValues || [], draft => draft));
    }, [initialValues]);

    return {
        values,
        setValues,
        setFormValues,
        handleChange,
        handleBlur,
        handleSubmit,
        resetForm,
        errors: Object.keys(errors).length > 0 ? errors : null,
    };
};