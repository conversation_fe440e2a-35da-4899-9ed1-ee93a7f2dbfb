import React, {useState, useEffect, useMemo} from 'react';
import { Calendar } from '@siteboss-frontend/shared/components';
import { eventFlatten } from '@siteboss-frontend/shared/utils';
import { useApi } from '@siteboss-frontend/shared';

import { startOfDay } from 'date-fns';

export const Events = ({userData, loading, ...props}) => {
    const [eventData, setEventData] = useState(null);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [searchText, setSearchText] = useState(null);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/event`, method: 'POST', data: {
            user_id: [userData.id], 
            event_status_id: [1, 2, 3, 4], 
            max_records: 1000, 
            include_users: 1,
            include_media: 1, 
            sort_col: "start_datetime",
            sort_direction: "asc",
        }}},
    ], [userData.id]);

    const { fetchData, loading:loadingData, ErrorBar } = useApi(apiParams[0]);

    // executes the api call
    useEffect(() => {
        const _loadData = async () => {
            const result = await fetchData({
                start_datetime: startOfDay(selectedDate).toISOString(), 
                search: searchText,        
            });
            if (result?.data?.events){
                let flat = eventFlatten(result.data.events);
                setEventData({
                    events: flat?.events || [], 
                    metaEvents: flat?.metaEvents || []
                });
            }
        }
        _loadData();
    }, [selectedDate, searchText, fetchData]);

    if (!userData) return null;

    return (
        <>
            {eventData && 
                <Calendar 
                    events={eventData} 
                    onDateChange={setSelectedDate} 
                    onSearchTextChange={setSearchText} 
                    loading={loading || loadingData}
                    selectedDate={selectedDate}
                />
            }
            <ErrorBar />
        </>
    );
}