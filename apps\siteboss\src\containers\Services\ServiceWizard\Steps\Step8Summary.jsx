import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, Paper, Box, Divider, Chip, Button } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { formatDate } from '@siteboss-frontend/shared/utils';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step8 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';

const Step8Summary = ({ data, formData, loading, serviceId, onChangeStep }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [localFormData, setLocalFormData] = useState(formData || {});
    const selectedLocations = useSelector(state => state.serviceWizard.selectedLocations);
    const selectedManagers = useSelector(state => state.serviceWizard.selectedManagers);

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        setLocalFormData(prev => {
            const updated = { ...prev, [field]: value };
            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Navigate to specific step
    const goToStep = (step) => {
        onChangeStep(step);
    };

    // Get step fields
    const fields = step8(localFormData);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Service Summary
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                    Review your service details before saving. Click on any section to edit.
                </Typography>
            </Grid>

            {/* Basic Information */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(0)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Basic Information
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Name:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.name || 'Not specified'}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Short Description:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.short_description || 'Not specified'}
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Availability */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(1)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Availability
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Start Date:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.has_start_date && localFormData.start_date 
                                    ? formatDate(localFormData.start_date) 
                                    : 'No start date'}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                End Date:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.has_end_date && localFormData.end_date 
                                    ? formatDate(localFormData.end_date) 
                                    : 'No end date'}
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Time Increments */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(2)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Time Increments
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Block Minutes:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.block_minutes || 'Not specified'} minutes
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Min/Max Timeslots:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                Min: {localFormData.min_timeslots || '1'}, 
                                Max: {localFormData.max_timeslots || '1'}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Booking Notice:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.min_booking_notice || '0'} minutes
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Locations */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(3)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Locations
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {selectedLocations && selectedLocations.length > 0 ? (
                                    selectedLocations.map(location => (
                                        <Chip 
                                            key={location.id} 
                                            label={location.name || `Location ${location.id}`} 
                                            size="small" 
                                        />
                                    ))
                                ) : (
                                    <Typography variant="body2">No locations selected</Typography>
                                )}
                            </Box>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Managers */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(4)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Managers
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {selectedManagers && selectedManagers.length > 0 ? (
                                    selectedManagers.map(manager => (
                                        <Chip 
                                            key={manager.id} 
                                            label={`${manager.first_name} ${manager.last_name}`} 
                                            size="small" 
                                        />
                                    ))
                                ) : (
                                    <Typography variant="body2">No managers selected</Typography>
                                )}
                            </Box>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Payment Options */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(5)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Payment Options
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Default Price:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                ${localFormData.default_price || '0.00'}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Token Name:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.default_token_name || 'Service Token'}
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Cancellation Policy */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2, cursor: 'pointer' }} onClick={() => goToStep(6)}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                            Cancellation Policy
                        </Typography>
                        <Button size="small" color="primary">
                            Edit
                        </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Cancellation Hours:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.cancellation_hours || '24'} hours
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="textSecondary">
                                Policy:
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Typography variant="body2">
                                {localFormData.cancellation_policy || 'No policy specified'}
                            </Typography>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>

            {/* Status */}
            <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Service Status
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    {fields[0].fields.map(field => (
                        <Grid item xs={12} key={field.name}>
                            {field.component === "RadioGroup" && (
                                <StyledRadioGroup
                                    label={field.label}
                                    name={field.name}
                                    value={localFormData[field.name] || field.value}
                                    onChange={(value) => handleChange(field.name, value)}
                                    required={field.required}
                                    options={field.options}
                                    disabled={loading}
                                />
                            )}
                        </Grid>
                    ))}
                </Paper>
            </Grid>
        </Grid>
    );
};

export default Step8Summary;
