import { EventSeatOutlined as ExtendedEventIcon, ChairAltOutlined as CompressedEventIcon } from '@mui/icons-material';
import { Extended } from './Extended/Extended';

export const widgetIcon = ExtendedEventIcon;

export const layouts = [
    {
        id: 1,
        name: 'Extended',
        component: Extended,
        icon: <ExtendedEventIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },
    /*{
        id: 2,
        name: 'Compressed',
        component: Compressed,
        icon: <CompressedEventIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
        }
    },*/
];