import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography } from '@mui/material';
import { Timeline, TimelineItem, TimelineContent, TimelineDot, TimelineConnector, TimelineSeparator } from '@mui/lab';
import {
    NewReleasesOutlined as NewIcon,
    HourglassEmptyOutlined as ProcessingIcon,
    LocalShippingOutlined as ShippingIcon,
    DoneOutlined as DeliveredIcon,
    ErrorOutlineOutlined as ErrorIcon,
    FiberNewOutlined as FiberNewIcon,
    KeyboardReturnOutlined as ReturnIcon
} from '@mui/icons-material';
import { formatDate } from '@siteboss-frontend/shared/utils';

const getStatusColor = (statusName) => {
    // Map status names to colors - using case-insensitive matching
    const status = statusName?.toLowerCase() || '';

    if (status.includes('new') || status.includes('pending')) return 'primary';
    if (status.includes('processing') || status.includes('preparing')) return 'warning';
    if (status.includes('fulfilling') || status.includes('packing')) return 'info';
    if (status.includes('shipped') || status.includes('shipping')) return 'secondary';
    if (status.includes('delivered') || status.includes('completed') || status.includes('done')) return 'success';
    if (status.includes('cancelled') || status.includes('canceled')) return 'error';
    if (status.includes('return')) return 'secondary';

    return 'primary'; // default color
};

const getStatusIcon = (statusName) => {
    // Map status names to icons - using case-insensitive matching
    const status = statusName?.toLowerCase() || '';

    if (status.includes('new') || status.includes('pending')) return <FiberNewIcon />;
    if (status.includes('processing') || status.includes('preparing')) return <ProcessingIcon />;
    if (status.includes('fulfilling') || status.includes('packing')) return <ShippingIcon />;
    if (status.includes('shipped') || status.includes('shipping')) return <ShippingIcon />;
    if (status.includes('delivered') || status.includes('completed') || status.includes('done')) return <DeliveredIcon />;
    if (status.includes('cancelled') || status.includes('canceled')) return <ErrorIcon />;
    if (status.includes('return')) return <ReturnIcon />;

    return <NewIcon />; // default icon
};

export const OrderHistory = ({ data, ...props }) => {
    const { t, isMobile } = useOutletContext();
    const language = useSelector(state => state.language);

    // Get order status history from the data prop
    const orderHistory = data?.order_status_history || [];

    if (!orderHistory || orderHistory.length === 0) return null;

    return (
        <Box sx={{ width: "100%", order: isMobile ? 3 : 2, mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
                Order History
            </Typography>

            <Timeline position={isMobile ? "right" : "alternate"}>
                {orderHistory.slice().reverse().map((historyItem, index) => (
                    <TimelineItem key={`order-history-${historyItem.id}`}>
                        <TimelineSeparator>
                            <TimelineDot color={getStatusColor(historyItem.order_status_name)}>
                                {getStatusIcon(historyItem.order_status_name)}
                            </TimelineDot>
                            {index < orderHistory.length - 1 && <TimelineConnector />}
                        </TimelineSeparator>

                        <TimelineContent sx={{ py: '12px', px: 2 }}>
                            <Typography variant="h6" component="span">
                                {historyItem.order_status_name}
                            </Typography>

                            <Typography variant="body2" color="text.secondary">
                                {formatDate(new Date(historyItem.logged_at), language.code)}
                            </Typography>

                            {historyItem.memo && (
                                <Typography variant="body2" sx={{ mt: 1 }}>
                                    {historyItem.memo}
                                </Typography>
                            )}

                            {historyItem.user_id && (
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                    Updated by User ID: {historyItem.user_id}
                                </Typography>
                            )}
                        </TimelineContent>
                    </TimelineItem>
                ))}
            </Timeline>
        </Box>
    );
};
