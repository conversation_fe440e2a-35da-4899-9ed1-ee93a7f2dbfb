import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { DataGrid, useGridApiContext } from '@mui/x-data-grid';
import { enUS, esES } from '@mui/x-data-grid/locales';

import Toolbar from './Toolbar';

export const ToolbarWrapper = ({allRows, onExpand, onDelete, toolbarSlots, toolbarSlotsProps, gridState, ...props}) => {
    const apiRef = useGridApiContext();
    const selectedRows = apiRef.current.getSelectedRows();
    const selectCount = selectedRows.size;
    const footerRowSelected = apiRef.current.getLocaleText('footerRowSelected');
    const toolbarExportCSV = apiRef.current.getLocaleText('toolbarExportCSV');
    const toolbarExportPrint = apiRef.current.getLocaleText('toolbarExportPrint');
    const filterPanelDeleteIconLabel = apiRef.current.getLocaleText('filterPanelDeleteIconLabel');
    const expandDetailPanel = apiRef.current.getLocaleText('expandDetailPanel');
    const allRowIds = apiRef.current.getAllRowIds();

    return (
        <Toolbar
            onExpand={onExpand}
            onDelete={onDelete}
            checkboxSelection={props.checkboxSelection || false}
            loading={Boolean(props.loading)}
            allRows={allRows}

            selectedRows={selectedRows}
            selectCount={selectCount}
            expandDetailPanel={expandDetailPanel}
            allRowIds={allRowIds}
            onExport={options => apiRef.current.exportDataAsCsv(options)}
            onPrint={options => apiRef.current.exportDataAsPrint(options)}
            localeText={{
                footerRowSelected,
                toolbarExportCSV,
                toolbarExportPrint,
                filterPanelDeleteIconLabel,
            }}

            {...(toolbarSlotsProps || {})}
        >
            {toolbarSlots}
        </Toolbar>
    );
}

export const Table = ({onExpand, onDelete, allRows, toolbarSlots, toolbarSlotsProps, ...props}) => {
    const language = useSelector(state => state.language);
    const langCode = language.code === "es" ? esES : enUS;
    const [gridState, setGridState] = useState({});

    return (
        <div style={{ display: 'flex', flexDirection: 'column' }}>
            <DataGrid
                {...props}
                localeText={langCode.components.MuiDataGrid.defaultProps.localeText}
                onStateChange={newState => setGridState(newState)}
                rowHeight={props.dynamicRowHeight ? 'auto' : props.rowHeight}
                //autoHeight={props.dynamicRowHeight}
                slots={{...props?.slots,
                    toolbar: () => (
                        <ToolbarWrapper
                            onExpand={onExpand}
                            onDelete={onDelete}
                            allRows={allRows}
                            toolbarSlots={toolbarSlots}
                            toolbarSlotsProps={toolbarSlotsProps}
                            checkboxSelection={props.checkboxSelection || false}
                            gridState={gridState}
                        />
                    ),
                }}
            />
       </div>
    );
}
