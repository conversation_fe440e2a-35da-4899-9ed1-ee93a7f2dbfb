import { useCallback, useState, useMemo, useEffect } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { useApi } from '../../../../../api/useApi';

export const useProductAddons = ({ variantId, product, selectedAddons:selected, onSelect }) => {
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);
    const [selectedAddons, setSelectedAddons] = useState([...((selected?.length ? selected : currentShopItem?.addons) || [])]);

    const apiParams = useMemo(() => ({params: {endpoint: `/public/product/variant/${variantId}/addons`, method: 'GET' }, enableCache: true, keepCache: true}), [variantId]);
    const { fetchData: fetchAddons, data:addons, loading, errors } = useApi(apiParams);

    const handleAddonSelection = useCallback((addon, limit = null) => {
        let _addons = [];
        setSelectedAddons(prev => {
            _addons = [...prev];

            // remove extra entries from the bottom of the array
            if (limit && prev.length >= limit) {
                _addons = _addons.slice(0, limit - 1);
            }

            //addon.variant_id = variantId; already set in the backend

            const index = prev.findIndex(a => +a.id === +addon.id);
            if (index > -1) _addons = _addons.filter(a => +a.id !== +addon.id);
            else _addons.push(addon);

            return _addons;
        });
        if (onSelect) onSelect(_addons);
    }, [onSelect, variantId]);

    useEffect(() => {
        if (variantId && product?.product_variants?.find(a => a.id === variantId && a.has_addons)) fetchAddons();
    }, [variantId, product, fetchAddons]);

    return {
        addons,
        loading,
        errors,
        handleAddonSelection,
        selectedAddons,
        setSelectedAddons,
    };
}