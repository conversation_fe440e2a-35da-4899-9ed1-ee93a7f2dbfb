import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Services } from '../Services';

// Mock the API calls
jest.mock('@siteboss-frontend/shared', () => ({
    ...jest.requireActual('@siteboss-frontend/shared'),
    useApi: jest.fn(() => ({
        data: null,
        loading: false,
        error: null,
        call: jest.fn()
    }))
}));

// Mock the shared components
jest.mock('@siteboss-frontend/shared/components', () => ({
    Title: ({ children }) => <h1>{children}</h1>,
    DataTable: () => <div data-testid="data-table">DataTable</div>,
    SuccessBar: ({ message }) => <div data-testid="success-bar">{message}</div>,
    ErrorBar: ({ error }) => <div data-testid="error-bar">{error?.message || 'Error'}</div>
}));

// Mock the child components
jest.mock('../ServicesList', () => ({
    __esModule: true,
    default: ({ services }) => (
        <div data-testid="services-list">
            {services.map(service => (
                <div key={service.id}>{service.name}</div>
            ))}
        </div>
    )
}));

jest.mock('../ServiceDetails', () => ({
    __esModule: true,
    default: ({ service }) => (
        <div data-testid="service-details">
            {service?.name || 'No service'}
        </div>
    )
}));

// Create a mock store
const mockStore = configureStore([]);
const theme = createTheme();

describe('Services Component', () => {
    let store;
    
    beforeEach(() => {
        store = mockStore({
            serviceWizard: {
                id: null,
                serviceData: null,
                selectedLocations: [],
                selectedManagers: [],
                formData: null
            }
        });
        
        // Reset the mock API
        const { useApi } = require('@siteboss-frontend/shared');
        useApi.mockImplementation(() => ({
            data: {
                services: [
                    { id: 1, name: 'Service 1', description: 'Description 1', status: 1 },
                    { id: 2, name: 'Service 2', description: 'Description 2', status: 0 }
                ]
            },
            loading: false,
            error: null,
            call: jest.fn()
        }));
    });
    
    it('renders the services list when no ID is provided', () => {
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services']}>
                        <Routes>
                            <Route path="/services" element={<Services />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByTestId('services-list')).toBeInTheDocument();
        expect(screen.getByText('Service 1')).toBeInTheDocument();
        expect(screen.getByText('Service 2')).toBeInTheDocument();
    });
    
    it('renders the service details when an ID is provided', () => {
        const { useApi } = require('@siteboss-frontend/shared');
        useApi.mockImplementation((config) => {
            if (config?.params?.endpoint === '/service/1') {
                return {
                    data: { id: 1, name: 'Service 1', description: 'Description 1', status: 1 },
                    loading: false,
                    error: null,
                    call: jest.fn()
                };
            }
            return {
                data: null,
                loading: false,
                error: null,
                call: jest.fn()
            };
        });
        
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services/1']}>
                        <Routes>
                            <Route path="/services/:id" element={<Services />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByTestId('service-details')).toBeInTheDocument();
        expect(screen.getByText('Service 1')).toBeInTheDocument();
    });
    
    it('displays an error message when API call fails', () => {
        const { useApi } = require('@siteboss-frontend/shared');
        useApi.mockImplementation(() => ({
            data: null,
            loading: false,
            error: new Error('Failed to fetch services'),
            call: jest.fn()
        }));
        
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services']}>
                        <Routes>
                            <Route path="/services" element={<Services />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByTestId('error-bar')).toBeInTheDocument();
        expect(screen.getByText('Error')).toBeInTheDocument();
    });
});
