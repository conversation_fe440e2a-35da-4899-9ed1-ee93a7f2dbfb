import React, { useState, useCallback, useId } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack, Button } from '@mui/material';

import FormItem from '../../../../../components/FormItem';
import { createCurrencyFormatter } from '../../../../../utils/currency';
import { ButtonWrapper, Caption } from '../../../common/pos';

const cashAmounts = [{id: 1}, {id: 5}, {id: 10}, {id: 20}, {id: 50}, {id: 100}];
export const Cash = ({paymentMethod, paymentMethodId, amount, loading: parentLoading, onPaymentProcess, onPaymentChange, slotProps, ...props}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);    
    const currencyFormatter = createCurrencyFormatter(language, currency, 0);
    const id = useId();

    const [payment, setPayment] = useState(0);

    const handleCashSelect = useCallback(item => {
        let newValue = null;
        setPayment(prev => {
            newValue = (item?.ignorePrev ? 0 : +prev) + +item.id;
            return newValue;
        });
        if (onPaymentChange) onPaymentChange({id, values: {amount: newValue}, paymentMethod, paymentMethodId});
    }, [id, onPaymentChange, paymentMethod, paymentMethodId]);

    const handleChange = useCallback(e => {
        e.preventDefault();
        setPayment(e.target.value);
    }, []);

    const handleBur = useCallback(e => {
        e.preventDefault();
        handleCashSelect({id: e.target.value, ignorePrev: true});
    }, [handleCashSelect]);

    const handlePay = useCallback(async () => {
        if (onPaymentProcess) await onPaymentProcess({id, values: {amount: payment}, paymentMethod, paymentMethodId});
    }, [id, onPaymentProcess, payment, paymentMethod, paymentMethodId]);

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            <Stack direction="row" spacing={1} useFlexGap flexWrap="wrap">
                <ButtonWrapper items={cashAmounts} disabled={amount <= -10} onClick={handleCashSelect} slotProps={slotProps} className="small">
                    {cashAmounts.map(item => (
                        <React.Fragment key={item.id}>
                            <Caption variant="h5" component="span" text={`${currencyFormatter.format(item.id, currency)}`} bold />
                        </React.Fragment>
                    ))}
                </ButtonWrapper>
            </Stack>
            <FormItem 
                label={t('pos:amount')}
                required
                component='MoneyField'
                name='amount'
                margin="normal"
                value={payment}
                onChange={handleChange}
                onBlur={handleBur}
                errors={!payment ? t('error:invalid') : null}
                {...slotProps?.input}
            />
            <Button 
                loading={parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl" 
                fullWidth 
                disabled={parentLoading || !payment} 
                onClick={handlePay} 
                {...slotProps?.button}
            >
                {t(`pos:${+payment < +amount ? 'processPartialPayment' : 'processPayment'}`)}
            </Button>
        </Stack>
    );
}