import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams, useOutletContext } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { resetInfo, setInfo } from '../../store/reducers/discountWizardSlice';
import { generateCouponCode } from '../../utils/couponUtils';
import {
    Container,
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    Chip,
    IconButton,
    Tooltip
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useApi } from '@siteboss-frontend/shared';
import { LoadingBar, ErrorBar, Confirm } from '@siteboss-frontend/shared/components';

export const DiscountManager = () => {
    const { t } = useOutletContext();
    const navigate = useNavigate();
    const { id } = useParams();
    const dispatch = useDispatch();
    const [showConfirm, setShowConfirm] = useState(false);
    const [deleteParams, setDeleteParams] = useState(null);

    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [discounts, setDiscounts] = useState([]);
    const [totalCount, setTotalCount] = useState(0);

    // API calls
    const { fetchData: fetchDiscounts, loading: discountsLoading, errors: discountsError } = useApi({
        params: {
            endpoint: '/coupon',
            method: 'GET'
        }
    });

    const { fetchData: deleteDiscount, loading: deleteLoading, errors: deleteError } = useApi({
        params: {
            endpoint: '/coupon/delete',
            method: 'DELETE'
        }
    });

    const loadDiscounts = useCallback(async () => {
        try {
            const response = await fetchDiscounts({
                page_no: page + 1,
                max_records: rowsPerPage,
                sort_col: 'name',
                sort_direction: 'ASC'
            });

            if (response?.data && Array.isArray(response.data)) {
                setDiscounts(response.data);
                setTotalCount(response.data.length);
            }
        } catch (error) {
            console.error('Error loading discounts:', error);
        }
    }, [fetchDiscounts, page, rowsPerPage]);

    useEffect(() => {
        loadDiscounts();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [page, rowsPerPage]);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleAddDiscount = () => {
        // Reset the discount wizard form data before navigating
        dispatch(resetInfo());

        // Set default values for new discount
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];

        // Create default form data with required defaults
        const defaultFormData = {
            // Auto-generated coupon code
            coupon_code: generateCouponCode(),
            // Require coupon code by default (auto_apply = 0)
            auto_apply: 0,
            // Unlimited uses by default
            max_uses: '0',
            unlimited: 1,
            // Valid from today
            valid_from: formattedToday,
            // No end date by default
            valid_until: null,
            no_end_date: 1,
            // Default status is active
            status: 1,
            // Other defaults
            combinable: 1,
            apply_to_all: 0,
            discount_type: 0,
            discount_amount: '10'
        };

        // Set the default form data in Redux
        dispatch(setInfo({ formData: defaultFormData }));

        // Navigate to the wizard
        navigate('/discounts/wizard');
    };

    const handleEditDiscount = (id) => {
        // Reset the discount wizard form data before navigating to edit
        dispatch(resetInfo());
        navigate(`/discounts/wizard/${id}`);
    };

    const handleDeleteDiscount = (id, name) => {
        setDeleteParams({ id, name });
        setShowConfirm(true);
    };

    const getDiscountTypeLabel = (type, amount) => {
        if (type === 0) {
            return `${amount}%`;
        } else if (type === 1) {
            return `$${amount}`;
        }
        return '';
    };

    const getStatusChip = (status) => {
        if (status === 1) {
            return <Chip label={t('discount:active')} color="success" size="small" />;
        } else {
            return <Chip label={t('discount:inactive')} color="default" size="small" />;
        }
    };

    if (discountsLoading || deleteLoading) {
        return <LoadingBar />;
    }

    return (
        <Container maxWidth="xl">
            {discountsError && <ErrorBar message={discountsError} />}
            {deleteError && <ErrorBar message={deleteError} />}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" component="h1">{t('discount:discountManager')}</Typography>
                <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleAddDiscount}
                >
                    {t('discount:addDiscount')}
                </Button>
            </Box>

            <Paper sx={{ width: '100%', overflow: 'hidden' }}>
                <TableContainer sx={{ maxHeight: 'calc(100vh - 250px)' }}>
                    <Table stickyHeader aria-label="discounts table">
                        <TableHead>
                            <TableRow>
                                <TableCell>{t('discount:name')}</TableCell>
                                <TableCell>{t('discount:code')}</TableCell>
                                <TableCell>{t('discount:type')}</TableCell>
                                <TableCell>{t('discount:validFrom')}</TableCell>
                                <TableCell>{t('discount:validUntil')}</TableCell>
                                <TableCell>{t('discount:status')}</TableCell>
                                <TableCell align="right">{t('common:actions')}</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {discounts.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={7} align="center">
                                        {t('discount:noDiscounts')}
                                    </TableCell>
                                </TableRow>
                            ) : (
                                discounts.map((discount) => (
                                    <TableRow key={discount.id} hover>
                                        <TableCell>{discount.name}</TableCell>
                                        <TableCell>{discount.coupon_code || t('discount:autoApply')}</TableCell>
                                        <TableCell>
                                            {getDiscountTypeLabel(discount.discount_type, discount.discount_amount)}
                                        </TableCell>
                                        <TableCell>
                                            {new Date(discount.valid_from).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell>
                                            {discount.valid_until
                                                ? new Date(discount.valid_until).toLocaleDateString()
                                                : t('discount:noEndDate')}
                                        </TableCell>
                                        <TableCell>
                                            {getStatusChip(discount.status)}
                                        </TableCell>
                                        <TableCell align="right">
                                            <Tooltip title={t('common:edit')}>
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleEditDiscount(discount.id)}
                                                >
                                                    <EditIcon fontSize="small" />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title={t('common:delete')}>
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleDeleteDiscount(discount.id, discount.name)}
                                                >
                                                    <DeleteIcon fontSize="small" />
                                                </IconButton>
                                            </Tooltip>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25, 50]}
                    component="div"
                    count={totalCount}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            </Paper>

            {showConfirm && deleteParams && (
                <Confirm
                    open={showConfirm}
                    title={t('discount:deleteConfirmTitle')}
                    message={t('discount:deleteConfirmMessage', { name: deleteParams.name })}
                    onConfirm={async () => {
                        const response = await deleteDiscount({ id: deleteParams.id });
                        if (!response?.errors) {
                            loadDiscounts();
                        }
                        setShowConfirm(false);
                        setDeleteParams(null);
                    }}
                    onDecline={() => {
                        setShowConfirm(false);
                        setDeleteParams(null);
                    }}
                />
            )}
        </Container>
    );
};

export default DiscountManager;
