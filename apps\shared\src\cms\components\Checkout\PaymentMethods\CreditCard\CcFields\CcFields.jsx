import React, { useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Container, Paper, Grid2, Typography, Stack, useTheme, useMediaQuery } from '@mui/material';

import styles from './CcFields.module.scss';

export const CcFields = React.forwardRef(({fields, amount, onTokenCapture, onErrors, slotProps, ...props}, ref) => {
    const { t } = useTranslation();
    const currency = useSelector(state => state.company.currency);   
    const company = useSelector(state => state.company);
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const theme = useTheme();

    const processCreditCardEntry = useCallback(token => {
        if (onTokenCapture) onTokenCapture(token);
    },[onTokenCapture]);

    const validateFields=useCallback((field, status)=>{
        onErrors(prev => ({...prev, [field]: null}));

        const _field = fields?.find(f => f.name === field);
        if (_field && !status && onErrors) {
            onErrors(prev => ({...prev, [field]: t("creditCard:invalidField")}));
        }
    },[fields, onErrors, t]);


    useEffect(() => {
        if (!window.CollectJS) return;

        const textField = theme.components.MuiTextField.styleOverrides.root({theme});

        window.CollectJS.configure({
            variant: 'inline',
            styleSniffer: true,
            price: amount,
            currency,
            country: 'US',
            callback: token => {
                processCreditCardEntry(token);
            },
            validationCallback: (field, status)=>{
                validateFields(field, status);
            },
            fieldsAvailableCallback: () => {
                validateFields("ccnumber", false);
                validateFields("cvv", false);
                validateFields("ccexp", false);
            },                
            //googleFont:css_styles.font,
            customCss: `{
                "background-color": "${theme.palette.grey[50]}",
                "color": "${theme.palette.common.black}",
                "border-width": "1px",
                "border-style": "none none solid none",
                "border-color": "${theme.palette.grey[theme.palette.mode === "dark" ? 300 : 200]}",
                "padding": "0.5rem",
                "height": "auto !important",
                "text-align": "${isMobile ? "left" : "center"}",
                "font-family": "${textField?.['& label']?.fontFamily || undefined}",
                "font-size": "${isMobile ? (textField?.['& label']?.fontSize || undefined) : "1.25rem"}",
                "transition": "border-bottom-color ${theme.transitions.duration.shortest}ms"
            }`,
            placeholderCss: `{
                "color": "${theme.palette.common.black}"
            }`,
            focusCss:`{
                "border-bottom-color": "${theme.palette.common.black}",
                "border-bottom-style": "solid"
            }`,
            fields: {
                ccnumber: {
                    placeholder: t("creditCard:cardNumber"),
                    selector: '#bill_ccnumber',
                    title: t("creditCard:cardNumber"),
                    enableCardBrandPreviews: true,
                },
                ccexp: {
                    placeholder: 'MM / YY',
                    selector: '#bill_ccexp',
                    title: t("creditCard:expiration")
                },
                cvv: {
                    placeholder: t("creditCard:cvv"),
                    selector: '#bill_cvv',
                    title: t("creditCard:cvv")
                }
            }
        });
            
    }, [validateFields, processCreditCardEntry, amount, currency, theme, isMobile, t]);


    if (!window.CollectJS) return;

    return (
        <Container 
            component={Paper} 
            elevation={0} 
            className={styles.card} 
            {...slotProps}
            sx={{
                ...slotProps?.sx,
                backgroundColor: theme => theme.palette.grey[50],
                //border: theme => `1px solid ${theme.palette.grey[400]}`,
                boxShadow: theme => theme.shadows[2],
            }} 
            
        >
            {!isMobile &&
                <Stack direction="column" spacing={1} alignItems="flex-start" className={styles["cc-header"]}>
                    <Stack direction="row" spacing={1} alignItems="center" justifyContent="space-between" width="100%">
                        <Typography variant="caption" color="grey.700">{company.name}</Typography>
                        <Typography variant="caption" color="grey.700">{t("pos:paymentMethods.creditCard")}</Typography>
                    </Stack>
                    <Stack direction="row" spacing={1} className={styles.container} >
                        <div className={styles.chip} />
                        <div className={styles.touchless} />
                    </Stack>
                </Stack>
            }
            <Grid2 container ref={ref} spacing={2}>
                <Grid2 size={{xs:12}}>
                    <div id="bill_ccnumber" className={styles.field} />
                </Grid2>
                <Grid2 size={{xs:12, md: 6}}>
                    <div id="bill_ccexp" className={styles.field} />
                </Grid2>
                <Grid2 size={{xs:12, md: 6}}>
                    <div id="bill_cvv" className={styles.field} />
                </Grid2>
            </Grid2>
        </Container>
    );
});