import React, { useContext, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid2 } from '@mui/material';

import FormItem from '../../../../../../../../components/FormItem';
import { WithExtraInfo } from '../../../../../../common/pos';
import { PosProductDetailContext } from '../../../../../../../hooks/PosProductDetailContext';
import { useNewUser } from './useNewUser';

export const NewUser = ({user, onClose, ...props}) => {
    const { t } = useTranslation();

    const { fullPage, goToPreviousView } = useContext(PosProductDetailContext) || {};
    const { loading, fields, formFields, errors, ErrorBar, handleSubmit, handleChange, success } = useNewUser({user, goToPreviousView});

    // group by row id
    const rows = useMemo(() => (
        [...(formFields || [])].reduce((acc, field) => { 
            const group = acc.find(g => g[0]?.rowId === field.rowId);
            if (group) group.push(field);
            else acc.push([field]);
            return acc;
        }, [])
    ), [formFields]);


    useEffect(() => {
        if (!user && !fullPage || success) goToPreviousView();
    }, [user, fullPage, success, goToPreviousView]);

    if (!user) return null;

    return (
        <WithExtraInfo 
            title={`${user.first_name} ${user.last_name}`} 
            subtitle={t("user:newFamilyMember")}
            fullPage={fullPage} 
            goToPreviousView={fullPage ? onClose : goToPreviousView} 
            onSave={handleSubmit}
        >
            <ErrorBar />
            {rows.map((_, i) => (
                <Grid2 key={i} container spacing={2}>
                    {formFields?.filter(a => a.rowId === i+1).map(field => (
                        <Grid2 key={field.name} size={{xs: 12, lg: "grow"}}>
                            <FormItem 
                                key={field.name}
                                {...field}
                                label={field.label}
                                required={Boolean(field.required)}
                                component={field.component}
                                name={field.name}
                                options={field?.options?.length > 0 ? field.options : null}
                                margin="normal"
                                value={fields?.[field.name]}
                                onChange={handleChange}
                                errors={errors?.[field.name]}
                                disabled={loading}
                            />
                        </Grid2>
                    ))}
                </Grid2>
            ))}
        </WithExtraInfo>
    );
}