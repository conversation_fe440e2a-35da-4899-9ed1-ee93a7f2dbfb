import { useState, useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Stack, Tab } from '@mui/material';
import {
    SettingsOutlined as ParametersIcon,
    NotificationsOutlined as NotificationsIcon,
    DescriptionOutlined as PackingSlipIcon,
    LocalShippingOutlined as LabelsIcon,
    VerifiedUserOutlined as ComplianceIcon,
    SecurityOutlined as InsuranceIcon,
    AccountBoxOutlined as AccountIcon,
    RouteOutlined as RoutingIcon
} from '@mui/icons-material';
import { TabContext, TabPanel, TabList } from '@mui/lab';

import BasicInfo from '../BasicInfo';
import { useShipping } from './useShipping';
import { ShippingNotifications } from './ShippingNotifications';
import { ShippingAccount } from './ShippingAccount';
import { RoutingTable } from './RoutingTable';

export const Shipping = ({ value, errors, onChange, loading, merchantId }) => {
    const { t } = useOutletContext();
    const [selectedTab, setSelectedTab] = useState('parameters');

    const {
        fieldsByTab,
        loading: apiLoading,
    } = useShipping({ merchantId, onMainFormChange: onChange });

    const handleTabChange = useCallback((_, newValue) => {
        setSelectedTab(newValue);
    }, []);

    return (
        <TabContext value={selectedTab}>
            <Stack spacing={2} direction="column" useFlexGap>
                <TabList onChange={handleTabChange} aria-label="shipping configuration tabs">
                    <Tab label={t('shipping:parameters')} value="parameters" icon={<ParametersIcon />} />
                    <Tab label={t('shipping:notifications')} value="notifications" icon={<NotificationsIcon />} />
                    <Tab label={t('shipping:packingSlip')} value="packingSlip" icon={<PackingSlipIcon />} />
                    <Tab label={t('shipping:labels')} value="labels" icon={<LabelsIcon />} />
                    <Tab label={t('shipping:compliance')} value="compliance" icon={<ComplianceIcon />} />
                    <Tab label={t('shipping:insurance')} value="insurance" icon={<InsuranceIcon />} />
                    <Tab label={t('shipping:account')} value="account" icon={<AccountIcon />} />
                    <Tab label={t('shipping:routingTable')} value="routing" icon={<RoutingIcon />} />
                </TabList>

                <TabPanel value="parameters" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.parameters}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="notifications" sx={{ p: 0 }}>
                    <ShippingNotifications
                        value={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="packingSlip" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.packingSlip}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="labels" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.labels}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="compliance" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.compliance}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="insurance" sx={{ p: 0 }}>
                    <BasicInfo
                        fields={fieldsByTab.insurance}
                        values={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                    />
                </TabPanel>

                <TabPanel value="account" sx={{ p: 0 }}>
                    <ShippingAccount
                        value={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                        merchantId={merchantId}
                    />
                </TabPanel>

                <TabPanel value="routing" sx={{ p: 0 }}>
                    <RoutingTable
                        value={value}
                        errors={errors}
                        onChange={onChange}
                        loading={loading || apiLoading}
                        merchantId={merchantId}
                    />
                </TabPanel>
            </Stack>
        </TabContext>
    );
};
