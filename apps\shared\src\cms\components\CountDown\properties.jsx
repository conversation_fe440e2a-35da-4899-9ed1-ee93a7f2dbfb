import { addDays } from 'date-fns';
const tomorrow = addDays(new Date().setHours(23, 59, 59, 999), 1);

export const properties = [
    {
        name: 'dateTime',
        label: 'builder:component.countDown.dateTime',
        component: "DateTimePicker",
        value: tomorrow.toDateString(),
        size: "small",
        margin: "normal",
        disablePast: true,
    },
    {
        name: 'format',
        label: 'builder:component.countDown.format',
        component: "Select",
        options: [
            { id: 'long', slug: 'builder:component.countDown.formats.long' },
            { id: 'short', slug: 'builder:component.countDown.formats.short' },
            { id: 'shortest', slug: 'builder:component.countDown.formats.shortest' },
        ],
        value: 'shortest',
        size: "small",
        margin: "normal",
    },    
    {
        name: 'variant',
        label: 'builder:component.countDown.variant',
        component: "Select",
        options: [
            { id: 'h1', slug: 'builder:component.heading.types.h1' },
            { id: 'h2', slug: 'builder:component.heading.types.h2' },
            { id: 'h3', slug: 'builder:component.heading.types.h3' },
            { id: 'h4', slug: 'builder:component.heading.types.h4' },
            { id: 'h5', slug: 'builder:component.heading.types.h5' },
            { id: 'h6', slug: 'builder:component.heading.types.h6' },
            { id: 'subtitle1', slug: 'builder:component.heading.types.subtitle1' },
            { id: 'subtitle2', slug: 'builder:component.heading.types.subtitle2' },
            { id: 'body1', slug: 'builder:component.heading.types.body1' },
            { id: 'body2', slug: 'builder:component.heading.types.body2' },
            { id: 'caption', slug: 'builder:component.heading.types.caption' },
            { id: 'overline', slug: 'builder:component.heading.types.overline' },
        ],
        value: 'h1',
        size: "small",
        margin: "normal",
    },
    {
        name: 'interval',
        label: 'builder:component.countDown.interval',
        component: "NumberField",
        value: 1,
        size: "small",
        margin: "normal",
    },
    {
        name: 'intervalType',
        label: 'builder:component.countDown.intervalType',
        component: "Select",
        value: 'seconds',
        size: "small",
        margin: "normal",
        options: [
            { id: 'seconds', slug: 'general:time.seconds' },
            { id: 'minutes', slug: 'general:time.minutes' },
            { id: 'hours', slug: 'general:time.hours' },
            { id: 'days', slug: 'general:time.days' },
            { id: 'weeks', slug: 'general:time.weeks' },
            { id: 'months', slug: 'general:time.months' },
            { id: 'years', slug: 'general:time.years' },
        ]
    },
    {
        name: 'timeUpMessage',
        label: 'builder:component.countDown.timeUpMessage',
        component: "TextField",
        value: 'Time is up!',
        size: "small",
        margin: "normal",
    },
];