import React from 'react';
import { Stack, Box } from '@mui/material';

export const MediaLeft = ({ type, slots, slotProps }) => {
    return (
        <Stack 
            direction="row" 
            spacing={4} 
            alignItems="center"
            className="VideoLeft"
            {...slotProps?.cmsStack}
        >
            <Stack sx={{ flex: 1 }}>
                {+type === 1 
                    ? slots?.images?.({ ...slotProps?.content?.images }) 
                    : slots?.video?.({ ...slotProps?.content?.video })
                }
            </Stack>
            <Box spacing={2}sx={{ flex: 1 }}>
                {slots?.title?.({ ...slotProps?.title })}
                {slots?.subtitle?.({ ...slotProps?.subtitle })}
                {slots?.body?.({ ...slotProps?.body })}
                {slots?.ctas?.({ ...slotProps?.ctas })}
            </Box>
        </Stack>
    );
};
