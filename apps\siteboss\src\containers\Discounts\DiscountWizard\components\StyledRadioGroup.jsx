import React from 'react';
import { styled } from '@mui/material/styles';
import {
    FormControl,
    FormLabel,
    FormHelperText,
    ToggleButtonGroup,
    ToggleButton,
    Box,
    Typography
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

// Styled ToggleButton to look like an outlined button/tab
const StyledToggleButton = styled(ToggleButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1.5, 3),
    margin: theme.spacing(0.5),
    minWidth: '180px',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    textTransform: 'none',
    '&.Mui-selected': {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
        '&:hover': {
            backgroundColor: theme.palette.primary.dark,
        },
    },
    '&:hover': {
        backgroundColor: theme.palette.action.hover,
    },
}));

// Styled FormControl to match the RadioGroup
const StyledFormControl = styled(FormControl)(({ theme }) => ({
    margin: theme.spacing(1, 0),
    width: '100%',
}));

// Styled FormLabel to match the RadioGroup
const StyledFormLabel = styled(FormLabel)(({ theme }) => ({
    marginBottom: theme.spacing(1),
}));

/**
 * StyledRadioGroup component that looks like outlined buttons/tabs
 *
 * @param {Object} props - Component props
 * @param {string} props.label - Label for the radio group
 * @param {string} props.name - Name of the radio group
 * @param {boolean} props.required - Whether the field is required
 * @param {string} props.helperText - Helper text for the radio group
 * @param {Array} props.options - Options for the radio group
 * @param {Function} props.onChange - Function to call when the value changes
 * @param {string|number} props.value - Current value of the radio group
 * @returns {JSX.Element}
 */
export const StyledRadioGroup = ({
    label,
    name,
    required,
    helperText,
    options,
    onChange,
    value,
    error,
    ...props
}) => {
    // Handle change to match the RadioGroup onChange signature
    const handleChange = (event, newValue) => {
        // Always call onChange, even if newValue is null
        // This ensures that the component doesn't break when deselecting
        onChange({
            target: {
                name,
                value: newValue !== null ? newValue.toString() : ''
            }
        });
    };

    return (
        <StyledFormControl required={required} error={!!error}>
            {label && (
                <StyledFormLabel required={required}>
                    {label}
                </StyledFormLabel>
            )}

            <ToggleButtonGroup
                exclusive
                value={value !== undefined && value !== null ? value.toString() : ''}
                onChange={handleChange}
                aria-label={label}
                orientation="vertical"
                fullWidth
                {...props}
            >
                {options?.map((option) => (
                    <StyledToggleButton
                        key={`toggle-button-${name}-${option.id}`}
                        value={option.value.toString()}
                        aria-label={option.label}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                            {option.icon && (
                                <Box sx={{ mr: 2, color: String(value) === String(option.value) ? 'inherit' : 'action.active' }}>
                                    {option.icon}
                                </Box>
                            )}
                            <Typography variant="body1" sx={{ flexGrow: 1, textAlign: 'left' }}>
                                {option.label}
                            </Typography>
                            {String(value) === String(option.value) && (
                                <CheckCircleIcon sx={{ ml: 1 }} />
                            )}
                        </Box>
                    </StyledToggleButton>
                ))}
            </ToggleButtonGroup>

            {helperText && <FormHelperText>{helperText}</FormHelperText>}
            {error && <FormHelperText error>{error}</FormHelperText>}
        </StyledFormControl>
    );
};

export default StyledRadioGroup;
