import React from 'react';
import { expect, within } from 'storybook/test';
import { Robot } from '../Robot';

export default {
  title: "Shared/Components/404",
  tags: ["autodocs"],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    width: {
      description: "Width of the robot in pixels",
      control: "number",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: 300 },
      }
    },
    height: {
      description: "Height of the robot in pixels",
      control: "number",
      table: {
        type: { summary: "number" },
      }
    }
  }
};

export const RobotIllustration = {
  args: {
    width: 300,
    height: 300
  },
  render: (args) => (
    <div style={{ padding: '20px' }}>
      <Robot {...args} />
    </div>
  ),
  play: async ({ canvasElement, args, step }) => {
    
    const robotSvg = canvasElement.querySelector('svg');

    
    await step('Confirm the SVG', ()=>{
        expect(robotSvg).toBeTruthy();
        expect(robotSvg.tagName.toLowerCase()).toBe('svg');
    })
    
    await step('check that the height and width are what is proposed by the args', ()=>{
        if (args.width) {
          expect(robotSvg.getAttribute('width')).toBe(args.width.toString());
        }
        if (args.height) {
          expect(robotSvg.getAttribute('height')).toBe(args.height.toString());
        }
    })
  }
};
