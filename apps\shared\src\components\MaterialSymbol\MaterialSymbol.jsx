import React from 'react';
import { Box } from '@mui/material';

/**
 * MaterialSymbol component for rendering Google's Material Symbols
 * @see https://fonts.google.com/icons?icon.set=Material+Symbols
 *
 * @param {Object} props - Component props
 * @param {string} props.icon - The name of the icon to display
 * @param {string} props.variant - The variant of the icon (outlined, rounded, sharp)
 * @param {string} props.fill - Whether the icon should be filled (0, 1)
 * @param {string} props.weight - The weight of the icon (100-700)
 * @param {string} props.grade - The grade of the icon (-25 to 200)
 * @param {string} props.size - The size of the icon (xs, sm, md, lg, xl or a custom size in px)
 * @param {string} props.color - The color of the icon
 * @param {Object} props.sx - Additional styles to apply to the icon
 */
export const MaterialSymbol = ({
  icon,
  variant = 'outlined',
  fill = 0,
  weight = 400,
  grade = 0,
  size = 'md',
  color,
  sx = {},
  ...props
}) => {
  // Map size names to pixel values
  const sizeMap = {
    xs: '16px',
    sm: '20px',
    md: '24px',
    lg: '32px',
    xl: '40px',
  };

  // Calculate the font size based on the size prop
  const fontSize = sizeMap[size] || size || '24px';

  // Determine the font family based on the variant
  const fontFamily = `"Material Symbols ${variant.charAt(0).toUpperCase() + variant.slice(1)}"`;

  return (
    <Box
      component="div"
      className="material-symbols"
      sx={{
        fontFamily,
        fontSize,
        width: fontSize,
        height: fontSize,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontVariationSettings: `
          'FILL' ${fill},
          'wght' ${weight},
          'GRAD' ${grade},
          'opsz' ${fontSize.replace('px', '')}
        `,
        color,
        userSelect: 'none',
        ...sx,
      }}
      {...props}
    >
      {icon}
    </Box>
  );
};

export default MaterialSymbol;
