import React, { useCallback, useMemo } from 'react';
import { Navigate } from 'react-router-dom';
import { Box, Divider, Stack } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import EmptyCart from '../common/pos/EmptyCart';
import PosSuccess from '../PosSuccess';
import { layouts } from './layouts';
import { properties } from './properties';
import { usePosCheckout } from './usePosCheckout';
import Tip from './Tip';
import Totals from './Totals';
import PaymentMethods from './PaymentMethods';

export const Checkout = ({
    id,
    layoutId,
    fullPage = false,
    items = ["subtotal", "shipping", "tax", "tip", "payments", "priceAdjustments"],
    paymentMethods = ["scanCard", "card", "cash", "check", "giftCard", "managerDiscount"],
    tips = [10, 15, 20],
    showTips = true,
    showTotals = true,
    allowMultiplePayments = false,
    redirectToSuccess = false,

    printTo = [], // array of printers

    slotProps = {
        cmsContainer: {},       // MUI container props
        cmsStack: {},           // MUI stack props
        totals: {
            container: {},      // MUI container props
            totals: {},         // multiple MUI objects (check PosTotals/<Totals/>)
        },
        paymentMethods: {},     // multiple MUI objects (check <PaymentMethods/>)
        success: {},            // multiple MUI objects (check <Success/>).
    },
    condition = null,           // condition to render the element
    isBuilder = false,          // renders the builder wrapper around the element
    builderProps,
    children,
    ...props
}) => {
    const { 
        extraFields, 
        setExtraFields, 
        loading, 
        reduxStore, 
        orderFullfilled, 
        successRoute,
        cart,
        handlePaymentChange, 
        handleTipChange, 
        handlePaymentProcess,
        errorBars,
    } = usePosCheckout();

    const { slotProps: updatedSlotProps, canRender, customCss } = prepareComponent({name: "checkout", layoutId, layouts, slotProps, isBuilder, condition, localState: {
        orderFullfilled,
        loading,
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;

    const allItems = useMemo(() => {
        if (items?.length){
            if (items?.[0]?.id){
                return items?.map(a => a.id);
            }
        }
        return items;
    }, [items]);

    const allPaymentMethods = useMemo(() => {
        if (paymentMethods?.length){
            if (paymentMethods?.[0]?.id){
                return paymentMethods?.map(a => a.id);
            }
        }
        return paymentMethods;
    }, [paymentMethods]);

    const handlePaymentReset = useCallback(() => {
        setExtraFields([]);
    }, [setExtraFields]);

    if (!isBuilder && orderFullfilled?.fullfilled === true && redirectToSuccess) return <Navigate to={successRoute} />;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            {errorBars?.map((ErrorBar, index) => <ErrorBar key={index} />)}
            <Stack direction={{xs: "column", md: "row"}} spacing={2} useFlexGap sx={{justifyContent: "space-between"}} {...slotProps?.cmsStack}>
                {!isBuilder && orderFullfilled?.fullfilled ?
                    <PosSuccess showOrderPreview={false} showTransactions={false} showPrintButton={true} />
                :
                    (cart?.cart?.length > 0 || isBuilder) ?
                        <>
                            {(showTips || showTotals) &&
                                <Box sx={{position:"sticky", top: 0, height: "fit-content", mr: 4}}>
                                    {showTips && 
                                        <>
                                            <Tip tips={tips} loading={loading} amount={cart?.totals?.subtotal} onTipChange={handleTipChange} slotProps={slotProps?.tips} />
                                            {showTotals && <Divider sx={{mt: 1, mb: 2}} />}
                                        </>
                                    }
                                    {showTotals && <Totals items={allItems} extraFields={extraFields} slotProps={slotProps?.totals} />}
                                </Box>
                            }
                            <PaymentMethods 
                                loading={loading} 
                                allowMultiplePayments={allowMultiplePayments} 
                                paymentMethods={allPaymentMethods} 
                                slotProps={slotProps?.paymentMethods} 
                                onPaymentProcess={handlePaymentProcess} 
                                onPaymentChange={handlePaymentChange} 
                                onPaymentReset={handlePaymentReset}
                            />
                        </>
                    : <EmptyCart showBackButton={redirectToSuccess} />
                }
            </Stack>
            {children}
        </CmsContainer>
    );
}