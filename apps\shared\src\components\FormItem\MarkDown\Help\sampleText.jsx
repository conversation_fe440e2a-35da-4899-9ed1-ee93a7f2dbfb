import { generateTitle, generateSubTitle, generateParagraph, getRandomWord, capitalize } from "../../../../utils";

export const sampleText = () => `
# ${generateTitle()} \`${getRandomWord(1)}\`

${generateParagraph(5)}.

![${generateTitle()}](https://placehold.co/600x200 "${generateSubTitle()}")

${generateParagraph(2)}

${generateParagraph(7)}
${generateParagraph(5)}

${generateParagraph(3)}

---

## ${generateTitle()}

* ${generateTitle()} [${generateSubTitle()}](https://siteboss.net)
* ${generateTitle()}

### ${generateTitle()}

| ${capitalize(getRandomWord(1))} | ${capitalize(getRandomWord(1))} |
| :--- | ---: |
| ${generateSubTitle()}  | ${getRandomWord(1)}      |
| ${generateSubTitle()}  | \`${getRandomWord(1)}\` |

#### ${generateTitle()}

* [ ] ${generateSubTitle()}
* [x] ${generateSubTitle()}
`;

export const cheatSheet = [
    { slug: 'markdown:h1', code: text => `# ${text}`, helper: text => `# ${text}` },
    { slug: 'markdown:h2', code: text => `## ${text}`, helper: text => `## ${text}` },
    { slug: 'markdown:h3', code: text => `### ${text}`, helper: text => `### ${text}` },
    { slug: 'markdown:h4', code: text => `#### ${text}`, helper: text => `#### ${text}` },
    { slug: 'markdown:italic', code: text => `*${text}*`, helper: text => `*${text}*` },
    { slug: 'markdown:bold', code: text => `**${text}**`, helper: text => `**${text}**` },
    { slug: 'markdown:link', code: text => `[${text}](https://siteboss.net)`, helper: text => `[${text}](https://siteboss.net)` },
    { slug: 'markdown:image', code: text => `![${text}](https://placehold.co/200x50 "${text}")`, helper: text => `![${text}](https://placehold.co/200x50 "${text}")` },
    { 
        slug: ['markdown:list1', 'markdown:list2', 'markdown:list3', 'markdown:list4'], 
        code: text => (
`
- ${text[0]}
- ${text[1]}
- ${text[2]}
  - ${text[3]}
`), 
        helper: text => `- ${text[0]}<br/>- ${text[1]}<br/>- ${text[2]}<br/>&nbsp;&nbsp;- ${text[3]}` 
    },
    { 
        slug: ['markdown:list1', 'markdown:list2', 'markdown:list3', 'markdown:list4', 'markdown:list5'], 
        code: text => `1. ${text[0]}\n2. ${text[1]}\n3. ${text[2]}\n3.1. ${text[3]}\n3.2. ${text[4]}`, 
        helper: text => `1. ${text[0]}<br/>2. ${text[1]}<br/>3. ${text[2]}<br/>3.1. ${text[3]}<br/>3.2. ${text[4]}` 
    },
    { slug: 'markdown:divider', code: _ => `---`, helper: _ => `---` },
    { slug: ['markdown:check1', 'markdown:check2'], code: text => `* [x] ${text[0]}\n * [ ] ${text[1]}`, helper: text => `* [ ] ${text[0]}<br/>* [x] ${text[1]}` },
    { slug: ['markdown:code', 'markdown:backticks'], code: text => '`'+text[0]+'`', helper: text => '`'+text[0]+'`'+`<br/><em>${text[1]}</em>` },
    { 
        slug: 'markdown:codeBlock', 
        code: _ => '```js\nconst c = (n1, n2) => n1 + n2;\nconst a = 1;\nconst b = 2;\nlet d = a + b + c(3, b);\n```', 
        helper: text => '```js<br/>const c = (n1, n2) => n1 + n2;<br/>const a = 1;<br/>const b = 2;<br/>let d = a + b + c(3, b);<br/>```<br/><br/><em>'+text+'</em>' },
    { 
        slug: ['markdown:header1', 'markdown:header2', 'markdown:headerHelp', 'markdown:row1', 'markdown:row2'], 
        code: text => `| ${text[0]} | ${text[1]} |\n| ---: | :--- |\n| ${text[3]} | ${text[3]} |\n| ${text[4]} | ${text[4]} |`, 
        helper: text => `| ${text[0]} | ${text[1]} |<br/>| ---: | :--- |<br/>| ${text[3]} | ${text[3]} |<br/>| ${text[4]} | ${text[4]} |<br/><br/><em>${text[2]}</em>` 
    },
];