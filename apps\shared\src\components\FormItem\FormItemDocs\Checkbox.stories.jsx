import React from 'react';
import { Checkbox } from '../Checkbox/Checkbox';
import { action } from 'storybook/actions';

export default {
    title: 'Shared/Component Groups/Form Items/Checkbox',
    component: Checkbox,
    tags: ['autodocs'],
    argTypes: {
        label: {
            description: "The label for the checkbox",
            control: 'text',
            table: {
                type: {
                    summary: 'string'
                }
            }
        },
        name: {
            description: "The name of the checkbox field",
            control: 'text',
            table: {
                type: {
                    summary: 'string'
                }
            }
        },
        required: {
            description: "Whether the checkbox is required",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        errors: {
            description: "Error message for the checkbox",
            control: 'text',
            table: {
                type: {
                    summary: 'string'
                }
            }
        },
        loading: {
            description: "Whether the form is in a loading state",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        value: {
            description: "The value of the checkbox",
            control: 'text',
            table: {
                type: {
                    summary: 'string'
                }
            }
        },
        isSwitch: {
            description: "Whether to render as a switch instead of a checkbox.  Completely changes the component rendered from MuiCheckbox to Switch",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
        checked: {
            description: "Whether the checkbox is checked",
            control: 'boolean',
            table: {
                type: {
                    summary: 'boolean'
                },
                defaultValue: {
                    summary: false
                }
            }
        },
       
        onChange: {
            action: 'changed',
            table: {
                type: {
                    summary: 'function'
                }
            }
        }, labelPlacement: {
            description: "The placement of the label",
            control: 'select',
            options: ['end', 'start', 'top', 'bottom'],
            table: {
                type: {
                    summary: 'string'
                },
                defaultValue: {
                    summary: 'end'
                }
            }
        },
        margin:{
            description:"The margin applied to the form control",
            control:"select",
            options:['none', 'dense', 'normal'],
            table:{
                type:{summary: 'string'},
                defaultValue:{summary: "undefined"}
            }
        },
        sx:{
            description: "The system prop that allows defining system overrides as well as additional CSS styles.",
            table:{
                type:{
                    summary: "array<func | object | bool> | func | object",
                },
                defaultValue:{
                    summary: "undefined"
                }
            }
        },
        size:{
            description: "The size of the component",
            control: "select",
            options: ["medium", "small"],
            table:{
                type:{summary: "string"},
                defaultValue:{summary: "undefined"}
            }
        }
    }
};

// Playground story with default props
export const Playground = {
    args: {
        label: 'Checkbox Label',
        name: 'checkbox-name',
        onChange: action('onChange')
    }
};

// Story with checkbox as required
export const Required = {
    args: {
        ...Playground.args,
        required: true
    }
};

// Story with error state
export const WithError = {
    args: {
        ...Playground.args,
        errors: 'This field is required'
    }
};

// Story with loading state
export const Loading = {
    args: {
        ...Playground.args,
        loading: true
    }
};

// Story with switch instead of checkbox
export const AsSwitch = {
    args: {
        ...Playground.args,
        isSwitch: true
    }
};

// Story with small size
export const Sizes = {
    render:()=>(
        <div>
            <p>
                <Checkbox size="small" label="Small" />
            </p>
            <p>
                <Checkbox size="medium" label="Large" />
            </p>
        </div>
    )
};

// Story with different label placement
export const LabelPlacement = {
    render:()=>(
        <div>
            <p>
                <Checkbox labelPlacement={"start"} label="Start" />
            </p>
            <p>
                <Checkbox labelPlacement={"top"} label="Top" />
            </p>
            <p>
                <Checkbox labelPlacement={"end"} label="End" />
            </p>
            <p>
                <Checkbox labelPlacement={"end"} label="Bottom" />
            </p>
        </div>
    )
};

// A whimsical story
export const AgreeToFun = {
    args: {
        label: '🎉 I agree to have fun! 🎉',
        name: 'agree-to-fun',
        onChange: (e) => {
            action('onChange')(e);
            if (e.target.checked) {
                alert('Woohoo! Let the fun begin! 🎊');
            } else {
                alert('Aw, come on! Give fun a chance! 😊');
            }
        }
    }
};