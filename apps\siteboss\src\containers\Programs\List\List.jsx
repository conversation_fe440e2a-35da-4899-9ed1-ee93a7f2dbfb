import React, { useEffect, useCallback } from 'react';
import { useParams, useOutletContext } from 'react-router-dom';
import { Grid2, Box, Pagination, Stack, FormControl, InputLabel, Select, MenuItem, Typography } from '@mui/material';
import { DataTableNoRows, DataTableSearchInput } from '@siteboss-frontend/shared/components';
import { useProgramList, pageSizeOptions } from './useProgramList';
import ProgramCard from './ProgramCard';

export const List = ({ onExpand, onDelete, setSelected, selected, fetchCounter = 0, loading:parentLoading, ...props}) => {
    const { t, isMobile } = useOutletContext();
    const params = useParams();

    const {
        data,
        rows,
        order,
        page,
        pageSize,
        totalPages,
        totalRows,
        loading,
        ErrorBar,
        LoadingBar,
        handleRowSelection,
        setSearchText,
        setOrder,
        setPage,
        setPageSize,
    } = useProgramList({setSelected, fetchCounter, params});

    useEffect(() => {
        if (params.id && rows && (!selected || selected?.length === 0 || !selected.find(r => +r.id === +params.id))) {
            setSelected(rows.filter(r => +r.id === +params.id));
        }
        console.log(rows, params.id, setSelected, selected)
    }, [rows, params.id, setSelected, selected]);

    useEffect(() => {
        if (selected?.length > 0 && params.id && onExpand) {
            onExpand();
        }
        console.log(selected, params, onExpand)
    }, [selected, params, onExpand]);

    // This effect will run whenever fetchCounter changes
    useEffect(() => {
        if (fetchCounter > 0) {
            // Force a refresh of the data when fetchCounter changes
            setPage(1); // Reset to first page
        }
    }, [fetchCounter]);

    const handleCardSelect = useCallback((e, program) => {
        const isSelected = selected.some(s => s.id === program.id);
        if (isSelected) {
            setSelected(selected.filter(s => s.id !== program.id));
        } else {
            setSelected([...selected, program]);
        }
    }, [selected, setSelected]);

    const handleCardExpand = useCallback((e, program) => {
        if (!selected.some(s => s.id === program.id)) {
            setSelected([program]);
        }
        if (onExpand) onExpand();
    }, [selected, setSelected, onExpand]);

    const handlePageChange = useCallback((e, value) => {
        setPage(value);
    }, [setPage]);

    const handlePageSizeChange = useCallback((e) => {
        // Ensure we only set valid page sizes
        const newSize = e.target.value;
        if (pageSizeOptions.includes(newSize)) {
            setPageSize(newSize);
            setPage(1);
        }
    }, [setPageSize, setPage]);

    return (
        <>
            <ErrorBar />
            {!data && <LoadingBar />}

            {data && rows && (
                <Stack spacing={3}>
                    <Box sx={{ mb: 2 }}>
                        <DataTableSearchInput onSearchChange={setSearchText} />
                    </Box>

                    {rows.length === 0 ? (
                        <DataTableNoRows title={t('program:empty')} />
                    ) : (
                        <>
                            <Grid container spacing={3}>
                                {rows.map(program => (
                                    <Grid item xs={12} sm={6} md={4} lg={3} key={program.id}>
                                        <ProgramCard
                                            program={program}
                                            selected={selected.some(s => s.id === program.id)}
                                            onSelect={handleCardSelect}
                                            onExpand={handleCardExpand}
                                            onDelete={onDelete}
                                        />
                                    </Grid>
                                ))}
                            </Grid>

                            <Stack
                                direction={isMobile ? "column" : "row"}
                                spacing={2}
                                justifyContent="space-between"
                                alignItems={isMobile ? "center" : "flex-end"}
                            >
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Typography variant="body2" color="text.secondary">
                                        {t('general:showing')} {rows.length} {t('general:of')} {totalRows} {t('program:programs')}
                                    </Typography>
                                    <FormControl variant="outlined" size="small" sx={{ minWidth: 80 }}>
                                        <InputLabel id="page-size-select-label">{t('general:perPage')}</InputLabel>
                                        <Select
                                            labelId="page-size-select-label"
                                            value={pageSize}
                                            onChange={handlePageSizeChange}
                                            label={t('general:perPage')}
                                        >
                                            {pageSizeOptions.map(option => (
                                                <MenuItem key={option} value={option}>{option}</MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Stack>

                                <Pagination
                                    count={totalPages}
                                    page={page}
                                    onChange={handlePageChange}
                                    color="primary"
                                    disabled={loading || parentLoading}
                                    siblingCount={isMobile ? 0 : 1}
                                />
                            </Stack>
                        </>
                    )}
                </Stack>
            )}
        </>
    );
}