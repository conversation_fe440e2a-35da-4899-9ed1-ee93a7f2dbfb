import React from 'react';
import { Box } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

export const CustomHtml = ({
    id,
    content = '',
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        content: {},        // Box props for the content wrapper
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, isMobile, canRender, customCss, noContent } = prepareComponent({name: "custom-html", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;    

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}

            {...builderProps}
        >
            {customCss}
            {!content ? noContent :
                <Box {...slotProps?.content} dangerouslySetInnerHTML={{ __html: content }}/>
            }
            {children}
        </CmsContainer>
    );
};