import { useState, useEffect, useMemo, useCallback } from 'react';
import { useOutletContext, useParams } from 'react-router-dom';
import { DataTable } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from '../../../components/KeycloakProvider/tenantConfig';

const apiParams = [
    {params: {endpoint: '/clients', method: 'GET', data: {
        page_no: 1,
        max_records: 10,
        sort_col: 'id',
        sort_direction: 'DESC',
    }, config: {headers: { 'X-Tenant': tenantConfig()?.name || import.meta.env.VITE_TENANT }}}},
];

export const List = ({
    onEdit,
    onDelete,
    setSelected,
    selected,
    loading:parentLoading
}) => {
    const { t } = useOutletContext();

    const params = useParams();
    
    const [rows, setRows] = useState();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'DESC'});
    const [searchText, setSearchText] = useState('');

    const { fetchData, data, loading, ErrorBar, LoadingBar } = useApi(apiParams[0]);

    const columns = useMemo(() => [
        { field: 'id', headerName: `${t('general:id')} #`, width: 90 },
        { field: 'name', headerName: t('merchant:name'), minWidth: 150, flex: 1 },
        { field: 'client_code', headerName: t('merchant:code'), width: 90 },
        { field: 'email', headerName: t('merchant:email'), minWidth: 150, valueGetter: (value, row) => row?.metadata?.contact_info?.email || ''  },
        { field: 'phone', headerName: t('merchant:phone'), minWidth: 120, valueGetter: (value, row) => row?.metadata?.contact_info?.phone || '' },
        { field: 'contact', headerName: t('merchant:contact'), minWidth: 150, flex: 1 },
        { field: 'company_type_name', headerName: t('merchant:type'), minWidth: 120 },
    ], [t]);

    useEffect(() => {
        const _loadData = async () => {
            const result = await fetchData({
                page_no: Math.round(page) || 1,
                max_records: pageSize > 10 ? pageSize : 10,
                sort_col: order?.column || 'id',
                sort_direction: order.direction || 'DESC',
                filters:{
                    merchant_ids: params?.id ? [params.id] : undefined,
                    search_words: searchText
                },
            });
            if (result?.data) {
                const _rows = result.data.items.map(item => ({
                    id: item.id,
                    name: `${item.name}`,
                    client_code: item.client_code,
                    email: item.email,
                    phone: item.phone,
                    contact: item.primary_contact?.full_name,
                    company_type: item.company_type,
                    metadata: item,
                }));
                setRows(_rows);
                setPageSize(result.data.page_record_count);
                setPage(result.data.this_page);
                setTotalRows(result.data.total_record_count);
                setTotalPages(Math.min(result.data.total_record_count / result.data.page_record_count));
            }
        };
        _loadData();
    }, [page, pageSize, order, fetchData, params.id, searchText]);



    // Handle row click - directly navigate to edit
    const handleRowSelection = useCallback(model => {
        const _model = [];
        rows?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [rows, setSelected]);

    const handleRowClick = useCallback((params) => {
        if (onEdit) {
            // Find the row data
            const rowData = rows?.find(row => row.id === params.id);
            if (rowData) {
                onEdit(null, rowData);
            }
        }
    }, [rows, onEdit]);

    return (
        <>
            <ErrorBar />
            {!data && <LoadingBar />}
            {data && rows &&
                <DataTable
                    checkboxSelection
                    hideFooterSelectedRowCount
                    rows={rows}
                    columns={columns}
                    onRowSelectionModelChange={model => handleRowSelection(model)}
                    rowSelectionModel={selected.map(s => s.id)}
                    setSelected={setSelected}
                    onRowClick={handleRowClick}
                    onDelete={onDelete}
                    order={order}
                    page={page}
                    pageSize={pageSize}
                    totalPages={totalPages}
                    totalRows={totalRows}
                    setPage={setPage}
                    setPageSize={setPageSize}
                    setOrder={setOrder}
                    setSearchText={setSearchText}
                    loading={loading || parentLoading}
                />
            }
        </>
    );
}
