import React, { useCallback, useState, useEffect, Suspense } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Paper, Button, Stack, InputAdornment, IconButton, Skeleton, useMediaQuery } from '@mui/material';
import { Add as AddIcon, Remove as RemoveIcon } from '@mui/icons-material';

import { useFormItem } from '../../../../../components/FormItem';
import { createCurrencyFormatter } from '../../../../../utils/currency';
import { setInfo } from '../../../../../store/reducers/currentShopItemSlice';

export const AddToCart = ({productId, variantId, amount = 0, showQuantity = true, isEdit = false, onAddToCart, size, variant, color, loading, ...props}) => {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);

    const [disabled, setDisabled] = useState(false);
    const [qty, setQty] = useState(1);

    const { Component, componentProps, handleKeyDown } = useFormItem({
        component: "NumberField", 
        min: 1, 
        max: 1000,
        step: 1,
        onChange: e => {
            setQty(e.target.value)
            dispatch(setInfo({qty: e.target.value}));
        },
        value: qty,
        name: "qty",
        label: t('pos:quantity'),
        showControls: true,
        disabled: !variantId,
        required: true,
        size: isMobile ? "medium" : "small",
        sx: {
            width: isMobile ? '100%' : '150px',
            '& input': {
                textAlign: 'center !important',
            }
        },
    });

    useEffect(() => {
        setQty(currentShopItem?.qty || 1);
    }, [currentShopItem?.qty]);

    useEffect(() => {
        let _disabled = false;
        if (!variantId) _disabled = true;
        if (!qty) _disabled = true;
        if (currentShopItem?.productCustomPrice !== null && currentShopItem?.productCustomPrice === 0) _disabled = true;
        if (currentShopItem?.hasEvent && (currentShopItem?.forUserIds?.length === 0 || !currentShopItem?.eventId)) _disabled = true;
        if ((!currentShopItem?.giftCardRecipient?.full_name || !currentShopItem?.giftCardRecipient?.email) && currentShopItem?.hasGiftCard) _disabled = true;
        setDisabled(_disabled);
    }, [variantId, qty, currentShopItem?.productCustomPrice, currentShopItem?.eventId, currentShopItem?.forUserIds, currentShopItem?.hasEvent, currentShopItem?.hasGiftCard, currentShopItem?.giftCardRecipient]);

    const handleQtyButtonClick = useCallback(key => e => {
        handleKeyDown({preventDefault: e.preventDefault, target: {value: qty}, key});
    }, [handleKeyDown, qty]);

    return (
        <Stack component={Paper} square elevation={24} direction={{xs: "column", md: "row"}} useFlexGap spacing={4} p={{xs: 2, lg: 0}} sx={{boxShadow:'none'}} {...props}>
            {showQuantity &&
                <Suspense fallback={
                    <Stack direction="column" spacing={0.5} useFlexGap sx={{width: '100%'}}>
                        <Skeleton animation="wave" variant="text" width={60} height={4} />
                        <Skeleton animation="wave" variant="rounded" height={45} width="100%" />
                    </Stack>
                }>
                    <Component 
                        {...componentProps} 
                        slotProps={{
                            ...componentProps?.slotProps,
                            input: {
                                ...componentProps?.slotProps?.input,
                                
                                /*startAdornment: 
                                    <InputAdornment position="start">
                                        <IconButton aria-label="remove" size="small" onClick={handleQtyButtonClick('ArrowDown')} disabled={!variantId}>
                                            <RemoveIcon fontSize="inherit" />
                                        </IconButton>
                                    </InputAdornment>,
                                endAdornment: 
                                    <InputAdornment position="end">
                                        <IconButton aria-label="add" size="small" onClick={handleQtyButtonClick('ArrowUp')} disabled={!variantId}>
                                            <AddIcon fontSize="inherit" />
                                        </IconButton>
                                    </InputAdornment>,*/
                            }
                        }}
                    />
                </Suspense>
            }

            <Button size={size || "large"} variant={variant || "contained"} color={color || "primary"} fullWidth disabled={disabled || loading} onClick={onAddToCart}>
                {t(`pos:${isEdit ? 'saveCart' : 'addToCart'}`)}
                {amount > 0 && ` - ${currencyFormatter.format(amount * qty, currency)}`}
            </Button>

        </Stack>
    );
}