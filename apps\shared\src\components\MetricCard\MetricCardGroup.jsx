import React from 'react';
import { Box, Grid2 } from '@mui/material';

/**
 * MetricCardGroup component for displaying a group of MetricCard components
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - MetricCard components to display
 * @param {Object} props.sx - Additional styles to apply to the container
 * @param {number} props.spacing - Spacing between cards (default: 2)
 * @param {Object} props.itemSizes - Responsive sizes for grid items (default: { xs: 12, md: 6, lg: 3 })
 * @param {boolean} props.equalHeight - Whether all cards should have equal height (default: true)
 */
const MetricCardGroup = ({
  children,
  sx = {},
  spacing = 2,
  itemSizes = { xs: 12, md: 6, lg: 3 },
  equalHeight = true
}) => {
  // Validate children are React elements
  const validChildren = React.Children.toArray(children).filter(
    child => React.isValidElement(child)
  );

  if (validChildren.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mb: spacing, ...sx }}>
      <Grid2 container spacing={spacing}>
        {validChildren.map((child, index) => (
          <Grid2
            key={child.key || index}
            size={itemSizes}
            sx={equalHeight ? { display: 'flex' } : undefined}
          >
            {equalHeight ? (
              React.cloneElement(child, {
                sx: {
                  height: '100%',
                  flexGrow: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  ...child.props.sx
                }
              })
            ) : (
              child
            )}
          </Grid2>
        ))}
      </Grid2>
    </Box>
  );
};

export default MetricCardGroup;
