// Helper function to get data value with default
export const getDataValue = ({ data, field, defaultValue = '' }) => {
    if (!data) return defaultValue;
    if (!data.hasOwnProperty(field)) return defaultValue;
    if (data[field] === null || data[field] === undefined) return defaultValue;

    // For all other fields, return the value or default
    return data[field];
};

// The list of steps for the stepper
export const stepList = [
    {
        key: 0,
        slug: 'service:wizard.name.title',
        label: 'Name & Description',
        short_description: 'service:wizard.name.subtitle',
        description: 'service:wizard.name.description',
    },
    {
        key: 1,
        slug: 'service:wizard.availability.title',
        label: 'Availability',
        short_description: 'service:wizard.availability.subtitle',
        description: 'service:wizard.availability.description',
    },
    {
        key: 2,
        slug: 'service:wizard.increments.title',
        label: 'Time Increments',
        short_description: 'service:wizard.increments.subtitle',
        description: 'service:wizard.increments.description',
    },
    {
        key: 3,
        slug: 'service:wizard.location.title',
        label: 'Locations',
        short_description: 'service:wizard.location.subtitle',
        description: 'service:wizard.location.description',
    },
    {
        key: 4,
        slug: 'service:wizard.manager.title',
        label: 'Managers',
        short_description: 'service:wizard.manager.subtitle',
        description: 'service:wizard.manager.description',
    },
    {
        key: 5,
        slug: 'service:wizard.payment.title',
        label: 'Payment Options',
        short_description: 'service:wizard.payment.subtitle',
        description: 'service:wizard.payment.description',
    },
    {
        key: 6,
        slug: 'service:wizard.cancellation.title',
        label: 'Cancellation Policy',
        short_description: 'service:wizard.cancellation.subtitle',
        description: 'service:wizard.cancellation.description',
    },
    {
        key: 7,
        slug: 'service:wizard.summary.title',
        label: 'Summary',
        short_description: 'service:wizard.summary.subtitle',
        description: 'service:wizard.summary.description',
    },
];

// Step 1: Name & Description
export const step1 = data => [
    {
        id: 1,
        title: 'Service Details',
        slug: 'service:wizard.name.title',
        description: 'service:wizard.name.description',
        fields: [
            {
                name: 'name',
                type: 'text',
                label: 'service:wizard.name.label',
                required: true,
                value: getDataValue({data, field: 'name'}),
                component: "TextField",
                margin: "normal",
                rowId: 1
            },
            {
                name: 'short_description',
                type: 'text',
                label: 'service:wizard.shortDescription.label',
                required: true,
                value: getDataValue({data, field: 'short_description'}),
                component: "TextField",
                minRows: 2,
                margin: "normal",
                rowId: 1
            },
            {
                name: 'description',
                type: 'text',
                label: 'service:wizard.description.label',
                required: false,
                value: getDataValue({data, field: 'description'}),
                component: "TextField",
                minRows: 4,
                margin: "normal",
                rowId: 1
            },
        ],
    },
];

// Step 2: Availability
export const step2 = data => [
    {
        id: 2,
        title: 'Service Availability',
        slug: 'service:wizard.availability.title',
        description: 'service:wizard.availability.description',
        fields: [
            {
                name: 'has_start_date',
                type: 'checkbox',
                label: 'service:wizard.availability.hasStartDate',
                required: false,
                value: getDataValue({data, field: 'has_start_date', defaultValue: false}),
                component: "Checkbox",
                margin: "normal",
                rowId: 2
            },
            {
                name: 'start_date',
                type: 'date',
                label: 'service:wizard.availability.startDate',
                required: data?.has_start_date,
                value: getDataValue({data, field: 'start_date'}),
                component: "DatePicker",
                margin: "normal",
                rowId: 2,
                disabled: !data?.has_start_date
            },
            {
                name: 'has_end_date',
                type: 'checkbox',
                label: 'service:wizard.availability.hasEndDate',
                required: false,
                value: getDataValue({data, field: 'has_end_date', defaultValue: false}),
                component: "Checkbox",
                margin: "normal",
                rowId: 2
            },
            {
                name: 'end_date',
                type: 'date',
                label: 'service:wizard.availability.endDate',
                required: data?.has_end_date,
                value: getDataValue({data, field: 'end_date'}),
                component: "DatePicker",
                margin: "normal",
                rowId: 2,
                disabled: !data?.has_end_date
            },
        ],
    },
];

// Step 3: Time Increments
export const step3 = data => [
    {
        id: 3,
        title: 'Time Increments',
        slug: 'service:wizard.increments.title',
        description: 'service:wizard.increments.description',
        fields: [
            {
                name: 'block_minutes',
                type: 'number',
                label: 'service:wizard.increments.blockMinutes',
                required: true,
                value: getDataValue({data, field: 'block_minutes', defaultValue: 30}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                inputProps: { min: 5, max: 240 }
            },
            {
                name: 'min_timeslots',
                type: 'number',
                label: 'service:wizard.increments.minTimeslots',
                required: true,
                value: getDataValue({data, field: 'min_timeslots', defaultValue: 1}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                inputProps: { min: 1, max: 10 }
            },
            {
                name: 'max_timeslots',
                type: 'number',
                label: 'service:wizard.increments.maxTimeslots',
                required: true,
                value: getDataValue({data, field: 'max_timeslots', defaultValue: 4}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                inputProps: { min: 1, max: 20 }
            },
            {
                name: 'timeslots_for_token',
                type: 'number',
                label: 'service:wizard.increments.timeslotsForToken',
                required: true,
                value: getDataValue({data, field: 'timeslots_for_token', defaultValue: 1}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                inputProps: { min: 1, max: 10 }
            },
            {
                name: 'min_booking_notice',
                type: 'number',
                label: 'service:wizard.increments.minBookingNotice',
                required: true,
                value: getDataValue({data, field: 'min_booking_notice', defaultValue: 60}),
                component: "TextField",
                margin: "normal",
                rowId: 3,
                inputProps: { min: 0, max: 10080 } // Max 1 week in minutes
            },
        ],
    },
];

// Step 4: Locations
export const step4 = data => [
    {
        id: 4,
        title: 'Service Locations',
        slug: 'service:wizard.location.title',
        description: 'service:wizard.location.description',
        fields: [
            // This step will use a custom component to select locations
            // The field below is just a placeholder
            {
                name: 'location_ids',
                type: 'custom',
                label: 'service:wizard.location.locations',
                required: true,
                value: getDataValue({data, field: 'location_ids', defaultValue: []}),
                component: "LocationSelector",
                margin: "normal",
                rowId: 4
            },
        ],
    },
];

// Step 5: Managers
export const step5 = data => [
    {
        id: 5,
        title: 'Service Managers',
        slug: 'service:wizard.manager.title',
        description: 'service:wizard.manager.description',
        fields: [
            // This step will use a custom component to select managers
            // The field below is just a placeholder
            {
                name: 'manager_ids',
                type: 'custom',
                label: 'service:wizard.manager.managers',
                required: true,
                value: getDataValue({data, field: 'manager_ids', defaultValue: []}),
                component: "ManagerSelector",
                margin: "normal",
                rowId: 5
            },
        ],
    },
];

// Step 6: Payment Options
export const step6 = data => [
    {
        id: 6,
        title: 'Payment Options',
        slug: 'service:wizard.payment.title',
        description: 'service:wizard.payment.description',
        fields: [
            {
                name: 'default_price',
                type: 'number',
                label: 'service:wizard.payment.defaultPrice',
                required: true,
                value: getDataValue({data, field: 'default_price', defaultValue: 0}),
                component: "TextField",
                margin: "normal",
                rowId: 6,
                inputProps: { min: 0, step: 0.01 }
            },
            {
                name: 'default_token_name',
                type: 'text',
                label: 'service:wizard.payment.defaultTokenName',
                required: false,
                value: getDataValue({data, field: 'default_token_name', defaultValue: 'Service Token'}),
                component: "TextField",
                margin: "normal",
                rowId: 6
            },
        ],
    },
];

// Step 7: Cancellation Policy
export const step7 = data => [
    {
        id: 7,
        title: 'Cancellation Policy',
        slug: 'service:wizard.cancellation.title',
        description: 'service:wizard.cancellation.description',
        fields: [
            {
                name: 'cancellation_policy',
                type: 'text',
                label: 'service:wizard.cancellation.policy',
                required: false,
                value: getDataValue({data, field: 'cancellation_policy'}),
                component: "TextField",
                minRows: 4,
                margin: "normal",
                rowId: 7
            },
            {
                name: 'cancellation_hours',
                type: 'number',
                label: 'service:wizard.cancellation.hours',
                required: true,
                value: getDataValue({data, field: 'cancellation_hours', defaultValue: 24}),
                component: "TextField",
                margin: "normal",
                rowId: 7,
                inputProps: { min: 0, max: 168 } // Max 1 week in hours
            },
        ],
    },
];

// Step 8: Summary
export const step8 = data => [
    {
        id: 8,
        title: 'Summary',
        slug: 'service:wizard.summary.title',
        description: 'service:wizard.summary.description',
        fields: [
            {
                name: 'status',
                type: 'radio',
                label: 'service:wizard.summary.statusLabel',
                required: true,
                value: getDataValue({data, field: 'status', defaultValue: 1}),
                component: "RadioGroup",
                margin: "normal",
                rowId: 8,
                options: [
                    {value: 1, label: 'service:wizard.summary.active'},
                    {value: 0, label: 'service:wizard.summary.inactive'},
                ]
            },
        ],
    },
];
