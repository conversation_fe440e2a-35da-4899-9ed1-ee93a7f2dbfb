import { useState, useEffect, useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useForm } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';
import { uuid } from '@siteboss-frontend/shared/utils';

const  temp_id = uuid();
/*
This is the form's logic
The fields we'll be using are defined on the object above. The only necessary properties are: name, and value, the rest are optional.
In this case, we use the optional fields for layout purposes and to load some components dynamically.
The logic for submits and validations are handled by the useForm hook.
We return whatever we need to use in our form ui, like values, errors, and functions to handle file uploads and password generation, etc.
*/
export const useFormLogic = ({groupTypes, eventTypes, parentLoading, typeId, id, onSuccess}) => {
    const { t } = useOutletContext();

    const [success, setSuccess] = useState(false);
    const [loading, setLoading] = useState(false);
    const [files, setFiles] = useState(null); // files to upload after the form is submitted

    const fields = useMemo(() => {
        let _fields = [
            {name: 'name', type: 'text', label: 'program:name', required: true, value: '', component: "TextField", margin: 'normal'},
            {name: 'short_description', type: 'text', label: 'program:shortDescription', required: true, value: '', component: "TextField", minRows: 2, margin: "normal"},
            {name: 'description', type: 'text', label: 'program:description', required: false, value: '', component: "MarkDown", minRows: 2, margin: "normal"},
            //{name: 'event_type_id', label: 'program:type', required: true, value: 0, component: "TileButtonGroup", options: eventTypes || [], margin: "normal"},
            {name: 'group_types', label: 'program:groupTypeName', value: [], component: "Autocomplete", margin: "normal", filterSelectedOptions: true, options: groupTypes || [], getOptionLabel: option => option.name || "", multiple: true },
            {name: 'tags', label: 'program:tags', required: false, value: [], component: "TagSelector", type: "autocomplete", margin: "normal"},
            {name: 'images', value: []},
        ];
        if (typeId) _fields = _fields.filter(a => a.name !== "event_type_id"); // if typeId router param is present, we remove the "event type" field from the list of fields because we already have the event type
        return _fields;

    }, [groupTypes, eventTypes, typeId]);

    // these are the api hooks
    const apiParams = useMemo(() => [
        {params: {endpoint: '/event/add_wiz', method: 'POST'}},
        {params: {endpoint: '/event/edit_wiz', method: 'POST'}},
        {params: {endpoint: `/event/${id}`, method: 'GET', data: {id, include_tags: true, include_media: true}}},
        {params: {endpoint: '/media/upload', method: 'POST'}},
    ], [id]);

    const { fetchData: saveData, loading: formLoading, ErrorBar, LoadingBar } = useApi(apiParams[0]);
    const { fetchData: updateData, loading: updateLoading, ErrorBar: UpdateErrorBar, LoadingBar: UpdateLoadingBar } = useApi(apiParams[1]);
    const { fetchData: fetchProgramData, data: programData, errors: programErrors, ErrorBar: ProgramErrorBar, loading: programLoading } = useApi(apiParams[2]);
    const { fetchData: uploadMedia, loading: uploadLoading, ErrorBar: UploadErrorBar } = useApi(apiParams[3]);

    const handleUpload = useCallback(async (programId = null, data = null) => {
        let process = false, formData = new FormData();
        if (files && files.length > 0) {
            formData.append('event_id', programId);
            formData.append('file', files);
            process = true;
        } else if (data?.entries()){
            formData = data;
            process = true;
        }

        let success = true;
        if (process){
            try {
                setLoading(true);
                const apiCall = id ? updateData : saveData;
                const response = await apiCall(formData);
                if (response.data) {
                    success = true;
                    setFiles(null); // clear files after upload
                }
            } catch(error){
                success = false;
            } finally {
                setLoading(false);
            }
        }
        return success;
    }, [saveData, updateData, id, files]);

    // Create a state to store form values for use in callbacks
    const [formState, setFormState] = useState({ values: [] });

    // this is the function that gets called when the form is submitted, it sets the params (form values) for the api call
    const formSubmit = useCallback(async (formValues, setErrors) => {
        if (formValues) {
            try {
                setLoading(true);

                // Determine which API endpoint to use based on whether this is a new or existing program
                const apiCall = id ? updateData : saveData;

                if (id) {
                    // For edit_wiz endpoint, we need to format the data differently
                    const formData = new FormData();

                    // Add required id parameter
                    formData.append('id', id);

                    // Add other form fields
                    if (formValues.name) formData.append('name', formValues.name);
                    if (formValues.description) formData.append('description', formValues.description);
                    if (formValues.start_datetime) formData.append('start_datetime', formValues.start_datetime);
                    if (formValues.end_datetime) formData.append('end_datetime', formValues.end_datetime);
                    if (formValues.location_id) formData.append('location_id', formValues.location_id);
                    if (formValues.event_type_id) formData.append('event_type_id', formValues.event_type_id);
                    if (formValues.is_public !== undefined) formData.append('is_public', formValues.is_public);

                    // Add files if they exist
                    if (files && files.length > 0) {
                        files.forEach((file, index) => {
                            formData.append('files[]', file);
                        });
                    }

                    // Add existing file IDs if they exist
                    const existingImages = formState.values.find(v => v.name === 'images')?.value || [];
                    if (existingImages.length > 0) {
                        existingImages.forEach(img => {
                            // Handle different formats of image data
                            if (typeof img === 'string') {
                                // If it's just a URL string, we can't extract an ID
                                // This is likely a newly uploaded image
                                return;
                            } else if (img && img.id) {
                                formData.append('file_ids[]', img.id);
                                formData.append('file_descriptions[]', img.description || '');
                            } else if (img && img.url) {
                                // For images from MediaManager that might not have an ID
                                // We'll need to extract the ID from the URL if possible
                                const urlParts = img.url.split('/');
                                const possibleId = urlParts[urlParts.length - 1];
                                if (possibleId && !isNaN(possibleId)) {
                                    formData.append('file_ids[]', possibleId);
                                    formData.append('file_descriptions[]', '');
                                }
                            }
                        });
                    }

                    const response = await apiCall(formData);
                    if (response.errors) {
                        setErrors({form: response.errors});
                    } else if (response.data) {
                        setSuccess(true);
                        // Call onSuccess to trigger the list refresh
                        // Use setTimeout to ensure all state updates have completed
                        setTimeout(() => {
                            if (onSuccess) onSuccess();
                        }, 100);
                    }
                } else {
                    // For add_wiz endpoint, use the existing format
                    let data = {events: JSON.stringify([formValues])};
                    if (files && files.length > 0) {
                        data = new FormData();
                        data.append('events', JSON.stringify([formValues]));
                        files.forEach(f => {
                            data.append('file[]', f);
                            data.append('temp_id[]', temp_id);
                        });
                    }

                    const response = await apiCall(data);
                    if (response.errors) {
                        setErrors({form: response.errors});
                    } else if (response.data) {
                        setSuccess(true);

                        // If we have files and the program was created successfully, upload the images
                        if (files && files.length > 0 && response.data.events && response.data.events[0]?.id) {
                            const programId = response.data.events[0].id;

                            try {
                                const formData = new FormData();
                                formData.append('entity_id', programId);
                                formData.append('entity_type', 'event');

                                // Add files
                                files.forEach(file => {
                                    formData.append('file', file);
                                });

                                await uploadMedia(formData);
                            } catch (error) {
                                console.error('Error uploading images for new program:', error);
                            }
                        }

                        // Call onSuccess to trigger the list refresh
                        // Use setTimeout to ensure all state updates have completed
                        setTimeout(() => {
                            if (onSuccess) onSuccess();
                        }, 100);
                    }
                }
            } catch (error) {
                setErrors({form: t('error:default')});
            } finally {
                setLoading(false);
            }
        } else setErrors({form: t('error:default')});
    }, [t, saveData, updateData, uploadMedia, files, id, onSuccess, formState.values]);

    // we send this function to the useForm hook
    const formBeforeSubmit = useCallback(formValues => {
        if (formValues) {
            const _checks = [
                {field: 'id', value: id},
                {field: 'temp_id', value: temp_id},
                {field: 'event_type_id', value: typeId},
                {field: 'is_program', value: 1},
            ];
            const _values = [];
            _checks.forEach(c => {
                if ((Array.isArray(c.value) ? c.value.length > 0 : c.value) && !formState.values.find(a => a.name === c.field)) {
                    _values.push({name: c.field, value: c.value});
                }
            });
            return [...formValues, ..._values];
        }
        return formValues;
    }, [id, typeId, formState.values]);

    // this is the form hook, it handles the form state and actions like validation and submission
    const {values, errors, handleChange, handleSubmit, setFormValues } = useForm(fields, { onSubmit: formSubmit, onBeforeSubmit: formBeforeSubmit });

    // Update formState when values change
    useEffect(() => {
        setFormState({ values });
    }, [values]);

    // do stuff when a file is dropped in the upload container
    const handleFileDrop = useCallback(async (data) => {
        if (data) {
            const files = data?.map(f => f.file) || data;

            if (id) {
                // If we have an ID, upload the image immediately using the media/upload endpoint
                try {
                    setLoading(true);
                    const formData = new FormData();
                    formData.append('entity_id', id);
                    formData.append('entity_type', 'event');

                    // Add files
                    files.forEach(file => {
                        formData.append('file', file);
                    });

                    const response = await uploadMedia(formData);

                    if (response.data) {
                        // Update the form values with the new image
                        const currentImages = formState.values.find(v => v.name === 'images')?.value || [];
                        const newImages = [...currentImages];

                        if (response.data.media) {
                            response.data.media.forEach(media => {
                                newImages.push({
                                    id: media.id,
                                    description: media.description || '',
                                    preview_url: media.preview_url || media.url,
                                    url: media.url
                                });
                            });
                        }

                        handleChange({
                            target: {
                                name: 'images',
                                value: newImages
                            }
                        });
                    }
                } catch (error) {
                    console.error('Error uploading image:', error);
                } finally {
                    setLoading(false);
                }
            } else {
                // If we don't have an ID yet (new program), store the files to upload when the form is submitted
                setFiles(files);
            }
        }
    }, [id, uploadMedia, formState.values, handleChange]);

    const handleFileRemove = useCallback(async (data) => {
        if (data) {
            if (!id) {
                // For new programs, just remove from the files state
                setFiles(prev => {
                    if (prev) {
                        const _files = [...prev];
                        const idx = _files.findIndex(a => a.name === data.name);
                        if (idx !== -1) _files.splice(idx, 1);
                        if (_files.length === 0) return null;
                        return _files;
                    }
                    return null;
                });
            }

            // Update the form values to remove the image from the images array
            if (data.id) {
                const currentImages = formState.values.find(v => v.name === 'images')?.value || [];
                const updatedImages = currentImages.filter(img => img.id !== data.id);

                // Update the form state
                handleChange({
                    target: {
                        name: 'images',
                        value: updatedImages.length > 0 ? updatedImages : null
                    }
                });

                // If we have an ID, we need to update the program with the new image list
                if (id) {
                    try {
                        setLoading(true);
                        const formData = new FormData();
                        formData.append('id', id);

                        // Add existing file IDs
                        if (updatedImages.length > 0) {
                            updatedImages.forEach(img => {
                                if (img.id) formData.append('file_ids[]', img.id);
                                formData.append('file_descriptions[]', img.description || '');
                            });
                        }

                        await updateData(formData);
                    } catch (error) {
                        console.error('Error removing image:', error);
                    } finally {
                        setLoading(false);
                    }
                }
            }
        }
    }, [id, formState.values, handleChange, setFiles, updateData]);

    useEffect(() => {
        if (formLoading || updateLoading || programLoading || uploadLoading || parentLoading) setLoading(true);
        else setLoading(false);
    }, [formLoading, updateLoading, programLoading, uploadLoading, parentLoading]);

    // fetch the data if the id is set
    useEffect(() => {
        const _loadProgram = async () => {
            if (id) {
                const result = await fetchProgramData({id: id, include_tags: 1, include_media: 1});
                if (result?.data) {
                    setFormValues(result.data[0]);
                }
            }
        }
        _loadProgram();
    }, [id, fetchProgramData]);

    // the stuff we'll be using in the form
    return {
        values,
        errors,
        success,
        loading,
        groupTypes,
        files,
        handleChange,
        handleSubmit,
        handleFileDrop,
        handleFileRemove,
        programErrors,
        programData,
        ErrorBar,
        LoadingBar,
        UpdateLoadingBar,
        ProgramErrorBar,
        UpdateErrorBar,
        UploadErrorBar,
    }
}