import { Meta } from '@@storybook/addon-docs';

<Meta title="Docs/Testing/Cypress Details" />

# Cypress Details

For using things like usernames and passwords (or any other variables that have a sensitive value), you can add them to a cypress.env.json file located at the root level.  There is a sample file that can be used for modification or, if need be, here are the current details:

```
{
    "impact_patron_symbol_name": "",
    "impact_patron_user": "",
    "impact_staff_user": "",
    "impact_admin_user": "", 
    "impact_sb_user": "", 
    "login_password": "",
    "impact_sb_name": "",
    "impact_patron_name": "",
    "impact_phone": "",
    "impact_email": "",
    "gift_card_dev_one":"",
    "gift_card_dev_two" : "",
    "gift_card_qa_one": "",
    "qa_api_url": "",
    "dev_api_url": "",
    "instruction": "Fill these out with relevant information and then remove the '_sample' from the file name.  Then it's gitignored. This patten for tests can be increased to include different companies, "
}
```

While developing, we should be writing components alongside their tests. You know your componnet best as you're creating it and best what it SHOULD be doing. It will also allow you to catch where you don't have it fully developed, such as error handling.

Despite the benefits of doing them side by side, if you don't have time, at the very least, please add a "data-cy" to all the key elements of your html.  This will allow a future test to easily and reliably pick out the same thing, even if the class or id changes in the future.\
For example, say you have a simple html, throw in data-cys -

```
<div data-cy="this-page-some-important-div">
    <p>
        I'm some stuff written here
    </p>
    <ul data-cy="this-page-important-list">
        {something.map((stuff)=>(
            <li key={`stuff-${stuff.id}} data-cy=`{this-page-list-item-${id}}>
                {stuff.name}
            </li>
        ))}
    </ul>
</div>
```

Note - I've made the li data-cy unique using the item id. It ensures you get the right one when you know what you want (like component testing) but does not change E2E testing where you should be querying and matching "manually", so to speak, by finding it on the page.  This seems like it will be especially important as MUI selectors are very vague.

# Cypress Redux 

Cypress has redux wrapped around it for component tests via the following file  

cypress > support > FakeApp.jsx

This fake app allows for providers to wrap around the component being mounted via the 'cy.mount' command in a test.
The mock store is able to take in a title (that tells it which store to mount) in the form of "siteboss", "cms", or "shared" currently.
It can also take in an "init" prop which tells it what else to populate in the store.


When mocking the store via the fake app, it accepts the component and props. The props can include the following:

```
{
    title: string //can be 'cms', 'siteboss', shared or will default to null (which is all of them)
    init: object //can be an object to initialize into the beginning of the redux store
    user: bool //if you want a mock user put into local storage
    userData: object //the mock user you want put into local storage.  Else it will be a default generic non user as listed in cypress > support > component.jsx as the defaultprops.   
}
```