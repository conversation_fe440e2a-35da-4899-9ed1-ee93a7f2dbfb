import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Box, AppBar, IconButton, Tab, Stack, Tooltip, useMediaQuery, Button } from '@mui/material';
import { <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TabContext } from '@mui/lab';
import { CloseOutlined as CloseIcon } from '@mui/icons-material';

import Title from '../Title';
import WithScrollEffect from '../WithScrollEffect';

/*
This is a container that either displays info, or shows the form to edit or create. It should be used within a drawer or a modal.
*/
export const WithDetails = React.forwardRef(({
    onClose, // function to be called when the close button is clicked
    onDelete, // function to be forwared to the data display component to be called when the delete button is clicked
    onEdit, // function to be forwared to the data display component to be called when the edit button is clicked
    onSuccess, // function to be called when a form submission is successful
    itemIds, // an array of objects with the following structure {id, label}. Label can be a string or a component
    isNew, // boolean flag to determine if we want to show the form to create
    isEdit, // boolean flag to determine if we want to show the form to edit
    titles, // object with titles of the container ({new: 'New User', edit: 'Edit User', view: 'User Details})
    slots, // slots to be displayed in the container ({headerButtons: additional buttons to show in the header, form: the form to display, details: the data to display})
    ...props // additional props
}, ref) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const headerRef = ref || useRef(null);

    const [selectedId, setSelectedId] = useState('0');
    
    const handleChangeTab = (e, tab) => {
        if (!tab) return;
        setSelectedId(`${tab}`);
    }

    useEffect(() => {
        setSelectedId('0')
    }, [itemIds]); // reset the selected tab when the item list changes (a new item is added or deleted, etc.)

    return (
        <TabContext value={itemIds.length >= +selectedId ? selectedId : '0'}>
            <Box sx={{ flexGrow: 1, p: 0, position: 'relative', overflow: 'hidden', overflowY: 'auto' }} ref={headerRef}>

                {/* TODO - make this work, somehow the headerRef is not being passed right
                <Toolbar 
                    parentRef={headerRef} 
                    onClose={onClose} 
                    itemIds={itemIds} 
                    isNew={isNew} 
                    isEdit={isEdit} 
                    titles={titles} 
                    slots={slots} 
                    onTabChange={handleChangeTab} 
                    {...props} 
                />
                */}

                <WithScrollEffect targetElement={headerRef.current} effect = {{elevation: 1}}>
                    <AppBar position="sticky" mobile={isMobile || isMobile ? 1 : 0} sx={{px: 2, pt: 1}} elevation={16}>
                        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{mb: (isNew || isEdit) ? 3 : undefined}}>
                            {(isNew || isEdit) ?
                                <Container sx={{mt:2}}>
                                    <Title title={isNew ? titles.new : isEdit ? titles.edit : titles.view} />
                                </Container>
                            :
                                itemIds.length > 1 && 
                                    <Box sx={{ borderBottom: 1 , borderColor: 'divider', flexGrow: 1, maxWidth: `calc(100% - ${48}px)`}}>
                                    
                                        <TabList 
                                            variant="scrollable" 
                                            scrollButtons="auto" 
                                            onChange={handleChangeTab} 
                                            aria-label={titles.view || undefined}
                                        >
                                            {itemIds.map((item, i) => (
                                                <Tab key={`order-detail-tab-${item.id}`} value={`${i}`} label={
                                                    typeof item.label === 'string' ? `${item.label}` : item.label
                                                } />
                                            ))}
                                        </TabList>
                                    </Box>
                            }
                            <Box sx={{ml: 2}}/>
                            {slots?.headerButtons ? slots.headerButtons() : null}
                            <Tooltip title={t('general:close')}>
                                <IconButton size="small" onClick={onClose}>
                                    <CloseIcon fontSize='inherit' />
                                </IconButton>
                            </Tooltip>
                        </Stack>
                    </AppBar>
                </WithScrollEffect>
                
                <Box sx={{p: 2}}>
                    {(isNew || isEdit) && slots.form ?
                        <Box sx={{mt: 1}}>
                            {slots.form({ id: isEdit ? itemIds?.[selectedId]?.id : undefined, onSuccess: onSuccess || onClose, onClose })}
                        </Box>
                    :
                        <TabPanel value={`${selectedId}`} sx={{position: "relative", px: isMobile ? 0 : 0, width: '100%'}}>
                            {itemIds?.[selectedId]?.id && slots.details &&
                                slots.details({ id: itemIds?.[selectedId]?.id, onDelete, onEdit })
                            }
                        </TabPanel>
                    }
                </Box>
            </Box>
                {/*(isNew || isEdit) && 

                    <Box sx={{
                        position: "sticky",
                        bottom: 0,
                        background: 'white',
                        zIndex: 1,
                    }}>
                        <Button variant='contained' color='primary' fullWidth size='large' sx={{mt:1}} onClick={onClose}>
                            {t('general:close')}
                        </Button>
                    </Box>
                */}
        </TabContext>
    );
});