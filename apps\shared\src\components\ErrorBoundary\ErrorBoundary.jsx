import React from 'react';
import { withTranslation } from 'react-i18next';
import { Container, Box, Button, Typography, Accordion, AccordionDetails, AccordionSummary } from '@mui/material';
import {ExpandMoreOutlined as ExpandMoreIcon} from '@mui/icons-material';


import Title from '../../components/Title';
import { Robot } from '../404/Robot';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    resetErrorBoundary = () => {
        this.setState({ hasError: false, error: null });
    };

    render() {
        if (this.state.hasError) {
    
            return (
                <Container sx={{
                    display: 'flex',
                    flexDirection: {
                        xs: 'column',
                        lg: 'row'
                    },
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100vh',
                }}>
                    <Box sx={{
                        width: '350px',
                        height: '350px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '72% 38% 42% 67% / 67% 67% 40% 44%;',
                        boxShadow: theme => theme.palette.mode === "dark" ? `inset ${theme.palette.background.paper} 0px 0px 250px 50px` : undefined,
                        background: theme => theme.palette.mode === "dark" ? `radial-gradient(circle, ${theme.palette.text.primary} 0%, ${theme.palette.background.default} 60%)` : undefined,
                    }}>
                        <Robot width="300px" />
                    </Box>
                    <Box sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'flex-start',
                        alignItems: 'flex-start',
                        ml: 2,
                    }}>
                        <Title title={this.props.t("error:oops")} subtitle={this.props.t("error:default")}>
                            <Typography variant="subtitle1" component="div" sx={{mt: 2}}>
                                {this.state.error.message || this.props.fallback}
                            </Typography>
                            <Container disableGutters maxWidth="sm" sx={{mt: 2, mx: 0}}>
                                <Accordion variant="outlined" sx={{bgcolor: 'transparent', mx: 0}}>
                                    <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel1-content" id="panel1-header">
                                        <Typography variant="code">
                                            {this.props.t("error:viewErrorDetails")}
                                        </Typography>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Typography variant="code" color="error" component="div" sx={{mt: 2, overflow: 'auto'}}>
                                            {this.state.error.stack}
                                        </Typography>
                                    </AccordionDetails>
                                </Accordion>
                            </Container>
                        </Title>
                        <Button onClick={this.resetErrorBoundary} variant="outlined" color="inherit" sx={{mt: 5}}>{this.props.t("error:tryAgain")}</Button>
                    </Box>
                </Container>
            );
        }

        return this.props.children;
    }
}

const TranslatedErrorBoundary = withTranslation()(ErrorBoundary);

export const withErrorBoundary = (WrappedComponent, errorBoundaryProps) => {
    return function WithErrorBoundary(props) {
        return (
            <TranslatedErrorBoundary {...errorBoundaryProps}>
                <WrappedComponent {...props} />
            </TranslatedErrorBoundary>
        );
    };
};

export default TranslatedErrorBoundary;
