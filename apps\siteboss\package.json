{"name": "@siteboss-frontend/siteboss", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@siteboss-frontend/shared": "*", "konva": "^9.3.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-konva": "^18.2.10"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@vitejs/plugin-react": "^4.3.4", "@welldone-software/why-did-you-render": "^8.0.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prop-types": "^15.8.1", "vite": "^5.4.11"}}