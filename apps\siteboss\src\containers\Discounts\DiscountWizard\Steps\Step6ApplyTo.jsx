import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step6 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import CategoryIcon from '@mui/icons-material/Category';

const Step6ApplyTo = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);
    const [localApplyToAll, setLocalApplyToAll] = useState(1);

    // Get apply_to_all value from form data
    useEffect(() => {
        if (formData?.apply_to_all !== undefined) {
            setLocalApplyToAll(formData.apply_to_all);
        } else if (data?.apply_to_all !== undefined) {
            setLocalApplyToAll(data.apply_to_all);
        }
    }, [formData, data]);

    // Handle apply_to_all change
    const handleApplyToAllChange = useCallback((e) => {
        const newValue = parseInt(e.target.value);
        setLocalApplyToAll(newValue);
        dispatch(updateFormData({
            ...formData,
            apply_to_all: newValue
        }));
    }, [dispatch, formData]);

    // Save fields to the form context - only run once when data changes
    useEffect(() => {
        // Save the step data to the form context
        if (data && !formData?.initialized) {
            const fields = step6(data);
            if (fields && fields.length > 0) {
                const updatedData = { ...formData, initialized: true };
                fields.forEach(field => {
                    updatedData[field.name] = field.value;
                });
                dispatch(updateFormData(updatedData));
            }
        }
    }, [data, dispatch]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Choose what this discount applies to
                </Typography>

                <Typography variant="body1" paragraph>
                    You can apply this discount to the entire order or only to specific qualifying items.
                    If you choose specific items, you'll be able to set conditions in a later step.
                </Typography>

                <StyledRadioGroup
                    name="apply_to_all"
                    label="Apply To"
                    required
                    options={[
                        {
                            id: 1,
                            value: 1,
                            label: 'Entire Order',
                            icon: <ShoppingCartIcon />
                        },
                        {
                            id: 0,
                            value: 0,
                            label: 'Specific Items',
                            icon: <CategoryIcon />
                        }
                    ]}
                    value={localApplyToAll}
                    onChange={handleApplyToAllChange}
                    helperText="Choose what this discount applies to"
                />
            </Grid>
        </Grid>
    );
};

export default Step6ApplyTo;
