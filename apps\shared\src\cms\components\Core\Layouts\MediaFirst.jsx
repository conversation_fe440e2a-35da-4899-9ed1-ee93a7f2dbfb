import React from 'react';
import { Stack } from '@mui/material';

export const MediaFirst = ({ type,slots, slotProps }) => {    
    return (
        <Stack spacing={2} direction="column" className="VideoFirst" {...slotProps?.cmsStack}>
            {+type === 1 
                ? slots?.images?.({ ...slotProps?.content?.images }) 
                : slots?.video?.({ ...slotProps?.content?.video })
            }
            {slots?.title?.({ ...slotProps?.title })}
            {slots?.subtitle?.({ ...slotProps?.subtitle })}
            {slots?.body?.({ ...slotProps?.body })}
            {slots?.ctas?.({ ...slotProps?.ctas })}
        </Stack>
    );
};
