:global(.mdxeditor-popup-container) {
  z-index: var(--mui-zIndex-modal);
  
  button {
    background-color: var(--mui-palette-primary-paper);
    color: var(--mui-palette-text-primary);
    border-radius: var(--mui-shape-borderRadius);

    &:hover {
      background-color: var(--mui-palette-action-hover);
      //color: var(--mui-palette-secondary-contrastText);
    }
  }

  div[role='dialog'] {
    background-color: var(--mui-palette-background-paper);
    background-image: var(--Paper-overlay);
    color: var(--mui-palette-text-primary);
  }
}

.editor {
  --basePageBg: var(--mui-palette-background-paper);
  background-color: transparent;
  color: var(--mui-palette-text-primary); 
  font-family: var(--mui-typography-fontFamily); 

  :global {
    --baseBase: var(--mui-palette-background-paper);
    --baseBgSubtle: var(--mui-palette-background-paper);
    --baseText: var(--mui-palette-text-primary);
    --baseTextContrast: var(--mui-palette-text-primary);
    --baseBg: var(--mui-palette-background-paper);
    --baseBgHover: var(--mui-palette-secondary-dark);
    --baseBgActive: var(--mui-palette-action-selected);
    --baseLine: var(--mui-palette-divider);
    --baseBorder: var(--mui-palette-divider);
    --baseBorderHover: var(--mui-palette-divider);
    --baseSolid: var(--mui-palette-divider);
    --baseSolidHover: var(--mui-palette-divider);

    --accentBase: var(--mui-palette-primary-main);
    --accentBgSubtle: var(--mui-palette-primary-light);
    --accentBg: var(--mui-palette-primary-main);
    --accentBgHover: var(--mui-palette-primary-dark);
    --accentBgActive: var(--mui-palette-primary-main);
    --accentLine: var(--mui-palette-primary-dark);
    --accentBorder: var(--mui-palette-divider);
    --accentBorderHover: var(--mui-palette-text-primary);

    --accentSolid: var(--mui-palette-primary-main);
    --accentSolidHover: var(--mui-palette-primary-dark);
    --accentText: var(--mui-palette-primary-contrastText);
    --accentTextContrast: var(--mui-palette-primary-contrastText);

    .mdxeditor {
      color: var(--mui-palette-text-primary);
      font-family: var(--mui-typography-fontFamily);
      font-size: var(--mui-typography-fontSize);
    }

    .mdxeditor-toolbar {
      background-color: rgba(0, 0, 0, 0.15);
      color: var(--mui-palette-text-primary);
      //border: 1px solid var(--mui-palette-divider);
      border-radius: var(--mui-shape-borderRadius);
      padding: 4px;
      box-shadow: var(--Paper-shadow);
      background-image: var(--Paper-overlay);
      flex-wrap: wrap;

      > div {
        flex-wrap: wrap;
      }

      > * button {
        background-color: transparent;
        border-radius: var(--mui-shape-borderRadius);
        color: var(--mui-palette-text-icon);
        
        > * svg {
          color: var(--mui-palette-text-primary);
        }
        
        &:hover {
          background-color: var(--mui-palette-action-hover);
        }

        &:disabled {
          color: var(--mui-palette-action-disabled);
        }

        &[role=combobox] {
          > span:first-child {
            color: var(--mui-palette-text-primary);
          }
        }
  
      }

      * [role=group] {
        gap: 4px;
      }
    }

    .mdxeditor-content-editable {
      padding: var(--mui-spacing-2);
      min-height: 200px;
      color: var(--mui-palette-text-primary);

      p, ul, ol {
        font-family: var(--mui-typography-fontFamily);
        color: var(--mui-palette-text-primary);
      }

      h1, h2, h3, h4, h5, h6 {
        font-family: var(--mui-typography-h1-fontFamily);
        color: var(--mui-palette-text-primary);
        font-weight: var(--mui-typography-h1-fontWeight);
      }

      code {
        font-family: var(--mui-typography-code-fontFamily);
        background-color: var(--mui-palette-action-hover);
        padding: 2px 4px;
        border-radius: var(--mui-shape-borderRadius);
      }

      blockquote {
        border-left: 4px solid var(--mui-palette-divider);
        margin: var(--mui-spacing-2) 0;
        padding-left: var(--mui-spacing-2);
        color: var(--mui-palette-text-secondary);
      }
    }
  }
}
