# Hierarchical Selector Component

A flexible component for selecting items in a hierarchical structure (like country → state → city).

## Features

- Supports multiple levels of selection
- Each level is an autocomplete field for easy searching
- Works with both hierarchical and grouped data structures
- Automatically filters options based on parent selections
- Supports custom option rendering
- Can load data from an API

## Usage

### Basic Example

```jsx
import HierarchicalSelector from '@siteboss-frontend/shared/components/HierarchicalSelector';

// Example data (hierarchical format)
const locationData = [
  {
    id: 1,
    name: 'United States',
    children: [
      {
        id: 101,
        name: 'California',
        children: [
          { id: 1001, name: 'Los Angeles' },
          { id: 1002, name: 'San Francisco' }
        ]
      },
      {
        id: 102,
        name: 'New York',
        children: [
          { id: 1003, name: 'New York City' },
          { id: 1004, name: 'Buffalo' }
        ]
      }
    ]
  },
  {
    id: 2,
    name: 'Canada',
    children: [
      {
        id: 201,
        name: 'Ontario',
        children: [
          { id: 2001, name: 'Toronto' },
          { id: 2002, name: 'Ottawa' }
        ]
      }
    ]
  }
];

function LocationSelector() {
  const [selectedLocations, setSelectedLocations] = useState({});

  return (
    <HierarchicalSelector
      label="Location"
      data={locationData}
      levels={[
        { id: 'country', label: 'Country', parent: null, parentField: 'id' },
        { id: 'state', label: 'State', parent: 'country', parentField: 'id', parentIdField: 'country_id' },
        { id: 'city', label: 'City', parent: 'state', parentField: 'id', parentIdField: 'state_id' }
      ]}
      value={selectedLocations}
      onChange={setSelectedLocations}
      margin="normal"
      gridSize={4}  // Each level will take up 4 columns (3 levels per row)
      required={true}  // All fields are required
    />
  );
}
```

### With Grouped Data

```jsx
// Example data (grouped format)
const groupedData = {
  country: [
    { id: 1, name: 'United States' },
    { id: 2, name: 'Canada' }
  ],
  state: [
    { id: 101, name: 'California', parent_id: 1 },
    { id: 102, name: 'New York', parent_id: 1 },
    { id: 201, name: 'Ontario', parent_id: 2 }
  ],
  city: [
    { id: 1001, name: 'Los Angeles', parent_id: 101 },
    { id: 1002, name: 'San Francisco', parent_id: 101 },
    { id: 1003, name: 'New York City', parent_id: 102 },
    { id: 1004, name: 'Buffalo', parent_id: 102 },
    { id: 2001, name: 'Toronto', parent_id: 201 },
    { id: 2002, name: 'Ottawa', parent_id: 201 }
  ]
};

function LocationSelector() {
  const [selectedLocations, setSelectedLocations] = useState({});

  return (
    <HierarchicalSelector
      data={groupedData}
      levels={[
        { id: 'country', label: 'Country', parent: null, parentField: 'id' },
        { id: 'state', label: 'State', parent: 'country', parentField: 'id', parentIdField: 'parent_id' },
        { id: 'city', label: 'City', parent: 'state', parentField: 'id', parentIdField: 'parent_id' }
      ]}
      value={selectedLocations}
      onChange={setSelectedLocations}
    />
  );
}
```

### With API Data

```jsx
function LocationSelector() {
  const [selectedLocations, setSelectedLocations] = useState({});
  const [loading, setLoading] = useState(false);

  // Function to fetch data from API
  const fetchLocationData = async (params) => {
    setLoading(true);
    try {
      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      const data = await response.json();
      setLoading(false);
      return data;
    } catch (error) {
      console.error('Error fetching locations:', error);
      setLoading(false);
      return { data: [] };
    }
  };

  // Function to generate search parameters
  const getSearchParams = (search) => ({
    filter: { search_words: search }
  });

  return (
    <HierarchicalSelector
      isAsync={true}
      fetchData={fetchLocationData}
      fetchParams={{ status: 'active' }}  // Base parameters for all requests
      getSearchParams={getSearchParams}
      dataField="locations"  // Field in the response that contains the data
      levels={[
        { id: 'country', label: 'Country', parent: null, parentField: 'id' },
        { id: 'state', label: 'State', parent: 'country', parentField: 'id', parentIdField: 'country_id' },
        { id: 'city', label: 'City', parent: 'state', parentField: 'id', parentIdField: 'state_id' }
      ]}
      value={selectedLocations}
      onChange={setSelectedLocations}
      loading={loading}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | Array or Object | - | The data to populate the selectors. Can be hierarchical (array with children) or grouped (object with arrays for each level). |
| `levels` | Array | - | Configuration for each level of selection. |

| `value` | Object | {} | The currently selected values for each level. |
| `onChange` | Function | - | Callback when selections change. |
| `label` | String | null | Group label for the entire component. |
| `margin` | String | 'normal' | Controls the spacing. Options: 'none', 'dense', 'normal'. |
| `gridSize` | Number | auto | Controls the width of each level in the grid. If not provided, calculated based on the number of levels. |
| `required` | Boolean | false | Whether all fields are required to be selected. |
| `loading` | Boolean | false | Whether the component is in a loading state. |
| `disabled` | Boolean | false | Whether the component is disabled. |
| `errors` | String or Object | null | Error message(s) to display. |
| `renderOption` | Function | null | Custom function to render autocomplete options. |
| `isAsync` | Boolean | false | Whether to fetch data asynchronously from an API. |
| `fetchData` | Function | null | Function to fetch data from an API. |
| `fetchParams` | Object | {} | Base parameters to send with all API requests. |
| `getSearchParams` | Function | null | Function that accepts a search term and returns parameters to be sent to the API. |
| `dataField` | String | null | Field in the API response that contains the data (e.g., "locations" in response.data.locations). |
| `slots` | Object | {} | Custom components for different parts of the component. Available slots: container, label, gridContainer, gridItem, error. |
| `slotProps` | Object | {} | Props for the different slots. Available keys: container, label, gridContainer, gridItem, error, formItem, and level IDs for level-specific props. |

### Level Configuration

Each level in the `levels` array should have the following properties:

| Property | Type | Description |
|----------|------|-------------|
| `id` | String | Unique identifier for this level. Also used as the key in grouped data. |
| `label` | String | Display label for this level. |
| `parent` | String or null | ID of the parent level. `null` for top level. |
| `parentField` | String | Field from parent selection to match with this level's parentIdField. |
| `parentIdField` | String | Field in this level's data that contains the reference to the parent. For example, 'country_id' for states or 'state_id' for cities. |

### Using Slots and SlotProps

You can customize different parts of the component using slots and slotProps:

```jsx
<HierarchicalSelector
  label="Location"
  data={locationData}
  levels={[
    { id: 'country', label: 'Country', parent: null, parentField: 'id' },
    { id: 'state', label: 'State', parent: 'country', parentField: 'id', parentIdField: 'country_id' },
    { id: 'city', label: 'City', parent: 'state', parentField: 'id', parentIdField: 'state_id' }
  ]}
  value={selectedLocations}
  onChange={setSelectedLocations}
  // Customize the container
  slots={{
    container: Paper, // Use Paper instead of Box
    label: (props) => <Typography variant="subtitle1" {...props} /> // Custom label component
  }}
  // Customize props for different parts
  slotProps={{
    container: { elevation: 1, sx: { p: 2 } },
    label: { sx: { fontWeight: 'bold', color: 'primary.main' } },
    gridContainer: { spacing: 3 },
    // Level-specific props
    country: { helperText: 'Select a country first' },
    state: { helperText: 'Select a state' }
  }}
/>
```
