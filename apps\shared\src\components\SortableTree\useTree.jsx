import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { buildTree, flattenTree, getProjection, removeItem, removeChildrenOf, setProperty } from '../../utils';

export const useTree = ({ defaultItems, indentationWidth, onRemove, onUpdate }) => {

    const sensors = useSensors(useSensor(PointerSensor));
    
    const [items, setItems] = useState(() => defaultItems);
    const [activeId, setActiveId] = useState(null);
    const [overId, setOverId] = useState(null);
    const [offsetLeft, setOffsetLeft] = useState(0);
    const [currentPosition, setCurrentPosition] = useState(null);

    const flattenedItems = useMemo(() => {
        const flattenedTree = flattenTree(items);
        const collapsedItems = flattenedTree.reduce((acc, { children, collapsed, id }) => collapsed && children.length ? [...acc, id] : acc, []);
        return removeChildrenOf(flattenedTree, activeId ? [activeId, ...collapsedItems] : collapsedItems);
    }, [activeId, items]);

    const sortedIds = useMemo(() => flattenedItems.map(({ id }) => id), [flattenedItems]);

    const sensorContext = useRef({items: flattenedItems, offset: offsetLeft });

    const projected = activeId && overId ? getProjection(flattenedItems, activeId, overId, offsetLeft, indentationWidth) : null;
    const activeItem = activeId ? flattenedItems.find(({ id }) => id === activeId) : null;

    const handleDragStart = ({ active: { id: activeId } }) => {
        setActiveId(activeId);
        setOverId(activeId);

        const activeItem = flattenedItems.find(({ id }) => id === activeId);
        if (activeItem) {
            setCurrentPosition({
                parentId: activeItem.parentId,
                overId: activeId
            });
        }
        document.body.style.setProperty("cursor", "grabbing");
    }

    const handleDragMove = ({ delta }) => {
        setOffsetLeft(delta.x);
    }

    const handleDragOver = ({ over }) => {
        setOverId(over?.id ?? null);
    }

    const resetState = () => {
        setOverId(null);
        setActiveId(null);
        setOffsetLeft(0);
        setCurrentPosition(null);
        document.body.style.setProperty("cursor", "");
    }

    const handleDragEnd = useCallback(({ active, over }) => {
        resetState();

        if (projected && over) {
            const { depth, parentId } = projected;
            const clonedItems = JSON.parse(JSON.stringify(flattenTree(items)));
            const overIndex = clonedItems.findIndex(({ id }) => id === over.id);
            const activeIndex = clonedItems.findIndex(({ id }) => id === active.id);
            const activeTreeItem = clonedItems[activeIndex];
            clonedItems[activeIndex] = { ...activeTreeItem, depth, parentId };
            const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);
            const newItems = buildTree(sortedItems);            

            let res;
            if (onUpdate) {
                const newClonedItems = JSON.parse(JSON.stringify(flattenTree(newItems)));
                const updatedItem = newClonedItems.find(({ id }) => id === active.id);    
                res = onUpdate(updatedItem.id, updatedItem.parentId);
            } else res = true;
            if (res) setItems(newItems);
        }
    }, [items, projected, resetState, onUpdate]);

    const handleDragCancel = () => {
        resetState();
    }

    const handleRemove = useCallback(id => {
        let res;
        if (onRemove) res = onRemove(id);
        else res = true;
        if (res) setItems(prev => removeItem(prev, id));
    }, [onRemove]);

    const handleCollapse = id => {
        setItems(prev => setProperty(prev, id, "collapsed", value => !value));
    }

    const toggleDetails = id => {
        setItems(prev => setProperty(prev, id, "showDetails", value => !value));
    }

    const getMovementAnnouncement = (eventName, activeId, overId) => {
        if (overId && projected) {
            if (eventName !== "onDragEnd") {
                if ( currentPosition && projected.parentId === currentPosition.parentId && overId === currentPosition.overId) return;
                else setCurrentPosition({parentId: projected.parentId, overId});
            }

            const clonedItems = JSON.parse(JSON.stringify(flattenTree(items)));
            const overIndex = clonedItems.findIndex(({ id }) => id === overId);
            const activeIndex = clonedItems.findIndex(({ id }) => id === activeId);
            const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);

            const previousItem = sortedItems[overIndex - 1];

            let announcement;
            const movedVerb = eventName === "onDragEnd" ? "dropped" : "moved";
            const nestedVerb = eventName === "onDragEnd" ? "dropped" : "nested";

            if (!previousItem) {
                const nextItem = sortedItems[overIndex + 1];
                announcement = `${activeId} was ${movedVerb} before ${nextItem.id}.`;
            } else {
                if (projected.depth > previousItem.depth) announcement = `${activeId} was ${nestedVerb} under ${previousItem.id}.`;
                else {
                    let previousSibling = previousItem;
                    while (previousSibling && projected.depth < previousSibling.depth) {
                        const parentId = previousSibling.parentId;
                        previousSibling = sortedItems.find(({ id }) => id === parentId);
                    }
                    if (previousSibling) announcement = `${activeId} was ${movedVerb} after ${previousSibling.id}.`;
                }
            }
            return announcement;
        }
        return;
    }

    const announcements = {
        onDragStart(id) {
            return `Picked up ${id}.`;
        },
        onDragMove(id, overId) {
            return getMovementAnnouncement("onDragMove", id, overId);
        },
        onDragOver(id, overId) {
            return getMovementAnnouncement("onDragOver", id, overId);
        },
        onDragEnd(id, overId) {
            return getMovementAnnouncement("onDragEnd", id, overId);
        },
        onDragCancel(id) {
            return `Moving was cancelled. ${id} was dropped in its original position.`;
        }
    };

    useEffect(() => {
        sensorContext.current = {items: flattenedItems, offset: offsetLeft };
    }, [flattenedItems, offsetLeft]);

    return {
        items,
        sensors,
        sensorContext,
        activeId,
        activeItem,
        overId,
        flattenedItems,
        sortedIds,
        projected,
        handleDragStart,
        handleDragMove,
        handleDragOver,
        handleDragEnd,
        handleDragCancel,
        handleRemove,
        handleCollapse,
        toggleDetails,
        announcements
    };
}