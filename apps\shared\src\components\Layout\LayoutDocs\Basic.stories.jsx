import { Basic } from '../Basic/Basic';
import { Box } from '@mui/material';

export default {
    title: 'Shared/Layout/Basic',
    component: Basic,
    argTypes: {
        containerRef: {
            description: "Reference to the container element",
            control: 'object',
            table: {
                type: { summary: "object" },
                defaultValue: { summary: "undefined" },
                detail: "A reference to the container element for scrolling or other purposes."
            }
        },
        children: {
            description: "Content to be displayed inside the main container",
            control: 'text',
            table: {
                type: { summary: "ReactNode" },
                defaultValue: { summary: "undefined" },
                detail: "The content to be rendered inside the main container."
            }
        },
        hideFooter: {
            description: "Flag to indicate if the footer should be hidden",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag to control the visibility of the footer."
            }
        },
        isMobile: {
            description: "Flag to indicate if the view is mobile",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag indicating whether the view is mobile."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        children: "Rawr",
        hideFooter: false,
        isMobile: false
    }
};

// Following stories to illustrate each significant prop
export const WithFooterHidden = {
    args: {
        children: "Rawr Rawr",
        hideFooter: true,
        isMobile: false
    }
};

export const MobileView = {
    args: {
        children: "Rawr Rawr Rawr",
        hideFooter: false,
        isMobile: true
    }
};