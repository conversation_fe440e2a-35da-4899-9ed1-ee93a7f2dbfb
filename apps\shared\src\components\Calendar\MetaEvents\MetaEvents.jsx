import React from 'react';
import { 
    startOfDay, endOfDay, differenceInCalendarDays,
    startOfWeek, endOfWeek, differenceInCalendarWeeks,
    startOfMonth, endOfMonth, differenceInCalendarMonths,
    startOfYear, endOfYear, differenceInCalendarYears,
} from 'date-fns';

import Event from '../Event';

import styles from './MetaEvents.module.scss';

export const MetaEvents = ({
    currentDate, // the current date
    metaEvents, // an array of meta events to display
    calendarType, // the type of calendar 'week', 'day', 'month', 'year', or schedule
    colors, // an array of colors (optional)
    size = "large",
    disabled,
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props // additional props
}) => {
    const renderEvents = (day) => {
        return metaEvents.map((event, i) => {
            let startDiff, endDiff, totalRange;
            switch(calendarType) {
                case 'day':
                    startDiff = differenceInCalendarDays(startOfDay(day), event.startDate);
                    endDiff = differenceInCalendarDays(event.endDate, endOfDay(day));
                    totalRange = differenceInCalendarDays(event.endDate, event.startDate) + 1;
                    break;
                case 'week':
                    startDiff = differenceInCalendarWeeks(startOfWeek(day), event.startDate);
                    endDiff = differenceInCalendarWeeks(event.endDate, endOfWeek(day));
                    //totalRange = differenceInCalendarWeeks(event.endDate, event.startDate) + 1;
                    break;
                case 'year':
                    startDiff = differenceInCalendarYears(startOfYear(day), event.startDate);
                    endDiff = differenceInCalendarYears(event.endDate, endOfYear(day));
                    totalRange = differenceInCalendarYears(event.endDate, event.startDate) + 1;
                    break;
                case 'month':
                case 'schedule':
                default:
                    startDiff = differenceInCalendarMonths(startOfMonth(day), event.startDate);
                    endDiff = differenceInCalendarMonths(event.endDate, endOfMonth(day));
                    totalRange = differenceInCalendarMonths(event.endDate, event.startDate);
                    break;
            }

            if (startDiff >= 0 && endDiff >= 0) {
                const backgroundColor = colors?.[i] || colors?.[i % colors.length] || undefined;
                return (
                    <Event 
                        event={event}
                        key={`event-${event.id}-${day}`}
                        disabled={disabled}
                        className={
                            `${styles.event} 
                            ${size === "small" ? styles.small : ''} 
                            ${startDiff > 0 && endDiff <= 0 ? styles["just-prev"] : ''} 
                            ${endDiff > 0 && startDiff <=0 ? styles["just-next"] : ''} 
                            ${endDiff > 0 && startDiff > 0 ? styles["both"] : ''}`.replace(/\s+/g, ' ')
                        }
                        sx={{
                            background: theme => backgroundColor || theme.palette.info.main,
                            color: theme => backgroundColor ? theme.palette.getContrastText(backgroundColor) : theme.palette.primary.contrastText,
                            zIndex: theme => theme.zIndex.mobileStepper + 1,
                        }}
                        typographyProps= {{
                            variant: size === "small" ? "subtitle3" : "subtitle2"
                        }}
                        onEventClick={onEventClick}
                    />
                );
            }
            return null;
        });
    }

    if (!currentDate) return null;

    return (
        <div>
            {renderEvents(currentDate)}
        </div>
    );

}