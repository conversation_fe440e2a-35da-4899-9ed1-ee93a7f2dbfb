import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Paper } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

export const UsageReports = () => {
    const { t } = useOutletContext();

    return (
        <Container>
            <Title
                title={t('reports:usageReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:usageReports')}
                ]}
            />

            <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h5" gutterBottom>
                    Usage Reports Content
                </Typography>
                <Typography variant="body1">
                    This page will contain usage reports including:
                </Typography>
                <ul>
                    <li>System Usage</li>
                    <li>Feature Adoption</li>
                    <li>Performance Metrics</li>
                    <li>Error Rates</li>
                    <li>User Sessions</li>
                </ul>
            </Paper>
        </Container>
    );
};

export default UsageReports;
