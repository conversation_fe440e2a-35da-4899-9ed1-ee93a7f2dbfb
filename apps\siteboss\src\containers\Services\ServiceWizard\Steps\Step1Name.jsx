import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step1 } from './stepList';

const Step1Name = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const [localFormData, setLocalFormData] = useState(formData || {});

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        setLocalFormData(prev => {
            const updated = { ...prev, [field]: value };
            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Get step fields
    const fields = step1(localFormData);

    return (
        <Grid container spacing={3}>
            {fields.map(section => (
                <Grid item xs={12} key={section.id}>
                    <Typography variant="h6" gutterBottom>
                        {section.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                        {section.description}
                    </Typography>
                    <Grid container spacing={2}>
                        {section.fields.map(field => (
                            <Grid item xs={12} key={field.name}>
                                {field.component === "TextField" && (
                                    <field.component
                                        fullWidth
                                        label={field.label}
                                        name={field.name}
                                        value={localFormData[field.name] || ''}
                                        onChange={(e) => handleChange(field.name, e.target.value)}
                                        required={field.required}
                                        multiline={field.minRows > 1}
                                        minRows={field.minRows}
                                        margin={field.margin}
                                        disabled={loading}
                                        error={field.error}
                                        helperText={field.helperText}
                                    />
                                )}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default Step1Name;
