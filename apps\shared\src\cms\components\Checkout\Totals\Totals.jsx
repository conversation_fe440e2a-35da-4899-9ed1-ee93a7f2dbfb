import React, { useMemo } from 'react';
import { Container } from '@mui/material';

import CommonTotals from '../../common/pos/Totals';

export const Totals = ({items = ["subtotal", "shipping", "tax", "tip", "payments", "priceAdjustments"], extraFields, slotProps, ...props}) => {
    const filteredItems = useMemo(() => {
        const allItems = ["subtotal", "shipping", "tax", "tip", "payments", "priceAdjustments"];
        // filter out the items that are not in the items array and order them according to the items array
        return items.map(item => allItems.find(i => i === item)).filter(i => i);
    }, [items]);

    return (
        <Container disableGutters {...slotProps?.container}>
            <CommonTotals items={filteredItems} extraFields={extraFields} slotProps={slotProps?.totals} />
        </Container>
    );
}