import { useState, useCallback, useEffect, useMemo } from 'react';
import { useTheme, lighten, darken } from '@mui/material';
import { addMonths, subMonths, startOfMonth, addWeeks, subWeeks, startOfWeek, addDays, subDays, startOfDay, addYears, subYears, startOfYear } from 'date-fns';

// generates a shade of a color
const generateShades = color => ([
    lighten(color, 0.6),
    lighten(color, 0.4),
    lighten(color, 0.2),
    color,
    darken(color, 0.2),
    darken(color, 0.4),
]);

export const useCalendar = ({
    events, // an object that holds events and metaEvents ({events: [], metaEvents: []})
    type = 'schedule', // the type of calendar 'week', 'day', 'month', 'year', or 'schedule'
    selectedDate, // the selected date (Date object)
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onDateChange = () => {}, // function to handle date changes
    onSearchTextChange = () => {}, // function to handle search text
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided)
    hideSearchBar, // whether to hide the search bar
    ...props // additional props
}) => {
    const theme = useTheme();
    const [currentDate, setCurrentDate] = useState(selectedDate || new Date());
    const [calendarType, setCalendarType] = useState(type);
    const [showSearch, setShowSearch] = useState(!hideSearchBar || false);
    const [searchText, setSearchText] = useState('');

    // resets the search bar
    const resetSearch = useCallback(() => {
        //setShowSearch(false);
        setSearchText('');
    }, []);

    const goToStart = useCallback((date) => {
        let _value;
        switch (calendarType) {
            case 'week':
                _value = startOfWeek(date);
                break;
            case 'day':
                _value = startOfDay(date);
                break;
            case 'year':
                _value = startOfYear(date);
                break;
            case 'month':
            case 'schedule':
            default:
                _value = startOfMonth(date);
                break;
        }
        if (_value) {
            setCurrentDate(_value);
            onDateChange(_value);
        }
    }, [calendarType, onDateChange]);

    // sets the current date to the previous date based on the calendar type
    const handlePrev = useCallback(() => {
        if (!currentDate) return;
        resetSearch();
        switch (calendarType) {
            case 'day':
                goToStart(subDays(startOfDay(currentDate), 1));
                break;
            case 'week':
                goToStart(subWeeks(startOfWeek(currentDate), 1));
                break;
            case 'year':
                goToStart(subYears(startOfYear(currentDate), 1));
                break;
            case 'month':
            case 'schedule':
            default:
                goToStart(subMonths(startOfMonth(currentDate), 1));
                break;
        }
    }, [calendarType, currentDate, resetSearch, goToStart]);

    // sets the current date to the next date based on the calendar type
    const handleNext = useCallback(() => {
        if (!currentDate) return;
        resetSearch();
        switch (calendarType) {
            case 'day':
                goToStart(addDays(startOfDay(currentDate), 1));
                break;
            case 'week':
                goToStart(addWeeks(startOfWeek(currentDate), 1));
                break;
            case 'year':
                goToStart(addYears(startOfYear(currentDate), 1));
                break;
            case 'month':
            case 'schedule':
            default:
                goToStart(addMonths(startOfMonth(currentDate), 1));
                break;
        }
    }, [calendarType, currentDate, resetSearch, goToStart]);

    // sets the current date to today
    const handleToday = useCallback(() => {
        resetSearch();
        goToStart(new Date());
    }, [resetSearch, goToStart]);

    // if the search text changes it will show the search bar even if the search text or results are empty
    const handleSearchTextChange = useCallback(value => {
        setShowSearch(true);
        setSearchText(value);
        onSearchTextChange(value);
    }, [onSearchTextChange]);    

    // create an array of colors from all the shades
    const colors = useMemo(() => [
        ...generateShades(theme.palette.primary.main),
        ...generateShades(theme.palette.secondary.main),
        ...generateShades(theme.palette.error.main),
        ...generateShades(theme.palette.warning.main),
        ...generateShades(theme.palette.info.main),
        ...generateShades(theme.palette.success.main),

        /* PLAYGROUND STUFF, BECAUSE WE'RE GAMERS AT HEART
        // create a mixed array of colors from the shades
        const primaryColors = generateShades(theme.palette.primary.main);
        const secondaryColors = generateShades(theme.palette.secondary.main);
        const errorColors = generateShades(theme.palette.error.main);
        const warningColors = generateShades(theme.palette.warning.main);
        const infoColors = generateShades(theme.palette.info.main);
        const successColors = generateShades(theme.palette.success.main);
        const colors = primaryColors.map((_, i) => [
            primaryColors[i],
            secondaryColors[i],
            errorColors[i],
            successColors[i],
            infoColors[i],
            warningColors[i],
        ]).flat();
        */
    ], [theme.palette]);

    const calendarProps = useMemo(() => ({
        ...events,
        currentDate,
        colors,
        loading,
        disabled,
        onEventClick: onEventClick || null,
    }), [events, currentDate, colors, loading, disabled, onEventClick]);

    // displays the search bar if there are events
    useEffect(() => {
        if (hideSearchBar) setShowSearch(false);
        else {
            if (events?.events?.length) setShowSearch(true);
            else if (!searchText) setShowSearch(false);
        }
    }, [events, searchText, hideSearchBar]);

    useEffect(() => {
        setCalendarType(type);
    }, [type]);

    return {
        currentDate,
        calendarType,
        setCalendarType,
        showSearch,
        searchText,
        resetSearch,
        handlePrev,
        handleNext,
        handleToday,
        handleSearchTextChange,
        calendarProps,
        colors,
    };
};