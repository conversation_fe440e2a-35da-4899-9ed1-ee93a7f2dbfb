import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Typography } from '@mui/material';

import { PosProductDetailContext } from '../../../../hooks/PosProductDetailContext';
import Memo from './Memo';
import { Button, Link } from './Layouts';

const items = [{id: 1, slug: "pos:itemMemo"}];

export const ProductMemo = ({ layoutType ="link", product, slotProps, onSave }) => {
    const { t } = useTranslation();
    const { fullPage, loading, changeView } = useContext(PosProductDetailContext) || {};

    const Component = layoutType === 'link' ? Link : Button;

    if (!product) return null;

    return (
        <Container disableGutters sx={{my: 1}}>
            <Typography variant="subtitle2" component="div">
                <Typography variant="bold" component="div">{t("pos:preferences")}</Typography>
            </Typography>
            
            <Component 
                items={items}
                onSelect={()=>{}}
                disabled={loading}
                selected={null}
                slotProps={{
                    fullPage,
                    changeView: fullPage ? null : changeView,
                    extraInfo: {
                        show: true
                    },
                    ...slotProps,
                }}
                slots={{
                    extraInfo: ({item, ...props}) => <Memo product={product} onSave={onSave} {...props} />,
                }}                   
            />

        </Container>    
    );
}