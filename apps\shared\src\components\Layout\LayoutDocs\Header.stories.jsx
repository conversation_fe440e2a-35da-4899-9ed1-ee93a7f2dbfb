import { Header } from '../Header/Header'


export default {
    title: 'Shared/Layout/Header',
    component: Header,
    tags:['autodocs'],
    argTypes: {
        isMobile: {
            description: "Flag to indicate if the view is mobile",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag indicating whether the view is mobile."
            }
        },
        toggleDrawer: {
            description: "Function to toggle the drawer",
            control: 'action',
            table: {
                type: { summary: "function" },
                defaultValue: { summary: "undefined" },
                detail: "A callback function to toggle the drawer."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        isMobile: false,
        toggleDrawer: () => console.log('Drawer toggled')
    }
};

// Following stories to illustrate each significant prop
export const MobileView = {
    args: {
        isMobile: true,
        toggleDrawer: () => console.log('Drawer toggled')
    }
};

export const DesktopViewWithUser = {
    args: {
        isMobile: false,
        toggleDrawer: () => console.log('Drawer toggled')
    }
};
