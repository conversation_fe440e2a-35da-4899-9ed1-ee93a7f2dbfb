
import { useOutletContext } from 'react-router-dom';
import { Stack, Typography, Grid2, Box } from '@mui/material';
import { FormItem } from '@siteboss-frontend/shared/components';

export const ShippingNotifications = ({ value, errors, onChange, loading }) => {
    const { t } = useOutletContext();

    // Notification types in order
    const notificationTypes = [
        { key: 'processed', label: t('shipping:processed') },
        { key: 'shipped_out', label: t('shipping:shippedOut') },
        { key: 'out_for_delivery', label: t('shipping:outForDelivery') },
        { key: 'delivery_attempt', label: t('shipping:deliveryAttempt') },
        { key: 'exception', label: t('shipping:exception') },
        { key: 'returned', label: t('shipping:returned') },
        { key: 'delivered', label: t('shipping:delivered') }
    ];

    const recipientTypes = [
        { key: 'bill_to', label: t('shipping:billTo') },
        { key: 'recipient', label: t('shipping:recipient') }
    ];

    return (
        <Stack spacing={2} direction="column" useFlexGap>
            {/* Header Row */}
            <Grid2 container columnSpacing={2} sx={{ borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                <Grid2 size={{ xs: 12, md: 2 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                        {t('shipping:notificationRecipient')}
                    </Typography>
                </Grid2>
                {notificationTypes.map(type => (
                    <Grid2 size={{ xs: 4, sm: 3, md: 1.4 }} key={type.key}>
                        <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ 
                                fontSize: '0.7rem', 
                                lineHeight: 1.2,
                                display: 'block',
                                fontWeight: 'medium'
                            }}>
                                {type.label}
                            </Typography>
                        </Box>
                    </Grid2>
                ))}
            </Grid2>

            {/* Notification Rows */}
            {recipientTypes.map(recipient => (
                <Grid2 container columnSpacing={2} alignItems="center" key={recipient.key}>
                    <Grid2 size={{ xs: 12, md: 2 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            {recipient.label}
                        </Typography>
                    </Grid2>
                    {notificationTypes.map(notification => {
                        const fieldName = `${recipient.key}_${notification.key}`;
                        return (
                            <Grid2 size={{ xs: 4, sm: 3, md: 1.4 }} key={fieldName}>
                                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                                    <FormItem
                                        name={fieldName}
                                        label=""
                                        required={false}
                                        value={value?.[fieldName] || false}
                                        checked={Boolean(value?.[fieldName])}
                                        component="Checkbox"
                                        margin="none"
                                        errors={errors?.[fieldName]}
                                        onChange={onChange}
                                        loading={loading}
                                        sx={{ 
                                            '& .MuiFormControlLabel-root': { 
                                                margin: 0,
                                                '& .MuiCheckbox-root': {
                                                    padding: '4px'
                                                }
                                            }
                                        }}
                                    />
                                </Box>
                            </Grid2>
                        );
                    })}
                </Grid2>
            ))}
        </Stack>
    );
};
