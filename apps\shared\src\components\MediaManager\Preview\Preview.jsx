import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Box, Dialog, DialogContent, useMediaQuery, ImageList } from "@mui/material";

import { useApi } from "../../../api";
import NoRows from '../../DataTable/NoRows';
import ImageCropper from "../../FileUpload/ImageCropper";

import PreviewCard from "./PreviewCard";

export const Preview = ({selected, mediaType, imageToCrop, onCrop, onHideImageCropper, onFileRemove, onError, onFileClick, loading, fetchCounter, ...props}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const user = useSelector(state => state.user.profile.id);

    const apiParams = useMemo(() => (
        {params: {endpoint: "/user/media/user", method: "POST", data: {user_id: mediaType === 9 ? undefined : user, media_type: mediaType }}}
    ), [mediaType, user]);

    // if mediaType is 9 (logo), we get all the logos and bypass the user_id
    const { fetchData: fetchMedia } = useApi(apiParams);

    const [media, setMedia] = useState([]);

    const getMedia = useCallback(async () => {
        try {
            const res = await fetchMedia();
            if (res) {
                if (res.errors) onError(res.errors);
                else if (res.data) {
                    setMedia(res.data);
                    const _files = [], _previews = [];
                    res.data?.forEach(item => {
                        let name = (media.description && isNaN(media.description)) ? media.description : null;
                        if (media.metadata && !name){
                            let meta = media.metadata;
                            if (typeof meta === 'string') meta = JSON.parse(meta);
                            if (meta.original_filename) name = meta.original_filename;
                        }

                        let type = 'other';
                        if (mediaType === 1 || mediaType === 9) type = 'image';
                        else if (mediaType === 4) type = 'video';
                        else if (mediaType === 7) type = 'audio';

                        _previews.push({name, url: item.url, type});

                    });
                }
            }
        } catch (error) {
            onError(t("error:default"));
        }
    }, [fetchMedia, mediaType, onError]);

    useEffect(() => {
        if (mediaType) getMedia();
    }, [getMedia, mediaType]);

    return (
        <Box sx={{display:'flex', flexDirection: 'column', flexGrow: 1}}>
            {media.length > 0 ?
                <>
                    <ImageList variant='masonry' cols={isMobile ? 2 : 5} gap={5} sx={{width: '100%'}}>
                        {media.map((item, i) => (
                            <PreviewCard 
                                key={i} 
                                media={item} 
                                onClick={onFileClick} 
                                onCrop={onCrop} 
                                onRemove={onFileRemove} 
                                selected={selected?.find(a => a === item.url)} 
                                loading={loading} 
                            />
                        ))}
                    </ImageList>
                    {/* image cropper */}
                    {imageToCrop &&
                        <Dialog open={Boolean(imageToCrop)} onClose={onHideImageCropper} fullWidth maxWidth="sm" fullScreen={isMobile} closeAfterTransition={false} sx={{overflow: 'hidden'}}>
                            <DialogContent sx={{p: 0, overflow: 'hidden'}}>
                                <ImageCropper 
                                    src={imageToCrop?.url} 
                                    onCrop={onCrop(imageToCrop)} 
                                    onClose={onHideImageCropper} 
                                    cropAreaProps={props?.sx || undefined} 
                                />
                            </DialogContent>
                        </Dialog>
                    }
                </>
            : 
                <Box sx={{flexGrow: 1}}>
                    <NoRows title={t("media:empty")} />
                </Box>
            }
        </Box>
    );
}