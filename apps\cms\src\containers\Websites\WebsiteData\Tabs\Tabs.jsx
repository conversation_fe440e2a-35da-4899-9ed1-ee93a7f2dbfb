import React, { useCallback, useMemo, useState, useContext, useEffect } from 'react';
import { useOutletContext, useLocation, useNavigate } from 'react-router-dom';
import { usePermission } from '@siteboss-frontend/shared/permissions';
import { WithDetailsToolbar } from '@siteboss-frontend/shared/components';
import { TabPanel, TabContext } from '@mui/lab';
import { 
    StorefrontOutlined as EcommerceIcon, 
    InfoOutlined as InfoIcon, 
    AutoAwesomeMotionOutlined as PagesIcon, 
    HighlightAltOutlined as TemplatesIcon, 
    AccountTreeOutlined as SiteMapIcon,
    ArticleOutlined as BlogIcon,
    DonutLargeOutlined as ChartIcon, 
    InventoryOutlined as FormsIcon 
} from '@mui/icons-material';

import { WebsiteDataContext } from '../WebsiteDataContext';
import WebsiteInfo from "../WebsiteInfo";
import Pages from "../Pages";

export const Tabs = ({contentRef}) => {    
    const { isMobile } = useOutletContext();
    const location = useLocation();
    const navigate = useNavigate();
    
    const [selectedTab, setSelectedTab] = useState(null);
    const [selectedTabProps, setSelectedTabProps] = useState(null);

    const { 
        websiteId,
        selectedWebsites,
        websiteData,
        pagesData,
        errorBars,
        loading,
        handleShowDetails,
        handleUpdate,
        onDelete,
        onEdit,
        triggerReload,
        setSelectedPageTypeId,
    } = useContext(WebsiteDataContext);

    // handle the toolbar click
    const handleToolbarClick = useCallback((e, action) => {
        if (!action) return;

        let _props = {
            websiteData: websiteData?.[0],
            websiteId: websiteId, 
            onUpdate: handleUpdate, 
            loading,
        };

        setSelectedTab(`${action}`);
        setSelectedPageTypeId(+action || null);
        setSelectedTabProps(_props);
        navigate(`#${action}`);
    }, [websiteData, websiteId, handleUpdate, loading, navigate]);

    // generate a list of tabs dynamically
    const tabs = useMemo(() => {
        const _tabs = [];

        let mobileIndex = 0, index = 0;
        if (isMobile) {
            mobileIndex = 1;
            _tabs.push({ id: 'info', index, slug: 'website:toolbar.basic', moduleId: 25, icon: <InfoIcon/>, component: props => <WebsiteInfo onDelete={onDelete} onEdit={onEdit} {...props} />});
            index++;
        }

        _tabs.push(
            { id: 'pages', index: index + 1 + mobileIndex, slug: 'website:toolbar.pages', moduleId: 25, icon: <PagesIcon/>, component: props => (
                <Pages {...props} />
            )},
            { id: 'shops', index: index + 2 + mobileIndex, slug: 'website:toolbar.ecommerce', moduleId: 25, icon: <EcommerceIcon/>, component: props => (
                <Pages pageTypeId={14} title="website:page.type.ecommerce.new" subtitle="website:page.type.ecommerce.newSubtitle" onNewClick={handleShowDetails} {...props} />
            )},
            { id: 'blogs', index: index + 3 + mobileIndex, slug: 'website:toolbar.blog', moduleId: 25, icon: <BlogIcon/>, component: props => (
                <Pages pageTypeId={4} title="website:page.type.blog.new" subtitle="website:page.type.blog.newSubtitle" onNewClick={handleShowDetails} {...props} />
            )},
            { id: 'templates', index: index + 4 + mobileIndex, slug: 'website:toolbar.templates', moduleId: 25, icon: <TemplatesIcon/>, component: props => (
                <Pages pageTypeId={9} title="website:page.type.template.new" subtitle="website:page.type.template.newSubtitle" {...props} />
            )},
            { id: 'sitemap', index: index + 5 + mobileIndex, slug: 'website:toolbar.sitemap', moduleId: 25, icon: <SiteMapIcon/>, component: props => <div />},
        );

        if (websiteData) setSelectedTab(prev => !prev ? _tabs[0].id : prev);
        
        return _tabs;
    }, [isMobile, onDelete, onEdit, websiteData, pagesData]);

    const { permissions } = usePermission({moduleIds: tabs.map(tab => tab.moduleId)});

    const visibleTabs = useMemo(() => tabs.filter(tab => {
        return permissions[tab.moduleId];
    }), [permissions]);


    useEffect(() => {
        const hash = location.hash.replace('#', '');
        if (hash) {
            setSelectedTab(hash);
            let _selectedPageTypeId = 1;
            switch(hash){
                case 'shops':
                    _selectedPageTypeId = 14;
                    break;
                case 'blogs':
                    _selectedPageTypeId = 4;
                    break;
                case 'templates':
                    _selectedPageTypeId = 9;
                    break;
                default:
                    break;
            }
            setSelectedPageTypeId(_selectedPageTypeId);
            setSelectedTabProps({
                websiteData: websiteData?.[0],
                websiteId: websiteId, 
                onUpdate: handleUpdate, 
                loading,
            });
        }
    }, [location.hash, websiteData, websiteId, handleUpdate, loading]);

    return (
        <>
            {errorBars?.map((ErrorBar, i) => ErrorBar && <ErrorBar key={i} />)}
            {websiteData?.[0] &&
                <TabContext value={selectedTab ?? visibleTabs?.[0]?.id ?? 'pages'}> 
                    {!isMobile &&
                        <WebsiteInfo websiteData={websiteData?.[0]} onDelete={onDelete} onEdit={onEdit} loading={loading} />
                    }
                    <WithDetailsToolbar 
                        tabs={visibleTabs}
                        websiteData={websiteData?.[0]} 
                        parentRef={contentRef} 
                        onSelection={handleToolbarClick} 
                        selectedTab={selectedTab}
                        setSelectedTab={setSelectedTab}
                        sx={{mt: 4, ml: 'auto'}} 
                        stickyTop={selectedWebsites?.length > 1 ? 68 : 38}
                    />
                    {visibleTabs.map(tab => (
                        <TabPanel key={tab.id} value={tab.id} sx={{ position:'relative', px: isMobile ? 1 : undefined, pb: isMobile ? theme => theme.sizes.headerHeight : undefined }}>
                            {tab?.component({...{
                                websiteData: websiteData?.[0], 
                                websiteId: websiteData?.[0]?.id,
                                onUpdate: handleUpdate, 
                                pagesData: selectedTab === tab.id ? pagesData : null,
                                loading,
                                triggerReload,
                            }, ...selectedTabProps})}
                        </TabPanel>
                    ))}
                </TabContext>
            }
        </>
    );
}