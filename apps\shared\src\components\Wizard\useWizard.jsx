import { useState, useCallback, useEffect } from 'react';

export const useWizard = (onChangeStep, onError) => {
    const [activeStep, setActiveStep] = useState(0);
    const [hasErrors, setHasErrors] = useState(false);
    const [visitedSteps, setVisitedSteps] = useState([0]); // Track visited steps, starting with step 0

    // check if there are errors
    const handleErrors = errors => {
        if ((typeof errors === 'object' && errors !== null && Object.keys(errors).length > 0) || (typeof errors !== 'object' && errors)) setHasErrors(true);
        else setHasErrors(false);
        if (onError) onError(errors);
    }

    // format the data for the api
    const formatData = useCallback(data => {
        const formattedData = {};

        for (const key in data){
            if (data[key]){
                data[key].forEach(field => {
                    formattedData[field.name] = field.value;
                });
            }
        }
        return formattedData;
    }, []);

    // Custom setActiveStep that also tracks visited steps
    const setActiveStepWithTracking = useCallback((stepOrFn) => {
        setActiveStep(prevStep => {
            const newStep = typeof stepOrFn === 'function' ? stepOrFn(prevStep) : stepOrFn;

            // Update visited steps
            setVisitedSteps(prev => {
                if (!prev.includes(newStep)) {
                    return [...prev, newStep];
                }
                return prev;
            });

            return newStep;
        });
    }, []);

    useEffect(() => {
        if (onChangeStep) onChangeStep(activeStep);
    }, [activeStep, onChangeStep]);

    return ({
        handleErrors,
        setActiveStep: setActiveStepWithTracking,
        formatData,
        activeStep,
        hasErrors,
        visitedSteps,
    });
}