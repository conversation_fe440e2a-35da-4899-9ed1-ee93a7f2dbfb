import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography } from '@mui/material';
import { InfiniteLoader } from '@siteboss-frontend/shared';

import { useKeycloak } from './useKeycloak';

export const ProtectedRoute = ({ children, roles = [], resources = [] }) => {
  //return children; // remove

  const { t } = useTranslation();
  const { authenticated, loading, error, hasRole, hasResourceRole, login } = useKeycloak();

  // Show loading spinner while Keycloak is initializing
  if (loading) {
    return <InfiniteLoader open={true} errors={null} setErrors={null} onRetry={null} message="Authenticating..." />;
  }

  // Show error if Keycloak initialization failed
  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={2}
      >
        <Typography variant="body1" color="textPrimary">
          {t("error:default")}
        </Typography>
        <Typography variant="caption" color="textSecondary">
          {error.message || error.error || (typeof error === 'string' ? error : 'Unknown error')}
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!authenticated) {
    // Trigger Keycloak login
    login();
    return <InfiniteLoader open={true} errors={null} setErrors={null} onRetry={null} message="Loading authentication..." />;
  }

  // Check role-based access if roles are specified
  if (roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          gap={2}
        >
          <Typography variant="body1" color="textPrimary">
            {t("error:forbidden")}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            Roles: {roles.join(', ')}
          </Typography>
        </Box>
      );
    }
  }

  // Check resource-based access if resources are specified
  if (resources.length > 0) {
    const hasRequiredResource = resources.some(({ role, resource }) => 
      hasResourceRole(role, resource)
    );
    if (!hasRequiredResource) {
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          gap={2}
        >
          <Typography variant="body1" color="textPrimary">
            {t("error:accessDenied")}
          </Typography>
        </Box>
      );
    }
  }

  // Render children if all checks pass
  return children;
};