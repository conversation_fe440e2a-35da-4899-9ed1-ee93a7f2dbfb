import { Checkbox } from './Checkbox';
import { fn } from 'storybook/test';

export default {
    title: 'Shared/Components/FormItem/Checkbox',
    component: Checkbox,
    tags: ['autodocs'],
    argTypes: {
        label: {
            description: 'The label for the checkbox',
            control: 'text',
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: null }
            }
        },
        name: {
            description: 'The name of the field',
            control: 'text',
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: null }
            }
        },
        margin: {
            description: 'The margin applied to the form control',
            control: 'select',
            options: ['dense', 'none', 'normal'],
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: 'none' }
            }
        },
        onChange: {
            description: 'Callback fired when the state is changed',
            action: 'changed',
            table: {
                type: { summary: 'function' },
                defaultValue: { summary: undefined }
            }
        },
        required: {
            description: 'If the field is required',
            control: 'boolean',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: false }
            }
        },
        errors: {
            description: 'Error message to display',
            control: 'text',
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: null }
            }
        },
        loading: {
            description: 'Disables the field when form is loading',
            control: 'boolean',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: false }
            }
        },
        isSwitch: {
            description: 'Renders as a switch instead of a checkbox',
            control: 'boolean',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: false }
            }
        },
        checked: {
            description: 'Controls the checked state',
            control: 'boolean',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: false }
            }
        },
        size: {
            description: 'The size of the component',
            control: 'select',
            options: ['small', 'medium', 'large'],
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: 'medium' }
            }
        },
        labelPlacement: {
            description: 'The position of the label',
            control: 'select',
            options: ['end', 'start', 'top', 'bottom'],
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: 'end' }
            }
        },
        helperText: {
            description: 'Helper text to display below the checkbox',
            control: 'text',
            table: {
                type: { summary: 'string' },
                defaultValue: { summary: null }
            }
        }
    }
};

// Playground story with minimal props
export const Playground = {
    args:{
        label:'Checkbox Label',
        name:'checkbox-name',
        onChange:fn(),
    }
};

// Basic checkbox with all common props
export const Default = {
    args: {
        ...Playground.args,
        helperText: 'This is helper text',
        required: false,
        checked: false
    }
};

// Switch variant
export const AsSwitch = {
    args: {
        ...Default.args,
        isSwitch: true,
        label: 'Switch Label'
    }
};

// Required checkbox
export const Required = {
    args: {
        ...Default.args,
        required: true,
        label: 'Required Checkbox'
    }
};

// With error state
export const WithError = {
    args: {
        ...Default.args,
        errors: 'This field is required',
        label: 'Error State'
    }
};

// Loading state
export const Loading = {
    args: {
        ...Default.args,
        loading: true,
        label: 'Loading State'
    }
};

// Small size variant
export const SmallSize = {
    args: {
        ...Default.args,
        size: 'small',
        label: 'Small Checkbox'
    }
};

// Different label placement
export const LabelStart = {
    args: {
        ...Default.args,
        labelPlacement: 'start',
        label: 'Label at Start'
    }
};