import { DockIcon } from '../Menu/DockIcon/DockIcon'

export default {
    title: 'Shared/Layout/Menu/DockIcon',
    component: DockIcon,
    tags:['autodocs'],
    argTypes: {
        isDocked: {
            description: "Flag to indicate if the drawer is docked",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag indicating whether the drawer is docked."
            }
        },
        drawerWidth: {
            description: "Width of the drawer",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 240 },
                detail: "The width of the drawer in pixels."
            }
        },
        setDrawerWidth: {
            description: "Function to set the drawer width",
            control: 'action',
            table: {
                type: { summary: "function" },
                defaultValue: { summary: "undefined" },
                detail: "A callback function to set the drawer width."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        isDocked: false,
        drawerWidth: 240,
        setDrawerWidth: width => console.log(`Drawer width set to: ${width}`)
    }
};

// Following stories to illustrate each significant prop
export const DockedIcon = {
    args: {
        isDocked: true,
        drawerWidth: 240,
        setDrawerWidth: width => console.log(`Drawer width set to: ${width}`)
    }
};

export const WithCustomDrawerWidth = {
    args: {
        isDocked: false,
        drawerWidth: 300,
        setDrawerWidth: width => console.log(`Drawer width set to: ${width}`)
    }
};
