import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useMediaQuery } from '@mui/material';

import { useApi } from '../../api/useApi';
import useCollectJs from '../../components/useCollectJs';
import { generateHash } from '../../utils/cms';
import { usePageRouter } from '../utils';
import { useLoggedInUser } from './useLoggedInUser';

import { useFieldsFromStore } from '../../store/useFieldsFromStore';
import { setInfo, resetProduct } from '../../store/reducers/currentShopItemSlice';
import { setInfo as setPosInfo, addToCart, resetCart, resetInfo } from '../../store/reducers/cartSlice';

const apiParams = [
    {enableCache: false, params: {endpoint: `/order`, method: 'POST', data: {
        max_records: 1,
        page_no: 1,
        sort_col: 'id',
        sort_direction: 'DESC',
    }}},
    {enableCache: false, params: {endpoint: `/order/add`, method: 'POST'}},
    {enableCache: false, params: {endpoint: `/order/update`, method: 'PUT'}},
    {enableCache: false, params: {endpoint: `/order/latest_open`, method: 'POST'}},
    {enableCache: false, params: {endpoint: `/order/order`, method: 'GET'}},
    {enableCache: false, params: {endpoint: `/payment/process`, method: 'POST'}},
];

export const usePos = ({
    registerId = null,
    shopId = null,
    registerDefinition,
    selectLoggedInUser = false,
    isBuilder,
}) => {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const posLoaded = useRef(false);

    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);
    const cart = useSelector(state => state.cart, shallowEqual);
    const user = useSelector(state => state.user?.profile, shallowEqual);
    const company = useSelector(state => state.company, shallowEqual);

    const { router } = usePageRouter({pageRouterId: shopId});

    const reduxStore = useFieldsFromStore([
        "user.id:user.userId", "cart.userId", "cart.orderId", "cart.totals", 
        "currentShopItem.productTypeId", "currentShopItem.productCategoryId", "currentShopItem.productId", "currentShopItem.forUserIds", 
        "currentShopItem.hasEvent", "currentShopItem.hasGiftCard", "currentShopItem.hasServiceBooking",
    ]);

    const [userSectionOpen, setUserSectionOpen] = useState(!isMobile);
    const [totalSectionOpen, setTotalSectionOpen] = useState(!isMobile);
    const [orderLoading, setOrderLoading] = useState(false);
    const [orderErrors, setOrderErrors] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);

    /*const { renderSection } = useClientRender({
        componentList,
        store: {
            ...reduxStore,
            userSectionOpen,
            totalSectionOpen,
            orderLoading,
            orderErrors,
        }
    });*/

    const registerDef = useMemo(() => {
        if (!registerDefinition) return null;
        return registerDefinition(isMobile);
    }, [isMobile, registerDefinition]);

    const params = useParams();
    if (params?.registerId) registerId = +params.registerId;

    // get the last order so we have a number to show... this number will change later when the user adds their first item
    const { fetchData: fetchLastOrder } = useApi(apiParams[0]);
    const { fetchData: createOrder } = useApi(apiParams[1]);
    const { fetchData: editOrder } = useApi(apiParams[2]);
    const { fetchData: latestOpen } = useApi(apiParams[3]);
    const { fetchData: reloadOrder } = useApi(apiParams[4]);
    const { fetchData: payOrder, ErrorBar: PaymentErrorBar } = useApi(apiParams[5]);

    useCollectJs({...company?.config?.find(a => a.config_type_id === 3)?.config || {}, isBuilder});

    const updatePosInfo = useCallback(data => {
        if (!data) return;
        //dispatch(setOrderInfo(data));
        dispatch(setPosInfo({
            orderId: data.id,
            userId: data?.user_id || null,
            registerId: data?.register_id || registerId || null,
            shopId: shopId || null,
            registerGroupId: data?.register_group_id || null,
            coupons: data.coupons || [],
            couponsApplied: data.coupons_applied || [], 
            subtotal: data?.subtotal_price || 0, 
            shipping: data?.shipping_total || 0, 
            tax: data?.tax_total || 0, 
            tip: data?.tip || 0, 
            total: data.total_price || 0, 
            payments: data?.payment_total  || 0,
            priceAdjustments: data?.price_adjustments || [],
            calculatedCashDiscount: data?.calculated_cash_discount || 0,
            transactions: data?.transactions?.filter(a => 
                +a.transaction_status_id===7 
                && +a.transaction_type_id !== 2 
                && +a.transaction_type_id !== 3
                && +a.amount - +a.amount_refunded > 0
            ) || [],
        }));
        posLoaded.current = true;
    }, [dispatch, registerId, shopId]);

    const resetOrder = useCallback(() => {
        dispatch(resetInfo());
        dispatch(resetCart());
        dispatch(resetProduct());
        localStorage.removeItem(`register-${registerId}-${shopId}-${user?.id}`);
        posLoaded.current = false;
    }, [dispatch, registerId, shopId, user?.id]);

    const loadLatestOrder = useCallback(async (userId, _registerId = null) => {
        if (!userId && (!registerDef || registerDef?.config?.useLoggedInUser && user?.id)) userId = user.id;
        if (!_registerId) _registerId = registerId;
        if (!userId || !_registerId || cart?.orderId) return;

        try{
            setOrderLoading(true);
            const res = await latestOpen({ user_id: userId, register_id: _registerId });
            if (res?.errors) setOrderErrors(res.errors);
            else if (res?.data) {
                updatePosInfo(res.data);
                dispatch(resetCart());
                dispatch(addToCart(res.data?.items || []));
                return res.data;
            }
        } catch (error) {
            setOrderErrors(error);
        } finally {
            setOrderLoading(false);
        }
    }, [dispatch, updatePosInfo, cart?.orderId, user?.id, registerDef, registerId, latestOpen]);


    // toggle the user or totals sections on mobile view
    const toggleSection = useCallback((section, open = null) => {
        if (section === 'user') setUserSectionOpen(prev => open === null ? !prev : open);
        if (section === 'total') setTotalSectionOpen(prev => open === null ? !prev : open);
    }, []);

    const handleLoadOrder = useCallback(async ({userId = null, _registerId = null, orderId, extraParams = {}}) => {
        if (!userId && (!registerDef || registerDef?.config?.useLoggedInUser && user?.id)) userId = user.id;
        if (!_registerId) _registerId = registerId;
        if (!orderId) return;

        setOrderLoading(true);
        try {
            const res = await fetchLastOrder({order_id: orderId, user_id: userId, register_id: _registerId, ...extraParams});
            if (res?.errors) setOrderErrors(res.errors);
            else if (res?.data?.orders?.length) {
                return res.data?.orders?.[0];
            }
        } catch (error) {
            setOrderErrors(error);
        } finally {
            setOrderLoading(false);
        }
        return null;
    }, [fetchLastOrder, user?.id, registerDef, registerId]);

    // updates (or creates) an order and syncs with redux
    const handleOrderUpdate = useCallback(async ({items, couponsApplied, override = false, fields = {}}, callback) => {
        setOrderLoading(true);
        let apiCall = editOrder;
        if (!cart?.orderId) apiCall = createOrder;

        const _transformItemData = data => ({
            id: data?.productVariantId || null,
            order_item_id: data?.metadata?.order_item_id || null,
            order_id: cart?.orderId || null,
            tmp_id: data?.metadata?.tmp_id || null,
            product_id: data?.productId || null,
            variant_id: data?.productVariantId || null,
            price_override: data?.productCustomPrice || null,
            qty: data?.qty || 1,
            addons: data?.addons?.map(a => ({
                id: a?.variantId || null,
                name: a?.name || null,
                order_item_id: a?.metadata?.order_item_id || null,
                addon_id: a?.id || null,
                variant_id: a?.variantId || null,
                product_id: a?.productId || null,
                qty: data?.qty || 1,
            })) || null,
            memo: data?.memo,
            giftcard: data?.giftCardRecipient?.full_name && data?.giftCardRecipient?.email ? data.giftCardRecipient : null,
            event: data?.forUserIds?.length ? {
                event_id: data?.eventId || null,
                custom_fields: data?.customFields || [],
                for_user_id: data?.forUserIds?.map(a => a.id) || [],
            } : null,
        });

        const params = {
            order_id: cart?.orderId,
            register_id: cart?.registerId || registerId || null,
            //shop_id: cart?.shopId || null,
            user_id: cart?.userId || company?.config?.find(a => +a.config_type_id === 1)?.config?.guest_user_id || null,
            applied_coupon_ids: (couponsApplied ? couponsApplied?.map(a => a.id) : cart?.couponsApplied.map(a => a.id)) || null,
            items: override ? [] : (cart?.cart?.map(a => _transformItemData(a)) || []),
            ...fields,
        }

        //console.log("CART", cart.cart, items)

        // push the newly added item to the cart
        if (items) {
            items.filter(a => +a?.qty > 0).forEach(item => {
                params.items = params.items.filter(a => a.tmp_id !== item?.metadata?.tmp_id);
                params.items.push(_transformItemData(item));
            });
        }

        try {
            const res = await apiCall(params);
            if (res?.errors) setOrderErrors(res.errors);
            else if (res?.data?.[0]?.id) {
                updatePosInfo(res.data[0]);

                // save the current order in localstorage so we can recover it if the user leaves the page
                localStorage.setItem(`register-${cart?.registerId || registerId}-${shopId}-${cart?.userId}`, res.data[0].id);

                dispatch(resetCart());
                dispatch(addToCart(res.data[0]?.items || []));

                // call the callback if it exists
                if (callback) callback(res.data[0]);
            }
        } catch (error) {
            setOrderErrors(error);
            console.log(error)
        } finally {
            setOrderLoading(false);
        }
    }, [cart, updatePosInfo, company?.config, dispatch, createOrder, editOrder, shopId, registerId]);

    const handleAddToCart = useCallback(async (product, reduxState) => {
        /* returns: 0 = fail, 1 = success, 2 = needs user input */

        let productId = currentShopItem?.productId || null,
            productVariantId = currentShopItem?.productVariantId || null,
            productCustomPrice = currentShopItem?.productCustomPrice || null,
            eventId = currentShopItem?.eventId || null,
            forUserIds = currentShopItem?.forUserIds || [],
            customFields = currentShopItem?.customFields || [],
            addons = currentShopItem?.addons || [],
            qty = currentShopItem?.qty || 1,
            giftCardRecipient = currentShopItem?.giftCardRecipient || [],
            memo = currentShopItem?.memo || null,
            tmpId = currentShopItem?.tmpId || null;

        /* THIS NEEDS TO BE FIXED: the reason we may need the redux state as a param is because sometimes we may update the state and add to the cart in the same render cycle */
        if (reduxState) {
            productId = reduxState?.productId || productId;
            productVariantId = reduxState?.productVariantId || productVariantId;
            productCustomPrice = reduxState?.productCustomPrice || productCustomPrice;
            eventId = reduxState?.eventId || eventId;
            forUserIds = reduxState?.forUserIds || forUserIds;
            customFields = reduxState?.customFields || customFields;
            addons = reduxState?.addons || addons;
            qty = reduxState?.qty || qty;
            giftCardRecipient = reduxState?.giftCardRecipient || giftCardRecipient;
            memo = reduxState?.memo || memo;
            tmpId = reduxState?.tmpId || tmpId;
        }

        const _addToCart = async () => {
            if (!productId || !productVariantId || !qty) return 0;

            const _variant = product?.product_variants.find(v => +v.id === +productVariantId) || {};
            const metadata = {
                order_item_id: product?.order_item_id || null,
                id: product?.id,
                name: product?.name,
                tmp_id: tmpId,
                variant_name: product?.product_variants?.length > 1 ? (_variant?.name.toLowerCase() === "default" ? t("pos:fullPrice") : _variant?.name) : null,
                activation_fee: _variant?.activation_fee || 0,
                price: _variant?.price || 0,
                bill_interval: product?.bill_interval,
                bill_num_times: product?.bill_num_times,
                interval_quantity: product?.interval_quantity,
                pro_rate: product?.pro_rate,
                bill_on_day: product?.bill_on_day,
                first_bill_after_x_cycles: product?.first_bill_after_x_cycles,
                custom_fields: product?.events?.[0]?.custom_fields || [],
            };

            // send the order
            try {
                await handleOrderUpdate({items: [{
                    tmpId,
                    productId,
                    productVariantId,
                    productCustomPrice,
                    qty,
                    eventId,
                    forUserIds,
                    customFields,
                    addons,
                    giftCardRecipient,
                    memo,
                    metadata,
                }]}, () => {
                    /*dispatch(addToCart({
                        productId, 
                        productVariantId, 
                        productCustomPrice, 
                        qty, 
                        eventId, 
                        forUserIds, 
                        customFields, 
                        addons, 
                        giftCardRecipient, 
                        memo, 
                        metadata
                    }));*/
                    dispatch(resetProduct());
                });
            } catch(e){
                console.error(e);
                return 0;
            }

            return 1;
        }

        if (product){
            if (!productId) productId = product?.id || product;
            if (!productVariantId) {
                if (product?.product_variants?.length === 1 && !product?.events?.length){
                    productVariantId = product?.product_variants[0]?.id;
                }

                dispatch(setInfo({productId, productVariantId, qty: 1, eventId: product?.events?.length ? product?.events[0]?.id : null}));
                
                if (product?.product_variants?.length > 1 || product?.events?.length > 0 || product?.product_variants?.[0]?.has_addons){
                    return 2;
                }
            }
        }
        return await _addToCart();
    }, [t, dispatch, currentShopItem, handleOrderUpdate]);


    const setUpOrder = useCallback(async ({ userId = null, extraParams = {} }) => {
        //console.log("setup")

        posLoaded.current = false;

        if (!userId && (!registerDef || registerDef?.config?.useLoggedInUser && user?.id)) userId = user.id;        
        let orderId = localStorage.getItem(`register-${registerId}-${shopId}-${userId}`) || null;

        try {
            setOrderLoading(true);
            if (orderId) {
                if (userId) extraParams.user_id = userId;
                // if there is an open order on localstorage, we load it
                try {
                    const result = await fetchLastOrder({order_id: orderId, register_id: registerId, with_extra_details: true, ...extraParams});
                    if (result?.errors) setOrderErrors(result.errors);
                    if (result?.data?.orders?.[0]) {
                        const order = result.data?.orders?.[0];
                        orderId = order?.id;
                        updatePosInfo(order);
                        dispatch(resetCart());
                        dispatch(addToCart(order?.items || []));
                        return order;
                    }
                } catch (error) {
                    setOrderErrors(error);
                }
            } else if (user?.id) {
                // if there is no open order we get the last order number to have something pretty to show
                try {
                    const result = await fetchLastOrder();
                    if (result?.errors) setOrderErrors(result.errors);
                    if (result?.data?.orders?.length) {
                        orderId = result.data?.orders?.[0]?.id;
                        if (orderId) {
                            orderId = `${+orderId + 1}*`;
                            dispatch(resetInfo());
                            dispatch(setPosInfo({userId, registerId, fakeOrderId: orderId}));
                        }
                    }
                    //loadLatestOrder(userId, registerId);
                } catch (error) {
                    setOrderErrors(error);
                } finally {
                    posLoaded.current = true;
                }
            }
        } catch (error) {
            setOrderErrors(error);
        } finally {
            setOrderLoading(false);
        }
    }, [user?.id, registerId, shopId, registerDef, dispatch, updatePosInfo, fetchLastOrder, reloadOrder, /*loadLatestOrder*/]);

    const updateOrder = useCallback(async ({fields}, callback) => {
        if (!fields || !cart?.orderId) return;

        try {
            await handleOrderUpdate({items: cart?.cart, couponsApplied: cart?.couponsApplied, override: true, fields}, callback);
        } catch (error) {
            setOrderErrors(error);
            console.log(error)
        }
    }, [cart?.orderId, cart?.cart, cart?.couponsApplied, handleOrderUpdate]);

    const closeOrder = useCallback(async (status_id = 3) => {
        try {
            await updateOrder({fields: {order_status_id: status_id}}, async data => {
                if (data){
                    resetOrder();
                    dispatch(setPosInfo({fakeOrderId: `${data.id}*`}));
                }
            });
        } catch(error){
            setOrderErrors(error);
        }
    }, [updateOrder, dispatch, resetOrder]);

    const handleOrderPay = useCallback(async ({payments = [], files = null}, callback) => {
        if (!Array.isArray(payments)) payments = [payments];
        let params = {
            order_id: cart?.orderId,
            hash: null,
            payments: [],
            files,
        }
        
        // we need to send payments one by one
        const allRequests = [];
        for (const payment of payments) {
            const _payment = [{
                payment_method_id: payment?.paymentMethodId,
                ...payment,
            }];
            const hash = await generateHash(payment);
            allRequests.push(payOrder({...params, hash, payments: _payment}));
        }
        
        setOrderLoading(true);
        try{
            const result = await Promise.all(allRequests);
            if (result){
                const _errors = [];
                const _success = {payments: [], order: {}};
                for (const res of result) {
                    if (res?.errors) _errors.push(res.errors);
                    else if (res?.data?.[0]) {
                        _success.payments.push(res.data[0]);
                    }
                }
                if (_errors.length > 0) setOrderErrors(_errors);
                else {
                    // grab the updated order to refresh the redux state
                    try {
                        const res2 = await setUpOrder({userId: cart?.userId});
                        if (res2) {
                            _success.order = JSON.parse(JSON.stringify(res2)); // res2;
                            if (callback) callback(_success);
                        }

                        /*
                        localStorage.setItem(`register-${cart?.registerId || registerId}-${shopId}-${cart?.userId}`, cart?.orderId);

                        const res2 = await reloadOrder({endpoint: `/order/order/${cart?.orderId}`});
                        if (res2.errors) setOrderErrors(res2.errors);
                        else if (res2?.data) {
                            _success.order = res2.data;
                            updatePosInfo(res2.data);
                            dispatch(resetCart());
                            dispatch(addToCart(res2.data?.items || []));
                            if (callback) callback(_success);
                        }
                            */
                    } catch (error) {
                        setOrderErrors(error);
                        console.log(error)
                    }
                }
            }
        } catch (error) {
            setOrderErrors(error);
            console.log(error)
        } finally {
            setOrderLoading(false);
        }
    }, [cart?.orderId, payOrder, shopId, registerId, setUpOrder]);

    const handleUserSelection = useCallback(async user => {
        setSelectedUser(user);
        dispatch(setPosInfo({userId: user?.id || null}));
    }, [dispatch]);

    const handleUserClear = useCallback(() => {
        setSelectedUser(null);
        dispatch(setPosInfo({userId: null}));
        dispatch(removeForUsers());
    }, [dispatch]);

    const { LoginForm } = useLoggedInUser({selectLoggedInUser, onUserSelection: handleUserSelection, selectedUser});

    // later when there is an api call to get the register definition, we will call that function here
    useEffect(() => {
        if (registerId && !posLoaded.current && !isBuilder) {
            setUpOrder({});
        }
    }, [registerId, setUpOrder, isBuilder]);

    return {
        registerDefinition: registerDef,
        //renderSection,
        userSectionOpen, 
        setUserSectionOpen,
        totalSectionOpen,
        setTotalSectionOpen,
        toggleSection,
        handleAddToCart,
        handleOrderUpdate,
        handleOrderPay,
        handleUserSelection,
        handleUserClear,
        handleLoadOrder,
        selectedUser,
        orderLoading,
        orderErrors,
        setOrderErrors,
        loadLatestOrder,
        updateOrder,
        closeOrder,
        resetOrder,
        setUpOrder,
        updatePosInfo,
        posLoaded: posLoaded.current,
        reduxStore,
        router,
        cart,
        LoginForm,
        errorBars: [PaymentErrorBar],
    };
}