import {NavigateNextOutlined as NavigateNextIcon, RouteOutlined as BreadcrumbsIcon} from '@mui/icons-material';

export const layouts = [
    {
        id: 1,
        name: 'Default',
        icon: <NavigateNextIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            cmsContainer: {
                sx: {
                    display: 'flex',
                }
            },
            cmsStack: {
                sx: {
                    width: '100%',
                    flexGrow: 1,
                }
            },
            breadcrumbs:{
                sx: {
                    my: 'auto',
                }
            }
        }
    }
];

export const widgetIcon = BreadcrumbsIcon;