import React, { useMemo } from 'react';
import { Stack } from '@mui/material';

import { Gallery as SbGallery } from '../../../components';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

export const Gallery = ({
    id,
    images = [],
    imageSize,
    objectFit,
    objectPosition,
    captions = [],
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        gallery: {},        // Gallery props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, contentWindow, canRender, customCss, noContent } = prepareComponent({name: "gallery", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const imageList = useMemo(() => {
        let _images = images;
        if (!Array.isArray(_images)) _images = [images];
        return _images.map((image, i) => {
            return {
                preview_url: image,                
                description: captions[i] || null,
            };
        });
    }, [images, captions]);


    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack spacing={0} direction="column" useFlexGap sx={isBuilder ? {minHeight: 30} : undefined} {...slotProps?.cmsStack}>
                {!imageList.length ? noContent :
                    <SbGallery 
                        images={imageList} 
                        disabled={isBuilder} 
                        contentWindow={contentWindow} 
                        type={+layoutId === 1 ? "carousel" : (+layoutId === 2 ? "list" : "masonry")} 
                        size={{xs: 1, md: 2, lg: 4}}
                        imageSize={imageSize}
                        objectFit={objectFit}
                        objectPosition={objectPosition}
                        {...slotProps?.gallery} 
                    />
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};
