import React, { useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Container,
  Paper,
  Tabs,
  Tab,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button
} from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

// Sample data for invoices
const invoices = [
  { id: 1, number: 'INV-2023-001', date: '2023-04-01', amount: 1250.00, status: 'Paid' },
  { id: 2, number: 'INV-2023-002', date: '2023-05-01', amount: 1250.00, status: 'Pending' },
  { id: 3, number: 'INV-2023-003', date: '2023-06-01', amount: 1250.00, status: 'Upcoming' },
];

// Sample data for customer invoices
const customerInvoices = [
  { id: 1, customer: 'Acme Corp', number: 'CUST-2023-001', date: '2023-04-15', amount: 450.00, status: 'Paid' },
  { id: 2, customer: 'Globex Inc', number: 'CUST-2023-002', date: '2023-04-20', amount: 750.00, status: 'Overdue' },
  { id: 3, customer: 'Initech', number: 'CUST-2023-003', date: '2023-05-10', amount: 325.00, status: 'Pending' },
];

export const BillingInvoices = (props) => {
  const { t } = useOutletContext();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container>
      <Title 
        title={t('billing:billingAndInvoices')}
        breadcrumbs={[{title: t('dashboard:dashboard'), to: '/'}, {title: t('billing:billingAndInvoices')}]}
      />
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="billing tabs">
          <Tab label="My Invoices" />
          <Tab label="Customer Invoices" />
          <Tab label="Payment Methods" />
        </Tabs>
      </Paper>

      {/* My Invoices Tab */}
      {tabValue === 0 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">My Invoices</Typography>
            <Button variant="contained" color="primary">Download Statement</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Invoice #</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>{invoice.number}</TableCell>
                    <TableCell>{invoice.date}</TableCell>
                    <TableCell>${invoice.amount.toFixed(2)}</TableCell>
                    <TableCell>{invoice.status}</TableCell>
                    <TableCell>
                      <Button size="small">View</Button>
                      {invoice.status === 'Pending' && (
                        <Button size="small" color="primary" sx={{ ml: 1 }}>Pay</Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Customer Invoices Tab */}
      {tabValue === 1 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Customer Invoices</Typography>
            <Button variant="contained" color="primary">Create Invoice</Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Customer</TableCell>
                  <TableCell>Invoice #</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {customerInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>{invoice.customer}</TableCell>
                    <TableCell>{invoice.number}</TableCell>
                    <TableCell>{invoice.date}</TableCell>
                    <TableCell>${invoice.amount.toFixed(2)}</TableCell>
                    <TableCell>{invoice.status}</TableCell>
                    <TableCell>
                      <Button size="small">View</Button>
                      <Button size="small" color="primary" sx={{ ml: 1 }}>Edit</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Payment Methods Tab */}
      {tabValue === 2 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Payment Methods</Typography>
            <Button variant="contained" color="primary">Add Payment Method</Button>
          </Box>
          <Typography variant="body1">
            Manage your payment methods and billing preferences here.
          </Typography>
        </Paper>
      )}
    </Container>
  );
}
