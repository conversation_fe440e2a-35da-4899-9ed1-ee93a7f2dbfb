import React, { useCallback, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Buttons, Select, List } from '../Layouts';

import { setInfo } from '../../../../store/reducers/currentShopItemSlice';

export const Types = ({ layoutId, loading, disabled, isBuilder, slotProps = {}, options = [], onSelect, selectedId }) => {
    const dispatch = useDispatch();

    const selectedType = useSelector(state => state.currentShopItem?.productTypeId);

    const handleSelect = useCallback(value => {
        if (options.length && !value) dispatch(setInfo({productTypeId: options.map(a => a.id), productCategoryId: null}));
        else dispatch(setInfo({productTypeId: value, productCategoryId: null}));
        if (onSelect) onSelect(value);
    }, [dispatch, onSelect, options]);

    const sharedProps = useMemo(() => ({
        options: options || [],
        allSlug: "product:productTypes.all",
        onSelect: handleSelect,
        reset: false, //selectedType === null
        isBuilder,
    }), [options, handleSelect, selectedType, isBuilder]);

    useEffect(() => {
        if (options.length) {
            dispatch(setInfo({productTypeId: options.map(a => a.id), productCategoryId: null}));
        }
    }, [options, dispatch]);

    useEffect(() => {
        if (selectedId) dispatch(setInfo({productTypeId: selectedId, productCategoryId: null}));
    }, [selectedId, dispatch]);

    switch (layoutId) {
        case 1:
            return <Buttons {...slotProps?.buttons} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        case 2:
            return <Select {...slotProps?.select} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        case 3:
        case 4:
            return <List {...slotProps?.list} loading={loading} disabled={disabled || loading} {...sharedProps} />;
        default:
            return null;
    }
}