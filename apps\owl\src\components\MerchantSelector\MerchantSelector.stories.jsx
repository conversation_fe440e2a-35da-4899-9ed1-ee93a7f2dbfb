import { MerchantSelector } from './MerchantSelector';

export default {
    title: 'OWL/Components/MerchantSelector',
    component: MerchantSelector,
    tags: ['autodocs'],
    argTypes: {
        label: {
            description: 'The label for the field',
            control: 'text',
            table: {
                defaultValue: { summary: 'merchant:merchant' },
                type: { summary: 'string' }
            }
        },
        name: {
            description: 'The name of the field',
            control: 'text',
            table: {
                defaultValue: { summary: 'merchant_id' },
                type: { summary: 'string' }
            }
        },
        required: {
            description: 'If the field is required',
            control: 'boolean',
            table: {
                defaultValue: { summary: false },
                type: { summary: 'boolean' }
            }
        },
        multiple: {
            description: 'Allow multiple selections',
            control: 'boolean',
            table: {
                defaultValue: { summary: false },
                type: { summary: 'boolean' }
            }
        },
        disabled: {
            description: 'If the field is disabled',
            control: 'boolean',
            table: {
                defaultValue: { summary: false },
                type: { summary: 'boolean' }
            }
        },
        loading: {
            description: 'Loading state of the form',
            control: 'boolean',
            table: {
                defaultValue: { summary: false },
                type: { summary: 'boolean' }
            }
        },
        placeholder: {
            description: 'Placeholder text',
            control: 'text',
            table: {
                type: { summary: 'string' }
            }
        },
        helperText: {
            description: 'Helper text to display',
            control: 'text',
            table: {
                type: { summary: 'string' }
            }
        },
        margin: {
            description: 'Margin for the field',
            control: 'select',
            options: ['none', 'dense', 'normal'],
            table: {
                defaultValue: { summary: 'normal' },
                type: { summary: 'string' }
            }
        },
        size: {
            description: 'Size of the field',
            control: 'select',
            options: ['small', 'medium'],
            table: {
                defaultValue: { summary: 'medium' },
                type: { summary: 'string' }
            }
        },
        variant: {
            description: 'Variant of the field',
            control: 'select',
            options: ['outlined', 'filled', 'standard'],
            table: {
                defaultValue: { summary: 'outlined' },
                type: { summary: 'string' }
            }
        }
    }
};

// Default story
export const Default = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false
    }
};

// Required field story
export const Required = {
    args: {
        label: 'Select Merchant *',
        name: 'merchant_id',
        required: true,
        multiple: false
    }
};

// Multiple selection story
export const MultipleSelection = {
    args: {
        label: 'Select Merchants',
        name: 'merchant_ids',
        required: false,
        multiple: true
    }
};

// With helper text
export const WithHelperText = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false,
        helperText: 'Choose the merchant for this product'
    }
};

// Disabled state
export const Disabled = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false,
        disabled: true
    }
};

// Loading state
export const Loading = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false,
        loading: true
    }
};

// Small size
export const SmallSize = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false,
        size: 'small'
    }
};

// Dense margin
export const DenseMargin = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: false,
        multiple: false,
        margin: 'dense'
    }
};

// With error
export const WithError = {
    args: {
        label: 'Select Merchant',
        name: 'merchant_id',
        required: true,
        multiple: false,
        errors: 'Please select a merchant'
    }
};

// Form integration example
export const FormIntegration = {
    render: (args) => {
        const handleChange = (event) => {
            console.log('Selected merchant:', event.target.value);
        };

        return (
            <div style={{ padding: '20px', maxWidth: '400px' }}>
                <h3>Product Form Example</h3>
                <MerchantSelector
                    {...args}
                    onChange={handleChange}
                />
            </div>
        );
    },
    args: {
        label: 'Product Merchant',
        name: 'merchant_id',
        required: true,
        multiple: false,
        helperText: 'Select the merchant who produces this product'
    }
};
