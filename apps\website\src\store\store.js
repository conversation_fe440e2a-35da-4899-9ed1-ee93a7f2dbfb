import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { languageReducer, userReducer, colorModeReducer, companyReducer, permissionReducer, apiCacheReducer, apiSlice, cartReducer, currentShopItemReducer } from '@siteboss-frontend/shared/store';

//exporting the rootReducer too so that it can be imported for things like Storybook
export const rootReducer = combineReducers({
    api: apiSlice.reducer,
    colorMode: colorModeReducer,
    language: languageReducer,
    user: userReducer,
    company: companyReducer,
    permission: permissionReducer,
    apiCache: apiCacheReducer,
    currentShopItem: currentShopItemReducer,
    cart: cartReducer,
});

export default configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware =>
        getDefaultMiddleware().concat(apiSlice.middleware),
});