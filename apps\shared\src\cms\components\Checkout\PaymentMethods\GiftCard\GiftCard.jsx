import React, { useState, useCallback, useId } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { <PERSON>ack, <PERSON>ert, Button } from '@mui/material';

import FormItem from '../../../../../components/FormItem';
import { createCurrencyFormatter } from '../../../../../utils/currency';

export const GiftCard = ({
    paymentMethod, 
    paymentMethodId, 
    amount, 
    loading: parentLoading, 
    removeCashDiscount,
    cashDiscount = 0,
    onPaymentProcess, 
    onPaymentChange, 
    slotProps, 
    ...props
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const id = useId();

    const [code, setCode] = useState('');
    const [errors, setErrors] = useState({});
    const [amountAvailable, setAmountAvailable] = useState(0);

    const handleChange = useCallback(e => {
        setCode(e.target.value);
        if (onPaymentChange) {
            onPaymentChange({
                id, 
                values: {
                    code: e.target.value, 
                    amount: amountAvailable, 
                    removeCashDiscount,
                }, 
                paymentMethod, 
                paymentMethodId,
            });
        }
        if (!e.target.value) setErrors({code: t('error:invalid')});
    }, [onPaymentChange, id, amountAvailable, paymentMethod, paymentMethodId, removeCashDiscount, t]);

    const handlePay = useCallback(async () => {
        if (onPaymentProcess) {
            await onPaymentProcess({
                id, 
                values: {
                    code, 
                    amount: amountAvailable,
                    removeCashDiscount,
                }, 
                paymentMethod, 
                paymentMethodId,
            });
        }
    }, [onPaymentProcess, id, code, amountAvailable, paymentMethod, paymentMethodId, removeCashDiscount]);

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            <FormItem 
                label={t('giftCard:code')}
                required
                component='TextField'
                name='card_code'
                margin="normal"
                value={code}
                onChange={handleChange}
                errors={errors?.code}
                {...slotProps?.input}
            />
            <Button 
                loading={parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl" 
                fullWidth 
                disabled={parentLoading || +amount <= 0 || +amountAvailable <= 0 || errors?.code } 
                onClick={handlePay} 
                {...slotProps?.button}
            >
                {t(`pos:${+amountAvailable < +amount ? 'processPartialPayment' : 'processPayment'}`)}
            </Button>
            {removeCashDiscount > 0 &&
                <Alert variant="outlined" severity="warning">{t("pos:warnings.removeCashDiscount", {amount: currencyFormatter.format(cashDiscount, currency)})}</Alert>
            }
        </Stack>
    );
}