import { useState } from 'react';
import {
  Grid2 as <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Typo<PERSON>,
  <PERSON>,
  Button
} from '@mui/material';
import {
  Receipt as OrderIcon,
  TrendingUp as TrendIcon,
  Schedule as TimeIcon,
  AttachMoney as MoneyIcon,
  <PERSON><PERSON>hart as ChartIcon,
  <PERSON><PERSON><PERSON> as PieIcon
} from '@mui/icons-material';
import { <PERSON><PERSON>hart, <PERSON><PERSON>hart, DataTable, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';
import { ReportCard, ReportFilters } from '../components';

export const OrderReports = () => {
  const [filters, setFilters] = useState({});

  // Mock data for charts
  const orderVolumeData = {
    data: [
      {
        id: 'Orders',
        data: [
          { x: 'Mon', y: 45 },
          { x: 'Tue', y: 52 },
          { x: 'Wed', y: 38 },
          { x: 'Thu', y: 61 },
          { x: 'Fri', y: 73 },
          { x: 'Sat', y: 29 },
          { x: 'Sun', y: 34 }
        ]
      }
    ]
  };

  const orderStatusData = {
    data: [
      { id: 'Pending', value: 23, color: '#FF9800' },
      { id: 'Processing', value: 45, color: '#2196F3' },
      { id: 'Shipped', value: 67, color: '#4CAF50' },
      { id: 'Delivered', value: 89, color: '#8BC34A' },
      { id: 'Cancelled', value: 12, color: '#F44336' }
    ]
  };

  const orderTableData = {
    columns: [
      { field: 'id', headerName: 'Order ID', width: 120 },
      { field: 'customer', headerName: 'Customer', width: 200 },
      { field: 'status', headerName: 'Status', width: 120 },
      { field: 'total', headerName: 'Total', width: 120 },
      { field: 'date', headerName: 'Date', width: 150 }
    ],
    rows: [
      { id: 'ORD-001', customer: 'Acme Corp', status: 'Shipped', total: '$1,250.00', date: '2024-01-15' },
      { id: 'ORD-002', customer: 'Tech Solutions', status: 'Processing', total: '$890.50', date: '2024-01-15' },
      { id: 'ORD-003', customer: 'Global Industries', status: 'Delivered', total: '$2,100.00', date: '2024-01-14' },
      { id: 'ORD-004', customer: 'StartUp Inc', status: 'Pending', total: '$450.75', date: '2024-01-14' },
      { id: 'ORD-005', customer: 'Enterprise Ltd', status: 'Shipped', total: '$3,200.00', date: '2024-01-13' }
    ]
  };

  const reports = [
    {
      title: 'Order Volume Trends',
      description: 'Track daily, weekly, and monthly order volumes',
      icon: <TrendIcon />,
      color: '#4CAF50',
      metrics: [
        { label: 'Today', value: '47 orders' },
        { label: 'This Week', value: '332 orders' },
        { label: 'Growth', value: '+12.5%' }
      ],
      tags: ['Volume', 'Trends', 'Growth'],
      lastUpdated: '2 hours ago'
    },
    {
      title: 'Order Status Distribution',
      description: 'View breakdown of orders by current status',
      icon: <PieIcon />,
      color: '#2196F3',
      metrics: [
        { label: 'Pending', value: '23' },
        { label: 'Processing', value: '45' },
        { label: 'Shipped', value: '67' }
      ],
      tags: ['Status', 'Distribution'],
      lastUpdated: '1 hour ago'
    },
    {
      title: 'Processing Times',
      description: 'Average time from order to shipment',
      icon: <TimeIcon />,
      color: '#FF9800',
      metrics: [
        { label: 'Avg Time', value: '2.3 hours' },
        { label: 'Fastest', value: '45 min' },
        { label: 'Target', value: '< 3 hours' }
      ],
      tags: ['Performance', 'SLA'],
      lastUpdated: '30 minutes ago'
    },
    {
      title: 'Order Value Analysis',
      description: 'Revenue trends and average order values',
      icon: <MoneyIcon />,
      color: '#9C27B0',
      metrics: [
        { label: 'Avg Order', value: '$1,247' },
        { label: 'Total Revenue', value: '$45,230' },
        { label: 'Growth', value: '+8.2%' }
      ],
      tags: ['Revenue', 'AOV', 'Growth'],
      lastUpdated: '1 hour ago'
    }
  ];

  const handleViewReport = (reportTitle) => {
    console.log('Viewing report:', reportTitle);
    // Implement report viewing logic
  };

  const handleDownloadReport = (reportTitle) => {
    console.log('Downloading report:', reportTitle);
    // Implement report download logic
  };

  const handlePrintReport = (reportTitle) => {
    console.log('Printing report:', reportTitle);
    // Implement report printing logic
  };

  return (
    <Box>
      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={() => setFilters({})}
        dateRange={true}
        statusFilter={true}
        customerFilter={true}
        locationFilter={true}
      />

      {/* Key Metrics */}
      <MetricCardGroup sx={{ mb: 4 }}>
        <MetricCard
          title="Total Orders"
          value="1,247"
          change="+12.5%"
          changeType="positive"
          icon={<OrderIcon />}
          color="primary"
        />
        <MetricCard
          title="Avg Order Value"
          value="$1,247"
          change="+8.2%"
          changeType="positive"
          icon={<MoneyIcon />}
          color="success"
        />
        <MetricCard
          title="Processing Time"
          value="2.3h"
          change="-15min"
          changeType="positive"
          icon={<TimeIcon />}
          color="warning"
        />
        <MetricCard
          title="Completion Rate"
          value="94.2%"
          change="+2.1%"
          changeType="positive"
          icon={<ChartIcon />}
          color="info"
        />
      </MetricCardGroup>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Order Volume Trends
              </Typography>
              <Box sx={{ height: 300 }}>
                <LineChart data={orderVolumeData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Order Status Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <PieChart data={orderStatusData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Report Cards */}
      <Typography variant="h5" sx={{ mb: 3 }}>
        Available Reports
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reports.map((report, index) => (
          <Grid size={{ xs: 12, md: 6, lg: 3 }} key={index}>
            <ReportCard
              {...report}
              onView={() => handleViewReport(report.title)}
              onDownload={() => handleDownloadReport(report.title)}
              onPrint={() => handlePrintReport(report.title)}
            />
          </Grid>
        ))}
      </Grid>

      {/* Recent Orders Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Recent Orders
            </Typography>
            <Button variant="outlined" size="small">
              View All Orders
            </Button>
          </Box>
          <DataTable
            columns={orderTableData.columns}
            rows={orderTableData.rows}
            pageSize={5}
            disableSelectionOnClick
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default OrderReports;
