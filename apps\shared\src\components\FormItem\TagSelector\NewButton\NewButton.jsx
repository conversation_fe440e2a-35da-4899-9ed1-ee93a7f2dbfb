import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Typography, Tooltip, IconButton } from '@mui/material';
import { AddOutlined as AddIcon } from '@mui/icons-material';

export const NewButton = ({ type, disabled, showEmptyText, onClick }) => {
    const { t } = useTranslation();

    if (type === "autocomplete") {
        return (
            <Button variant="text" color="inherit" size="small" onClick={onClick} disabled={disabled}>
                {t('tag:newTag')}
            </Button>
        );
    } else if (showEmptyText) {
        return (
            <>
                <Typography variant="subtitle2" component="div" sx={{width: '100%'}}>
                    {t('tag:empty')}
                </Typography>
                <Button variant="text" color="inherit" size="small" onClick={onClick} disabled={disabled}>
                    {t('tag:newTag')}
                </Button>
            </>
        )
    } else {
        return (
            <div>
                <Tooltip title={t('tag:newTag')}>
                    <IconButton size="small" disabled={disabled} onClick={onClick}>
                        <AddIcon fontSize='inherit' />
                    </IconButton>
                </Tooltip>
            </div>
        );
    }
}
