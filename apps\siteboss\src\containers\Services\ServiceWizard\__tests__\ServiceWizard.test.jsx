import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { ServiceWizard } from '../ServiceWizard';

// Mock the API calls
jest.mock('@siteboss-frontend/shared', () => ({
    ...jest.requireActual('@siteboss-frontend/shared'),
    useApi: jest.fn(() => ({
        data: null,
        loading: false,
        error: null,
        call: jest.fn()
    }))
}));

// Mock the shared components
jest.mock('@siteboss-frontend/shared/components', () => ({
    SuccessBar: ({ message }) => <div data-testid="success-bar">{message}</div>,
    Wizard: ({ title, slots }) => (
        <div data-testid="wizard">
            <h2>{title}</h2>
            <div data-testid="wizard-steps">
                {slots.steps.map((Step, index) => (
                    <div key={index} data-testid={`step-${index}`}>
                        <Step />
                    </div>
                ))}
            </div>
        </div>
    )
}));

// Mock the useWizard hook
jest.mock('../useWizard', () => ({
    __esModule: true,
    useWizard: jest.fn(() => ({
        handleErrors: jest.fn(),
        handleSubmit: jest.fn(),
        setActiveStep: jest.fn(),
        activeStep: 0,
        loading: false,
        success: false,
        setSuccess: jest.fn(),
        hasErrors: false,
        errorBars: [],
        serviceId: null,
        serviceData: null,
        resetForm: jest.fn(),
        completedSteps: [0],
        serviceWizard: {
            id: null,
            serviceData: null,
            selectedLocations: [],
            selectedManagers: [],
            formData: {
                name: 'Test Service',
                description: 'Test Description',
                block_minutes: 30,
                default_price: 50
            }
        }
    }))
}));

// Mock the step components
jest.mock('../Steps/Step1Name', () => ({
    __esModule: true,
    default: () => <div data-testid="step1">Step 1</div>
}));

jest.mock('../Steps/Step2Availability', () => ({
    __esModule: true,
    default: () => <div data-testid="step2">Step 2</div>
}));

jest.mock('../Steps/Step3Increments', () => ({
    __esModule: true,
    default: () => <div data-testid="step3">Step 3</div>
}));

jest.mock('../Steps/Step4Location', () => ({
    __esModule: true,
    default: () => <div data-testid="step4">Step 4</div>
}));

jest.mock('../Steps/Step5Manager', () => ({
    __esModule: true,
    default: () => <div data-testid="step5">Step 5</div>
}));

jest.mock('../Steps/Step6Payment', () => ({
    __esModule: true,
    default: () => <div data-testid="step6">Step 6</div>
}));

jest.mock('../Steps/Step7Cancellation', () => ({
    __esModule: true,
    default: () => <div data-testid="step7">Step 7</div>
}));

jest.mock('../Steps/Step8Summary', () => ({
    __esModule: true,
    default: () => <div data-testid="step8">Step 8</div>
}));

// Create a mock store
const mockStore = configureStore([]);
const theme = createTheme();

describe('ServiceWizard Component', () => {
    let store;
    
    beforeEach(() => {
        store = mockStore({
            serviceWizard: {
                id: null,
                serviceData: null,
                selectedLocations: [],
                selectedManagers: [],
                formData: {
                    name: 'Test Service',
                    description: 'Test Description',
                    block_minutes: 30,
                    default_price: 50
                }
            }
        });
        
        // Reset the mock API
        const { useApi } = require('@siteboss-frontend/shared');
        useApi.mockImplementation(() => ({
            data: null,
            loading: false,
            error: null,
            call: jest.fn()
        }));
    });
    
    it('renders the wizard with all steps', () => {
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services/wizard']}>
                        <Routes>
                            <Route path="/services/wizard" element={<ServiceWizard />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByTestId('wizard')).toBeInTheDocument();
        expect(screen.getByTestId('step1')).toBeInTheDocument();
        expect(screen.getByTestId('step2')).toBeInTheDocument();
        expect(screen.getByTestId('step3')).toBeInTheDocument();
        expect(screen.getByTestId('step4')).toBeInTheDocument();
        expect(screen.getByTestId('step5')).toBeInTheDocument();
        expect(screen.getByTestId('step6')).toBeInTheDocument();
        expect(screen.getByTestId('step7')).toBeInTheDocument();
        expect(screen.getByTestId('step8')).toBeInTheDocument();
    });
    
    it('displays success message when success is true', () => {
        const { useWizard } = require('../useWizard');
        useWizard.mockImplementation(() => ({
            handleErrors: jest.fn(),
            handleSubmit: jest.fn(),
            setActiveStep: jest.fn(),
            activeStep: 0,
            loading: false,
            success: true,
            setSuccess: jest.fn(),
            hasErrors: false,
            errorBars: [],
            serviceId: null,
            serviceData: null,
            resetForm: jest.fn(),
            completedSteps: [0],
            serviceWizard: {
                id: null,
                serviceData: null,
                selectedLocations: [],
                selectedManagers: [],
                formData: {
                    name: 'Test Service',
                    description: 'Test Description',
                    block_minutes: 30,
                    default_price: 50
                }
            }
        }));
        
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services/wizard']}>
                        <Routes>
                            <Route path="/services/wizard" element={<ServiceWizard />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByTestId('success-bar')).toBeInTheDocument();
    });
    
    it('displays the correct title when editing a service', () => {
        const { useWizard } = require('../useWizard');
        useWizard.mockImplementation(() => ({
            handleErrors: jest.fn(),
            handleSubmit: jest.fn(),
            setActiveStep: jest.fn(),
            activeStep: 0,
            loading: false,
            success: false,
            setSuccess: jest.fn(),
            hasErrors: false,
            errorBars: [],
            serviceId: '123',
            serviceData: {
                id: '123',
                name: 'Existing Service',
                description: 'Existing Description'
            },
            resetForm: jest.fn(),
            completedSteps: [0, 1, 2, 3, 4, 5, 6, 7],
            serviceWizard: {
                id: '123',
                serviceData: {
                    id: '123',
                    name: 'Existing Service',
                    description: 'Existing Description'
                },
                selectedLocations: [],
                selectedManagers: [],
                formData: {
                    name: 'Existing Service',
                    description: 'Existing Description',
                    block_minutes: 30,
                    default_price: 50
                }
            }
        }));
        
        render(
            <Provider store={store}>
                <ThemeProvider theme={theme}>
                    <MemoryRouter initialEntries={['/services/wizard/123']}>
                        <Routes>
                            <Route path="/services/wizard/:serviceId" element={<ServiceWizard />} />
                        </Routes>
                    </MemoryRouter>
                </ThemeProvider>
            </Provider>
        );
        
        expect(screen.getByText('Edit Service')).toBeInTheDocument();
    });
});
