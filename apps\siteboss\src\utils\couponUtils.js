/**
 * Generates a random coupon code with the following rules:
 * - 6 characters long
 * - All uppercase letters and 2 numbers
 * - Avoids 0, O, I, and 1 to prevent confusion
 * @returns {string} A random coupon code
 */
export const generateCouponCode = () => {
    // Define allowed characters (uppercase letters excluding O and I)
    const allowedLetters = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
    // Define allowed numbers (excluding 0 and 1)
    const allowedNumbers = '23456789';
    
    // Generate 4 random letters
    let code = '';
    for (let i = 0; i < 4; i++) {
        const randomIndex = Math.floor(Math.random() * allowedLetters.length);
        code += allowedLetters[randomIndex];
    }
    
    // Generate 2 random numbers
    for (let i = 0; i < 2; i++) {
        const randomIndex = Math.floor(Math.random() * allowedNumbers.length);
        code += allowedNumbers[randomIndex];
    }
    
    // Shuffle the code to mix letters and numbers
    return code.split('').sort(() => 0.5 - Math.random()).join('');
};
