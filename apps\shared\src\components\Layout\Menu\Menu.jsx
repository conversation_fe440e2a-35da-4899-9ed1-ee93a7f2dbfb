import React, { useCallback, useEffect, useState } from 'react';
import { useLocation } from "react-router";
//import { useTranslation } from 'react-i18next';
import { Box, Drawer, List, useTheme } from '@mui/material';
import {
    DashboardOutlined as DashboardIcon,
    ShoppingCartOutlined as ShoppingCartIcon,
    PeopleOutlined as PeopleIcon,
    PointOfSaleOutlined as POSIcon,
    LocalActivityOutlined as ProgramIcon,
    ConfirmationNumberOutlined as EventIcon,
    DiscountOutlined as DiscountIcon,
    SmartToyOutlined as AiChatIcon,
    WebOutlined as WebIcon,
    FormatPaintOutlined as ThemeIcon,
    AssessmentOutlined as ReportsIcon,
    SpaOutlined as ServiceIcon,
    LiquorOutlined as WineIcon,
} from '@mui/icons-material';

import Logo from '../../Logo';
import Module from './Module';
import DockIcon from './DockIcon';
import Item from './Item';
import Footer from './Footer';

const menuModules = [
    { id: 1, slug: 'siteBoss', path: '/p/' },
    { id: 2, slug: 'pos', path: '/p/pos/' },
    { id: 3, slug: 'wms', path: '/p/wms/' },
    { id: 4, slug: 'cms', path: '/p/cms/' },
    { id: 5, slug: 'owl', path: '/p/owl/' },
    //{ id: 9, slug: 'siteBossAdmin', path: '/admin/' },
];

const menuItems = [
    {
        moduleId: 1,
        menu: [
            {
                id: 1,
                slug: "dashboard",
                icon: <DashboardIcon />,
                path: "/"
            },
            {
                id: 2,
                slug: "orders",
                icon: <ShoppingCartIcon />,
                path: "/orders"
            },
            {
                id: 3,
                slug: "users",
                icon: <PeopleIcon />,
                path: "/users"
            },
            {
                id: 4,
                slug: "programs",
                icon: <ProgramIcon />,
                path: "/programs"
            },
            {
                id: 5,
                slug: "events",
                icon: <EventIcon />,
                path: "/events"
            },
            {
                id: 6,
                slug: "discounts",
                icon: <DiscountIcon />,
                path: "/discounts"
            },
            {
                id: 7,
                slug: "services",
                icon: <ServiceIcon />,
                path: "/services"
            },
            {
                id: 8,
                slug: "reports",
                icon: <ReportsIcon />,
                path: "/reports"
            },
            {
                id: 999,
                slug: "chat",
                icon: <AiChatIcon />,
                path: "/chat"
            },
        ]
    },
    {
        moduleId: 2,
        menu: [
            {
                id: 1,
                slug: "dashboard",
                icon: <POSIcon />,
                path: "/"
            },
            {
                id: 999,
                slug: "chat",
                icon: <AiChatIcon />,
                path: "/chat"
            },
        ]
    },
    {
        moduleId: 4,
        menu: [
            {
                id: 1,
                slug: "dashboard",
                icon: <DashboardIcon />,
                path: "/"
            },
            {
                id: 2,
                slug: "websites",
                icon: <WebIcon />,
                path: "/websites"
            },
            {
                id: 3,
                slug: "themes",
                icon: <ThemeIcon />,
                path: "/themes"
            },
            {
                id: 999,
                slug: "builder",
                icon: <AiChatIcon />,
                path: "/builder"
            }
        ]

    },
    {
        moduleId: 5,
        menu: [
            {
                id: 1,
                slug: "dashboard",
                icon: <DashboardIcon />,
                path: "/"
            },
            {
                id: 2,
                slug: "products",
                icon: <WineIcon />,
                path: "/products"
            },
            {
                id: 3,
                slug: "merchants",
                icon: <PeopleIcon />,
                path: "/merchants"
            },
            {
                id: 4,
                slug: "orders",
                icon: <ShoppingCartIcon />,
                path: "/orders"
            },
            {
                id: 5,
                slug: "reports",
                icon: <ReportsIcon />,
                path: "/reports"
            }

        ]
    }
];

export const Menu = ({moduleId = 1, toggleDrawer, setDrawerSize, isMobile, drawerOpen, ...props}) =>{
    //const { t } = useTranslation("menu");
    const theme = useTheme();
    const location = useLocation();

    const [selectedModule, setSelectedModule] = useState(menuModules.find(m => +m.id === +moduleId) || null);
    const [drawerWidth, setDrawerWidth] = useState(theme.sizes.menuWidth);
    const [isDocked, setIsDocked] = useState(false);

    const handleClick = useCallback(menuModule => {
        console.log(menuModule);
        if (!menuModule) return;
        setSelectedModule(menuModule);
        if (menuModule.path) {
            window.location.href = window.location.origin + menuModule.path;
            //redirect(menuModule.path);
        }
    }, []);

    useEffect(() => {
        const _width = (drawerWidth === theme.sizes.menuWidth ? theme.sizes.menuDockedWidth : theme.sizes.menuWidth);
        setIsDocked(_width === theme.sizes.menuWidth);
        if (setDrawerSize) setDrawerSize(_width);
    }, [drawerWidth, theme.sizes, setDrawerSize]);

    /*useEffect(() => {
        if (moduleId) {
            setSelectedModule(menuModules.find(m => +m.id === +moduleId) || null);
        }
    }, [moduleId]);*/

    return (
        <Drawer
            menu={1}
            variant={isMobile ? "temporary" : "permanent"}
            open={isMobile ? drawerOpen : true}
            onClose={toggleDrawer}
            PaperProps={{
                sx: {
                    overflowX: "hidden",
                    boxSizing: 'border-box',
                    width: drawerWidth,
                    transition: `width ${isDocked
                                    ? theme.transitions.duration.leavingScreen
                                    : theme.transitions.duration.enteringScreen}ms
                                ${isDocked
                                    ? theme.transitions.easing.sharp
                                    : theme.transitions.easing.easeOut }`
                }
            }}
            ModalProps={{keepMounted: true}}
            sx={{width: drawerWidth, flexShrink: 0}}
        >
            {!isMobile &&
                <DockIcon drawerWidth={drawerWidth} setDrawerWidth={setDrawerWidth} isDocked={isDocked} />
            }

            <Box sx={{ display: 'flex', flexDirection: 'column', pt: isMobile ? 2 : 0, height: '100%' }}>
                <Box sx={{display: "flex", flexDirection: 'column', alignItems: isDocked ? 'center' : 'flex-start', justifyContent: (isMobile || isDocked) ? "center" : undefined, pt: isMobile ? 1 : 2, pb: 2, px: 3}}>
                    <Logo size={isDocked ? "icon" : "sm"} type={isDocked ? "icon" : undefined} solid={theme.palette.mode !== "light"} sx={{mb:2}} />
                    <Module modules={menuModules} selectedModuleId={moduleId} onModuleChange={handleClick} isDocked={isDocked} />
                </Box>
                <List variant="menu" disablePadding>
                    {menuItems.find(a => +a.moduleId === +selectedModule?.id)?.menu?.map(item => (
                        <Item key={`menu-item-${item.id}`} item={item} isDocked={isDocked} drawerWidth={drawerWidth} selectedModuleName={selectedModule?.slug} />
                    ))}
                </List>
                <Footer isDocked={isDocked} drawerWidth={drawerWidth} />
            </Box>
        </Drawer>
    );
}