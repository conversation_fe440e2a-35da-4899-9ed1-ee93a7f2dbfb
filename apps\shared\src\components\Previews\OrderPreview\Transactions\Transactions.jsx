import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import clsx from 'clsx';
import { Box, Typography, Table, TableHead, TableBody, TableRow, TableCell, useMediaQuery } from '@mui/material';
import { createCurrencyFormatter, toCamelCase, formatDate, capitalize } from '../../../../utils';

import styles from './Transactions.module.scss';

export const Transactions = ({
    data, // the data to display
    className, // additional class names to apply to the wrapper (used for different print formats)
    ...props // additional props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    if (!data || !data.transactions || data.transactions.length <= 0) return null;

    return (
        <Box className={clsx(styles.wrapper, className)} sx={{order: isMobile ? 2 : 0}}>
            {/*<Typography variant='h6'>{t('order:payments')}</Typography>*/}
            <Table aria-label="payments">
                <TableHead>
                    <TableRow>
                        <TableCell>{t('order:payment')}</TableCell>
                        <TableCell align="right">{t('order:amount')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {data.transactions.map((transaction, i) => (
                        <TableRow key={`order-transaction-${transaction.id}-${i}`} sx={{ 
                            '&:last-child td, &:last-child th': { border: 0 }, 
                            verticalAlign: 'top' 
                        }}>
                            <TableCell sx={{display:'flex', flexDirection: 'column'}}>
                                <Typography variant='subtitle2' sx={{fontWeight: 500}}>{t(`order:${toCamelCase(transaction.transaction_payment_method_name)}`)}</Typography>
                                {transaction.cc_type && transaction.cc_number && 
                                    <Typography variant='subtitle3'>{capitalize(transaction.cc_type)} {t('order:endingIn')} {transaction.cc_number}</Typography>
                                }
                                {+transaction.transaction_payment_method_id === 3 &&
                                    <>
                                        {transaction.transaction_response?.check_number && 
                                            <Typography variant='subtitle3'>#{transaction.transaction_response.check_number}</Typography>
                                        }
                                        {transaction.transaction_response?.check_name && 
                                            <Typography variant='subtitle3'>{transaction.transaction_response.check_name}</Typography>
                                        }
                                    </>
                                }
                                {+transaction.transaction_payment_method_id === 2 &&
                                    <>
                                        {+transaction.transaction_response?.amount > 0 && +transaction.transaction_response?.amount !== +transaction.amount && 
                                            <Typography variant='subtitle3'>
                                                {t('order:tendered')}: {currencyFormatter.format(transaction.transaction_response.amount)}
                                            </Typography>
                                        }
                                        {+transaction.transaction_response?.change > 0 && 
                                            <Typography variant='subtitle3'>
                                                {t('order:change')}: {currencyFormatter.format(transaction.transaction_response.change)}
                                            </Typography>
                                        }
                                    </>
                                }
                                <Typography variant='subtitle3'>{t('order:date')}: {formatDate(new Date(transaction.date), language.code)}</Typography>
                                <Typography
                                    variant="subtitle3"
                                    component="div"
                                    color={
                                        [3, 4, 5, 8, 9].filter(s => s === transaction.transaction_status_id).length > 0
                                            ? 'error'
                                            : 'success'
                                    }
                                >
                                    {t(`status:${toCamelCase(transaction.transaction_status_name)}`)}
                                </Typography>
                            </TableCell>
                            <TableCell align="right">
                                {currencyFormatter.format(transaction.amount)}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            {/*data.transactions.map((transaction, i)=>(
                <React.Fragment key={`order-transaction-${transaction.id}-${i}`}>
                    <Typography variant='subtitle2' sx={{fontWeight: 500}}>{t(`order:${toCamelCase(transaction.transaction_payment_method_name)}`)}</Typography>
                    <Typography variant='subtitle2'>{t('order:date')}: {formatDate(new Date(transaction.date), language.code)}</Typography>
                    {transaction.cc_type && transaction.cc_number && 
                        <Typography variant='subtitle2'>{transaction.cc_type} {t('order:endingIn')} {transaction.cc_number}</Typography>
                    }
                    {+transaction.transaction_payment_method_id === 3 &&
                        <>
                            {transaction.transaction_response?.check_number && 
                                <Typography variant='subtitle2'>#{transaction.transaction_response.check_number}</Typography>
                            }
                            {transaction.transaction_response?.check_name && 
                                <Typography variant='subtitle2'>{transaction.transaction_response.check_name}</Typography>
                            }
                        </>
                    }
                    <Typography variant='subtitle2'>{t('order:amount')}: {currencyFormatter.format(transaction.amount)}</Typography>
                    {+transaction.transaction_payment_method_id === 2 &&
                        <>
                            {transaction.transaction_response?.amount && 
                                <Typography variant='subtitle3'>
                                    {t('order:tendered')}: {currencyFormatter.format(transaction.transaction_response.amount)}
                                </Typography>
                            }
                            {transaction.transaction_response?.change && 
                                <Typography variant='subtitle3'>
                                    {t('order:change')}: {currencyFormatter.format(transaction.transaction_response.change)}
                                </Typography>
                            }
                        </>
                    }
                    <Typography variant='subtitle2' component='div' color={[3, 4, 5, 8, 9].filter(s => s === transaction.transaction_status_id).length > 0 ? 'error' : 'success'}>
                        {t('order:status')}: {t(`status:${toCamelCase(transaction.transaction_status_name)}`)}
                    </Typography>
                    {i < data.transactions.length - 1 && <Divider sx={{my: 2}} />}
                </React.Fragment>
            ))*/}
        </Box>
   );
}