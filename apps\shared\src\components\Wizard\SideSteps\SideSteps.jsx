import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Paper, Stepper, Step, StepLabel, StepContent, StepButton, Button, Typography, useMediaQuery } from '@mui/material';
import { useFormContext } from '../../useForm/FormContext';

import Mobile from './Mobile';

export const SideSteps = ({ loading, hasErrors, stepList, activeStep, onStepChange, visitedSteps = [], isEditing = false, ...props }) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('lg'));
    const { submitForm } = useFormContext();

    const handleNext = useCallback(() => {
        if (activeStep === stepList.length - 1) return;
        if (onStepChange) onStepChange(prev => prev + 1);
    }, [activeStep, stepList, onStepChange]);

    const handleBack = useCallback(() => {
        if (activeStep === 0) return;
        if (onStepChange) onStepChange(prev => prev - 1);
    }, [activeStep, onStepChange]);

    const handleReset = useCallback(() => {
        if (onStepChange) onStepChange(0);
    }, [onStepChange]);

    const handleStep = useCallback(i => () => {
        if (i < 0 || i >= stepList.length) return;

        // If trying to navigate forward (to a higher step number)
        if (i > activeStep) {
            // Don't allow forward navigation if there are validation errors
            if (hasErrors) {
                return;
            }

            // Only allow if editing or the step has been visited (meaning previous steps are complete)
            const canNavigateForward = isEditing || visitedSteps.includes(i) || i === activeStep + 1;

            // If we can't navigate forward, don't do anything
            if (!canNavigateForward) return;
        }

        // Always allow navigation to current or previous steps
        if (onStepChange) {
            onStepChange(i);
        }
    }, [onStepChange, stepList, visitedSteps, isEditing, activeStep, hasErrors]);

    if (!stepList || stepList?.length === 0) return null;

    if (isMobile) {
        return;
    }


    return (

        <Box sx={{ maxWidth: theme => theme.sizes.menuWidth, position: 'sticky', top: theme => theme.sizes.headerHeight, zIndex: theme => theme.zIndex.appBar - 1 }} {...props}>
            <Stepper activeStep={activeStep} orientation="vertical">
                {stepList.map((step, i) => (
                    <Step key={step.label}>
                        <StepButton
                            color="inherit"
                            onClick={handleStep(i)}
                            disabled={(!isEditing && !visitedSteps.includes(i) && i !== activeStep && i !== activeStep + 1) || (i > activeStep && hasErrors)}
                        >
                            <StepLabel
                                optional={step.short_description ? <Typography variant="caption">{t(step.short_description)}</Typography> : null}
                                slotProps={{label: { sx: theme => theme.typography.subtitle2 }}}
                            >
                                {step.slug ? t(step.slug) : step.label}
                            </StepLabel>
                        </StepButton>
                        <StepContent>
                            <Typography variant="subtitle2">{t(step.description)}</Typography>
                        </StepContent>
                    </Step>
                ))}
            </Stepper>
            {activeStep === stepList.length && (
                <Paper square elevation={0} sx={{ p: 3 }}>
                    <Typography variant="subtitle2">{t('success:saved')}</Typography>
                    <Button onClick={handleReset} sx={{ mt: 1, mr: 1 }}>
                        {t('general:save')}
                    </Button>
                </Paper>
            )}
        </Box>
    );
}