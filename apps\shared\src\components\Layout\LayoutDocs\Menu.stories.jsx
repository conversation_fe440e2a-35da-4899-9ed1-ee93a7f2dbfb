import { Menu } from '../Menu/Menu';


export default {
    title: 'Shared/Layout/Menu/Menu',
    tags:['autodocs'],
    component: Menu,
    argTypes: {
        moduleId: {
            description: "The module id that's being used (this is to load the right menu items)",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 1 },
                detail: "The module ID to load the appropriate menu items."
            }
        },
        toggleDrawer: {
            description: "Function to toggle the drawer's open state",
            action: 'toggleDrawer',
            table: {
                type: { summary: "function" },
                defaultValue: { summary: "undefined" },
                detail: "A function to handle the toggling of the drawer's open state."
            }
        },
        setDrawerSize: {
            description: "Function to set the size of the drawer",
            action: 'setDrawerSize',
            table: {
                type: { summary: "function" },
                defaultValue: { summary: "undefined" },
                detail: "A function to handle setting the size of the drawer."
            }
        },
        isMobile: {
            description: "Flag to indicate if the device is mobile",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag to indicate if the current device is mobile."
            }
        },
        drawerOpen: {
            description: "Flag to indicate if the drawer is open",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag to control the open state of the drawer."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        moduleId: 1,
        toggleDrawer: () => {},
        setDrawerSize: () => {},
        isMobile: false,
        drawerOpen: false,
    }
};

// Following stories to illustrate each significant prop
export const MobileView = {
    args: {
        moduleId: 1,
        toggleDrawer: () => {},
        setDrawerSize: () => {},
        isMobile: true,
        drawerOpen: true,
    }
};

export const CustomModule = {
    args: {
        moduleId: 2,
        toggleDrawer: () => {},
        setDrawerSize: () => {},
        isMobile: false,
        drawerOpen: false,
    }
};

export const DrawerOpen = {
    args: {
        moduleId: 1,
        toggleDrawer: () => {},
        setDrawerSize: () => {},
        isMobile: false,
        drawerOpen: true,
    }
};
