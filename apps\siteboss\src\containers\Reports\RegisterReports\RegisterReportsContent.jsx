import React, { useState, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { enUS, es } from 'date-fns/locale';
import {
    Grid,
    Card,
    CardContent,
    Typography,
    Box,
    Paper,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Button,
    useMediaQuery,
    useTheme,
    Tab
} from '@mui/material';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
    FilterAltOutlined as FilterIcon,
    DownloadOutlined as DownloadIcon,
    PrintOutlined as PrintIcon,
    ReceiptOutlined as DailySalesIcon,
    AccountBalanceOutlined as SettlementsIcon,
    MonetizationOnOutlined as CashFlowIcon,
    ReceiptLongOutlined as TransactionsIcon,
    AttachMoneyOutlined as TotalSalesIcon,
    ShoppingCartOutlined as TransactionsCountIcon,
    BarChartOutlined as AverageSaleIcon
} from '@mui/icons-material';
import { LineChart, DataTable, SimpleBar, WithScrollEffect, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';

// Mock data for demonstration
const mockChartData = {
    data: [
        {
            id: "Cash Sales",
            data: [
                { x: "2023-06-01", y: 1200 },
                { x: "2023-06-02", y: 1500 },
                { x: "2023-06-03", y: 1100 },
                { x: "2023-06-04", y: 1800 },
                { x: "2023-06-05", y: 2100 },
                { x: "2023-06-06", y: 1900 },
                { x: "2023-06-07", y: 2300 }
            ]
        },
        {
            id: "Card Sales",
            data: [
                { x: "2023-06-01", y: 2200 },
                { x: "2023-06-02", y: 2500 },
                { x: "2023-06-03", y: 2100 },
                { x: "2023-06-04", y: 2800 },
                { x: "2023-06-05", y: 3100 },
                { x: "2023-06-06", y: 2900 },
                { x: "2023-06-07", y: 3300 }
            ]
        }
    ]
};

const mockTableData = {
    columns: [
        { field: 'id', headerName: 'ID', width: 70 },
        { field: 'date', headerName: 'Date', width: 120 },
        { field: 'register', headerName: 'Register', width: 150 },
        { field: 'cashier', headerName: 'Cashier', width: 150 },
        { field: 'transactions', headerName: 'Transactions', width: 120, type: 'number' },
        { field: 'cash', headerName: 'Cash', width: 120, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
        { field: 'card', headerName: 'Card', width: 120, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
        { field: 'other', headerName: 'Other', width: 120, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
        { field: 'total', headerName: 'Total', width: 120, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
    ],
    rows: [
        { id: 1, date: '06/01/2023', register: 'Main Register', cashier: 'John Doe', transactions: 45, cash: 1200, card: 2200, other: 300, total: 3700 },
        { id: 2, date: '06/02/2023', register: 'Main Register', cashier: 'Jane Smith', transactions: 52, cash: 1500, card: 2500, other: 400, total: 4400 },
        { id: 3, date: '06/03/2023', register: 'Main Register', cashier: 'John Doe', transactions: 38, cash: 1100, card: 2100, other: 200, total: 3400 },
        { id: 4, date: '06/04/2023', register: 'Main Register', cashier: 'Jane Smith', transactions: 61, cash: 1800, card: 2800, other: 500, total: 5100 },
        { id: 5, date: '06/05/2023', register: 'Main Register', cashier: 'John Doe', transactions: 72, cash: 2100, card: 3100, other: 600, total: 5800 },
        { id: 6, date: '06/06/2023', register: 'Main Register', cashier: 'Jane Smith', transactions: 65, cash: 1900, card: 2900, other: 400, total: 5200 },
        { id: 7, date: '06/07/2023', register: 'Main Register', cashier: 'John Doe', transactions: 78, cash: 2300, card: 3300, other: 700, total: 6300 },
    ]
};

const RegisterReportsContent = () => {
    const { t, isMobile } = useOutletContext();
    const theme = useTheme();
    const language = useSelector(state => state.language);

    // State for tabs and filters
    const [tabValue, setTabValue] = useState('dailySales');
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [register, setRegister] = useState('all');
    const [cashier, setCashier] = useState('all');

    // Define tabs for register reports
    const reportTabs = useMemo(() => [
        {
            id: 'dailySales',
            name: t('reports:register.dailySales'),
            icon: <DailySalesIcon />
        },
        {
            id: 'settlements',
            name: t('reports:register.settlements'),
            icon: <SettlementsIcon />
        },
        {
            id: 'cashFlow',
            name: t('reports:register.cashFlow'),
            icon: <CashFlowIcon />
        },
        {
            id: 'transactions',
            name: t('reports:register.transactions'),
            icon: <TransactionsIcon />
        }
    ], [t]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleFilterApply = () => {
        // In a real implementation, this would fetch filtered data
        console.log('Applying filters:', { startDate, endDate, register, cashier });
    };

    const handleExport = () => {
        // In a real implementation, this would export the data
        console.log('Exporting data');
    };

    const handlePrint = () => {
        // In a real implementation, this would print the report
        console.log('Printing report');
    };

    // Filter component - reused across tabs
    const FilterPanel = () => (
        <Paper sx={{ p: 2, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
                {t('reports:filters')}
            </Typography>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={6} lg={3}>
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                        <DatePicker
                            label={t('reports:startDate')}
                            value={startDate}
                            onChange={(newValue) => setStartDate(newValue)}
                            slotProps={{ textField: { fullWidth: true } }}
                        />
                    </LocalizationProvider>
                </Grid>
                <Grid item xs={12} sm={6} md={6} lg={3}>
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                        <DatePicker
                            label={t('reports:endDate')}
                            value={endDate}
                            onChange={(newValue) => setEndDate(newValue)}
                            slotProps={{ textField: { fullWidth: true } }}
                        />
                    </LocalizationProvider>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2}>
                    <FormControl fullWidth>
                        <InputLabel>{t('reports:register.name')}</InputLabel>
                        <Select
                            value={register}
                            label={t('reports:register.name')}
                            onChange={(e) => setRegister(e.target.value)}
                        >
                            <MenuItem value="all">{t('reports:all')}</MenuItem>
                            <MenuItem value="main">Main Register</MenuItem>
                            <MenuItem value="secondary">Secondary Register</MenuItem>
                        </Select>
                    </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2}>
                    <FormControl fullWidth>
                        <InputLabel>{t('reports:cashier')}</InputLabel>
                        <Select
                            value={cashier}
                            label={t('reports:cashier')}
                            onChange={(e) => setCashier(e.target.value)}
                        >
                            <MenuItem value="all">{t('reports:all')}</MenuItem>
                            <MenuItem value="john">John Doe</MenuItem>
                            <MenuItem value="jane">Jane Smith</MenuItem>
                        </Select>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                    <Button
                        variant="contained"
                        startIcon={<FilterIcon />}
                        onClick={handleFilterApply}
                        fullWidth
                    >
                        {t('reports:apply')}
                    </Button>
                </Grid>
            </Grid>
        </Paper>
    );

    // Summary stats component - reused across tabs
    const SummaryStats = () => (
        <MetricCardGroup>
            <MetricCard
                title={t('reports:totalSales')}
                value="$34,900"
                icon={<TotalSalesIcon />}
                color="blue"
                trend="+15% from last month"
                width={4}
            />
            <MetricCard
                title={t('reports:transactions')}
                value="411"
                icon={<TransactionsCountIcon />}
                color="orange"
                trend="+8% from last month"
                width={4}
            />
            <MetricCard
                title={t('reports:averageSale')}
                value="$84.91"
                icon={<AverageSaleIcon />}
                color="green"
                trend="+5% from last month"
                width={4}
            />
        </MetricCardGroup>
    );

    // Chart component
    const SalesChart = () => (
        <Card sx={{ mb: 4 }}>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        {t('reports:salesByPaymentMethod')}
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 300, minWidth: { xs: '600px', md: 'auto' }, overflow: 'auto' }}>
                    <LineChart
                        data={mockChartData}
                        curve="natural"
                        margin={{ top: 20, right: 20, bottom: 60, left: 80 }}
                        responsive={true}
                        xScale={{
                            type: 'time',
                            format: '%Y-%m-%d',
                            useUTC: false,
                            precision: 'day',
                        }}
                        xFormat="time:%m/%d/%Y"
                        yFormat=" >-$.2f"
                        axisBottom={{
                            format: '%b %d',
                            tickValues: 'every 1 day',
                            legend: 'Date',
                            legendOffset: 36,
                            legendPosition: 'middle'
                        }}
                        axisLeft={{
                            legend: 'Amount',
                            legendOffset: -60,
                            legendPosition: 'middle'
                        }}
                        enablePoints={true}
                        pointSize={8}
                        pointBorderWidth={1}
                        pointBorderColor={{ from: 'color', modifiers: [['darker', 0.3]] }}
                        useMesh={true}
                        enableSlices="x"
                        legends={[
                            {
                                anchor: 'bottom-right',
                                direction: 'row',
                                justify: false,
                                translateX: 0,
                                translateY: 50,
                                itemsSpacing: 0,
                                itemDirection: 'left-to-right',
                                itemWidth: 80,
                                itemHeight: 20,
                                symbolSize: 12,
                                symbolShape: 'circle',
                            }
                        ]}
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Data table component
    const SalesTable = () => (
        <Card>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        {t('reports:dailyRegisterSummary')}
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 400, overflow: 'auto' }}>
                    <DataTable
                        sx={{ minWidth: { xs: '800px', md: 'auto' } }}
                        columns={mockTableData.columns}
                        rows={mockTableData.rows}
                        pageSize={5}
                        rowsPerPageOptions={[5, 10, 25]}
                        checkboxSelection={false}
                        disableRowSelectionOnClick
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Daily Sales Tab Content
    const DailySalesTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <SalesChart />
            <SalesTable />
        </>
    );

    // Settlements Tab Content
    const SettlementsTab = () => (
        <>
            <FilterPanel />
            <Typography variant="h5" gutterBottom>
                Settlements Report
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="body1">
                    This tab will display settlement reports including:
                </Typography>
                <ul>
                    <li>Daily Settlements</li>
                    <li>Settlement by Register</li>
                    <li>Settlement by Payment Method</li>
                </ul>
            </Paper>
        </>
    );

    // Cash Flow Tab Content
    const CashFlowTab = () => (
        <>
            <FilterPanel />
            <Typography variant="h5" gutterBottom>
                Cash Flow Report
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="body1">
                    This tab will display cash flow reports including:
                </Typography>
                <ul>
                    <li>Cash In/Out</li>
                    <li>Register Balances</li>
                    <li>Cash Discrepancies</li>
                </ul>
            </Paper>
        </>
    );

    // Transactions Tab Content
    const TransactionsTab = () => (
        <>
            <FilterPanel />
            <Typography variant="h5" gutterBottom>
                Transactions Report
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="body1">
                    This tab will display transaction reports including:
                </Typography>
                <ul>
                    <li>Transaction Details</li>
                    <li>Transaction by Type</li>
                    <li>Voided Transactions</li>
                </ul>
            </Paper>
        </>
    );

    return (
        <>
            <TabContext value={tabValue}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                    <TabList
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        aria-label="register report types"
                        sx={{
                            '& .MuiTab-root': {
                                display: 'flex',
                                flexDirection: isMobile ? 'column' : 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                minHeight: isMobile ? '72px' : '48px',
                            }
                        }}
                    >
                        {reportTabs.map((tab) => (
                            <Tab
                                key={tab.id}
                                label={tab.name}
                                value={tab.id}
                                icon={tab.icon}
                                iconPosition={isMobile ? "top" : "start"}
                            />
                        ))}
                    </TabList>
                </Box>

                <TabPanel value="dailySales" sx={{ p: 0 }}>
                    <DailySalesTab />
                </TabPanel>

                <TabPanel value="settlements" sx={{ p: 0 }}>
                    <SettlementsTab />
                </TabPanel>

                <TabPanel value="cashFlow" sx={{ p: 0 }}>
                    <CashFlowTab />
                </TabPanel>

                <TabPanel value="transactions" sx={{ p: 0 }}>
                    <TransactionsTab />
                </TabPanel>
            </TabContext>
        </>
    );
};

export default RegisterReportsContent;
