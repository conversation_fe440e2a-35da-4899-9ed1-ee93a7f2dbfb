import { useState, useCallback, useMemo, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';
import { toCamelCase } from '@siteboss-frontend/shared/utils';

import { tenantConfig } from "../../../../components/KeycloakProvider/tenantConfig";

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

const fieldsByTab = {
    config: [
        {name: 'smtp_server', type: 'text', label: 'emailTemplates:smtpServer', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_port', type: 'number', label: 'emailTemplates:smtpPort', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_username', type: 'text', label: 'emailTemplates:smtpUsername', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'smtp_password', type: 'password', label: 'emailTemplates:smtpPassword', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'from_email_address', type: 'email', label: 'emailTemplates:fromEmailAddress', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'from_name', type: 'text', label: 'emailTemplates:fromName', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'cc_recipient', type: 'email', label: 'emailTemplates:ccRecipient', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'bcc_recipient', type: 'email', label: 'emailTemplates:bccRecipient', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
    ],
    email: {
        templates: [
            {name: 'templates', value: [], component: "EmailTemplatesList"}
        ]
    }
};

export const useEmailTemplates = ({ merchantId, onMainFormChange }) => {
    const { t } = useOutletContext();
    const orderTypes = useSelector(state => state.fixedData.orderTypes);

    const templateFields = useMemo(() => [
        {name: 'template_name', type: 'text', label: 'emailTemplates:templateName', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'order_type', label: 'emailTemplates:orderType', required: true, value: '', component: "Select", options: orderTypes?.map(a => ({
            id: a.id, slug: `merchant:orderTypes.${toCamelCase(a.name)}`
        })), margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'subject', type: 'text', label: 'emailTemplates:emailSubject', required: true, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'bcc', type: 'email', label: 'emailTemplates:bcc', required: false, value: '', component: "TextField", margin: "normal", rowSize: {xs: 12, md: 6}},
        {name: 'template', type: 'text', label: 'emailTemplates:template', required: true, value: '', component: "MarkDown", margin: "normal", multiline: true, rows: 8, rowSize: {xs: 12}},
    ], [orderTypes]);

    const apiParams = useMemo(() => [
        {params: {endpoint: `/clients/${merchantId}/email-templates`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/email-templates`, method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/email-templates`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${merchantId}/email-templates`, method: 'DELETE', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [merchantId]);

    const { fetchData, data, errors, ErrorBar, loading } = useApi(apiParams[0]);
    const { fetchData: createData, errors: createErrors, ErrorBar: CreateErrorBar, loading: createLoading } = useApi(apiParams[1]);
    const { fetchData: updateData, errors: updateErrors, ErrorBar: UpdateErrorBar, loading: updateLoading } = useApi(apiParams[2]);
    const { fetchData: deleteData, errors: deleteErrors, ErrorBar: DeleteErrorBar, loading: deleteLoading } = useApi(apiParams[3]);

    const [modalOpen, setModalOpen] = useState(false);
    const [formData, setFormData] = useState({});
    const [selected, setSelected] = useState([]);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    
    const handleChange = useCallback((e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    }, []);

    const handleToggleModal = useCallback(open => _ => setModalOpen(open), []);

    const handleLoadItems = useCallback(async () => {
        if (!merchantId) return [];
        try {
            const res = await fetchData({page: page, per_page: pageSize});
            if (res?.data) return res.data;
            return [];
        } catch(e){
            return [];
        }
    }, [fetchData, page, pageSize, merchantId]);

    const handleEditItem = useCallback(() => {
        if (!selected?.length) return;

        const selectedTemplate = selected[0];
        const _data = {
            id: selectedTemplate?.id,
            template_name: selectedTemplate?.template_name,
            template_type: selectedTemplate?.template_type,
            subject: selectedTemplate?.subject,
            email_body: selectedTemplate?.email_body,
            is_active: selectedTemplate?.is_active ?? true,
        }
        
        setFormData(_data);
        setModalOpen(true);
    }, [selected]);

    const handleDeleteItem = useCallback(async row => {
        if (!row?.length) return;

        try{
            for (const item of row) {
                await deleteData({endpoint: `/clients/${merchantId}/email-templates/${item?.id}`});
            }
        } catch(e){
            return false;
        } finally {
            fetchData();
        }
    }, [deleteData, fetchData, merchantId]);



    const handleSaveItem = useCallback(async () => {
        if (!formData) return;

        if (merchantId){
            // when a merchantId is defined, save directly
            const apiCall = formData.id ? updateData : createData;
            try {
                const res = await apiCall(formData);
                if (res?.data) {
                    fetchData();
                    setModalOpen(false);
                }
                return true;
            } catch(e){
                return false;
            }
        } else {
            // if its not defined, add it to the main form data
            onMainFormChange({
                target: {
                    name: 'email_templates',
                    value: {...formData}
                }
            }, true);
            setModalOpen(false);
            return true;
        }
    }, [formData, updateData, createData, fetchData, merchantId, onMainFormChange]);

    const handleRowSelection = useCallback(model => {
        const _model = [];
        data?.items?.forEach(row => {
            if (model.includes(row.id)) _model.push(row);
        })
        setSelected(_model);
    }, [data]);

    const handleResetForm = useCallback(() => {
        setFormData({});
    }, []);

    // DataTable columns
    const columns = useMemo(() => [
        {
            field: 'template_name',
            headerName: t('merchant:templateName'),
            flex: 1,
            minWidth: 150
        },
        {
            field: 'template_type',
            headerName: t('merchant:templateType'),
            flex: 1,
            minWidth: 150,
            valueGetter: (_, row) => {
                const type = row.template_type;
                return type ? t(`merchant:emailTemplateTypes.${type}`) : '-';
            }
        },
        {
            field: 'subject',
            headerName: t('merchant:emailSubject'),
            flex: 2,
            minWidth: 200
        },
        {
            field: 'is_active',
            headerName: t('merchant:status'),
            flex: 0.5,
            minWidth: 100,
            valueGetter: (_, row) => {
                return row.is_active ? t('general:active') : t('general:inactive');
            }
        }
    ], [t]);
    
    const totalPages = Math.ceil((data?.items?.length || 0) / pageSize);

    useEffect(() => {
        handleLoadItems();
    }, [handleLoadItems]);

    return {
        data: data?.items || [],
        selected,
        setSelected,
        handleRowSelection,
        handleLoadItems,
        handleEditItem,
        handleDeleteItem,
        handleSaveItem,
        handleResetForm,
        handleToggleModal,
        handleChange,
        modalOpen,
        formData,
        setFormData,
        page,
        setPage,
        pageSize,
        setPageSize,
        totalPages,
        columns,
        loading: loading || createLoading || updateLoading || deleteLoading,
        errorBars: [ErrorBar, CreateErrorBar, UpdateErrorBar, DeleteErrorBar],
        errors: errors || createErrors || updateErrors || deleteErrors,
        fieldDefinitions: templateFields || [],
        fieldsByTab,
    };
};
