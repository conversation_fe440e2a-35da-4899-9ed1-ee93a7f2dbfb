server {

  listen 80;

  add_header Cache-Control no-cache;

  root /usr/share/nginx/html;

  location /p/ {
    alias /usr/share/nginx/html/siteboss/;
    try_files $uri $uri/ /p/siteboss/index.html;
  }

  location /p/cms/ {
    alias /usr/share/nginx/html/cms/;
    try_files $uri $uri/ /p/cms/index.html;
  }

  location /p/pos/ {
    alias /usr/share/nginx/html/pos/;
    try_files $uri $uri/ /p/pos/index.html;
  }

  location  / {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
  }

  location = /50x.html {
    root   /usr/share/nginx/html;
  }

}