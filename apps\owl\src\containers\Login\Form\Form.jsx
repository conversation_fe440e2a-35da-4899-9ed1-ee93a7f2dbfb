import React, { useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { Box, Button, TextField, FormControlLabel, Switch, useMediaQuery } from '@mui/material';

import { setUserInfo, setUserToken } from '@siteboss-frontend/shared/store';
import { useApi } from '@siteboss-frontend/shared';
import { FormItem } from '@siteboss-frontend/shared/components';

const apiParams = {params: {endpoint: "/auth/login", method: "POST"}};

export const Form = ({onBeforeLogin, onAfterLogin,...props}) => {
    const dispatch = useDispatch();

    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const ref = useRef(null);

    const [errors, setErrors] = useState({});
    const { fetchData, loading, ErrorBar } = useApi(apiParams);
    
    const handleSubmit = useCallback(async e => {
        e.preventDefault();
        setErrors({});
        if (ref.current) {
            const username = ref.current.querySelector('input[name="username"]').value;
            const password = ref.current.querySelector('input[name="password"]').value;

            if (username && password) {
                const result = await fetchData({username, password, tenant: "wineco"});
                if (result?.data?.token && result?.data?.profile) {
                    dispatch(setUserToken(result.data.token))
                    dispatch(setUserInfo(result.data));
                    localStorage.setItem('user',JSON.stringify({...result?.data, date: new Date()}));
                    if (onAfterLogin) onAfterLogin(result.data);
                }
            } else {
                if (!username) setErrors(prev => ({...prev, username: t("error:required")}));
                if (!password) setErrors(prev => ({...prev, password: t("error:required")}));
            }
        } else {
            setErrors({username: t("error:default")});
        }
    }, [fetchData, t, dispatch]);

    return (
        <Box ref={ref}>
            <TextField
                label={t("login:username")}
                name="username"
                variant="outlined"
                fullWidth
                margin="normal"
                sx={{mt: 0}}
                error={!!errors.username}
                helperText={errors.username || undefined}
                onKeyDown={e => {
                    if (e.key === 'Enter') {
                        handleSubmit(e);
                    }
                }}
                disabled={loading}
                autoFocus
            />
            <FormItem
                component="PasswordField"
                label={t("login:password")}
                name="password"
                variant="outlined"
                fullWidth
                margin="normal"
                type="password"
                error={!!errors.password}
                disabled={loading}
                helperText={errors.password || undefined}
                onKeyDown={e => {
                    if (e.key === 'Enter') {
                        handleSubmit(e);
                    }
                }}
            />
            <Box sx={{display: "flex", flexDirection: "column"}}>
                <Box sx={{display: "flex", flexDirection: isMobile ? "row" : "row", justifyContent: "space-between", m: 1}} order={isMobile ? 1 : 0}>
                    <FormControlLabel control={<Switch defaultChecked size="small" disabled={loading} />} label={t("login:rememberMe")} slotProps={{typography: {variant: "body2"}}} />
                    <Button color="primary" variant="text" disabled={loading}>{t("login:forgotPassword")}</Button>
                </Box>
                <Button variant="contained" color="primary" fullWidth loading={loading} disabled={loading} order={isMobile ? 0 : 1} sx={{mt:1}} onClick={handleSubmit}>
                    {t("login:login")}
                </Button>
            </Box>
            <ErrorBar />
        </Box>
    );
}