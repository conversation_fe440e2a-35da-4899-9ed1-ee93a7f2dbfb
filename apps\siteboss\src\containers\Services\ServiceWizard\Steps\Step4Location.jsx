import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, Box, Chip, Button, Paper, TextField, Autocomplete, CircularProgress } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { useApi } from '@siteboss-frontend/shared';
import { setSelectedLocations, addLocation, removeLocation } from '../../../../store/reducers/serviceWizardSlice';
import { step4 } from './stepList';

const Step4Location = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const selectedLocations = useSelector(state => state.serviceWizard.selectedLocations);
    const [searchValue, setSearchValue] = useState('');

    // API call to get locations
    const { data: locationsData, loading: locationsLoading, error: locationsError, call: fetchLocations } =
        useApi({
            fetchOnMount: false, // Only fetch when search value changes
            params: {
                endpoint: '/location',
                method: 'POST',
                data: {
                    search: searchValue,
                    max_records: 10
                }
            }
        });

    // Fetch locations when search value changes
    useEffect(() => {
        if (searchValue) {
            fetchLocations();
        }
    }, [searchValue, fetchLocations]);

    // Handle adding a location
    const handleAddLocation = useCallback((location) => {
        dispatch(addLocation(location));
        setSearchValue('');
    }, [dispatch]);

    // Handle removing a location
    const handleRemoveLocation = useCallback((locationId) => {
        dispatch(removeLocation(locationId));
    }, [dispatch]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Service Locations
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                    Select the locations where this service is offered
                </Typography>

                <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        Search and add locations
                    </Typography>
                    <Autocomplete
                        options={locationsData?.locations || []}
                        getOptionLabel={(option) => option.name || ''}
                        loading={locationsLoading}
                        inputValue={searchValue}
                        onInputChange={(event, newValue) => setSearchValue(newValue)}
                        onChange={(event, newValue) => {
                            if (newValue) {
                                handleAddLocation(newValue);
                            }
                        }}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label="Search locations"
                                fullWidth
                                variant="outlined"
                                InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                        <>
                                            {locationsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                            {params.InputProps.endAdornment}
                                        </>
                                    ),
                                }}
                            />
                        )}
                        renderOption={(props, option) => (
                            <li {...props}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <LocationOnIcon sx={{ color: 'text.secondary', mr: 1 }} />
                                    <Box>
                                        <Typography variant="body1">{option.name}</Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {option.address}
                                        </Typography>
                                    </Box>
                                </Box>
                            </li>
                        )}
                    />
                </Paper>

                <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        Selected Locations
                    </Typography>
                    {selectedLocations && selectedLocations.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                            {selectedLocations.map((location) => (
                                <Chip
                                    key={location.id}
                                    label={location.name}
                                    onDelete={() => handleRemoveLocation(location.id)}
                                    color="primary"
                                    variant="outlined"
                                    icon={<LocationOnIcon />}
                                />
                            ))}
                        </Box>
                    ) : (
                        <Typography variant="body2" color="text.secondary">
                            No locations selected. Please search and add locations above.
                        </Typography>
                    )}
                </Paper>
            </Grid>
        </Grid>
    );
};

export default Step4Location;
