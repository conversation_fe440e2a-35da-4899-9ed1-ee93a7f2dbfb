import React from 'react';
import { UrlChips } from './UrlChips'; 
import { action } from 'storybook/actions';
import { fn } from 'storybook/test';

// Storybook default export
export default {
  title: 'CMS/Components/Url Chips - GPT',
  component: UrlChips,
  tags: ['autodocs'],
  argTypes: {
    chips: {
      description: 'Array of chip data or strings to display.',
      control: 'object',
      action: fn(), // Mocked function
      type: { required: true },
      table: {
        type: {
          summary: 'array',
        },
        defaultValue: {
          summary: '[]',
        },
        detail: `
          Chips can be provided as an array of objects or strings. 
          Each object can include an id, label, slug, color, and additional chip properties.
        `,
      },
    },
    onClick: {
      description: 'Callback function triggered when a chip is clicked.',
      control: 'action',
      action: fn(), // Mocked function
      table: {
        type: {
          summary: 'function',
        },
        defaultValue: {
          summary: '() => {}',
        },
        detail: `
          This function is called with the chip data when a chip is clicked.
        `,
      },
    },
    onDelete: {
      description: 'Callback function triggered when a chip is deleted.',
      control: 'action',
      action: fn(), // Mocked function
      table: {
        type: {
          summary: 'function',
        },
        defaultValue: {
          summary: '() => {}',
        },
        detail: `
          This function is called with the chip data when a chip is deleted.
        `,
      },
    },
  },
};

// First story with required props only
export const Playground = {
  args: {
    chips: [
      { id: '1', label: 'Chip 1', slug: 'chip1', color: 'primary' },
      { id: '2', label: 'Chip 2', slug: 'chip2', color: 'secondary' },
      'Static Chip 3'
    ],
  },
};

// Story to illustrate various chip configurations
export const Variants = {
  render: () => (
    <div>
      <h3>Default Chips</h3>
      <UrlChips 
        chips={[
          { id: '1', label: 'Chip 1' },
          { id: '2', label: 'Chip 2', color: 'primary' }
        ]}
        onClick={action('Chip Clicked')}
        onDelete={action('Chip Deleted')}
      />
      <h3>Chips with Custom Colors</h3>
      <UrlChips 
        chips={[
          { id: '1', label: 'Red Chip', color: 'error' },
          { id: '2', label: 'Green Chip', color: 'success' }
        ]}
        onClick={action('Chip Clicked')}
        onDelete={action('Chip Deleted')}
      />
      <h3>Chips with Different Props</h3>
      <UrlChips 
        chips={[
          { id: '1', label: 'Clickable Chip', chipProps: { clickable: true } },
          { id: '2', label: 'Deletable Chip', chipProps: { deletable: true } }
        ]}
        onClick={action('Chip Clicked')}
        onDelete={action('Chip Deleted')}
      />
    </div>
  ),
};

// Story to demonstrate chips with custom labels
export const WithCustomLabels = {
  args: {
    chips: [
      { id: '1', label: 'Custom Label 1' },
      { id: '2', label: 'Custom Label 2', color: 'info' }
    ],
  },
};

// Additional whimsical story
export const FunChips = {
  args: {
    chips: [
      { id: '1', label: '🚀 Blast Off!' },
      { id: '2', label: '💡 Bright Idea', color: 'warning' }
    ],
  },
};