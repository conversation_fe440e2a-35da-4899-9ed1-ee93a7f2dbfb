import React from 'react';
import { Box, Paper, Stack, IconButton } from '@mui/material';
import { KeyboardArrowLeft as LeftIcon, KeyboardArrowRight as RightIcon, KeyboardArrowUpOutlined as UpIcon, KeyboardArrowDownOutlined as DownIcon } from '@mui/icons-material';

import { useImageCarousel } from './useImageCarousel';
import { theme } from '../../../theme';

const Button = ({position = "left", disabled, ...props}) => {
    const top = (position === "left" || position === "right") ? '50%' : undefined;
    const left = (position === "left" || position === "right") ? undefined : '50%';
    const transform = (position === "left" || position === "right") ? 'translateY(-50%)' : 'translateX(-50%)';

    return (
        <IconButton {...props} 
            disabled={disabled}
            sx={{
                position: 'absolute', 
                top, 
                left,                
                transform,
                [position]: 5, 
                bgcolor: 'background.paper',
        }}>
            {position === "left" && <LeftIcon fontSize="inherit" /> }
            {position === "right" && <RightIcon fontSize="inherit" /> }
            {position === "top" && <UpIcon fontSize="inherit" /> }
            {position === "bottom" && <DownIcon fontSize="inherit" /> }
        </IconButton>
    );
}

/*
Params:
    - images: an array with the following structure:
    {
        preview_url: "https://example.com/image.jpg",
        description: "Image description",
    }
    - imageSize: an object with the following structure: {width: "100%", height: 200}
    - objectFit: a string with the value "cover", "contain", "fill", "scale-down", "none", "initial", or "inherit". This will determine how the image will be displayed
    - objectPosition: a string with the value "center", "top", "bottom", "left", "right", "top left", "top right", "bottom left", "bottom right". This will determine the position of the image
    - aspectRatio: a string with the format "width:height". For square images use "1:1". For better looking images use a common aspect ratio like "16:9" or "4:3".
    - orientation: a string with the value "horizontal" or "vertical". This will determine the direction of the carousel, and the buttons to show
    - animation: a string with the value "slide" or "fade". This will determine the animation to use when changing images
    - timeout: the duration of the animation in milliseconds
    - disabled: a boolean to disable the carousel

This component also binds the following events to the window object:
    - ArrowLeft: go to the previous image
    - ArrowRight: go to the next image
*/
export const ImageCarousel = ({
    images = [], 
    imageSize, 
    aspectRatio = "16:9",
    objectFit = "cover", 
    objectPosition = "center", 
    orientation = "horizontal", 
    animation = "slide", 
    timeout = 500, 
    disabled, 
    layoutType, 
    ...props
}) => {
    const {
        items,
        Animation,
        activeStep,
        slideDirection,
        slideInverseDirection,
        setIsAnimating,
        handleNext,
        handleBack,
        handleTouchStart,
        handleTouchMove,
        handleTouchEnd,
        ratio,        
    } = useImageCarousel({images, imageSize, objectFit, objectPosition, aspectRatio, orientation, animation, timeout, disabled, ...props});

    if (disabled) timeout = 0;
    
    if (!images.length) return null;

    return (
        <Box 
            {...props}
            sx={{ width: '100%', height: imageSize?.height || '100%', flexGrow: 1, position: 'relative', bgcolor: 'background.paper', ...props?.sx }}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}            
        >
            <Stack direction="row" flexWrap="nowrap" useFlexGap sx={{
                position: 'relative',
                width: '100%',
                height: '100%',
                overflow: 'hidden',
                paddingBottom: `${(ratio[1] / ratio[0]) * 100}%`
            }}>
                {items.map((item, index) => (
                    <Animation 
                        direction={disabled ? undefined : (animation === "slide" ? (activeStep === index ? slideDirection : slideInverseDirection) : undefined)} 
                        in={activeStep === index} 
                        timeout={timeout}
                        key={index} 
                        mountOnEnter
                        unmountOnExit
                        onEnter={() => setIsAnimating(true)}
                        onEntered={() => setIsAnimating(false)}
                        onExit={() => setIsAnimating(true)}
                        onExited={() => setIsAnimating(false)}
                    >
                        <Box component={Paper} square elevation={0} sx={{
                            position: 'absolute',
                            width: '100%',
                            height: imageSize?.height || '100%',
                            maxHeight: '100%',
                            top: 0,
                            left: 0,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}>
                            {item}
                        </Box>
                    </Animation>
                ))}                
            </Stack>
            {images.length > 1 &&
                <>
                    <Button size="small" onClick={handleNext} disabled={disabled} position={orientation === "horizontal" ? "right" : "bottom"}/>
                    <Button size="small" onClick={handleBack} disabled={disabled} position={orientation === "horizontal" ? "left" : "top"}/>
                </>
            }
        </Box>
    );
}