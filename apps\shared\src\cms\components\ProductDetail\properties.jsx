export const properties = [
    /*{
        /*name: 'shopId',
        label: 'builder:component.productDetail.shopId',
        component: "TextField",//"GeneralItemSelector",
        value: 1,
        /*fetchParams: {params: {endpoint: "/cms/site/page", data: {website_id: websiteId, page_type_id: 14}, method: 'POST'}},
        valueFormatter: value => {
            if (!value) return null;
            if (!Array.isArray(value)) value = [value];
            return value.map(v => ({id: v?.id, slug: v?.title}));
        },
        size: "small",
        margin: "normal",
    },*/
    {
        name: 'loadType',
        label: 'builder:component.productDetail.type',
        component: "RadioGroup",
        value: '1',
        options: [{ id: '1', slug: 'builder:component.productDetail.types.automatic' }, { id: '2', slug: 'builder:component.productDetail.types.manual' }],
        size: "small",
        margin: "normal",
        helperText: 'builder:component.productDetail.types.helperText',
    },
    {
        name: 'productId',
        label: 'builder:component.productDetail.id',
        component: 'NumberField',
        value: 0,
        size: 'small',
        margin: 'normal',
        condition: { field: 'loadType', value: '2' }
    },
    {
        name: 'productVariantId',
        label: 'builder:component.productDetail.variantId',
        component: 'NumberField',
        value: 0,
        size: 'small',
        margin: 'normal',
        condition: { field: 'loadType', value: '2' }
    },
];