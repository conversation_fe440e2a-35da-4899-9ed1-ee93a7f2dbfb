import { useContext, useState, useCallback, useMemo, useEffect } from 'react';
import { useSelector, useDispatch, shallowEqual } from 'react-redux';

import { PosContext } from '../../hooks';
import { resetProduct, setInfo } from '../../../store/reducers/currentShopItemSlice';
import { toKebabCase, formatSlug, uuid } from '../../../utils';

export const usePosCart = ({
    slotProps,
}) => {
    const dispatch = useDispatch();
    const cart = useSelector(state => state.cart, shallowEqual);

    const { orderLoading: loading, handleOrderUpdate = () => {}, loadLatestOrder = () => {}, reduxStore, registerId, router, selectedUser } = useContext(PosContext) || {};

    const [openProductDetail, setOpenProductDetail] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);

    const handleDelete = useCallback(id => {
        if (!id || !cart?.cart?.length > 0) return;
        const _cart = cart.cart.filter(c => c.id !== id);
        handleOrderUpdate({items: _cart, couponsApplied: cart?.couponsApplied, override: true});
    }, [cart?.cart, cart?.couponsApplied, handleOrderUpdate]);

    const handleDuplicate = useCallback(id => {
        if (!id || !cart?.cart?.length > 0) return;
        let row = cart.cart.find(c => c.id === id);
        if (row) {
            row = JSON.parse(JSON.stringify(row));
            row.id = uuid();
            if (row?.metadata) {
                row.metadata.order_item_id = null;
                row.metadata.tmp_id = row.id;
            }
            if (row?.addons) row.addons.map(a => a.metadata = {...a.metadata, order_item_id: null});
            handleOrderUpdate({items: [row], couponsApplied: cart?.couponsApplied});
        }
    }, [cart?.cart, cart?.couponsApplied, handleOrderUpdate]);

    const handleDetailsOpen = useCallback(id => {
        if (!id || !cart?.cart?.length > 0) return;
        const row = cart.cart.find(c => c.id === id);
        if (row) {
            dispatch(setInfo(row));
            setSelectedProduct(row);
            setOpenProductDetail(true);            
        }
    }, [cart?.cart, dispatch]);

    const handleDetailsClose = useCallback(() => {
        setSelectedProduct(null);
        setOpenProductDetail(false);
    }, []);

    const handleEditItem = useCallback((id, values) => {
        if (!id || !cart?.cart?.length > 0) return;
        const _cart = [...cart?.cart || []];
        const idx = cart.cart.findIndex(c => c.id === id);
        if (idx > -1) {
            _cart[idx] = {...values};
            handleOrderUpdate({items: _cart, couponsApplied: cart?.couponsApplied, override: true}, () => {
                setOpenProductDetail(false);
                setSelectedProduct(null);
                dispatch(resetProduct());
            });
        }
    }, [cart?.cart, cart?.couponsApplied, handleOrderUpdate, dispatch]);

    const productRoute = useMemo(() => {
        let singleRoute = null, listRoute = null;
        if (router) {
            singleRoute = router?.slug?.value;
            listRoute = router?.slug?.value;
            
            if (selectedProduct?.eventId) {
                if (router?.events) listRoute += `/${router?.events?.value}`;
                if (router?.event) {
                    singleRoute += `/${router?.event?.value}/${selectedProduct?.eventId}/${toKebabCase(selectedProduct?.eventName || "c")}/${selectedProduct.id}`;
                }
            } else {
                if (router.products) listRoute += `/${router?.products?.value}`;
                if (router?.product){
                    singleRoute += `/${router?.product?.value}`;
                    if (selectedProduct) {
                        singleRoute += `/${selectedProduct?.productId}/${toKebabCase(selectedProduct?.productName || "c")}/${selectedProduct.id}`;
                    }
                }
            }
            singleRoute=`/${formatSlug(singleRoute)}`;
            listRoute=`/${formatSlug(listRoute)}`;
        }
        return [singleRoute, listRoute];
    }, [router, selectedProduct]);

    const sharedDetailsProps = useMemo(() => ({
            slotProps: slotProps?.productDetails,
            productId: selectedProduct?.productId,
            variantId: selectedProduct?.productVariantId,
            cartItemId: selectedProduct?.id,
            onAddToCart: (product, values) => handleEditItem(selectedProduct?.id, values),
    }), [slotProps?.productDetails, selectedProduct?.productId, selectedProduct?.productVariantId, selectedProduct?.id, handleEditItem]);

    useEffect(() => {
        loadLatestOrder(selectedUser?.id, registerId);
    }, [loadLatestOrder, selectedUser?.id, registerId]);

    return {
        loading,
        handleOrderUpdate,
        reduxStore,
        router,
        openProductDetail,
        handleDelete,
        handleDuplicate,
        handleDetailsOpen,
        handleDetailsClose,
        handleEditItem,
        productRoute: productRoute[0],
        productsRoute: productRoute[1],
        sharedDetailsProps,
        selectedProduct,
        cart,
    }
}