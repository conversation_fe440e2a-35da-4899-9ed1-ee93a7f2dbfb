import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Button } from '@mui/material';
import { Success<PERSON><PERSON>, Wizard } from '@siteboss-frontend/shared/components';

import Step1Name from './Steps/Step1Name';
import Step2Auto from './Steps/Step2Auto';
import Step3MaxUses from './Steps/Step3MaxUses';
import Step4Dates from './Steps/Step4Dates';
import Step5Type from './Steps/Step5Type';
import Step6ApplyTo from './Steps/Step6ApplyTo';
import Step7Combo from './Steps/Step7Combo';
import Step8Conditions from './Steps/Step8Conditions';
import Step9Summary from './Steps/Step9Summary';

import * as stepList from './Steps/stepList';
import { useWizard } from './useWizard';

export const DiscountWizard = () => {
    const { t } = useOutletContext();
    const [error, setError] = useState(null);

    // Use a ref to track if we've already set an error to avoid infinite loops
    const errorSetRef = useRef(false);

    // Use the hook outside of try/catch to avoid React Hook issues
    const wizardData = useWizard();

    // Handle any errors from the hook
    useEffect(() => {
        if (!wizardData && !errorSetRef.current) {
            // Remove console error to prevent browser performance issues
            setError(new Error('Failed to initialize discount wizard'));
            errorSetRef.current = true;
        }
    }, [wizardData]);

    const {
        handleErrors,
        setActiveStep,
        loading,
        success,
        setSuccess,
        errorBars,
        discountId,
        resetForm,
        completedSteps,
    } = wizardData;

    // Get the discount wizard data from Redux store
    const discountWizard = useSelector(state => state.discountWizard);
    
    // Create a submit handler that uses the Redux form data
    const handleSubmit = useCallback(() => {
        if (discountWizard?.formData) {
        // Use the formData from Redux instead of FormContext
        wizardData.handleSubmit(discountWizard.formData);
        }
    }, [discountWizard?.formData, wizardData]);

    // Add debug logging
    useEffect(() => {
        console.log('DiscountWizard - discountWizard from Redux:', discountWizard);
    }, [discountWizard]);

    // Use a ref to track if we've already handled success
    const successHandledRef = useRef(false);
    useEffect(() => {
        if (success && !successHandledRef.current) {
            // Remove console log to prevent browser performance issues
            setActiveStep(0);
            successHandledRef.current = true;
        } else if (!success) {
            // Reset the ref if success becomes false
            successHandledRef.current = false;
        }
    }, [success, setActiveStep]);

    if (error) {
        // Remove console error to prevent browser performance issues
        return (
            <Container>
                <Typography variant="h4" color="error" gutterBottom>
                    Error loading discount wizard
                </Typography>
                <Typography variant="body1">
                    {error.message}
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => window.location.href = '/discounts'}
                    sx={{ mt: 2 }}
                >
                    Return to Discounts
                </Button>
            </Container>
        );
    }

    try {
        return (
            <Container>
                {success && <SuccessBar message={t('discount:success')} onClose={() => setSuccess(false)} />}
                {errorBars && errorBars.filter(Boolean).map((ErrorBarComponent, i) =>
                    React.isValidElement(ErrorBarComponent)
                        ? React.cloneElement(ErrorBarComponent, { key: i })
                        : ErrorBarComponent && <ErrorBarComponent key={i} />
                )}
                <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '1rem' }}>
                    <Button
                        variant="outlined"
                        color="secondary"
                        onClick={resetForm}
                        sx={{ mr: 1 }}
                    >
                        Reset Form
                    </Button>
                </div>
                <Wizard
                    title={discountId ? t('discount:editDiscount') : t('discount:newDiscount')}
                    onSubmit={handleSubmit}
                    onError={handleErrors}
                    onChangeStep={setActiveStep}
                    loading={loading}
                    externalVisitedSteps={completedSteps}
                    isEditing={!!discountId}
                    slots={{
                        breadcrumbs: [
                            {title: t('dashboard:dashboard'), to: '/'},
                            {title: t('discount:discounts'), to: '/discounts'},
                            {title: discountId ? t('discount:editDiscount') : t('discount:newDiscount')}],
                        stepList,
                        steps: [
                            Step1Name,
                            Step2Auto,
                            Step3MaxUses,
                            Step4Dates,
                            Step5Type,
                            Step6ApplyTo,
                            Step7Combo,
                            Step8Conditions,
                            Step9Summary
                        ],
                    }}
                    slotProps={{
                        steps: {
                            data: discountWizard?.discountData || null,
                            loading: loading || false,
                            formData: discountWizard?.formData || {},
                            discountId: discountId || null,
                        }
                    }}
                    />
            </Container>
        );
    } catch (err) {
        // Remove console error to prevent browser performance issues
        return (
            <Container>
                <Typography variant="h4" color="error" gutterBottom>
                    Error rendering discount wizard
                </Typography>
                <Typography variant="body1">
                    {err.message}
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => window.location.href = '/discounts'}
                    sx={{ mt: 2 }}
                >
                    Return to Discounts
                </Button>
            </Container>
        );
    }
};

export default DiscountWizard;
