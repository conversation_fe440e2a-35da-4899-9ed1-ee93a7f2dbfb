import { useCallback, useState, useRef, useMemo, memo, Suspense } from 'react';
import { useApi } from '../../api';
import { fullList } from '../components';
import { EmptyWidget } from './CmsContainer/CmsContainer';

const apiParams = {enableCache: true, params: {endpoint: `/cms/site/page`, method: 'POST'}};

export const useClientRender = ({
    componentList = [], // list of components that can be rendered
    store = [], // store to get the values for the conditions
    sectionExtraProps = null, // extra props to pass to the section
}) => {
    const { fetchData: fetchPage } = useApi(apiParams);
    const [errors, setErrors] = useState(null);
    const renderedIds = useRef([]);

    //const allComponents = useMemo(() => [...fullList, ...componentList], [componentList]);
    const allComponents = useMemo(() => componentList?.length > 0 ? componentList : fullList, [componentList]);

    const getSectionConditionData = useCallback(({condition, data = null}) => {
        let prevData = data || {};
        if (condition?.field){
            if (condition?.field?.includes("local.")){
                //prevData.local = [...(prevData.local || []), condition.field];
            } else {
                const _value = store?.[condition.field] || undefined;
                if (!prevData?.[condition.field] || prevData?.[condition.field] !== _value) {
                    prevData[condition.field] = _value;
                }
            }
        } else if (condition?.conditions) {
            condition.conditions.forEach(a => {
                prevData = getSectionConditionData(a, prevData);
            });
        }
        return prevData;
    }, [store]);

    const getSectionProps = useCallback(({section, parentCondition = null, extraProps = null}) => {
        let componentProps = {...(section.component.content || {})};
        let _condition = null;
        if (section.component?.slotProps) componentProps.slotProps = section.component.slotProps;
        if (section.component?.layout?.id) componentProps.layoutId = section.component.layout.id;
        if (parentCondition) {
            if (Array.isArray(parentCondition)) parentCondition = { operator: "AND", conditions: [...parentCondition] };
            _condition = {...(_condition || {}), ...parentCondition};
        }
        if (section.component?.condition) {
            let __condition = section.component?.condition;
            if (Array.isArray(__condition)) __condition = { operator: "AND", conditions: [...__condition] };
            _condition = {...(_condition || {}), ...__condition};
        }
        if (_condition) {
            const {local: localData, ...restOfData} = getSectionConditionData(_condition) || {};
            componentProps.condition = {
                condition: _condition,
                data: restOfData || null,
            }
        }

        // add any extra props passed to the component
        if (extraProps) componentProps = {...componentProps, ...extraProps};

        return componentProps;
    }, [getSectionConditionData]);

    const getWebsiteTemplate = useCallback(async ({websiteId = null}) => {
        try {
            const res = await fetchPage({endpoint: `/cms/site/page`, website_id: websiteId, page_type_id: 9});
            if (res?.data?.[0]?.content?.length > 0) {
                return JSON.parse(JSON.stringify(res.data[0].content));
            }
        } catch (error) {
            console.log(error);
        }
        return null;
    }, [fetchPage]);

    const getPageDefinition = useCallback(async ({section}, loadedPageIds) => {
        if (!loadedPageIds) loadedPageIds = new Set();
        if (!Array.isArray(section)) section = [section];
        const _result = [];
        for (let sect of section) {
            if (sect.component) {
                let children = null;
                if (sect.component?.content?.children?.length > 0) {
                    children = await getPageDefinition({section: sect.component.content.children}, loadedPageIds);
                }
                if (children) sect.component.content.children = children;
                _result.push(sect);
                //return sect;
            } else if (sect.pageId) {
                if (loadedPageIds.has(sect.pageId)) return null; // prevent loop
                loadedPageIds.add(sect.pageId);
                try {
                    const res = await fetchPage({endpoint: `/cms/site/page/${sect.pageId}`});
                    if (res?.data?.[0]?.content) {
                        let _content = res.data[0].content;
                        if (_content && !Array.isArray(_content)) _content = [_content];
                
                        await Promise.all(_content?.map(async (block, i) => {
                            try {
                                const _res = await getPageDefinition({section: block}, loadedPageIds);
                                if (_res) _result.push(..._res);
                            } catch (error) {
                                console.log(error);
                            }
                        }) || []);
                    }
                } catch (error) {
                    console.log(error);
                }
            }
        }
        return _result || null;
    }, [fetchPage]);

    // render a page definition without any async calls
    const renderStaticSection = useCallback(({data = [], index = 0, ref = null, parentCondition = null, extraProps = null, isBuilder = false, isDragging = false}, callback) => {
        if (!data) return null;
        if (!Array.isArray(data)) data = [data];

        return data.map((section, i) => {
            if (!section.component) return null;
            const widget = allComponents.find(a => a.name === section.component.name);
            if (widget){
                let _ref = null;
                if (ref) {
                    if (Array.isArray(ref)) _ref = ref?.[i];
                    else _ref = ref;
                }

                if (isDragging) return <div key={`page-section-${section.id}-${index}`} ref={_ref || undefined} id={section.id || uuid()} style={{height: '40px'}}/>

                //const Component = widget.component;
                const Component = props => ( 
                    <Suspense fallback={EmptyWidget({isBuilder, height: 64})}>
                        <widget.component {...props} />
                    </Suspense>
                );

                let children = null;
                if (section.component?.content?.children) {
                    children = renderStaticSection({data: section.component.content.children, index: `${section.id}-${i}`, extraProps, isBuilder});
                }

                const componentProps = getSectionProps({section, parentCondition, extraProps});
                if (callback) callback();

                // this is where the component is rendered
                return (
                    <Component key={`page-section-${section.id}-${index}`} ref={_ref || undefined} isBuilder={isBuilder} id={section.id || uuid()} {...sectionExtraProps} {...componentProps}>
                        {children}
                    </Component>
                );
            }
            return null;
        }).filter(a=>a);
    }, [getSectionProps, allComponents, sectionExtraProps]);

    // render a page definition with async calls in case a section has a pageId defined
    const renderSection = useCallback(async ({section, index = 0, ref = null, parentCondition = null, extraProps = null, isBuilder = false}) => {
        if (section.component){
            const widget = allComponents.find(a => a.id === section.component.name);
            if (widget){
                const Component = widget.component;

                let children = await Promise.all(section.component?.content?.children?.map(async (child, i)=> {
                    try {
                        return await renderSection({section: child, index: `${section.id}-${i}`, extraProps, isBuilder});
                    } catch(error) {
                        setErrors(error);
                    }
                    return null;
                }) || []);

                const componentProps = getSectionProps({section, parentCondition, extraProps});

                // this is where the component is rendered
                return (
                    <Component key={`page-section-${section.id}-${index}`} ref={ref || undefined} isBuilder={isBuilder} id={section.id || uuid()} {...componentProps} {...sectionExtraProps}>
                        {children}
                    </Component>
                );
            }
        } else if (section.pageId) { // some sections are content blocks (other pages), so we store the id of the page to render
            try {
                const res = await fetchPage({endpoint: `/cms/site/page/${section.pageId}`});
                if (res?.data?.[0]?.content) {
                    let _content = res.data[0].content;
                    if (_content && !Array.isArray(_content)) _content = [_content];

                    return await Promise.all(_content?.map(async (block, i)=> {
                        try {
                            return await renderSection({
                                section: block, 
                                index: `content-block-${section.id}-${section.pageId}-${i}`, 
                                parentCondition: section?.condition, 
                                extraProps,
                                isBuilder,
                            });
                        } catch(error) {
                            setErrors(error)
                        }
                        return null;
                    }) || []);
                }
            } catch (error) {
                setErrors(error)
            }
        }
        return null;
    }, [getSectionProps, fetchPage, allComponents, sectionExtraProps]);

    return {
        renderSection,              // async renders a page
        renderStaticSection,        // renders a page object without any async calls (used in ssr)
        getSectionConditionData,    // returns the data for a section's condition (like {id: 1, name: 'John'})
        getPageDefinition,          // returns a full page definition even if the page is made up of multiple content blocks
        getWebsiteTemplate,         // returns a website's default template
        errors,
    }
}
