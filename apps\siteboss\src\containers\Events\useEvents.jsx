import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useOutletContext, useParams, useNavigate } from 'react-router-dom';
import { useApi } from '@siteboss-frontend/shared';

import { setInfo } from '../../store/reducers/eventWizardSlice';

const apiParams = [
    {params: {endpoint: `/event/type`, method: 'POST'}},
    {params: {endpoint: `/event/delete`, method: 'DELETE'}},
    {params: {endpoint: `/event/edit_wiz`, method: 'POST'}},
];

export const useEvents = ({ setLoading }) => {
    const { t } = useOutletContext();

    const params = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const [open, setOpen] = useState(false);
    const [isNew, setIsNew] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [showConfirm, setShowConfirm] = useState(false);
    const [selectedEvents, setSelectedEvents] = useState([]);
    const [deleteParams, setDeleteParams] = useState([]);
    const [fetchCounter, setFetchCounter] = useState(0);
    const [errors, setErrors] = useState(null);

    // api calls
    const { fetchData: fetchEventTypes, data: eventTypes, loading: eventTypesLoading, errors: eventTypesErrors, ErrorBar: EventTypesErrorBar } = useApi(apiParams[0]);
    const { fetchData: processDelete } = useApi(apiParams[1]);
    const { fetchData: updateEvent, loading: updateLoading } = useApi(apiParams[2]);

    /*const { permissions } = usePermission({moduleIds: [74, 204]});*/

    const toggleDrawer = useCallback(open => e => {
        if (e?.type === 'keydown' && (e?.key === 'Tab' || e?.key === 'Shift')) return;

        console.log('Toggle drawer called with open:', open);

        if (!open && (isNew || isEdit)) {
            setFetchCounter(prev => prev + 1); // resets the page and order to trigger a refresh
        }

        if (!open) {
            // When closing the drawer, clear the selection
            setSelectedEvents([]);
            if (params.id) {
                params.id = null;
            }
        }

        // When opening the drawer, make sure we have a selection
        if (open && selectedEvents.length === 0 && params.id) {
            console.log('Opening drawer with params.id:', params.id);
        }

        setIsNew(false);
        setIsEdit(false);
        setOpen(open);
    }, [isNew, isEdit, params.id, selectedEvents]);

    const handleDelete = useCallback((e, event) => {
        if (event) setDeleteParams([event]);
        else setDeleteParams(selectedEvents);
        if (event || selectedEvents.length > 0) setShowConfirm(true);
    }, [selectedEvents]);

    // update the event: when the status is changed, or an image is added
    const handleUpdate = useCallback(async (data, callback) => {
        setLoading(true);
        try {
            setFetchCounter(prev => prev + 1);
            const result = await updateEvent(data);
            if (result){
                if (result?.errors) setErrors(result?.errors);
                else if (result?.data) {
                    if (callback) await callback();
                }
            } else setErrors(t('error:default'));
        } catch (error) {
            setErrors(error);
        } finally {
            setLoading(false);
        }
    }, [updateEvent]);

    const handleEdit = useCallback((e, event) => {
        if (event) navigate(`/events/wizard/${event.id}`);
    }, []);

    // when the event type is selected, navigate to the wizard
    const handleEventTypeSelect = useCallback(id => {
        dispatch(setInfo({eventTypeId: id}));
        navigate(`/events/wizard/t/${id}`); // we send the id to the wizard so it doesn't lose the selected event type when the user refreshes
    }, [dispatch, navigate]);

    // set the delete params when the selected events change
    useEffect(() => {
        if (selectedEvents.length <= 0) {
            setOpen(false);
            setDeleteParams([]);
        } else {
            setDeleteParams(selectedEvents);
            setShowConfirm(false);
        }
    }, [selectedEvents]);

    // get the event types on load
    useEffect(() => {
        fetchEventTypes();
    }, [fetchEventTypes]);

    return {
        open,
        setOpen,
        isNew,
        setIsNew,
        isEdit,
        setIsEdit,
        showConfirm,
        setShowConfirm,
        selectedEvents,
        setSelectedEvents,
        deleteParams,
        setDeleteParams,
        fetchCounter,
        setFetchCounter,
        errors,
        setErrors,
        eventTypes,
        eventTypesErrors,
        EventTypesErrorBar,
        toggleDrawer,
        handleDelete,
        handleUpdate,
        processDelete,
        handleEdit,
        handleEventTypeSelect,
        eventTypesLoading,
        updateLoading,
        params,
    };
}