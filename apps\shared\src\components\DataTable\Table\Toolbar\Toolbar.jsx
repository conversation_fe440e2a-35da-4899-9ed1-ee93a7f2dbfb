import React, { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { GridToolbarContainer } from '@mui/x-data-grid';
import { Box, IconButton, Typography, Tooltip, useTheme } from '@mui/material';
import {
    DocumentScannerTwoTone as DocumentIcon,
    DeleteTwoTone as DeleteIcon,
    DownloadTwoTone as DownloadIcon,
    PrintTwoTone as PrintIcon,
    VisibilityOffTwoTone as VisibilityOffIcon
} from '@mui/icons-material';
import { formatDateTime } from '../../../../utils';

export const Toolbar = ({
    onExpand,
    onDelete,
    loading,
    checkboxSelection,
    allRows: allDataRows,

    selectedRows,
    selectCount,
    expandDetailPanel,
    allRowIds,
    localeText,
    onExport,
    onPrint,

    ...props
}) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const company = useSelector(state => state.company);
    const language = useSelector(state => state.language);

    const [open, setOpen] = useState(true);

    const backgroundColor = useCallback(() => selectCount > 0 ? theme.palette.mode === "dark" ? theme.palette.primary.main : theme.palette.primary.light : 'transparent', [selectCount, theme.palette]);
    const color = useCallback(() => selectCount > 0 ? theme.palette.primary.contrastText : theme.palette.text.primary, [selectCount, theme.palette]);

    const handleExport = useCallback(options => {
        if (onExport) onExport(options);
    }, [onExport]);

    const handlePrint = useCallback(options => {
        if (onPrint) onPrint(options);
    }, [onPrint]);

    const handleDelete = useCallback(() => {
        if (onDelete) onDelete(Array.from(selectedRows.values()));
    }, [onDelete, selectedRows]);

    if (selectCount <= 0 || !open) return null;

    return (
        <GridToolbarContainer sx={{
            position: 'absolute',
            top: 0,
            left: checkboxSelection ? 50 : 0,
            zIndex: theme.zIndex.appBar - 1,
            width: `calc(100% - ${checkboxSelection ? 50 : 0}px)`,
            height: '55px',
            borderRadius: `0 ${theme.shape.borderRadius}px 0 0`,
            backgroundColor: backgroundColor,
            color: color,
            px: 2
        }}>
            <Typography variant="subtitle2" component="div">
                {selectCount > 0 && `${localeText.footerRowSelected(selectCount)}`}
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            {selectCount > 0 &&
                <>
                    <Tooltip title={localeText.toolbarExportPrint}>
                        <span>
                        <IconButton size="small" sx={{color: color}} disabled={loading} onClick={() => handlePrint({
                            hideToolbar: true,
                            hideFooter: true,
                            includeCheckboxes: false,
                            getRowsToExport: () => checkboxSelection ? Array.from(selectedRows.values()).map(item => item.id) : allRowIds,
                            pageStyle: () => {
                                const companyAddress =
                                    (company.address ? `${company.address} \\A` : '') +
                                    (company.address2 ? `${company.address2} \\A` : '') +
                                    (company.city ? `${company.city}, ` : '') + (company.state ? `${company.state}. ` : '') + (company.postalCode ? `${company.postalCode} ` : '') +
                                    (company.email ? `\\A${company.email}`: '') +
                                    (company.phone ? `\\A${company.phone}` : '');
                                return (`
                                    .MuiDataGrid-root .MuiDataGrid-main::before{
                                        content: '${company.name ? company.name.toUpperCase() : ''}';
                                        position: absolute;
                                        top: -100px;
                                        left: 0;
                                        font-weight: 600;
                                        font-size: 10pt;
                                    }
                                    .MuiDataGrid-root .MuiDataGrid-main::after{
                                        content: '${companyAddress}';
                                        position: absolute;
                                        top: -80px;
                                        left: 0;
                                        font-size: 6pt;
                                        white-space: pre-wrap;
                                    }
                                    .MuiDataGrid-root:after{
                                        content: '${t('general:printedOn')} ${formatDateTime(new Date(), language.code)}';
                                        position: absolute;
                                        bottom: 0;
                                        right: 0;
                                        font-size: 6pt;
                                        color: rgba(0, 0, 0, 0.87);
                                    }
                                    .MuiDataGrid-root {font-family: Arial, sans-serif; background: #fff; position: relative; border-radius: 0!important; border-bottom: 1px solid rgba(0, 0, 0, 0.12) !important; height: calc(100% - 20px) !important;}
                                    .MuiDataGrid-root .MuiDataGrid-main { margin-top: 100px; position: relative; color: rgba(0, 0, 0, 0.87);}
                                    .MuiDataGrid-root .MuiDataGrid-row {min-height: 28px !important; height:auto !important; background: #fff !important; border-bottom: 0.5px dotted rgba(0, 0, 0, 0.12) !important;}
                                    .MuiDataGrid-root .MuiDataGrid-row:last-of-type {border-bottom: 0 !important;}
                                    .MuiDataGrid-root .MuiDataGrid-columnHeader,
                                    .MuiDataGrid-root .MuiDataGrid-cell {min-height: 28px !important; height:auto !important; border: 0 !important; font-size: 8pt !important;}
                                    .MuiDataGrid-root .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer {display: none}
                                    .MuiDataGrid-root .MuiDataGrid-columnHeaders {min-height: 28px !important; line-height: 8pt !important; height:auto !important; background: #000; color: #fff; border-radius: 0; border: 0 !important; box-shadow: none !important; }
                                    .MuiDataGrid-root .MuiDataGrid-cellContent,
                                    .MuiDataGrid-root .MuiDataGrid-columnHeaderTitle {font-family: Arial, sans-serif; font-size: 8pt; border: 0 !important; box-shadow: none !important;}
                                    .MuiDataGrid-root .MuiDataGrid-footerContainer {display: none;}
                                `)
                            }})}>
                            <PrintIcon fontSize="inherit" />
                        </IconButton>
                        </span>
                    </Tooltip>
                    <Tooltip title={localeText.toolbarExportCSV}>
                        <span>
                        <IconButton size="small" sx={{color: color}} disabled={loading} onClick={() => handleExport({
                            hideFooter: true,
                            hideToolbar: true,
                            includeCheckboxes: false,
                            getRowsToExport: () => checkboxSelection ? Array.from(selectedRows.values()).map(item => item.id) : allRowIds,
                        })}>
                            <DownloadIcon fontSize="inherit" />
                        </IconButton>
                        </span>
                    </Tooltip>
                    {onExpand &&
                        <Tooltip title={localeText.expandDetailPanel}>
                            <span>
                            <IconButton size="small" sx={{color: color}} disabled={loading} onClick={onExpand}>
                                <DocumentIcon fontSize="inherit" />
                            </IconButton>
                            </span>
                        </Tooltip>
                    }
                    {onDelete &&
                        <Tooltip title={localeText.filterPanelDeleteIconLabel}>
                            <span>
                            <IconButton size="small" sx={{color: color}} disabled={loading} onClick={handleDelete}>
                                <DeleteIcon fontSize="inherit" />
                            </IconButton>
                            </span>
                        </Tooltip>
                    }
                    <Tooltip title={t("general:hide")}>
                        <span>
                        <IconButton size="small" sx={{color: color}} disabled={loading} onClick={e=>setOpen(false)}>
                            <VisibilityOffIcon fontSize="inherit" />
                        </IconButton>
                        </span>
                    </Tooltip>
                </>
            }
        </GridToolbarContainer>
    );
}