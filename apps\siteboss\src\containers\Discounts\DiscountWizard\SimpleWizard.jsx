import React from 'react';
import { Container, Typography, Paper, Button, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const SimpleWizard = () => {
    const navigate = useNavigate();

    return (
        <Container>
            <Paper sx={{ p: 4, mt: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Discount Wizard
                </Typography>
                <Typography variant="body1" paragraph>
                    We're experiencing technical difficulties with the discount wizard.
                </Typography>
                <Typography variant="body1" paragraph>
                    Our team is working to resolve this issue as soon as possible.
                </Typography>
                <Box sx={{ mt: 4 }}>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => navigate('/discounts')}
                    >
                        Return to Discounts
                    </Button>
                </Box>
            </Paper>
        </Container>
    );
};

export default SimpleWizard;
