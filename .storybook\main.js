

import { join, dirname } from "path"

/**
* This function is used to resolve the absolute path of a package.
* It is needed in projects that use Yarn PnP or are set up within a monorepo.
*/
function getAbsolutePath(value) {
    return dirname(require.resolve(join(value, 'package.json')))
}

/** @type { import('@storybook/react-vite').StorybookConfig } */
const config = {
    stories: [
        "../stories/**/*.mdx",
        "../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)",
        "../docs/*.mdx", //root level documentation
        "../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)", //external stories, such as demonstrating MUI, a 3rd party we're using, or something else that needs to be sorted away from its home.  Root level folder
        "../apps/**/src/**/*.stories.@(js|jsx|mjs|ts|tsx)", //siteboss internal stories, such as demonstrating components we've built. 
        "../apps/**/src/**/*.mdx", //internal docs
    ],
    addons: [
        getAbsolutePath('@chromatic-com/storybook'),
        getAbsolutePath('@storybook/addon-docs'),
        getAbsolutePath("@storybook/addon-a11y"),
        getAbsolutePath("@storybook/addon-vitest")
    ],
    framework: {
        "name": getAbsolutePath('@storybook/react-vite'),
        "options": {}
    },
    build: {
        test: { //for chromatic
            disableBlocks: true,
            disableAutoDocs: true,
            disableMDXEntries: true,
            disableDocgen: true,
        }
    },
    core: {
        builder: getAbsolutePath("@storybook/builder-vite"),
    },
    // async viteFinal(config) {
    //     // Merge custom configuration into the default config
    //     const { mergeConfig } = await import('vite');

    //     return mergeConfig(config, {
    //         // Add dependencies to pre-optimization
    //         // optimizeDeps: {
    //         //   // include: ['storybook-dark-mode'],
    //         // },
    //     });
    // },
};
export default config;