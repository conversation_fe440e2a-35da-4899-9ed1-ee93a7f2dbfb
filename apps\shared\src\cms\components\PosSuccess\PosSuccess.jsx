import React, { useContext, useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, Navigate, useParams } from 'react-router-dom';
import { Stack, Button, Typography, useMediaQuery, Divider, Paper, Container } from '@mui/material';
import { CheckCircleOutlineOutlined as CheckIcon } from '@mui/icons-material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { PosContext } from '../../hooks/PosContext';
import { Title, PrintFormats } from '../../../components';
import { formatSlug } from '../../../utils/cms';

import { layouts } from './layouts';
import { properties } from './properties';
import Transactions from './Transactions'

export const PosSuccess = ({
    id,
    layoutId,
    showOrderPreview = true,
    showTransactions = true,
    showPrintButton = false,
    slotProps = {
        cmsContainer: {},       // MUI container props
        cmsStack: {},           // MUI stack props
        button: {},             // MUI button props
        title: {},              // MUI typography props
        thankYouBar: {},        // MUI stack props
        buttonBar: {},          // MUI stack props
        transactions: {},       // MUI grid2 props
    },
    condition = null,           // condition to render the element
    isBuilder = false,          // renders the builder wrapper around the element
    builderProps,
    children,
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md')); 
    const { id: orderId } = useParams();

    const [loading, setLoading] = useState(true);
    const [order, setOrder] = useState(null);
    const { reduxStore, registerId, selectedUser, router, handleLoadOrder = () => {}, orderErrors } = useContext(PosContext) || {};
    
    const { slotProps: updatedSlotProps, canRender, customCss } = prepareComponent({name: "success", layoutId, layouts, slotProps, isBuilder, condition, localState: {
        orderId,
        registerId,
        ...reduxStore,
    }});
    slotProps = updatedSlotProps;


    //const { printData, loading: previewLoading } = PrintFormats.orders.usePrint({id: orderFullfilled?.order?.id, formats: [{id: 'order', slug: 'order:formats.fullPage'}]});

    const routes = useMemo(() => {
        let cartRoute = null, indexRoute = null;
        if (router) {
            if (router?.index) indexRoute=`/${formatSlug(`${router?.slug?.value}/${router?.index?.value}`)}`;
            if (router?.cart) cartRoute=`/${formatSlug(`${router?.slug?.value}/${router?.cart?.value}`)}`;
        }
        return [indexRoute, cartRoute];
    }, [router]);


    useEffect(() => {
        if (orderId && !isBuilder) {
            try{
                setLoading(true);
                handleLoadOrder({orderId, _registerId: registerId, extraParams: {with_extra_details: true}}).then(order => setOrder(order));
            } catch (error) {
                console.log(error);
            } finally {
                setLoading(false);
            }
        } else setLoading(false);
    }, [orderId, handleLoadOrder, registerId, isBuilder]);

    if (!isBuilder && ((!loading && !orderId))) return <Navigate to={routes[1] || routes[0]} />;
    if (!canRender) return null;
    
    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack component={Container} direction="column" spacing={2} useFlexGap {...slotProps?.cmsStack}>
                <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={2} {...slotProps?.thankYouBar}>
                    <CheckIcon fontSize="large" />
                    <Stack direction="column" spacing={0} useFlexGap>
                        <Typography component="div" variant="subtitle2">{`${t("order:order")} #${order?.id || ""}`}</Typography>
                        <Title {...slotProps?.title} title={t("pos:success.thankYou")} />
                    </Stack>
                </Stack>
                <Typography component="div" variant="body1" dangerouslySetInnerHTML={{__html: t("pos:success.description")}} />
                
                <Stack direction="row" spacing={1} useFlexGap {...slotProps?.buttonBar}>
                    {showPrintButton &&
                        <Button variant="contained" size={isMobile ? "large" : undefined} disabled={isBuilder} {...slotProps?.button}>
                            {t('pos:printReceipt')}
                        </Button>
                    }
                    <Button component={Link} to={routes[0]} variant="contained" size={isMobile ? "large" : undefined} disabled={isBuilder} {...slotProps?.button}>
                        {t('pos:success.continueShopping')}
                    </Button>
                </Stack>

                {showTransactions && 
                    <>
                        <Divider />
                        <Transactions payments={order?.transactions} slotProps={slotProps} />
                    </>
                }
                {order?.id && showOrderPreview && !isBuilder &&
                    <>
                        <Divider />
                        <Paper sx={{p: 2}} variant="outlined">
                            <PrintFormats.orders.Preview id={order?.id} showPayNow={false} formats={[{id: 'order', slug: 'order:formats.fullPage'}]} />
                        </Paper>
                    </>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
}
