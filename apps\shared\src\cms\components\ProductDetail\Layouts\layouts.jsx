import { Inventory2Outlined as ItemIcon, TableRowsOutlined as <PERSON>I<PERSON>, ViewWeekOutlined as ColumnIcon } from '@mui/icons-material';

import { Default } from './Default';
import { Standard } from './Standard';

export const widgetIcon = ItemIcon;
export const layouts = [
    {
        id: 1,
        name: 'Default',
        component: Default,
        icon: <RowIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
    {
        id: 2,
        name: 'Standard',
        component: Standard,
        icon: <ColumnIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
];