import { Login } from '../Login';

export default {
    title: "OWL/Components/Login",
    component: Login,
    tags: ['autodocs'],
    argTypes:{

    }
};

export const Playground ={
    parameters:{
        design:[
            {
                name: "All",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=0-1"
            },
            {
                name: "Initial Login",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=1-2"
            },
            {
                name: "Sign Up",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=20-42473"
            },
            {
                name: "Forgot Username",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=14-24624"
            },
            {
                name: "Forgot Password",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=14-15220"
            },
            {
                name: "Create Password",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=28-4810"
            
            },
            {
                name: "Reset Password",
                type: "figma",
                url: "https://www.figma.com/design/eaYzk2kd5oX7cRoSlvMc8W/SiteBoss_Portal1?node-id=14-24721"
            }
        ]
    },
    args:{

    }
}
