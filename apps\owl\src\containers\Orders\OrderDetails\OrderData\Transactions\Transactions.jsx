import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography, Divider } from '@mui/material';
import { createCurrencyFormatter, toCamelCase, formatDate } from '@siteboss-frontend/shared/utils';

export const Transactions = (props) => {
    const { data } = props;
    const { t, isMobile, currency } = useOutletContext();

    const language = useSelector(state => state.language);
    const currencyFormatter = createCurrencyFormatter(language.code, currency);

    if (!data?.transactions || data.transactions.length === 0) return null;

    return (
        <Box sx={{width: "100%", order: isMobile ? 2 : 0, display: "flex", flexDirection: "column"}}>
            <Typography variant="h6">{t("order:payments")}</Typography>
            {data.transactions.map((transaction, i)=>(
                <React.Fragment key={`order-transaction-${transaction.id}-${i}`}>
                    <Typography variant="subtitle2">{t("order:date")}: {formatDate(new Date(transaction.date || transaction.created_at), language.code)}</Typography>
                    <Typography variant="subtitle2">{t(`order:${toCamelCase(transaction.transaction_payment_method_name || transaction.payment_method)}`)}</Typography>
                    {transaction.cc_type && transaction.cc_number && 
                        <Typography variant="subtitle2">{transaction.cc_type} {t("order:endingIn")} {transaction.cc_number}</Typography>
                    }
                    {transaction.transaction_response?.authorization_code && 
                        <Typography variant="subtitle3">
                            {t("order:authorizationCode")}: {transaction.transaction_response.authorization_code}
                        </Typography>
                    }
                    {transaction.transaction_response?.reference_number && 
                        <Typography variant="subtitle3">
                            {t("order:referenceNumber")}: {transaction.transaction_response.reference_number}
                        </Typography>
                    }
                    <Typography variant="subtitle2">{t("order:amount")}: {currencyFormatter.format(transaction.amount)}</Typography>
                    {+transaction.transaction_payment_method_id === 2 &&
                        <>
                            {transaction.transaction_response?.amount > 0 && 
                                <Typography variant="subtitle3">
                                    {t("order:tendered")}: {currencyFormatter.format(transaction.transaction_response.amount)}
                                </Typography>
                            }
                            {transaction.transaction_response?.change > 0 && 
                                <Typography variant="subtitle3">
                                    {t("order:change")}: {currencyFormatter.format(transaction.transaction_response.change)}
                                </Typography>
                            }
                        </>
                    }
                    <Typography variant="subtitle2" component="div" color={[3, 4, 5, 8, 9].filter(s => s === transaction.transaction_status_id).length > 0 ? "error" : "success"}>
                        {t("order:status")}: {t(`status:${toCamelCase(transaction.transaction_status_name || transaction.status)}`)}
                    </Typography>
                    {i < data.transactions.length - 1 && <Divider sx={{my: 2}} />}
                </React.Fragment>
            ))}
        </Box>
   );
}
