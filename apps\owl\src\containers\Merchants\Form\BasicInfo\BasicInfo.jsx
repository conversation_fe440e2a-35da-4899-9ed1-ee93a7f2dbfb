import React from 'react';
import { Grid2 } from '@mui/material';
import { FormItem } from '@siteboss-frontend/shared/components';

export const BasicInfo = ({ fields, values, errors, onChange, loading, parentRef }) => {
    if (!fields) return null;

    return (
        <Grid2 container columnSpacing={2}>
            {fields.map(field => (
                <Grid2 size={field?.rowSize || {xs: 12}} key={`form-${field.name}`}>
                    <FormItem
                        {...field}
                        value={values?.[field.name] || field.value}
                        checked={field.component === "Switch" ? Boolean(values?.[field.name]) : undefined}
                        parentRef={parentRef}
                        errors={errors?.[field.name]}
                        onChange={onChange}
                        loading={loading}
                    />
                </Grid2>
            ))}
        </Grid2>
    );
}