.button{
    max-width: 220px;
    min-height: 120px;
    min-width: 120px;
    width: 100%;
    align-items: flex-start;
    transition: max-width 500ms cubic-bezier(0.4, 0, 0.2, 1);

    .content{
        margin-top: 0 !important;
        width: 0;
    }

    &.active{
        max-width: 100%;

        .content{
            width: 100%;
        }
    }
}

.small {
    min-width: 100px;
    width: auto;
    min-height: 80px;
    align-items: center;
}

.auto {
    min-width: 100px;
    width: auto;
    min-height: inherit;
    align-items: center;
}