import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Button, Menu, MenuItem, useMediaQuery } from '@mui/material';

import SendEmail  from '../../SendEmail';

export const Actions = ({id, printRef, onPrint, userEmails, formats, showPayNow = true, ...props}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [emailOpen, setEmailOpen] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);
    const printOpen = Boolean(anchorEl);

    const handlePrintClose = () => setAnchorEl(null);
    const handleEmailClose = () => setEmailOpen(false);

    const handlePrint = useCallback(format => e => {
        if (onPrint) onPrint(format);
        handlePrintClose();
    }, [onPrint, handlePrintClose]);

    const handlePrintClick = useCallback(e => {
        if (formats?.length > 1) setAnchorEl(e.currentTarget);
        else handlePrint(formats?.[0]?.id)(e);
    }, [formats, handlePrint]);


    useEffect(() => {
        return () => {
            setAnchorEl(null);
            setEmailOpen(false);
        }
    }, []);

    if (!id) return null;

    return (
        <>
        <Stack spacing={1} direction="row" sx={{my: 2}}>
            {showPayNow &&
                <Button variant={isMobile ? "contained" : "outlined"} color="primary">{t("order:payNow")}</Button>
            }
            <Button
                id="print-button"
                aria-controls={formats?.length > 1 && printOpen ? 'print-menu' : undefined}
                aria-haspopup={formats?.length > 1 ? "true" : undefined}
                aria-expanded={formats?.length > 1 && printOpen ? 'true' : undefined}
                onClick={handlePrintClick}
                variant={isMobile ? "contained" : "outlined"} 
                color="inherit"
            >
                {t("general:print")}
            </Button>
            {formats?.length > 1 &&
                <Menu
                    id="print-menu"
                    anchorEl={anchorEl}
                    open={printOpen}
                    onClose={handlePrintClose}
                    MenuListProps={{'aria-labelledby': 'print-button'}}
                >
                    {formats && formats.map(format => (
                        <MenuItem key={`print-format-${format.id}`} onClick={handlePrint(format.id)}>{t(format.slug)}</MenuItem>
                    ))}
                </Menu>
            }
            <Button variant={isMobile ? "contained" : "outlined"} color="inherit" onClick={e => setEmailOpen(true)}>{t("general:email")}</Button>
        </Stack>
        
        <SendEmail id={id} open={emailOpen} onClose={handleEmailClose} emails={userEmails} />
        </>
    );
}