import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Typography } from '@mui/material';
import { formatDateTime } from '../../../../../utils';

export const Time = () => {
    // Use a ref to store the language to prevent re-renders
    const languageRef = useRef(null);
    const languageFromStore = useSelector(state => state.language);

    // Only update the ref if it's null or if the language has changed
    if (!languageRef.current || languageFromStore.code !== languageRef.current.code) {
        languageRef.current = languageFromStore;
    }

    // Use the ref value instead of the direct selector result
    const language = languageRef.current || { code: 'en' };

    const [currentTime, setCurrentTime] = useState(new Date());

    // Compute the format directly based on the language code
    const format = language.code === 'es' ? 'hh:mmaaa dd-MM-yy' : 'hh:mmaaa MM/dd/yy';

    // Use a ref to track if the interval has been set up
    const intervalRef = useRef(null);

    useEffect(() => {
        // Only set up the interval once
        if (!intervalRef.current) {
            intervalRef.current = setInterval(() => setCurrentTime(new Date()), 1000);
        }

        // Clean up the interval on unmount
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, []);

    return (
        <Typography variant="body2" component="div" color="initial" sx={{textAlign:'center', width: '100%', mt: 1}} data-chromatic="ignore">
            {formatDateTime(currentTime, language.code, format)}
        </Typography>
    );
}