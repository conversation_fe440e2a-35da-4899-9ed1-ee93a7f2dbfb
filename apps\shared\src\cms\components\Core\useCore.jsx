import { use<PERSON>allback, useMemo } from 'react';
import { <PERSON><PERSON>, Typo<PERSON>, Button, Box } from '@mui/material';
import { Gallery } from '../../../components';

const getEmbedUrl = url => {
    if (!url) return ''; 
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
        const videoId = url.split('v=')[1] || url.split('/').pop();
        if (videoId.includes('&')) {
            return `https://www.youtube.com/embed/${videoId.split('&')[0]}`;
        }
        if (videoId.includes('?')) {
            return `https://www.youtube.com/embed/${videoId.split('?')[0]}`;
        }
        if (videoId.includes('/')) {
            return `https://www.youtube.com/embed/${videoId.split('/').pop()}`;
        }
        return `https://www.youtube.com/embed/${videoId}`;
    } else if (url.includes('vimeo.com')) {
        const videoId = url.split('/').pop();
        if (videoId.includes('&')) {
            return `https://player.vimeo.com/video/${videoId.split('&')[0]}`;
        }
        if (videoId.includes('?')) {
            return `https://player.vimeo.com/video/${videoId.split('?')[0]}`;
        }
        return `https://player.vimeo.com/video/${videoId}`;
    }
    return url;
};

const defaultHeight = 315;

export const useCore = ({
    title,
    titleVariant,
    subtitle,
    subtitleVariant,
    body,
    bodyVariant,
    videoUrl,
    mediaType,
    images,
    imageSize,
    autoplay,
    muted,
    controls,
    ctas,
    layouts,
    layoutId,
    slotProps,
    isBuilder,
    contentWindow,
}) => {
    const Layout = useMemo(() => layouts.find(layout => layout.id === layoutId)?.component, [layoutId]);

    const mainContent = useMemo(() => {
        const _content = {};
        if (+mediaType === 1){ // image
            let _images = images;
            if (!Array.isArray(_images)) _images = [images];
            if (!_images.length) return {};
            _content.images = { 
                images:_images.map((image, i) => ({
                    preview_url: image,                
                    description: null,
                })),
                imageSize,
                ...slotProps?.gallery
            }
        } else {
            if (!videoUrl) return {};
            const params = [];
            if (autoplay) params.push('autoplay=1');
            if (muted) params.push('mute=1');
            if (!controls) params.push('controls=0');
            _content.video = {
                url: getEmbedUrl(videoUrl),
                params: params.join('&'),
                ...slotProps?.video
            }
        }
        return _content;
    }, [mediaType, images, imageSize, videoUrl, autoplay, muted, controls]);


    const handleCtaClick = useCallback((e, cta) => {
    }, []);

    const _slotProps = useMemo(() => ({
        ...slotProps,
        title: {variant: titleVariant, children: title, ...slotProps?.title},
        subtitle: {variant: subtitleVariant, children: subtitle, ...slotProps?.subtitle},
        body: {variant: bodyVariant, children: body, ...slotProps?.body},
        ctas: {ctas, ...slotProps?.ctaContainer},
        content: {...mainContent},
    }), [title, subtitle, body, ctas, mainContent]);

    const slots = useMemo(() => ({
        title: props => props?.children && <Typography {...props} />,
        subtitle: props => props?.children && <Typography {...props} />,
        body: props => props?.children && <Typography {...props} />,
        ctas: ({ctas, ...props}) => (
            !ctas?.length ? null :
                <Stack direction="row" spacing={2} {...props?.ctaContainer}>
                    {ctas?.filter(a => a.label)?.map((cta, index) => (
                        <Button 
                            key={`cta-${index}`} 
                            variant="contained"
                            onClick={isBuilder ? null : e => handleCtaClick(e, cta)} 
                            {...props?.cta}
                        >
                            {cta.label}
                        </Button>
                    ))}
                </Stack>
        ),
        video: ({url, params, ...props}) => (
            <Box position="relative" sx={{width: '100%'}}>
                {url &&
                    <iframe
                        src={`${url}${params ? `?${params}` : ''}`}
                        width="100%"
                        height={props?.video?.height || defaultHeight}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        style={{ border: 0, width: '100%', overflow: 'hidden' }}
                        {...props}
                    />
                }
                {isBuilder && <div style={{position: "absolute", top: 0, left:0, right: 0, bottom: 0}} />}
            </Box>
        ),
        images: ({images, ...props}) => (
            !images?.length ? null :
                <Gallery 
                    images={images} 
                    disabled={isBuilder} 
                    contentWindow={contentWindow} 
                    type="list"
                    size={{xs: 1}}
                    {...props}
                />
        ),
    }), [isBuilder, handleCtaClick]);

    return {
        mainContent,
        slotProps: _slotProps,
        slots,
        Layout,
    };
}