import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography, TextField, Box, Slider, InputAdornment } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/serviceWizardSlice';
import { step3 } from './stepList';

const Step3Increments = ({ data, formData, loading, serviceId }) => {
    const dispatch = useDispatch();
    const [localFormData, setLocalFormData] = useState(formData || {});

    // Update local form data when props change
    useEffect(() => {
        if (formData) {
            setLocalFormData(formData);
        }
    }, [formData]);

    // Handle form field changes
    const handleChange = useCallback((field, value) => {
        // Convert string values to numbers for number fields
        const parsedValue = field === 'block_minutes' || 
                           field === 'min_timeslots' || 
                           field === 'max_timeslots' || 
                           field === 'timeslots_for_token' || 
                           field === 'min_booking_notice' 
            ? parseInt(value, 10) || 0 
            : value;

        setLocalFormData(prev => {
            const updated = { ...prev, [field]: parsedValue };
            dispatch(updateFormData(updated));
            return updated;
        });
    }, [dispatch]);

    // Get step fields
    const fields = step3(localFormData);

    return (
        <Grid container spacing={3}>
            {fields.map(section => (
                <Grid item xs={12} key={section.id}>
                    <Typography variant="h6" gutterBottom>
                        {section.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                        {section.description}
                    </Typography>
                    <Grid container spacing={3}>
                        {section.fields.map(field => (
                            <Grid item xs={12} md={6} key={field.name}>
                                {field.component === "TextField" && (
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            {field.label}
                                        </Typography>
                                        <TextField
                                            fullWidth
                                            name={field.name}
                                            type="number"
                                            value={localFormData[field.name] || ''}
                                            onChange={(e) => handleChange(field.name, e.target.value)}
                                            required={field.required}
                                            margin={field.margin}
                                            disabled={loading}
                                            error={field.error}
                                            helperText={field.helperText}
                                            InputProps={{
                                                inputProps: field.inputProps,
                                                endAdornment: field.name === 'block_minutes' || field.name === 'min_booking_notice' ? (
                                                    <InputAdornment position="end">minutes</InputAdornment>
                                                ) : null
                                            }}
                                        />
                                        {field.name === 'block_minutes' && (
                                            <Box sx={{ px: 2, mt: 2 }}>
                                                <Slider
                                                    value={localFormData.block_minutes || 30}
                                                    onChange={(e, newValue) => handleChange('block_minutes', newValue)}
                                                    step={5}
                                                    marks
                                                    min={5}
                                                    max={240}
                                                    valueLabelDisplay="auto"
                                                    disabled={loading}
                                                />
                                            </Box>
                                        )}
                                        {field.name === 'min_booking_notice' && (
                                            <Box sx={{ px: 2, mt: 2 }}>
                                                <Slider
                                                    value={localFormData.min_booking_notice || 60}
                                                    onChange={(e, newValue) => handleChange('min_booking_notice', newValue)}
                                                    step={15}
                                                    marks
                                                    min={0}
                                                    max={1440} // 24 hours
                                                    valueLabelDisplay="auto"
                                                    disabled={loading}
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                )}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default Step3Increments;
