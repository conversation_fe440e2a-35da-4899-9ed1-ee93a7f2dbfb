import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  // Use the base path from the environment variable
  const BASE_PATH = env.VITE_BASE_PATH ? `${env.VITE_BASE_PATH}/` : '/';

  return {
    base: BASE_PATH,
    plugins: [react()],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern'
        }
      }
    },
    server: {
      // Force Vite to use port 5175 for consistency
      port: 5175,
      strictPort: false, // Allow fallback to another port if 5175 is in use

      // Configure WebSocket for HMR
      hmr: {
        protocol: 'ws',
        host: 'localhost',
      },

      // Proxy API requests to the development API
      proxy: {
        '/api': {
          target: env.VITE_API_URL ? env.VITE_API_URL.replace('/api', '') : 'https://api-dev.impactathleticsny.com',
          changeOrigin: true,
          secure: true,
        }
      }
    }
  }
});
