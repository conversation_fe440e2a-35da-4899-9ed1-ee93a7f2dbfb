import React, { useCallback } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Stack, Box, Typography, ListItem, ListItemText, ListItemIcon, ListItemButton, Chip, CircularProgress, circularProgressClasses } from '@mui/material';
import { FormItem } from '@siteboss-frontend/shared/components';
import { formatDate } from '@siteboss-frontend/shared/utils';

import styles from './Task.module.scss';
import { typography } from 'storybook/internal/theming';

const Graph = ({value}) => {
    return (
        <Box className={styles["status-graph"]}>
            <CircularProgress
                variant="determinate"
                sx={{
                    color: (theme) => theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800]
                }}
                size={48}
                thickness={2}
                value={100}
            />
            <CircularProgress
                thickness={3} 
                variant="determinate" 
                value={value} 
                color="secondary"
                size={48}
                sx={{
                    borderRadius: '50%',
                }}
            />
            <Typography variant="caption" component="p">
                {`${value}%`}
            </Typography>
        </Box>
    )
}

export const Task = ({ task, onChange, ...props }) => {
    const { t, language, isMobile } = useOutletContext();

    // Get priority color
    const getPriorityColor = useCallback(priority => {
        switch (priority) {
            case 'high':
                return 'error';
            case 'medium':
                return 'warning';
            case 'low':
                return 'info';
            default:
                return 'default';
        }
    }, []);

    return (
        <ListItem
            alignItems="flex-start"
            disablePadding
            divider
            secondaryAction={
                <Graph value={task.progress}/>
            }
        >
            <ListItemButton role={undefined} onClick={onChange}>
                <ListItemIcon>
                    <FormItem
                        component="Checkbox"
                        name="completed"
                        value="1"
                        checked={task.completed}
                        labelPlacement="start"
                        size="small"
                        label={null}
                        slotProps={{
                            typography: {m: 0}
                        }}
                    />
                </ListItemIcon>
                <ListItemText disableTypography
                    primary={
                        <>
                            <Typography
                                variant="body1"
                                sx={{
                                    mr: 1,
                                    textDecoration: task.completed ? 'line-through' : 'none',
                                    color: task.completed ? 'text.secondary' : 'text.primary'
                                }}
                            >
                                {task.title}
                            </Typography>
                            <Stack direction="row" useFlexGap spacing={1} alignItems="center">                            
                                <Chip label={formatDate(task.dueDate, language)} size="small" variant="outlined" />
                                <Chip label={task.priority} size="small" color={getPriorityColor(task.priority)} />
                            </Stack>
                        </>
                    }
                />

            </ListItemButton>
        </ListItem>
    );
};
