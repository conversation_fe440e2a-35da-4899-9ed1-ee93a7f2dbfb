import React, { useRef, useState, useCallback } from 'react';
import clsx from 'clsx';
import { Paper, useTheme } from '@mui/material';
import VsMonacoEditor, { loader } from '@monaco-editor/react';

import { shortHexToHex } from '../../../../utils';

export const MonacoEditor = ({ language = "markdown", value, errors, onChange, onBlur, ...props }) => {
    const theme = useTheme();
    const editorRef = useRef(null);

    const [focused, setFocused] = useState(false);

    const handleEditorDidMount = useCallback((editor, monaco) => {
        editor.onDidFocusEditorWidget(() => setFocused(true));
        editor.onDidBlurEditorWidget(() => {
            setFocused(false);
            if (onBlur) onBlur(editor);
        });
        /*editor.onDidBlurEditorText(() => {
            if (onBlur) onBlur(editor);
        });*/
        editorRef.current = editor;  
    }, [onBlur]);

    const handleEditorChange = useCallback((value, e) => {
        try {
            if (editorRef.current && onChange) onChange(e, value);
        } catch(error){
            console.log(error);
        }
    }, [onChange]);

    loader.init().then(monaco => {
        monaco.editor.defineTheme('markdownTheme', {
            base: theme.palette.mode === "dark" ? "vs-dark" : "vs",
            inherit: true,
            rules: [],
            colors: { 'editor.background': shortHexToHex(theme.palette.background.paper) },
        });
    });

    return (        
        <Paper variant="textField" className={clsx(focused ? 'Mui-focused' : '', errors ? 'Mui-error' : '')} {...props} sx={{ bgcolor: 'background.paper', width: '100%', height: '100%' }}>
            <VsMonacoEditor
                height="100%"
                width="100%"
                defaultLanguage={language}
                options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    //fontSize:"12px",
                    mouseWheelZoom: true,
                    smoothScrolling: true,
                    tabSize: 2,
                    //wordWrap: "on"
                }}
                loading={null}
                theme={"markdownTheme"}
                value={value}
                onChange={handleEditorChange}
                onMount={handleEditorDidMount}
                /*wrapperProps={{
                    onBlur: () => {
                        handleEditorBlur();
                    },
                }}*/
            />
        </Paper>
    );
}