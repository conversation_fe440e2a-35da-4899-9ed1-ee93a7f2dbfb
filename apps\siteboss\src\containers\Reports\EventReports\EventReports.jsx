import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Paper } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

export const EventReports = () => {
    const { t } = useOutletContext();

    return (
        <Container>
            <Title
                title={t('reports:eventReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:eventReports')}
                ]}
            />

            <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h5" gutterBottom>
                    Event Reports Content
                </Typography>
                <Typography variant="body1">
                    This page will contain event reports including:
                </Typography>
                <ul>
                    <li>Event Registrations</li>
                    <li>Event Attendance</li>
                    <li>Event Revenue</li>
                    <li>Event Popularity</li>
                    <li>Event Demographics</li>
                </ul>
            </Paper>
        </Container>
    );
};

export default EventReports;
