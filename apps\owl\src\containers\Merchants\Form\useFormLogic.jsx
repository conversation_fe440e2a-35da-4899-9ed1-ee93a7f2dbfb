import { useState, useEffect, useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useForm } from '@siteboss-frontend/shared/components';
import { useApi } from '@siteboss-frontend/shared';
import { fieldsByTab } from './fields';

import { tenantConfig } from '../../../components/KeycloakProvider/tenantConfig';

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

// flatten all fields for form logic (we add the tab id to group them again)
const fields = Object.entries(fieldsByTab).reduce((acc, [tabId, fields]) => {
    fields.forEach(field => {
        acc.push({...field, tabId});
    });
    return acc;
}, []);

export const useFormLogic = ({loading:parentLoading, id}) => {
    const { t } = useOutletContext();

    const [success, setSuccess] = useState(false);
    const [loading, setLoading] = useState(false);

    // these are the api hooks
    const apiParams = useMemo(() => [
        {params: {endpoint: '/clients', method: 'POST', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${id}`, method: 'PUT', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${id}`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${id}/billing-config`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
        {params: {endpoint: `/clients/${id}/brands`, method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}},
    ], [id]);

    const { fetchData:saveData, loading:formLoading, ErrorBar, LoadingBar } = useApi(apiParams[0]);
    const { fetchData:UpdateData, loading:updateLoading, ErrorBar:UpdateErrorBar, LoadingBar:UpdateLoadingBar } = useApi(apiParams[1]);
    const { fetchData:fetchMerchantData, data:merchantData, errors:merchantErrors, ErrorBar:MerchantErrorBar, loading:merchantLoading } = useApi(apiParams[2]);
    const { fetchData:fetchBillingData, data:billingData, errors:billingErrors, ErrorBar:BillingErrorBar, loading:billingLoading } = useApi(apiParams[3]);
    const { fetchData:fetchBrandsData, data:brandsData, errors:brandsErrors, ErrorBar:BrandsErrorBar, loading:brandsLoading } = useApi(apiParams[4]);

    // we send this function to the useForm hook to add the id (if it's an update) to the form values
    const formBeforeSubmit = formValues => {
        if (formValues) {
            const _values = [];
            if (id && !values.find(a=>a.name === 'id')) {
                _values.push({name: 'id', value: id});
            }
            return [...formValues, ..._values];
        }
        return formValues;        
    }

    // this is the function that gets called when the form is submitted, it sets the params (form values) for the api call. The api is called on a useEffect below
    const formSubmit = useCallback(async (formValues, setErrors) => {
        //if (formValues) setParams(formValues);
        if (formValues) {
            const _formValues = JSON.parse(JSON.stringify(formValues));
            // remove temp ids from fields so that the api knows it need to add them
            if (Array.isArray(_formValues?.licenses)) {
                // licences
                for (const license of _formValues.licenses) {
                    if (typeof license.id === "string") delete license.id;
                }
                // brands
                if (Array.isArray(_formValues?.brands)) {
                    for (const brand of _formValues.brands) {
                        if (typeof brand.id === "string") delete brand.id;
                    }
                }
                // contacts
                if (Array.isArray(_formValues?.contacts)) {
                    for (const contact of _formValues.contacts) {
                        if (typeof contact.id === "string") delete contact.id;
                    }
                }
            }

            try {
                setLoading(true);
                console.log('=== MERCHANT FORM SUBMISSION ===');
                console.log(_formValues);
                console.log('=====================================');

                /*const apiCall = id ? UpdateData : saveData;
                const response = await apiCall(_formValues);
                if (response.data) {
                    setSuccess(true);
                }*/

            } catch (error) {
                setErrors({form: t('error:default')});
            } finally {
                setLoading(false);
            }
        } else setErrors({form: t('error:default')});
    }, [t, saveData, UpdateData, id]);

    // this is the form hook, it handles the form state and actions like validation and submission
    const {values, errors, handleChange, handleSubmit, setFormValues } = useForm(fields, { onSubmit: formSubmit, onBeforeSubmit: formBeforeSubmit });

    // fetch the data if the id is set
    useEffect(() => {
        const _loadMerchant = async () => {
            if (id) {
                const result = await Promise.all([fetchMerchantData(), fetchBillingData(), fetchBrandsData()]);
                if (result) {
                    const item = {

                        // info
                        id: result[0].data.id,
                        name: `${result[0].data.name}`,
                        client_code: result[0].data.client_code,
                        email: result[0].data?.contact_info?.email,
                        phone: result[0].data?.contact_info?.phone,
                        address_line1: result[0].data?.address?.address_line1,
                        address_line2: result[0].data?.address?.address_line2,
                        //city: result[0].data?.address?.city,
                        //state_province_code: result[0].data?.address?.state_province_code,
                        postal_code: result[0].data?.address?.postal_code,
                        //country: result[0].data?.address?.country,
                        location: {
                            country: result[0].data?.address?.country,
                            state_province_code: result[0].data?.address?.state_province_code,
                            city: result[0].data?.address?.city,
                        },
                        company_type: result[0].data.company_type,

                        // setup
                        //allow_manual_upload: false,
                        rename_on_order_cancel: result[0].data?.order_config?.rename_on_order_cancel,
                        qa_weight_enabled: result[0].data?.qa_config?.qa_weight_enabled,
                        reject_unknown_sku: result[0].data?.order_config?.reject_unknown_sku,
                        upload_format: result[0].data?.api_config?.upload_format,


                        // contact: fetching from /Form/Contacts/useContacts
                        contacts: [],

                        // licenses: fetching from /Form/Licenses/useLicenses
                        licenses: [],

                        //billing
                        credit_limit: result[1]?.data?.financial_settings?.credit_limit,
                        billing_default_terms: result[1]?.data?.billing_configuration?.billing_default_terms?.id,
                        billing_frequency: result[1]?.data?.billing_configuration?.billing_frequency?.id,
                        billing_type: result[1]?.data?.billing_configuration?.billing_type?.id,
                        billing_method: result[1]?.data?.billing_configuration?.billing_method?.id,
                        billing_group_by: result[1]?.data?.billing_configuration?.billing_group_by?.id,
                        //send_to: result.data?.billing_config?.send_to,

                        // brands
                        /*
                        brands: result.data?.brands?.map?.(brand => ({
                            id: brand.id,
                            brand_name: brand.brand_name,
                            brand_code: brand.brand_code,
                            brand_phone: brand.brand_phone,
                            brand_from_email: brand.brand_from_email,
                            brand_from_name: brand.brand_from_name,
                        })) || [],
                        */
                    }

                    setFormValues(item);
                }
            }
        }
        _loadMerchant();
    }, [id, fetchMerchantData, setFormValues, fetchBillingData, fetchBrandsData]);

    // the stuff we'll be using in the form
    return {
        values,
        errors,
        success,
        loading: loading || formLoading || updateLoading || merchantLoading || billingLoading || brandsLoading || parentLoading,
        handleChange,
        handleSubmit,
        merchantErrors,
        merchantData,
        ErrorBar,
        LoadingBar,
        UpdateLoadingBar,
        MerchantErrorBar,
        UpdateErrorBar,
    }
}