import React from 'react';
import { useSelector } from 'react-redux';
import { Container, Grid2, Typography, Box } from '@mui/material';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, startOfDay, endOfDay, isToday, isWithinInterval, isSameDay } from 'date-fns';
import { formatDate } from '../../../utils/date';

import Events from './Events';
import MetaEvents from '../MetaEvents';

import styles from './Week.module.scss';

const hoursInDay = Array.from({ length: 24 }, (_, i) => i);

export const Week = ({
    currentDate, // the current date (Date object)
    events, // an array of events (optional)
    metaEvents, // an array of meta events (optional)
    colors, // an array of colors (optional)
    type, // the type of calendar 'week', 'day', or undefined (defaults to 'week')
    loading = false, // whether the data is loading
    disabled = false, // whether the calendar is disabled
    onEventClick, // function to handle event clicks (will open a modal with the event details if not provided) 
    ...props
}) => {
    const language = useSelector(state => state.language);
    const startDay = type === 'day' ? currentDate : startOfWeek(currentDate);
    const endDay = type === 'day' ? currentDate : endOfWeek(currentDate);
    const daysArray = eachDayOfInterval({ start: startDay, end: endDay });

    // we split the events into short and long events, short events go in the week view, long events go on top
    const longEvents = [], shortEvents = [];
    events.forEach(event => {
        if (isSameDay(event.startDate, event.endDate)) shortEvents.push(event);
        else longEvents.push(event);
    });
    
    if (!currentDate) return null;

    return (
        <>
            {longEvents.length > 0 &&
                <MetaEvents currentDate={currentDate} calendarType="week" size="small" metaEvents={longEvents} disabled={disabled} colors={colors} loading={loading}/>
            }
            <Container disableGutters>
                <Grid2 container>
                    <Grid2 size={{xs: 1}} />
                    {daysArray.map((day, i) => (
                        <Grid2 size="grow" key={`day-slot-${i}`} className={styles["day-slot"]} sx={{ 
                            border: theme => `1px solid ${theme.palette.divider}`, 
                            borderRightWidth: i === 6 ? undefined : 0,
                            boxShadow: theme => isToday(day) ? `inset 0 0 0 2px ${theme.palette.primary.light}` : undefined,
                        }}>
                            <Typography variant="subtitle2" sx={{ textAlign: 'center', py: 1 }}>
                                {formatDate(day, language.code, 'EEE')}<br/>
                                {format(day, 'd')}
                            </Typography>
                        </Grid2>
                    ))}
                </Grid2>
                <Grid2 container>
                    <Grid2 size={{xs: 1}} sx={{ minHeight: '100%' }}>
                        {hoursInDay.map(hour => (
                            <Box 
                                key={`hour-slot-${hour}`} 
                                className={styles["hour-slot"]} 
                                sx={{ borderRight: theme => `1px solid ${theme.palette.divider}!important`}}
                            >
                                <Typography variant="subtitle3" component="h6" sx={{ px: 1}}>
                                    {hour > 0 ? format(new Date(0, 0, 0, hour, 0), 'h aaa') : null}
                                </Typography>
                                <Box className={styles["offset-line"]} sx={{ borderBottom: 1, borderColor: 'divider' }}/>
                            </Box>
                        ))}
                    </Grid2>
                    {daysArray.map((day, i) => (
                        <Grid2 size="grow" key={`event-slot-${i}`} className={styles["day-slot"]} sx={{ borderRight: theme => `1px solid ${theme.palette.divider}` }}>
                            {hoursInDay.map(hour => (
                                <Box key={`hour-slot-${hour}`} className={styles['hour-slot']} sx={{ border: theme => `1px solid ${theme.palette.divider}`}} />
                            ))}
                            <Events 
                                currentDate={day} 
                                colors={colors} 
                                disabled={disabled}
                                events={shortEvents.filter(event => 
                                    isWithinInterval(endOfDay(day), { start: new Date(event.startDate), end: new Date(event.endDate) }) ||
                                    isWithinInterval(new Date(event.endDate), { start: startOfDay(day), end: endOfDay(day) })
                                )}
                                onEventClick={onEventClick}
                            />
                        </Grid2>
                    ))}
                </Grid2>
            </Container>
        </>
    );
};