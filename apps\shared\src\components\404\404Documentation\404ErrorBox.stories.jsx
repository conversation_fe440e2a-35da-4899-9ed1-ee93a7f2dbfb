import React from 'react';
import { expect, within } from 'storybook/test';
import { ErrorBox } from '../ErrorBox';

export default {
    title: "Shared/Components/404/ErrorBox",
    tags: ["autodocs"],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        width: {
            description: "Width of the error box in pixels",
            control: "number",
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 300 },
            }
        },
        noEffects: {
            description: "Whether to disable visual effects on the error box",
            control: "boolean",
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
            }
        }
    }
};

export const ErrorBoxSizes = {
    parameters: {
        docs: {
            description: {
                story: `
                    ### Error Box Sizes
                    This story demonstrates the ErrorBox component at different sizes.
                    - Small (200px)
                    - Medium (300px - Default)

                    The test verifies:
                    - Both error boxes render correctly
                    - Each has the correct width applied
                    - Each contains an SVG robot illustration
                    - The robot illustrations have the correct width
                `
            }
        }
    },
    render: (args, context) =>(
          <div>
              <div>
                  <context.storyDescription title={<h4>Small (200px)</h4>} />
                  <ErrorBox width={200} />
              </div>
              <div>
                  <context.storyDescription title={<h4>Medium (300px - Default)</h4>} />
                  <ErrorBox width={300} />
              </div>
          </div>
    ),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        
        await step('Confirm headings exist', async () => {
            const headings = canvas.getAllByRole('heading', { level: 4 });
            expect(headings.length).toBe(2);
            expect(headings[0].textContent).toBe('Small (200px)');
            expect(headings[1].textContent).toBe('Medium (300px - Default)');
        });
        
        await step('Confirm error boxes exist', async () => {
            const boxes = canvasElement.querySelectorAll('.MuiBox-root');
            expect(boxes.length).toBeGreaterThan(1);
            
            // Get the outer boxes (the ones with display: flex)
            const outerBoxes = Array.from(boxes).filter(box => {
                const style = window.getComputedStyle(box);
                return style.display === 'flex' && style.justifyContent === 'center';
            });
        
            expect(outerBoxes.length).toBe(2);
        });
    
        await step('Confirm SVGs exist inside error boxes', async () => {
            const svgs = canvasElement.querySelectorAll('svg');
            expect(svgs.length).toBe(2);
        });
    
        await step('Confirm SVGs have correct widths', async () => {
            const svgs = canvasElement.querySelectorAll('svg');
            expect(svgs[0].getAttribute('width')).toBe('200');
            expect(svgs[1].getAttribute('width')).toBe('300');
        });
  }
};

export const ErrorBoxEffects = {
  parameters: {
    docs: {
      description: {
        story: `
## Error Box Effects
This story demonstrates the ErrorBox component with and without visual effects.

The test verifies:
- Both error boxes render correctly
- One has effects enabled (default)
- One has effects disabled (noEffects=true)
- Both contain an SVG robot illustration
        `
      }
    }
  },
  render: () => (
    <div style={{ display: 'flex', gap: '30px', justifyContent: 'center' }}>
      <div>
        <h4>With Effects (Default)</h4>
        <ErrorBox width={250} noEffects={false} />
      </div>
      <div>
        <h4>Without Effects</h4>
        <ErrorBox width={250} noEffects={true} />
      </div>
    </div>
  ),
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);
    
    await step('Confirm headings exist', async () => {
      const headings = canvas.getAllByRole('heading', { level: 4 });
      expect(headings.length).toBe(2);
      expect(headings[0].textContent).toBe('With Effects (Default)');
      expect(headings[1].textContent).toBe('Without Effects');
    });
    
    await step('Confirm error boxes exist', async () => {
      const boxes = canvasElement.querySelectorAll('.MuiBox-root');
      expect(boxes.length).toBeGreaterThan(1);
      
      // Get the outer boxes (the ones with display: flex)
      const outerBoxes = Array.from(boxes).filter(box => {
        const style = window.getComputedStyle(box);
        return style.display === 'flex' && style.justifyContent === 'center';
      });
      
      expect(outerBoxes.length).toBe(2);
    });
    
    await step('Confirm SVGs exist inside error boxes', async () => {
      const svgs = canvasElement.querySelectorAll('svg');
      expect(svgs.length).toBe(2);
    });
    
    await step('Confirm both SVGs have the same width', async () => {
      const svgs = canvasElement.querySelectorAll('svg');
      expect(svgs[0].getAttribute('width')).toBe('250');
      expect(svgs[1].getAttribute('width')).toBe('250');
    });
    
    await step('Confirm visual difference between boxes', async () => {
      const boxes = canvasElement.querySelectorAll('.MuiBox-root');
      
      // Get the outer boxes (the ones with display: flex)
      const outerBoxes = Array.from(boxes).filter(box => {
        const style = window.getComputedStyle(box);
        return style.display === 'flex' && style.justifyContent === 'center';
      });
      
      // In dark mode, the first box should have a background and box-shadow
      // In light mode, both might look the same, so we're just checking they exist
      expect(outerBoxes[0]).toBeTruthy();
      expect(outerBoxes[1]).toBeTruthy();
      
      // We can't reliably test the actual visual difference in this automated test
      // as it depends on the theme mode, but we can verify both rendered
    });
  }
};