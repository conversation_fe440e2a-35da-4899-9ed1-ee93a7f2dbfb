import React from 'react';
import { FormatListBulletedRounded as ListIcon } from '@mui/icons-material';

/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/

export const widgetIcon = ListIcon;
export const layouts = [
    {
        id: 1,
        name: 'Default',
        icon: <ListIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {}
    },
];