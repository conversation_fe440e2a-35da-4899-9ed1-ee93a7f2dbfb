import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { saveInfo, updateSubscriptions, updateFetchCounter } from '../../store/reducers/apiCacheSlice';

const deepSortObject = obj => {
    return obj;
    /*if (Array.isArray(obj)) return obj.map(deepSortObject);
    else if (obj !== null && typeof obj === 'object') {
        return Object.keys(obj).sort().reduce((result, key) => {
            result[key] = deepSortObject(obj[key]);
            return result;
        }, {});
    }
    return obj;*/
};

export const useCache = () => {
    const dispatch = useDispatch();
    const apiCache = useSelector(state => state.apiCache);

    const getCachedData = useCallback(key => {
        const cacheData = apiCache.cacheData?.[key] || null;
        if (!cacheData) return null;
        return apiCache.cache[cacheData.index];
    }, [apiCache.cache, apiCache.cacheData]);

    const setCachedData = useCallback((key, data) => {
        dispatch(saveInfo({ key, data }));
    }, [dispatch]);

    const updateCacheCounter = useCallback(() => {
        dispatch(updateFetchCounter());
    }, [dispatch]);

    const updateCacheSubscriptions = useCallback((key, value) => {
        dispatch(updateSubscriptions({ key, value }));
    }, [dispatch]);

    const getCacheKey = useCallback((endpoint, data) => {
        return `${endpoint}:${JSON.stringify(deepSortObject(data || {}))}`
    }, []);

    return { getCachedData, setCachedData, updateCacheCounter, updateCacheSubscriptions, getCacheKey };
};