import React from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Box, Paper, useMediaQuery } from '@mui/material';
import { Title, LoadingBar, ErrorBar, ErrorBox } from '../../../../components';

import { useProductLayout } from './useProductLayout';

export const Default = ({
    onAddToCart,
    slotProps,
    isEdit = false,
    replacePageTitle,               // the page title to be replaced with the item's name
    ...props 
    // everything else comes from the context
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const {
        errors,
        setErrors,
        loading,
        hideQuantity,
        slotProps: hookSlotProps,
        hides,
        slots,
        product, 
        media,
        modalSize,
        fullPage,
    } = useProductLayout({slotProps, isEdit, onAddToCart, replacePageTitle, ...props});


    if (!product?.id && !loading) {
        return (
            <Stack direction={isMobile ? "column" : "row"} spacing={2} useFlexGap alignItems="center" justifyContent="center" pb={3}>
                <ErrorBox width={200} />
                <Title title={t("error:oops")} subtitle={t("product:notFound")}></Title>
            </Stack>
        );
    }

    return (
        <>
            <Paper 
                elevation={24}
                square
                {...hookSlotProps?.header}
                sx={{...slotProps?.header?.sx, width:'100%', boxShadow:'none', pb:2, position: (modalSize && !isMobile) ? 'sticky' : 'relative', top: 0, zIndex: theme => theme.zIndex.appBar - 1}}
            >
                {product?.id && slots.title({})}
            </Paper>
            <Stack direction="column" spacing={2} useFlexGap position="relative" {...hookSlotProps?.body}>
                {loading &&
                    <LoadingBar type='linear' sx={{height: '2px', position: 'sticky', top: 0, zIndex: theme => theme.zIndex.appBar}} />
                }
                <ErrorBar open={Boolean(errors)} message={errors} onClose={()=>setErrors(null)} />
                {product?.events?.length === 1 &&
                    <Box sx={{width: '100%', overflow: 'hidden'}}>
                        {slots.eventInfo({slotProps: slotProps?.events})}
                    </Box>
                }
                {media?.length > 0 && !hides.gallery &&
                    <Box sx={{width: '100%'}}>
                        {slots.gallery(slotProps?.gallery)}
                    </Box>
                }
                {!hides.variants && slots.variants({slotProps: slotProps?.variants})}
                {!hides.addons && slots.addons({slotProps: slotProps?.addons})}
                {slots.events({
                    slotProps: {events: slotProps?.events, eventUsers: slotProps?.eventUsers},
                    layouts: {
                        events: {...hookSlotProps?.events, hide: hides?.events}, 
                        eventUsers: {...hookSlotProps?.eventUsers, hide: hides?.eventUsers}
                    },
                })}
                {!hides.giftCards && slots.giftCards({slotProps: slotProps?.giftcards})}
                {/* this should always be the last item*/
                !hides.memo && slots.memo({slotProps: slotProps?.memo})}
            </Stack>
            
            <Stack 
                direction="column" 
                spacing={2} 
                {...hookSlotProps?.footer}
                sx={{position: (modalSize && !isMobile) ? 'sticky' : isMobile ? 'fixed' : 'relative', left: 0, bottom: 0, zIndex: theme => theme.zIndex.appBar, width: '100%', mt: 2}}
            >
                {product?.id && slots.cartButton({})}
            </Stack>
        </>
    );
};