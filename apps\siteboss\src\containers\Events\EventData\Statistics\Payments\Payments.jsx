import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { createCurrencyFormatter, dataOrderBy, dataPaginate, dataSearch, formatDate } from '@siteboss-frontend/shared/utils';
import { DataTable, Modal, PrintFormats } from '@siteboss-frontend/shared/components';
//import { OrderPreview } from '../../../../../components/Previews';

export const Payments = ({eventData, loading:parentLoading, ...props}) => {
    const { t, language, currency, isMobile } = useOutletContext();
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRows, setTotalRows] = useState(0);
    const [order, setOrder] = useState({column: 'id', direction: 'desc'});
    const [selected, setSelected] = useState([]);
    const [searchText, setSearchText] = useState('');
    const [rows, setRows] = useState([]);
    const [open, setOpen] = useState(false);

    const columns = useMemo(() => [
        { field: 'order_id', headerName: `${t('order:order')} #`, width: 90 },
        { field: 'user', headerName: t('pos:forUser'), minWidth: 200, sortable: false, flex: 1, 
            valueGetter: (value, row) => ({user_fname: row?.user_fname, user_lname: row?.user_lname}),
            valueFormatter: value => `${value?.user_fname} ${value?.user_lname}`,
            sortComparator: (v1, v2,) => {
                const fullName1 = `${v1.user_fname || ''} ${v1.user_lname || ''}`.toLowerCase();
                const fullName2 = `${v2.user_fname || ''} ${v2.user_lname || ''}`.toLowerCase();
                return fullName1.localeCompare(fullName2);
            },
        },
        { field: 'created_at', headerName: t('order:date'), width: 100, valueFormatter: value => formatDate(new Date(value), language) },
        { field: 'order_item_final_price', headerName: t('order:expected'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        //{ field: 'total_price', headerName: t('order:total'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        { field: 'payment_total', headerName: t('order:payments'), type: 'number', width: 110, valueFormatter: value => currencyFormatter.format(value, currency) },
        { field: 'balance_due', headerName: t('order:balance'), type: 'number', width: 110, 
            valueGetter: (value, row) => ({total_price: row.total_price, payment_total: row.payment_total}),
            valueFormatter: value => currencyFormatter.format(+value.total_price - +value.payment_total, currency),
            sortComparator: (v1, v2,) => {
                const balance1 = +v1.total_price - +v1.payment_total;
                const balance2 = +v2.total_price - +v2.payment_total;
                return balance1 - balance2;
            },
        },
    ], [t, language, currency, currencyFormatter]);

    const handleRowSelection = model => {
        const _model = [];
        rows?.forEach(row => {
            if (model.includes(row.id)) {
                _model.push(row);
                setOpen(true);
            }
        });
        setSelected(_model);
    }

    useEffect(() => {
        let _data = JSON.parse(JSON.stringify(eventData?.orders || []));
        if (order.column && order.direction) _data = dataOrderBy(order.column, order.direction, _data);
        if (page && pageSize) _data = dataPaginate(page, pageSize, _data);
        //if (searchText) _data = dataSearch(searchText, _data, ['first_name', 'last_name', 'group_member_role_name']);

        setTotalRows(eventData?.orders?.length || 0);
        setTotalPages(Math.ceil((eventData?.orders?.length || 0) / pageSize));
        setRows(_data);
    }, [page, pageSize, order, searchText, eventData?.orders]);

    //if (!eventData?.orders?.length) return null;

    return (
        <>
            <DataTable
                hideFooterSelectedRowCount
                hideFooter
                rows={rows}
                columns={columns}
                onRowSelectionModelChange={model => handleRowSelection(model)}
                rowSelectionModel={selected.map(s => s.order_id)}
                order={order}
                page={page}
                pageSize={pageSize}
                totalPages={totalPages}
                totalRows={totalRows}
                setPage={setPage}
                setPageSize={setPageSize}
                setOrder={setOrder}
                setSearchText={setSearchText}
                loading={parentLoading}
                disableSearch
                allRows={eventData?.orders || []}
                //disableRowSelectionOnClick
                pageSizeOptions={[10, 20, 50, 100]}
            />
            <Modal
                open={open}
                onClose={e =>setOpen(false)}
                title={`${t('order:order')} #${selected?.[0]?.order_id}`}
                maxWidth="md"
                fullScreen={isMobile}
            >
                <PrintFormats.orders.Preview id={selected?.[0]?.order_id} />
            </Modal>
        </>
    );
}