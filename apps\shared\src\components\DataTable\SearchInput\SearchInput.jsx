import React from 'react';
import { useTranslation } from 'react-i18next';
import { TextField, InputAdornment, useMediaQuery, Stack } from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

export const SearchInput = ({ value, label, variant, size, onSearchChange, disabled, slots, slotProps, children, ...props }) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const SearchField = slots?.searchInput || TextField;
    const Filters = slots?.filters || <React.Fragment />;

    return (
        <Stack direction={isMobile ? "column" : "row"} spacing={1} useFlexGap justifyContent='flex-end' sx={{ width: '100%', mb: 1 }}>
            {Filters}
            <SearchField
                label={label || t("general:search")}
                variant={variant || "outlined"}
                defaultValue={value}
                disabled={disabled}
                onChange={e => onSearchChange(e.target.value)}
                fullWidth
                margin="normal"
                size={size || undefined}
                sx={{
                    maxWidth: isMobile ? '100%' : '400px',
                    ...props?.sx
                }}
                slotProps={{
                    ...slotProps,
                    input: {
                        ...slotProps?.input,
                        endAdornment: <InputAdornment position="end"><SearchIcon /></InputAdornment>,
                    }
                }}
            />
            {children}
        </Stack>
    );    
}