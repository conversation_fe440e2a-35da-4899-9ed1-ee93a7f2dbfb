import { TimerOutlined as Timer<PERSON>con } from '@mui/icons-material';
/*
Example to generate an icon:
        CmsIcon({
            iconProps: {height: 48, width: 96, direction: 'row', spacing: 3},
            elements: [
                {type: 'circle', size: 20, my: 'auto'},
                {type: 'group', direction: 'column', spacing: 3, children: [
                    {type: 'title', width: '100%'},
                    {type: 'text', width: '100%'},
                    {type: 'text', width: '50%'},
                    {type: 'text', width: '25%'},
                ]},
            ],
        })
*/
export const widgetIcon = TimerIcon;
export const layouts = [
    {
        id: 1,
        name: 'Default',
        icon: <TimerIcon sx={{color: theme => theme.palette.text.secondary}}/>,
        slotProps: {
            countDown: {
            },
        }
    },
];