import { useState, useMemo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { usePageRouter } from '../../../utils/usePageRouter';
import { useApi } from '../../../../api/useApi';
import { uuid, formatSlug } from '../../../../utils';
import { useCart } from '../../../hooks/useCart';


// LOOK FOR ANOTHER WAY TO DO THIS (in ssr company is not set)
const apiParams = [
    {enableCache: true, keepCache: true, params: {endpoint: "/cms/my_theme", params: {cms_version: 2}}},
];

export const useMenu = ({ 
    items = [], 
    fetchPages = false,
    showCart = false,
    cartType = "icon",
    showProfile = false,
    profileType = "icon",
    shopId = null,
    isBuilder = false,
}) => {
    const { t } = useTranslation();
    const menuLoaded = useRef(false);
    
    useCart({shopId, isBuilder});

    const { fetchData, data, loading } = useApi(apiParams[0]);
    const { router } = usePageRouter({pageRouterId: shopId});
    const [open, setOpen] = useState(false);

    const handleDrawerToggle = open => e => {
        if (e.type === 'keydown' && (e.key === 'Tab' || e.key === 'Shift')) return;
        setOpen(open);
    };

    const menuItems = useMemo(() => {
        if (!data && !items?.length) return [];
        let _items = [...items].filter(a => a.title && a.url).map(a => ({...a, id: uuid()}));
        if (data) {
            _items=[
                ...data?.[0]?.pages?.filter(a => a.add_to_menu === 1 && a.full_path)
                    .sort((a, b) => a.sort_order - b.sort_order)
                    .map(b => ({id: b.id, title: b.title, url: `/${formatSlug(b.full_path)}`})), 
                ..._items
            ];
        }
        if (showCart) {
            _items.push({
                id: uuid(), 
                isCart: true, 
                title: t("general:cart"), 
                url: `/${formatSlug(`${router?.slug?.value}/${router?.cart?.value}`)}`, 
                icon: cartType === "icon" ? "ShoppingCartOutlined" : null
            });
        }
        if (showProfile) {
            _items.push({
                id: uuid(), 
                isProfile: true, 
                title: t("user:profile"), 
                url: "/profile", 
                icon: profileType === "icon" ? "PersonOutlineOutlined" : null
            });
        }
        return _items;
    }, [data, items, showCart, showProfile, cartType, profileType, t, router]);

    useEffect(() => {
        if (fetchPages && !menuLoaded.current) {
            fetchData();
            menuLoaded.current = true;
        }
    }, [fetchData, fetchPages]);

    return {
        items: menuItems,
        loading,
        open,
        handleDrawerToggle,
    }
}