import React from 'react';
import useBreakpoint from '../useBreakpoint';
import ImageList from './ImageList';
import Image<PERSON>arousel from './ImageCarousel';
/*
    - type: the type of gallery to display. Can be "carousel", "list" or "masonry"
    - size: an object with the following structure: {xs: 1, sm: 2, md: 3, lg: 4, xl: 5}
    - images: an array with the following structure: {preview_url: "https://example.com/image.jpg", description: "Image description"}
    - imageSize: an object with the following structure: {width: "100%", height: 200}
    - objectFit: a string with the value "cover", "contain", "fill", "scale-down", "none", "initial", or "inherit". This will determine how the image will be displayed
    - objectPosition: a string with the value "center", "top", "bottom", "left", "right", "top left", "top right", "bottom left", "bottom right". This will determine the position of the image
    - disabled: a boolean to disable the gallery
    - contentWindow: the window object to use for the media queries
    - ...props: any other props to pass to the gallery
*/
export const Gallery = ({ type = "carousel", size, images = [], imageSize, objectFit = "cover", objectPosition = "center", contentWindow, ...props }) => {
    const { getClosestBreakpoint, isMobile } = useBreakpoint({contentWindow});
    const _size = getClosestBreakpoint(size);
    const cols = props?.cols || (_size ? size[_size] : null) || (isMobile ? 1 : 4);

    switch (type) {
        case "carousel":
            return <ImageCarousel images={images} imageSize={imageSize} objectFit={objectFit} objectPosition={objectPosition} {...props} />;
        case "list":
            return <ImageList images={images} imageSize={imageSize} objectFit={objectFit} objectPosition={objectPosition} variant="standard" cols={cols} {...props} />;
        default:
            return <ImageList images={images} imageSize={imageSize} objectFit={objectFit} objectPosition={objectPosition} variant="masonry" cols={cols} {...props} />;
    }
}