import React, { useEffect, useState, useCallback } from 'react';
import { Grid, Typography } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { updateFormData } from '../../../../store/reducers/discountWizardSlice';
import { step7 } from './stepList';
import StyledRadioGroup from '../components/StyledRadioGroup';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

const Step7Combo = ({ data }) => {
    const dispatch = useDispatch();
    const formData = useSelector(state => state.discountWizard.formData);
    const [localCombinable, setLocalCombinable] = useState(1);

    // Get combinable value from form data
    useEffect(() => {
        if (formData?.combinable !== undefined) {
            setLocalCombinable(formData.combinable);
        } else if (data?.combinable !== undefined) {
            setLocalCombinable(data.combinable);
        }
    }, [formData, data]);

    // Handle combinable change
    const handleCombinableChange = useCallback((e) => {
        const newValue = parseInt(e.target.value);
        setLocalCombinable(newValue);
        dispatch(updateFormData({
            ...formData,
            combinable: newValue
        }));
    }, [dispatch, formData]);

    // Save fields to the form context - only run once when data changes
    useEffect(() => {
        // Save the step data to the form context
        if (data && !formData?.initialized) {
            const fields = step7(data);
            if (fields && fields.length > 0) {
                const updatedData = { ...formData, initialized: true };
                fields.forEach(field => {
                    updatedData[field.name] = field.value;
                });
                dispatch(updateFormData(updatedData));
            }
        }
    }, [data, dispatch]);

    return (
        <Grid container spacing={3}>
            <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                    Set combination rules for this discount
                </Typography>

                <Typography variant="body1" paragraph>
                    Choose whether this discount can be used in combination with other discounts.
                    If you select "No", this discount cannot be applied if another discount is already active.
                </Typography>

                <StyledRadioGroup
                    name="combinable"
                    label="Can this discount be combined with others?"
                    required
                    options={[
                        {
                            id: 1,
                            value: 1,
                            label: 'Yes',
                            icon: <CheckIcon />
                        },
                        {
                            id: 0,
                            value: 0,
                            label: 'No',
                            icon: <CloseIcon />
                        }
                    ]}
                    value={localCombinable}
                    onChange={handleCombinableChange}
                    helperText="Choose whether this discount can be combined with other discounts"
                />
            </Grid>
        </Grid>
    );
};

export default Step7Combo;
