import React from 'react';
import { Layout } from '../Layout';

export default {
    title: 'Shared/Layout/Layout',
    component: Layout,
    tags:['autodocs'],
    argTypes: {
        basic: {
            description: "Flag to indicate if the basic layout should be used (no menu, header, or footer)",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag to determine if the basic layout should be used."
            }
        },
        moduleId: {
            description: "The module id that's being used (this is to load the right menu items)",
            control: 'number',
            table: {
                type: { summary: "number" },
                defaultValue: { summary: 1 },
                detail: "The module ID to load the appropriate menu items."
            }
        },
        disableGutters: {
            description: "Flag to indicate if the gutters (margins) should be disabled in the main content area",
            control: 'boolean',
            table: {
                type: { summary: "boolean" },
                defaultValue: { summary: false },
                detail: "A boolean flag to control the visibility of gutters in the main content area."
            }
        },
        children: {
            description: "Content to be displayed inside the main container",
            control: 'text',
            table: {
                type: { summary: "ReactNode" },
                defaultValue: { summary: "undefined" },
                detail: "The content to be rendered inside the main container."
            }
        }
    },
};

// First story: Playground with only required props
export const Playground = {
    args: {
        basic: false,
        moduleId: 1,
        disableGutters: false,
        children: <div>Content goes here</div>
    }
};

// Following stories to illustrate each significant prop
export const BasicLayout = {
    args: {
        basic: true,
        moduleId: 1,
        disableGutters: false,
        children: <div>Content goes here</div>
    }
};

