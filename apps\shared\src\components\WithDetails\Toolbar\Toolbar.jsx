import React, {useEffect, useCallback, useRef} from 'react';
import { useTranslation } from 'react-i18next';
import { AppBar, Tab, Box, useMediaQuery } from '@mui/material';
import { TabList } from '@mui/lab';
import Mobile from './Mobile';
import WithScrollEffect from '../../WithScrollEffect';

// This is a toolbar component that can be used in the WithDetails component, by default it is pinned to the right.
export const Toolbar = ({
    tabs, // array of objects with this format: { id, slug, icon }. Slug should be a key in the translation file, icon should be a MUI icon component
    selectedTab, // the id of the selected tab
    setSelectedTab, // function to set the selected tab
    excludedMobileTabs, // array of tab ids to exclude from the mobile toolbar (because mobile toolbars need to have as few tabs as possible)
    onSelection, // function to be called when a tab is selected
    parentRef, // a ref to the parent container
    stickyTop = 55, // the top position of the toolbar when sticky
    hideIcons, // boolean to hide the icons in the toolbar
    slotProps, // slotProps to be passed to the internal components
    loading, // loading state of the parent component
    children,
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const ref = useRef(null);    
    const toolbarTop = useRef(0);

    const handleSelection = useCallback((e, tab) => {
        if (tab?.id) { 
            tab = String(tab.id);
            if (setSelectedTab) setSelectedTab(tab);
        }
        if (onSelection) onSelection(e, tab);
    }, [onSelection, setSelectedTab]);

    useEffect(() => {
        if (ref.current) toolbarTop.current = ref.current?.offsetTop || 0;
    }, [ref.current]);


    if (isMobile) {
        return (
            <Mobile 
                selectedTab={selectedTab} 
                onSelection={handleSelection} 
                tabs={excludedMobileTabs ? tabs.filter(a => !excludedMobileTabs.includes(a.id)) : tabs} 
            />
        );
    }

    return (
        <WithScrollEffect threshold={toolbarTop.current/* isMobile ? 415 : 380*/} targetElement={parentRef?.current || window} effect={{elevation: 5}}>
            <AppBar ref={ref} position="sticky" sx={{zIndex: theme => theme.zIndex.appBar - 1, top: stickyTop || 55, ...props?.sx}} elevation={16}>
                <Box sx={{/*borderBottom: 1,*/ borderColor: 'divider', maxWidth: '100%', width: 'fit-content', ml: 'auto', ...slotProps?.wrapper?.sx}}>
                    <TabList 
                        aria-label="options" 
                        variant="scrollable" 
                        scrollButtons="auto" 
                        onChange={onSelection}
                        profile={1}
                        {...slotProps?.tabs}
                    >
                        {tabs.map(tab => (
                            <Tab 
                                key={`option-tab-${tab.id}`} 
                                label={t(`${tab.slug}`)} 
                                icon={hideIcons ? null : tab.icon} 
                                iconPosition={isMobile ? 'top' : 'start'}
                                value={`${tab.id}`} 
                                disabled={loading}
                                {...slotProps?.tab}
                            />
                        ))}
                    </TabList>
                </Box>
                {children}
            </AppBar>            
        </WithScrollEffect>
    );
}