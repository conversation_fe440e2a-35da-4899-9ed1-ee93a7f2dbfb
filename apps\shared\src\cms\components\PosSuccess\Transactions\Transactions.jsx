import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack, Typography, Paper, Grid2, Box, Divider, useMediaQuery } from '@mui/material';

import { createCurrencyFormatter, toCamelCase, capitalize, formatDate } from '../../../../utils';

const Line = ({label, value, children}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    return (
        <Stack direction="column" spacing={0} useFlexGap sx={{mb: 1}}>
            <Typography variant="caption" color="textSecondary">{t(label, label)}</Typography>
            <Typography variant={isMobile ? "body2" : "body1"}>{t(value, value)}</Typography>
            {children}
        </Stack>
    );
}

export const Transactions = ({payments, slotProps, children}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);    
    const currencyFormatter = createCurrencyFormatter(language, currency, 0);
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    
    return (
        <Grid2 container spacing={1} {...slotProps?.transactions}>
            {payments?.map(payment => (
                <Grid2 size={{xs: 12, lg: 6}} key={`payment-${payment.id}`}>
                    <Stack component={Paper} variant="outlined" direction="row" spacing={{xs: 1, lg: 2}} sx={{p: 2, pl: {xs: 1, lg: 2}}} useFlexGap>
                        <Stack direction="column" useFlexGap spacing={0} sx={{textAlign: "center", alignSelf: "center", minWidth: 70}}>
                            <Typography variant="caption" color="textSecondary">{formatDate(payment.date, language.code, "EEE")}</Typography>
                            <Typography variant="h5">{formatDate(payment.date, language.code, "d")}</Typography>
                            <Typography variant="caption" color="textSecondary">{formatDate(payment.date, language.code, "MMM yyy")}</Typography>
                        </Stack>
                        <Divider orientation="vertical" flexItem />
                        <Box sx={{flexGrow: 1}}>
                            <Line label={`order:paymentMethod`} value={`pos:paymentMethods.${toCamelCase(payment.transaction_payment_method_name)}`} />
                            {payment?.cc_number && <Line label={capitalize(payment.cc_type)} value={payment.cc_number} />}
                            <Line label="order:transactionId" value={payment.id} />
                            {payment?.cc_trans_id && <Line label="creditCard:gatewayTransaction" value={payment.cc_trans_id} />}
                            {payment?.check_number && <Line label="check:number" value={payment.check_number} />}
                            {payment?.check_name && <Line label="check:name" value={payment.check_name} />}
                            {payment?.check_number && <Line label="check:number" value={payment.check_number} />}
                        </Box>
                        <Stack direction="column" useFlexGap spacing={2} sx={{textAlign: "right"}}>
                            <Typography variant="h4" color="success.light">{currencyFormatter.format(payment.amount)}</Typography>
                            {payment?.change > 0 &&
                                <Stack direction="column" useFlexGap spacing={0.5}>
                                    <Typography variant="subtitle3">
                                        {!isMobile && <Typography component="span" variant="caption" color="textSecondary">{`${t("order:tendered")}: `}</Typography>}
                                        {currencyFormatter.format(payment.transaction_response.amount)}
                                        {isMobile && <Typography component="span" variant="caption" color="textSecondary">{` (${t("order:tendered").substring(0, 1)})`}</Typography>}
                                    </Typography>
                                    <Typography variant="subtitle3">
                                        {!isMobile && <Typography component="span" variant="caption" color="textSecondary">{`${t("order:change")}: `}</Typography>}
                                        {currencyFormatter.format(payment.change)}
                                        {isMobile && <Typography component="span" variant="caption" color="textSecondary">{` (${t("order:change").substring(0, 1)})`}</Typography>}
                                    </Typography>
                                </Stack>
                            }
                        </Stack>
                    </Stack>
                </Grid2>
            ))}
            {children}
        </Grid2>
    );
}