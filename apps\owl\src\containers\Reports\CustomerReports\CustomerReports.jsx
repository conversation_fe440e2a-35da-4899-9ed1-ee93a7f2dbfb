import { useState } from 'react';
import {
  Grid2 as <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  <PERSON>,
  Button
} from '@mui/material';
import {
  People as CustomerIcon,
  TrendingUp as GrowthIcon,
  Receipt as BillingIcon,
  AccountBalance as BalanceIcon,
  PersonAdd as NewCustomerIcon,
  Repeat as ReturnIcon
} from '@mui/icons-material';
import { LineChart, PieChart, DataTable, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';
import { ReportCard, ReportFilters } from '../components';

export const CustomerReports = () => {
  const [filters, setFilters] = useState({});

  // Mock data for charts
  const customerGrowthData = {
    data: [
      {
        id: 'New Customers',
        data: [
          { x: 'Jan', y: 12 },
          { x: 'Feb', y: 18 },
          { x: 'Mar', y: 15 },
          { x: 'Apr', y: 22 },
          { x: 'May', y: 28 },
          { x: 'Jun', y: 25 },
          { x: 'Jul', y: 31 }
        ]
      },
      {
        id: 'Returning Customers',
        data: [
          { x: 'Jan', y: 45 },
          { x: 'Feb', y: 52 },
          { x: 'Mar', y: 48 },
          { x: 'Apr', y: 58 },
          { x: 'May', y: 65 },
          { x: 'Jun', y: 62 },
          { x: 'Jul', y: 71 }
        ]
      }
    ]
  };

  const customerStatusData = {
    data: [
      { id: 'Active', value: 156, color: '#4CAF50' },
      { id: 'Inactive', value: 23, color: '#FF9800' },
      { id: 'Overdue', value: 8, color: '#F44336' },
      { id: 'New', value: 31, color: '#2196F3' }
    ]
  };

  const customersTableData = {
    columns: [
      { field: 'id', headerName: 'Customer ID', width: 120 },
      { field: 'name', headerName: 'Company Name', width: 200 },
      { field: 'status', headerName: 'Status', width: 120 },
      { field: 'orders', headerName: 'Total Orders', width: 120 },
      { field: 'revenue', headerName: 'Total Revenue', width: 150 },
      { field: 'lastOrder', headerName: 'Last Order', width: 150 }
    ],
    rows: [
      { id: 'CUST-001', name: 'Acme Corporation', status: 'Active', orders: 47, revenue: '$58,750.00', lastOrder: '2024-01-14' },
      { id: 'CUST-002', name: 'Tech Solutions Inc', status: 'Active', orders: 23, revenue: '$20,450.00', lastOrder: '2024-01-15' },
      { id: 'CUST-003', name: 'Global Industries', status: 'Overdue', orders: 89, revenue: '$187,200.00', lastOrder: '2023-12-28' },
      { id: 'CUST-004', name: 'StartUp Logistics', status: 'New', orders: 3, revenue: '$1,350.00', lastOrder: '2024-01-15' },
      { id: 'CUST-005', name: 'Enterprise Ltd', status: 'Active', orders: 156, revenue: '$498,600.00', lastOrder: '2024-01-13' }
    ]
  };

  const reports = [
    {
      title: 'Customer Activity Report',
      description: 'Track customer order frequency and engagement patterns',
      icon: <CustomerIcon />,
      color: '#4CAF50',
      metrics: [
        { label: 'Active Customers', value: '156' },
        { label: 'Avg Orders/Month', value: '12.3' },
        { label: 'Engagement Rate', value: '78.5%' }
      ],
      tags: ['Activity', 'Engagement', 'Frequency'],
      lastUpdated: '2 hours ago'
    },
    {
      title: 'Customer Growth Analysis',
      description: 'Monitor new customer acquisition and retention rates',
      icon: <GrowthIcon />,
      color: '#2196F3',
      metrics: [
        { label: 'New This Month', value: '31' },
        { label: 'Retention Rate', value: '89.2%' },
        { label: 'Growth Rate', value: '+18.5%' }
      ],
      tags: ['Growth', 'Acquisition', 'Retention'],
      lastUpdated: '1 hour ago'
    },
    {
      title: 'Billing Summary Report',
      description: 'Overview of customer billing status and payment patterns',
      icon: <BillingIcon />,
      color: '#FF9800',
      metrics: [
        { label: 'Total Billed', value: '$766,350' },
        { label: 'Paid On Time', value: '94.2%' },
        { label: 'Overdue Amount', value: '$12,450' }
      ],
      tags: ['Billing', 'Payments', 'Collections'],
      lastUpdated: '30 minutes ago'
    },
    {
      title: 'Account Status Overview',
      description: 'Current status of all customer accounts',
      icon: <BalanceIcon />,
      color: '#9C27B0',
      metrics: [
        { label: 'Active Accounts', value: '156' },
        { label: 'Inactive Accounts', value: '23' },
        { label: 'At Risk', value: '8' }
      ],
      tags: ['Status', 'Accounts', 'Risk'],
      lastUpdated: '1 hour ago'
    }
  ];

  const customFilters = [
    {
      key: 'customerType',
      label: 'Customer Type',
      type: 'select',
      options: [
        { value: 'enterprise', label: 'Enterprise' },
        { value: 'small-business', label: 'Small Business' },
        { value: 'startup', label: 'Startup' }
      ]
    },
    {
      key: 'accountStatus',
      label: 'Account Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'overdue', label: 'Overdue' },
        { value: 'new', label: 'New' }
      ]
    }
  ];

  const handleViewReport = (reportTitle) => {
    console.log('Viewing report:', reportTitle);
  };

  const handleDownloadReport = (reportTitle) => {
    console.log('Downloading report:', reportTitle);
  };

  const handlePrintReport = (reportTitle) => {
    console.log('Printing report:', reportTitle);
  };

  return (
    <Box>
      {/* Filters */}
      <ReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={() => setFilters({})}
        dateRange={true}
        customerFilter={true}
        locationFilter={true}
        customFilters={customFilters}
      />

      {/* Key Metrics */}
      <MetricCardGroup sx={{ mb: 4 }}>
        <MetricCard
          title="Total Customers"
          value="218"
          change="+31"
          changeType="positive"
          icon={<CustomerIcon />}
          color="primary"
        />
        <MetricCard
          title="New Customers"
          value="31"
          change="+18.5%"
          changeType="positive"
          icon={<NewCustomerIcon />}
          color="success"
        />
        <MetricCard
          title="Customer Retention"
          value="89.2%"
          change="+2.3%"
          changeType="positive"
          icon={<ReturnIcon />}
          color="info"
        />
        <MetricCard
          title="Avg Revenue/Customer"
          value="$3,514"
          change="+$247"
          changeType="positive"
          icon={<BillingIcon />}
          color="warning"
        />
      </MetricCardGroup>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Customer Growth Trends
              </Typography>
              <Box sx={{ height: 300 }}>
                <LineChart data={customerGrowthData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Customer Status Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <PieChart data={customerStatusData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Report Cards */}
      <Typography variant="h5" sx={{ mb: 3 }}>
        Available Reports
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {reports.map((report, index) => (
          <Grid size={{ xs: 12, md: 6, lg: 3 }} key={index}>
            <ReportCard
              {...report}
              onView={() => handleViewReport(report.title)}
              onDownload={() => handleDownloadReport(report.title)}
              onPrint={() => handlePrintReport(report.title)}
            />
          </Grid>
        ))}
      </Grid>

      {/* Customer Summary Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Customer Summary
            </Typography>
            <Button variant="outlined" size="small">
              View All Customers
            </Button>
          </Box>
          <DataTable
            columns={customersTableData.columns}
            rows={customersTableData.rows}
            pageSize={5}
            disableSelectionOnClick
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default CustomerReports;
