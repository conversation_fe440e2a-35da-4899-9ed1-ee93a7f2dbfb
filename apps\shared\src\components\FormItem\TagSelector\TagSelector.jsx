import React, { useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Button, useMediaQuery } from '@mui/material';

import { SuccessBar } from '../../Snacks';
import Modal from '../../Modal';
import ItemSelector from '../../ItemSelector';

import { useTags } from './useTags';
import Form from './Form';
import NewButton from './NewButton';

export const TagSelector = ({ 
    label, // the label for the field
    name, // the name of the field
    required, // if the field is required
    errors: initialErrors, // the errors for the field
    disabled, // if the field is disabled
    loading: loadingState, // the loading status of the form so we can disable the fields
    value: initialValue, // the value of the field
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    type = "chip", // the component type

    items, // optional array of items to display, if not provided, it will fetch from the server
    selectedColor,  // color of selected items
    color, // color of items
    variant, // variant of the component
    extraFields, // extra fields to add to the api requests
    ...props 
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const ref = useRef(null);

    const { 
        data,
        loading,
        errorBars,
        addSuccess,
        setAddSuccess,
        fetchTags,
        formFields,
        formErrors,
        handleChange,
        handleSubmit,
        handleSelection,
        handleDialogToggle,
        formModalOpen,
    } = useTags({ name, onChange, extraFields });

    useEffect(() => {
        if (!items) fetchTags();
    }, [items, fetchTags]);

    return (
        <Box ref={ref} sx={{position: 'relative', my: 2, ...props?.sx}}>
            {errorBars?.map((ErrorBar, i) => ErrorBar && <ErrorBar key={i} />)}
            {/*loading && <LoadingBar type='linear' sx={{height: '2px', position: 'absolute', top: -16}} />*/}

            <Modal
                open={formModalOpen}
                onClose={handleDialogToggle(false)}
                maxWidth="xs"
                fullScreen={isMobile}
                triggerRef={ref}
                aria-describedby={t("tag:newTag")}
                slots={{
                    actions: (
                        <>
                            <Button onClick={handleDialogToggle(false)}>{t("general:cancel")}</Button>
                            <Button variant="primary" onClick={handleSubmit} disabled={loadingState}>{t("general:save")}</Button>
                        </>
                    ),
                }}
            >
                <Form onChange={handleChange} errors={formErrors} fields={formFields} loading={loading || loadingState} />
            </Modal>

            {addSuccess && <SuccessBar onClose={e=>setAddSuccess(null)} />}

            <ItemSelector
                type={type}
                items={items || data}
                selectedItems={initialValue}
                onSelect={handleSelection}
                selectedColor={selectedColor}
                color={color}
                variant={variant}
                disabled={loading || loadingState || disabled}
                size="small"
                errors={initialErrors}
                label={t("tag:tags")}
                name={name}
            >
                <NewButton 
                    type={type}
                    disabled={loading || loadingState || disabled} 
                    onClick={handleDialogToggle(true)} 
                    showEmptyText={(!items || items?.length === 0) && (!data || data?.length === 0)} 
                />                
            </ItemSelector>
        </Box>
    );
};