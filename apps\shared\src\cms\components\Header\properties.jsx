import store from '../../../store/store';
import { generateTitle } from "../../../utils";

const company = store.getState().company;

export const properties = [
    {
        name: 'title',
        label: 'builder:component.header.title',
        component: "TextField",
        value: company.name || "",
        size: "small",
        required: true,
        margin: "normal",
    },
    {
        name: 'logo',
        label: 'builder:component.header.logo',
        component: "MediaManager",
        value: company?.logoUrl ? [company.logoUrl] : '',
        size: "large",
        margin: "normal",
        mediaType: 9,
        accept: 'image/*',
        showThumbs: true,
    },
    /*
    {
        name: 'logoPosition',
        label: 'builder:component.header.logo.position.title',
        component: "Select",
        options: [
            {id: 'default', slug: 'builder:component.header.logo.position.default'},
            {id: 'start', slug: 'builder:component.header.logo.position.start'},
            {id: 'center', slug: 'builder:component.header.logo.position.center'},
            {id: 'end', slug: 'builder:component.header.logo.position.end'},
        ],
        value: 'default',
        size: "small",
        margin: "normal",
    },
    */
    {
        name: 'sticky',
        label: 'builder:component.header.sticky',
        component: "Switch",
        value: true,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'showMenu',
        label: 'builder:component.header.showMenu',
        component: "Switch",
        value: true,
        checked: true,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
    },
    {
        name: 'fetchPages',
        label: 'builder:component.menu.fetchPages',
        component: "Switch",
        value: true,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
        condition: { field: 'showMenu', value: true}
    },
    {
        name: 'showCart',
        label: 'builder:component.menu.showCart',
        component: "Switch",
        value: true,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
        condition: { field: 'showMenu', value: true}
    },
    {
        name: 'cartType',
        label: 'builder:component.menu.optionType',
        component: "Select",
        options: [
            {id: 'icon', slug: 'builder:component.menu.optionTypes.icon'},
            {id: 'menu', slug: 'builder:component.menu.optionTypes.menu'},
        ],
        value: 'icon',
        size: "small",
        margin: "normal",
        condition: [{ field: 'showCart', value: true}, { field: 'showMenu', value: true}]
    },
    {
        name: 'shopId',
        label: 'builder:component.menu.shopId',
        component: "local.ShopId",
        value: 0,
        size: "small",
        margin: "normal",
        //helperText: 'builder:component.menu.shopIdHelperText',
        condition: { field: 'showCart', value: true}
    },
    {
        name: 'showProfile',
        label: 'builder:component.menu.showProfile',
        component: "Switch",
        value: true,
        checked: false,
        size: "small",
        margin: "normal",
        sx: {ml: 1},
        condition: { field: 'showMenu', value: true}
    },
    {
        name: 'profileType',
        label: 'builder:component.menu.optionType',
        component: "Select",
        options: [
            {id: 'icon', slug: 'builder:component.menu.optionTypes.icon'},
            {id: 'menu', slug: 'builder:component.menu.optionTypes.menu'},
        ],
        value: 'icon',
        size: "small",
        margin: "normal",
        condition: [{ field: 'showProfile', value: true}, { field: 'showMenu', value: true}]
    },
    {
        name: 'menuItems',
        multiple: true,
        component: "local.MultiField",
        dividerTop: 'builder:component.menu.customItems',
        value: [],
        children: [{
            name: 'title',
            label: 'builder:component.menu.title',
            component: "TextField",
            value:  generateTitle(),
            size: "small",
            margin: "normal",
        }, 
        {
            name: 'url',
            label: 'builder:component.menu.url',
            component: "TextField",
            value:  '#',
            size: "small",
            margin: "normal",
        },
        {
            name: 'icon',
            label: 'builder:component.button.icon.title',
            component: "IconSelector",
            value: '',
            size: "small",
            fullWidth: true,
            variant: 'outlined',
            margin: "normal",
        }],
        condition: { field: 'showMenu', value: true}
    },    
];
