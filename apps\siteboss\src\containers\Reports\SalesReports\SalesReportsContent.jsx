import React, { useState, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
    Container, Typography, Paper, Box, Grid, Tab, Card, CardContent,
    Button, FormControl, InputLabel, Select, MenuItem, TextField,
    useMediaQuery, useTheme
} from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { enUS, es } from 'date-fns/locale';
import {
    ShoppingBagOutlined as ProductsIcon,
    CategoryOutlined as CategoriesIcon,
    PaymentOutlined as PaymentMethodsIcon,
    TrendingUpOutlined as TrendsIcon,
    StarOutlined as TopSellingIcon,
    AttachMoneyOutlined as RevenueIcon,
    ShoppingCartOutlined as OrdersIcon,
    ReceiptOutlined as TransactionsIcon,
    PercentOutlined as ConversionIcon,
    DownloadOutlined as DownloadIcon,
    PrintOutlined as PrintIcon,
    FilterAltOutlined as FilterIcon
} from '@mui/icons-material';
import {
    Title, WithScrollEffect, MetricCard, MetricCardGroup,
    LineChart, PieChart, SuccessBar, ErrorBar, DataTable
} from '@siteboss-frontend/shared/components';
// import translation
import { useTranslation } from 'react-i18next';


const SalesReportsContent = () => {
    const { t, language } = useOutletContext();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    // State for active tab
    const [tabValue, setTabValue] = useState('overview');

    // State for filters
    const [startDate, setStartDate] = useState(new Date(new Date().setDate(new Date().getDate() - 30)));
    const [endDate, setEndDate] = useState(new Date());
    const [productCategory, setProductCategory] = useState('all');
    const [paymentMethod, setPaymentMethod] = useState('all');
    const [showFilters, setShowFilters] = useState(false);

    // State for success/error messages
    const [successMessage, setSuccessMessage] = useState('');
    const [errorMessage, setErrorMessage] = useState('');

    // Mock data for charts
    const mockRevenueData = {
        data: [
            {
                id: "Revenue",
                data: [
                    { x: "Jan", y: 42500 },
                    { x: "Feb", y: 38200 },
                    { x: "Mar", y: 45600 },
                    { x: "Apr", y: 51200 },
                    { x: "May", y: 49800 },
                    { x: "Jun", y: 55400 },
                    { x: "Jul", y: 58900 }
                ]
            }
        ]
    };

    const mockCategoryData = {
        data: [
            { id: "Apparel", value: 35 },
            { id: "Equipment", value: 25 },
            { id: "Accessories", value: 20 },
            { id: "Nutrition", value: 15 },
            { id: "Other", value: 5 }
        ]
    };

    // Mock data for tables
    const mockProductsData = [
        { id: 1, name: "Premium Training Shirt", category: "Apparel", price: "$45.00", quantity: 256, revenue: "$11,520.00" },
        { id: 2, name: "Performance Running Shoes", category: "Footwear", price: "$120.00", quantity: 187, revenue: "$22,440.00" },
        { id: 3, name: "Training Resistance Bands", category: "Equipment", price: "$35.00", quantity: 312, revenue: "$10,920.00" },
        { id: 4, name: "Protein Powder (2lb)", category: "Nutrition", price: "$59.99", quantity: 203, revenue: "$12,177.97" },
        { id: 5, name: "Workout Water Bottle", category: "Accessories", price: "$24.99", quantity: 428, revenue: "$10,695.72" }
    ];

    // Define tabs for sales reports
    const reportTabs = useMemo(() => [
        {
            id: 'overview',
            name: t('reports:overview'),
            icon: <TrendsIcon />
        },
        {
            id: 'products',
            name: t('reports:salesByProduct'),
            icon: <ProductsIcon />
        },
        {
            id: 'categories',
            name: t('reports:salesByCategory'),
            icon: <CategoriesIcon />
        },
        {
            id: 'payment',
            name: t('reports:salesByPaymentMethod'),
            icon: <PaymentMethodsIcon />
        },
        {
            id: 'topSelling',
            name: t('reports:topSellingProducts'),
            icon: <TopSellingIcon />
        }
    ], [t]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleFilterApply = () => {
        // In a real implementation, this would fetch filtered data
        console.log('Applying filters:', { startDate, endDate, productCategory, paymentMethod });
        setSuccessMessage(t('reports:filtersApplied'));

        // Clear success message after 3 seconds
        setTimeout(() => {
            setSuccessMessage('');
        }, 3000);
    };

    const handleExport = () => {
        // In a real implementation, this would export data
        console.log('Exporting data');
        setSuccessMessage(t('reports:dataExported'));

        // Clear success message after 3 seconds
        setTimeout(() => {
            setSuccessMessage('');
        }, 3000);
    };

    const handlePrint = () => {
        // In a real implementation, this would print data
        console.log('Printing data');
        window.print();
    };

    // Filter component - reused across tabs
    const FilterPanel = () => (
        <Paper sx={{ p: 2, mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                    {t('reports:filters')}
                </Typography>
                <Button
                    startIcon={<FilterIcon />}
                    onClick={() => setShowFilters(!showFilters)}
                    size="small"
                >
                    {showFilters ? t('reports:hideFilters') : t('reports:showFilters')}
                </Button>
            </Box>

            {showFilters && (
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6} md={3}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                            <DatePicker
                                label={t('reports:startDate')}
                                value={startDate}
                                onChange={(newValue) => setStartDate(newValue)}
                                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                            />
                        </LocalizationProvider>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                            <DatePicker
                                label={t('reports:endDate')}
                                value={endDate}
                                onChange={(newValue) => setEndDate(newValue)}
                                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                            />
                        </LocalizationProvider>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth size="small">
                            <InputLabel id="category-select-label">{t('reports:category')}</InputLabel>
                            <Select
                                labelId="category-select-label"
                                value={productCategory}
                                label={t('reports:category')}
                                onChange={(e) => setProductCategory(e.target.value)}
                            >
                                <MenuItem value="all">{t('reports:allCategories')}</MenuItem>
                                <MenuItem value="apparel">Apparel</MenuItem>
                                <MenuItem value="footwear">Footwear</MenuItem>
                                <MenuItem value="equipment">Equipment</MenuItem>
                                <MenuItem value="nutrition">Nutrition</MenuItem>
                                <MenuItem value="accessories">Accessories</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth size="small">
                            <InputLabel id="payment-select-label">{t('reports:paymentMethod')}</InputLabel>
                            <Select
                                labelId="payment-select-label"
                                value={paymentMethod}
                                label={t('reports:paymentMethod')}
                                onChange={(e) => setPaymentMethod(e.target.value)}
                            >
                                <MenuItem value="all">{t('reports:allPaymentMethods')}</MenuItem>
                                <MenuItem value="credit">Credit Card</MenuItem>
                                <MenuItem value="debit">Debit Card</MenuItem>
                                <MenuItem value="cash">Cash</MenuItem>
                                <MenuItem value="transfer">Bank Transfer</MenuItem>
                                <MenuItem value="other">Other</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} display="flex" justifyContent="flex-end">
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleFilterApply}
                        >
                            {t('reports:apply')}
                        </Button>
                    </Grid>
                </Grid>
            )}
        </Paper>
    );

    // Summary stats component - reused across tabs
    const SummaryStats = () => (
        <MetricCardGroup>
            <MetricCard
                title={t('reports:totalRevenue')}
                value="$342,500"
                icon={<RevenueIcon />}
                color="blue"
                trend="+12% from last month"
                width={3}
            />
            <MetricCard
                title={t('reports:totalOrders')}
                value="4,287"
                icon={<OrdersIcon />}
                color="green"
                trend="+8% from last month"
                width={3}
            />
            <MetricCard
                title={t('reports:averageOrderValue')}
                value="$79.89"
                icon={<TransactionsIcon />}
                color="orange"
                trend="+3% from last month"
                width={3}
            />
            <MetricCard
                title={t('reports:conversionRate')}
                value="3.2%"
                icon={<ConversionIcon />}
                color="purple"
                trend="+0.5% from last month"
                width={3}
            />
        </MetricCardGroup>
    );

    // Revenue chart component
    const RevenueChart = () => (
        <Card sx={{ mb: 4 }}>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        {t('reports:revenueOverTime')}
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 300, minWidth: { xs: '600px', md: 'auto' }, overflow: 'auto' }}>
                    <LineChart
                        data={mockRevenueData}
                        curve="natural"
                        area={{ opacity: 0.4 }}
                        margin={{ top: 20, right: 20, bottom: 60, left: 80 }}
                        grid={{ enableY: true }}
                        axis={{
                            bottom: {
                                tickSize: 5,
                                tickPadding: 5,
                                tickRotation: 0,
                                legend: 'Month',
                                legendOffset: 36,
                                legendPosition: 'middle'
                            },
                            left: {
                                tickSize: 5,
                                tickPadding: 5,
                                tickRotation: 0,
                                legend: 'Revenue ($)',
                                legendOffset: -60,
                                legendPosition: 'middle'
                            }
                        }}
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Category chart component
    const CategoryChart = () => (
        <Card sx={{ mb: 4 }}>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        {t('reports:salesByCategory')}
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 300, minWidth: { xs: '600px', md: 'auto' }, overflow: 'auto' }}>
                    <PieChart
                        data={mockCategoryData}
                        margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                        innerRadius={0.5}
                        padAngle={0.7}
                        cornerRadius={3}
                        activeOuterRadiusOffset={8}
                        borderWidth={1}
                        borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
                        arcLinkLabelsSkipAngle={10}
                        arcLinkLabelsTextColor="#333333"
                        arcLinkLabelsThickness={2}
                        arcLinkLabelsColor={{ from: 'color' }}
                        arcLabelsSkipAngle={10}
                        arcLabelsTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
                        legend={{
                            anchor: 'right',
                            direction: 'column',
                            justify: false,
                            translateX: 0,
                            translateY: 0,
                            itemsSpacing: 2,
                            itemWidth: 100,
                            itemHeight: 20,
                            itemTextColor: '#999',
                            itemDirection: 'left-to-right',
                            itemOpacity: 1,
                            symbolSize: 18,
                            symbolShape: 'circle'
                        }}
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Products table component
    const ProductsTable = () => (
        <Card sx={{ mb: 4 }}>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        {t('reports:topSellingProducts')}
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 400, width: '100%' }}>
                    <DataTable
                        rows={mockProductsData}
                        columns={[
                            { field: 'id', headerName: 'ID', width: 70 },
                            { field: 'name', headerName: t('reports:productName'), flex: 1, minWidth: 200 },
                            { field: 'category', headerName: t('reports:category'), width: 150 },
                            { field: 'price', headerName: t('reports:price'), width: 120 },
                            { field: 'quantity', headerName: t('reports:quantitySold'), width: 150 },
                            { field: 'revenue', headerName: t('reports:revenue'), width: 150 }
                        ]}
                        pageSize={5}
                        rowsPerPageOptions={[5, 10, 25]}
                        checkboxSelection={false}
                        disableRowSelectionOnClick
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Overview Tab Content
    const OverviewTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <RevenueChart />
            <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                    <CategoryChart />
                </Grid>
                <Grid item xs={12} md={6}>
                    <Card sx={{ mb: 4, height: '100%' }}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                {t('reports:salesHighlights')}
                            </Typography>
                            <Box sx={{ mt: 2 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    🔥 {t('reports:bestSellingCategory')}: Apparel
                                </Typography>
                                <Typography variant="subtitle1" gutterBottom>
                                    📈 {t('reports:fastestGrowingCategory')}: Nutrition (+24%)
                                </Typography>
                                <Typography variant="subtitle1" gutterBottom>
                                    💰 {t('reports:highestRevenueProduct')}: Performance Running Shoes
                                </Typography>
                                <Typography variant="subtitle1" gutterBottom>
                                    🌟 {t('reports:highestRatedProduct')}: Premium Training Shirt (4.8/5)
                                </Typography>
                                <Typography variant="subtitle1" gutterBottom>
                                    📊 {t('reports:averageOrderItems')}: 2.3 items
                                </Typography>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
            <ProductsTable />
        </>
    );

    // Products Tab Content
    const ProductsTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <ProductsTable />
        </>
    );

    // Categories Tab Content
    const CategoriesTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <CategoryChart />
        </>
    );

    // Payment Methods Tab Content
    const PaymentMethodsTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <Card sx={{ mb: 4 }}>
                <CardContent>
                    <Typography variant="h6" gutterBottom>
                        {t('reports:salesByPaymentMethod')}
                    </Typography>
                    <Typography variant="body1">
                        {t('reports:paymentMethodsDescription')}
                    </Typography>
                </CardContent>
            </Card>
        </>
    );

    // Top Selling Tab Content
    const TopSellingTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <ProductsTable />
        </>
    );

    return (
        <Container>
            <Title
                title={t('reports:salesReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:salesReports')}
                ]}
            />

            {successMessage && <SuccessBar message={successMessage} />}
            {errorMessage && <ErrorBar message={errorMessage} />}

            <TabContext value={tabValue}>
                <WithScrollEffect threshold={0} targetElement={window} effect={{ elevation: 3 }}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                        <TabList
                            onChange={handleTabChange}
                            variant="scrollable"
                            scrollButtons="auto"
                            aria-label="sales report tabs"
                            sx={{
                                '& .MuiTab-root': {
                                    display: 'flex',
                                    flexDirection: isMobile ? 'column' : 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minHeight: isMobile ? '72px' : '48px',
                                }
                            }}
                        >
                            {reportTabs.map((tab) => (
                                <Tab
                                    key={tab.id}
                                    label={tab.name}
                                    value={tab.id}
                                    icon={tab.icon}
                                    iconPosition={isMobile ? "top" : "start"}
                                />
                            ))}
                        </TabList>
                    </Box>
                </WithScrollEffect>

                <TabPanel value="overview" sx={{ p: 0 }}>
                    <OverviewTab />
                </TabPanel>

                <TabPanel value="products" sx={{ p: 0 }}>
                    <ProductsTab />
                </TabPanel>

                <TabPanel value="categories" sx={{ p: 0 }}>
                    <CategoriesTab />
                </TabPanel>

                <TabPanel value="payment" sx={{ p: 0 }}>
                    <PaymentMethodsTab />
                </TabPanel>

                <TabPanel value="topSelling" sx={{ p: 0 }}>
                    <TopSellingTab />
                </TabPanel>
            </TabContext>
        </Container>
    );
};

export default SalesReportsContent;
