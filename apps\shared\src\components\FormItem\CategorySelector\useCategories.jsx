
import { useMemo, useState, useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useApi } from "../../../api";
import useForm from "../../useForm";

export const useCategories = ({
    name, // the name of the field
    categoryType = "cms", // the type of categories to fetch
    onChange, // callback function to handle the selection of categories
    extraFields, // extra fields to include in the api requests
    initialValue, // initial value for the field
}) => {
    const { t } = useTranslation();

    const apiParams = useMemo(() => [
        // cms page router
        {enableCache: true, params: {endpoint: "/cms/site/page/category", method: "POST", data: {max_records: 1000, include_children: true, ...extraFields}}},
        {params: {endpoint: "/cms/site/page/category/add", method: "POST", data: {...extraFields}}},
        // product categories
        {enableCache: true, params: {endpoint: "/category", method: "POST", data: {max_records: 1000, include_children: true, ...extraFields}}},
        {params: {endpoint: "/category/add", method: "POST", data: {...extraFields}}},
    ], [extraFields]);

    const {fetchData: fetchCmsCategories, data: cmsCategories, loading: cmsCategoriesLoading} = useApi(apiParams[0]);
    const {fetchData: addCmsCategory, loading: addCmsCategoryLoading, ErrorBar: AddCmsCategoryErrorBar} = useApi(apiParams[1]);
    const {fetchData: fetchProductCategories, data: productCategories, loading: productCategoriesLoading} = useApi(apiParams[2]);
    const {fetchData: addProductCategory, loading: addPRoductCategoryLoading, ErrorBar: AddProductCategoryErrorBar} = useApi(apiParams[3]);

    const [selectedItems, setSelectedItems] = useState(initialValue);
    const [addSuccess, setAddSuccess] = useState(false);
    const [formModalOpen, setFormModalOpen] = useState(false);

    const fields = useMemo(() => {
        let options = cmsCategories?.categories;
        if (categoryType === "products") options = productCategories?.categories;
        options = options ? options?.map(a => ({value: a.id, label: a.name})) : [];

        return [
            {name: 'parent_id', label: 'category:parentId', required: false, value: null, options, component: "Select", margin: "normal"},
            {name: 'name', type: 'text', label: 'category:name', required: true, value: '', component: "TextField", margin: "normal"},
            {name: 'slug', type: 'text', label: 'category:slug', required: false, value: '', component: "TextField", margin: "normal"},
        ];
    }, [categoryType, cmsCategories, productCategories]);


    const handleDialogToggle = useCallback((value = false) => e => {
        setFormModalOpen(value);
    }, []);

    const handleSelection = useCallback(values => {
        if (onChange) {
            const e = {target: {name, value: values.map(a => a?.id || a)}};
            onChange(e);
        }
    }, [onChange, name]);

    const handleFormSubmit = useCallback(async values => {
        let endpoints = [addCmsCategory, fetchCmsCategories];
        if (categoryType === "product") endpoints = [addProductCategory, fetchProductCategories];
        try {
            const res = await endpoints[0](values);
            if (res?.errors) {
                console.error(res.errors);
            } else if (res?.data) {
                setSelectedItems(prev => [...prev, res.data?.[0].id]);
                await endpoints[1]({___: new Date().getTime()});
                setAddSuccess(true);

                //setFormModalOpen(false);
            }
        } catch (error) {
            console.error(error);
        }
    }, [categoryType, addCmsCategory, addProductCategory, fetchCmsCategories, fetchProductCategories]);

    const handleFormValidate = useCallback(fields => {
        let data = cmsCategories?.categories;
        if (categoryType === "products") data = productCategories?.categories;

        let _error = {};
        fields.forEach(field => {
            if (field.required && !field.value) _error[field.name] = t("error:required");
            // checks that the name or slug does not already exist in the same level
            if (data && data.some(a => a?.[field.name] === field.value && +a.parent_id === +field.parent_id)) _error[field.name] = t("error:exists");
        });

        return _error;
    }, [categoryType, cmsCategories, productCategories]);

    useEffect(() => {
        //if (selectedItems) handleSelection(selectedItems);
    }, [selectedItems, handleSelection]);

    const {values, errors, handleChange, handleSubmit, resetForm } = useForm(fields, { validate: handleFormValidate, onSubmit: handleFormSubmit, onAfterSubmit: () => {
        resetForm();
    }});

    return {
        data: categoryType === "products" ? productCategories?.categories : cmsCategories?.categories,
        loading: cmsCategoriesLoading || addCmsCategoryLoading || productCategoriesLoading || addPRoductCategoryLoading,
        fetchCategories: categoryType === "products" ? fetchProductCategories : fetchCmsCategories,
        addSuccess,
        setAddSuccess,
        errorBars: [AddCmsCategoryErrorBar, AddProductCategoryErrorBar],
        formFields: values,
        formErrors: errors,
        handleChange,
        handleSubmit,
        handleSelection,
        handleDialogToggle,
        formModalOpen,
        selectedItems,
    }
}
