import React, { useCallback, useMemo, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Stack, Box, List, ListItem, ListItemText, IconButton, Tab, Divider, Tooltip } from '@mui/material';
import { TabContext, TabList } from '@mui/lab';
import { AddOutlined as AddIcon, FilterListOutlined as FilterIcon } from '@mui/icons-material';

import Header from '../common/Header';
import { Task } from './Task';

// Mock data - in a real app, this would come from an API
const mockTasks = [
    { id: 1, title: 'Review new user registrations', priority: 'high', dueDate: '2023-10-18', completed: false, progress: 0 },
    { id: 2, title: 'Update product inventory', priority: 'medium', dueDate: '2023-10-20', completed: false, progress: 30 },
    { id: 3, title: 'Prepare monthly sales report', priority: 'high', dueDate: '2023-10-25', completed: false, progress: 60 },
    { id: 4, title: 'Schedule social media posts', priority: 'low', dueDate: '2023-10-19', completed: false, progress: 20 },
    { id: 5, title: 'Follow up with new clients', priority: 'medium', dueDate: '2023-10-17', completed: false, progress: 10 },
    { id: 6, title: 'Update website content', priority: 'low', dueDate: '2023-10-22', completed: false, progress: 45 },
    { id: 7, title: 'Respond to customer inquiries', priority: 'high', dueDate: '2023-10-16', completed: true, progress: 100 },
    { id: 8, title: 'Organize team meeting', priority: 'medium', dueDate: '2023-10-15', completed: true, progress: 100 },
];

const tabs = [
    { id: 0, slug: 'widget:tasks.all' },
    { id: 1, slug: 'widget:tasks.highPriority' },
    { id: 2, slug: 'widget:tasks.today' },
];

export const TasksWidget = ({ settings = {}, isEditing, ...props }) => {
    const { t, isMobile } = useOutletContext();
    const [tabValue, setTabValue] = useState(0);
    const [showCompleted, setShowCompleted] = useState(settings.showCompleted !== undefined ? settings.showCompleted : false);
    const [showUpcoming, setShowUpcoming] = useState(settings.showUpcoming !== undefined ? settings.showUpcoming : true);

    const handleTabChange = useCallback((e, newValue) => {
        setTabValue(newValue);
    }, []);

    // Filter tasks based on tab and settings
    const filteredTasks = useMemo(() => mockTasks.filter(task => {
        if (tabValue === 0) { // All tasks
            return (task.completed && showCompleted) || (!task.completed && showUpcoming);
        } else if (tabValue === 1) { // High priority
            return task.priority === 'high' && (
                (task.completed && showCompleted) || (!task.completed && showUpcoming)
            );
        } else if (tabValue === 2) { // Today
            const today = new Date().toISOString().split('T')[0];
            return task.dueDate === today && (
                (task.completed && showCompleted) || (!task.completed && showUpcoming)
            );
        }
        return false;
    }), [tabValue, showCompleted, showUpcoming]);

    return (
        <Stack data-cy="tasks-widget" direction="column" spacing={2} useFlexGap sx={{ height: '100%' }}>
            <Header title={t('widget:tasks.title')}>
                <Stack direction="row" spacing={0} useFlexGap>
                    <Tooltip title={t('general:filter')}>
                        <IconButton size="small" aria-label={t('general:filter')}>
                            <FilterIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t('general:addNew')}>
                        <IconButton size="small" aria-label={t('general:addNew')}>
                            <AddIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Stack>
            </Header>

            <TabContext value={tabValue}>
                <TabList 
                    value={tabValue} 
                    onChange={handleTabChange} 
                    aria-label="tabs"
                    variant={isMobile ? "scrollable" : "fullWidth"}
                    scrollButtons="auto"
                    allowScrollButtonsMobile
                    sx={{ width: '100%'}}
                >
                    {tabs.map(tab => <Tab key={tab.id} label={t(tab.slug)} value={tab.id} />)}
                </TabList>
            </TabContext>

            <Box sx={{ flex: 1, overflow: 'auto' }}>
                <List sx={{ width: '100%' }}>
                    {filteredTasks.length > 0 ?
                        filteredTasks.map((task, i) => (
                            <Task key={task.id} task={task} onChange={() => console.log('change')} />
                        ))
                    :
                        <ListItem>
                            <ListItemText
                                primary={t('widget:tasks.noTasks')}
                                secondary={t('widget:tasks.noTasksDescription')}
                            />
                        </ListItem>
                    }
                </List>
            </Box>
        </Stack>
    );
};