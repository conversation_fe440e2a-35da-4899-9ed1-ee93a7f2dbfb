import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Keycloak from 'keycloak-js';
import { setUserInfo, setUserToken } from '@siteboss-frontend/shared/store';
import { keycloakConfig, keycloakInitOptions, keycloakLogoutOptions } from './keycloakConfig';

export const KeycloakContext = createContext();

export const KeycloakProvider = ({ children }) => {
  const [keycloak, setKeycloak] = useState(null);
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const dispatch = useDispatch();
    const navigate = useNavigate();

  const updateUserInfo = useCallback((kc) => {
    const userInfo = {
      token: kc.token,
      profile: {
        id: kc.tokenParsed?.sub,
        username: kc.tokenParsed?.preferred_username,
        email: kc.tokenParsed?.email,
        firstName: kc.tokenParsed?.given_name,
        lastName: kc.tokenParsed?.family_name,
        name: kc.tokenParsed?.name,
      },
      date: new Date().toISOString(),
    };

    localStorage.setItem('keycloakToken', kc.token);
    localStorage.setItem('user', JSON.stringify(userInfo));

    dispatch(setUserToken(kc.token));
    dispatch(setUserInfo(userInfo));
  }, [dispatch]);

  const initKeycloak = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const kc = new Keycloak(keycloakConfig);

      const authenticated = await Promise.race([
        kc.init(keycloakInitOptions),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Keycloak initialization timeout - iframe check failed')), 8000)
        )
      ]);

      setKeycloak(kc);
      setAuthenticated(authenticated);

      if (authenticated) {
        updateUserInfo(kc);
      }

      setLoading(false);
    } catch (err) {
      console.error('Keycloak initialization failed:', err);
      setError(err);
      setLoading(false);

      // Set up keycloak instance for manual login attempts
      try {
        const kc = new Keycloak(keycloakConfig);
        setKeycloak(kc);
        setAuthenticated(false);
      } catch (setupError) {
        console.error('Failed to set up Keycloak instance:', setupError);
      }
    }
  }, [updateUserInfo]);

  const login = useCallback(() => {
    if (keycloak) {
      // Let Keycloak automatically determine the redirect URI
      keycloak.login();
    }
  }, [keycloak]);

  const logout = useCallback(() => {
    // Clear local storage
    localStorage.removeItem('keycloakToken');
    localStorage.removeItem('user');
    localStorage.removeItem('prevUrl');

    // Clear Redux store
    dispatch(setUserInfo(null));
    dispatch(setUserToken(null));

    // Logout from Keycloak if available
    if (keycloak) {
      keycloak.logout(keycloakLogoutOptions);
    } else {
        navigate('/', { replace: true });
    }
  }, [keycloak, dispatch, navigate]);

  // Set up token refresh effect
  useEffect(() => {
    if (!keycloak || !authenticated) return;

    const refreshInterval = setInterval(() => {
      keycloak.updateToken(60)
        .then((refreshed) => {
          if (refreshed) {
            console.log('Token was successfully refreshed');
            updateUserInfo(keycloak);
          }
        })
        .catch((error) => {
          console.error('Failed to refresh token', error);
          logout();
        });
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [keycloak, authenticated, updateUserInfo, logout]);

  const hasRole = useCallback((role) => {
    return keycloak?.hasRealmRole?.(role) || false;
  }, [keycloak]);

  const hasResourceRole = useCallback((role, resource) => {
    return keycloak?.hasResourceRole?.(role, resource) || false;
  }, [keycloak]);

  useEffect(() => {
    initKeycloak();
  }, [initKeycloak]);

  const contextValue = {
    keycloak,
    authenticated,
    loading,
    error,
    login,
    logout,
    hasRole,
    hasResourceRole,
    token: keycloak?.token,
    tokenParsed: keycloak?.tokenParsed,
  };

  return (
    <KeycloakContext.Provider value={contextValue}>
      {children}
    </KeycloakContext.Provider>
  );
};