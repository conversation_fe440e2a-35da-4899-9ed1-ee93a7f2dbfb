
import { useState, useCallback } from "react";
import { useApi } from "../../../api";

export const useFetch = ({
    name, // the name of the field
    onChange, // callback function to handle the selection
    onBlur, // callback function to handle the blur event
    initialValue, // initial value for the field
    fetchParams, // object with parameters to be sent to the fetch function, for example: {enableCache: true, params: {endpoint: "/event", data: {event_id: 1, status_id: 2}}}
    valueFormatter, // function to format the value to be displayed
}) => {
    const [selectedItems, setSelectedItems] = useState(initialValue);
    const {fetchData, data, loading, ErrorBar} = useApi(fetchParams);

    const handleChange = useCallback(values => {
        setSelectedItems(values);
        const e = {target: {name, value: values.map(a => a?.id || a)}};
        if (onChange) onChange(e);
        if (onBlur) onBlur(e);
    }, [onChange, onBlur, name]);

    return {
        data: valueFormatter ? valueFormatter(data) : data,
        loading,
        fetchData,
        ErrorBar,
        handleChange,
        selectedItems,
    }
}
