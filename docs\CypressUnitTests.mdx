import { Meta } from '@@storybook/addon-docs';

<Meta title="Docs/Testing/Cypress Unit Tests" />

# Cypress Unit Tests

Unit tests are the most traditional way of testing - does this unit (or usually in the case of JavaScript, function) do what it's supposed to do.  While a relatively simple function probably does not need a unit test, functions that are longer or more complex or functions dealing with complex data should.

Like a component test, the unit tests should live close to the functions they're testing.

If we have a more complex component, like services, for example, we could put different tests with the booking function, the dashboard, the wizard, etc.  But we should have the individual tests close by for development and continued maintenance as the components grow, expand, or change.

A lot of projects keep mock data and component tests in the parent cypress folder, however, if we follow this patter, long term our Cypress component and unit testing folders would be very large messes of trying to find the proper files.  By keeping the tests with what they're testing, it's easy to develop both the tests and functionality at the same time and to be reminded when making changes to functionality that the test will need to be updated as well.  And we'll be avoiding a mess of files.

Unit tests are probably the simplest to write as you're already writing said function you're testing.  Like component tests, your unit tests should account for the good results (ideal data) and the bad results (no data, wrong type of data, etc).  Thinking about testing for all these things will help us error handle these things in a better way.

So, if for example, we have this simple function:

```
export const addTheSum=(a, b)=>{
    return a+b;
}
```

All it does is add the two numbers, pretty simple function.  However, if we pass in (1, '3'), we're not going to get 4 back.  So we're going to error handle our function to make sure we get the optimal results.

```
export const addTheSum=(a, b)=>{
    if(typeof a !== "number" || typeof b !== "number"){
        return "Invalid Input"
    }
    else if(a + b < 0) return "We only like positive things around here"
    else return a+b;
}
```

Now, in our cypress unit test file, we can write these tests:

```
/*eslint-disable*/
/// <reference types="cypress" />

import { addTheSum } from './SampleFunctions';

describe('Sample Unit Test', () => {

    before(()=>{
        expect(addTheSum).to.be.a('function')
    })

    it('should add two numbers', () =>{
        expect(addTheSum(1,3)).to.equal(4)
    })

    it('should return a message if the sum is negative', ()=>{
        expect(addTheSum(-1, -3)).to.equal('We only like positive things around here')
    })

    it('should return a message if an argument is not a number', ()=>{
        expect(addTheSum(1, '3')).to.equal('Invalid Input')
    })

    it("should return nothing if parameters aren't passed in",()=>{
        expect(addTheSum()).to.equal('Invalid Input');
    })
})
```
