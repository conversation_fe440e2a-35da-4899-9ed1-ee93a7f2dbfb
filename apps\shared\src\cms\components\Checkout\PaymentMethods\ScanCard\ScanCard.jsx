import React, { useState, useCallback, useId } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Stack, Alert, Button } from '@mui/material';

import FormItem from '../../../../../components/FormItem';
import { createCurrencyFormatter } from '../../../../../utils/currency';

export const ScanCard = ({
    paymentMethod, 
    paymentMethodId, 
    amount, 
    loading: parentLoading, 
    removeCashDiscount,
    cashDiscount = 0,
    onPaymentProcess, 
    onPaymentChange, 
    slotProps, 
    ...props
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    const id = useId();

    const [payment, setPayment] = useState(+amount || 0);

    const handleChange = useCallback(e => {
        setPayment(e.target.value);
        if (onPaymentChange) {
            onPaymentChange({
                id, 
                values: {
                    amount: e.target.value,
                    removeCashDiscount,
                }, 
                paymentMethod, 
                paymentMethodId,
            });
        }
    }, [onPaymentChange, id, paymentMethod, paymentMethodId, removeCashDiscount]);

    const handlePay = useCallback(async () => {
        if (onPaymentProcess) {
            await onPaymentProcess({
                id, 
                values: {
                    amount: payment,
                    removeCashDiscount,
                }, 
                paymentMethod, 
                paymentMethodId,
            });
        }
    }, [onPaymentProcess, id, payment, paymentMethod, paymentMethodId, removeCashDiscount]);

    return (
        <Stack direction="column" spacing={1} useFlexGap {...slotProps?.stack}>
            <FormItem 
                label={t('pos:amount')}
                required
                component='MoneyField'
                name='amount'
                margin="normal"
                value={payment}
                onChange={handleChange}
                errors={(+payment > +amount || !+payment) ? t('error:invalid') : null}
                {...slotProps?.input}
            />
            <Button 
                loading={parentLoading} 
                loadingPosition={slotProps?.button?.startIcon ? "start" : undefined}
                variant="contained" 
                color="secondary" 
                size="xl" 
                fullWidth 
                disabled={parentLoading || +payment > +amount || !+payment} 
                onClick={handlePay} 
                {...slotProps?.button}
            >
                {t(`pos:${+payment < +amount ? 'processPartialPayment' : 'processPayment'}`)}
            </Button>
            {removeCashDiscount &&
                <Alert variant="outlined" severity="warning">{t("pos:warnings.removeCashDiscount", {amount: currencyFormatter.format(cashDiscount, currency)})}</Alert>
            }
        </Stack>
    );
}