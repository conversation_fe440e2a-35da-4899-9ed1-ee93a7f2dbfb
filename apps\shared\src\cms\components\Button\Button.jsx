import React, { useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, ButtonGroup, IconButton } from '@mui/material';
import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';
import { Icon } from '../common';

export const Button = ({
    id,
    label,
    url,
    target,
    icon,
    iconPosition,
    variant,
    color,
    size,
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        button: {},           // MUI button props
        icon: {},           // MUI material icon props
    },
    condition = null,       // condition to render the element
    isBuilder = false,      // renders the builder wrapper around the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, isMobile, canRender, customCss, noContent } = prepareComponent({name: "button", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    // makes sure these values are arrays, in case the user wants to add multiple buttons
    if (label && !Array.isArray(label)) label = [label]; 
    if (icon && !Array.isArray(icon)) icon = [icon]; 
    if (url && !Array.isArray(url)) url = [url];
    if (target && !Array.isArray(target)) target = [target];

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack component={ButtonGroup} direction="row" spacing={1} useFlexGap {...slotProps?.cmsStack}>
                {!label.length ? noContent :
                    <>
                        {label.map((content, i) => {
                            let _icon = icon?.[i] || null;
                            if (!_icon) _icon = icon?.[0] || null;
                            let _url = url?.[i] || null; 
                            if (!_url) _url = url?.[0] || null;
                            let _target = target?.[i] || null;
                            if (!_target) _target = target?.[0] || null;
                            
                            // icon button
                            if (layoutId === 3) {
                                return (
                                    <IconButton 
                                        component={Link} 
                                        key={`button-${id}-${i}`}
                                        aria-label={content} 
                                        size={size || "medium"}
                                        color={color || "primary"} 
                                        //onClick={isBuilder ? null : e => onClick(e, i)} 
                                        target={isBuilder ? "_self" : target || "_self"}
                                        to={isBuilder ? "#!" : _url}
                                        //disableRipple={isBuilder}
                                        edge={iconPosition === "default" ? false : iconPosition}
                                        {...slotProps?.button}
                                        style={{
                                            pointerEvents: isBuilder ? "none" : "auto",
                                        }}
                                    >
                                        {_icon && <Icon name={_icon} fontSize="inherit" {...slotProps?.icon}/>}
                                    </IconButton>
                                );
                            }
                            // normal button
                            return (
                                <MuiButton
                                    component={Link} 
                                    key={`button-${id}-${i}`}
                                    aria-label={content}
                                    variant={variant || "contained"} 
                                    color={color || "primary"} 
                                    //onClick={isBuilder ? null : e => onClick(e, i)}
                                    target={isBuilder ? "_self" : target || "_self"}
                                    to={isBuilder ? "#!" : _url}
                                    disableRipple={isBuilder}
                                    startIcon={(iconPosition === "start" || iconPosition === "default") && _icon ? <Icon name={_icon} {...slotProps?.icon}/> : undefined}
                                    endIcon={iconPosition === "end" && _icon ? <Icon name={_icon} {...slotProps?.icon}/> : undefined}
                                    size={size || "medium"}
                                    {...slotProps?.button}
                                    style={{
                                        pointerEvents: isBuilder ? "none" : "auto",
                                    }}
                                >
                                    {content}
                                </MuiButton>
                            );
                        })}
                    </>
                }
            </Stack>
            {children}
        </CmsContainer>
    );
};