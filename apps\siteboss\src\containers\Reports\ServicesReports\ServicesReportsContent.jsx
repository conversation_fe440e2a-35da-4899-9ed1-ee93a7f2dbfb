import React, { useState, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { enUS, es } from 'date-fns/locale';
import {
    Grid,
    Card,
    CardContent,
    Typography,
    Box,
    Paper,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Button,
    Tab
} from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
    FilterAltOutlined as FilterIcon,
    DownloadOutlined as DownloadIcon,
    PrintOutlined as PrintIcon,
    CalendarTodayOutlined as BookingsIcon,
    PersonOutlined as ProvidersIcon,
    AssessmentOutlined as PerformanceIcon,
    PieChartOutlined as UtilizationIcon,
    EventAvailableOutlined as TotalBookingsIcon,
    MonetizationOnOutlined as RevenueIcon,
    ReceiptOutlined as BookingValueIcon,
    SpeedOutlined as UtilizationRateIcon
} from '@mui/icons-material';
import { LineChart, DataTable, SimpleBar, WithScrollEffect, MetricCard, MetricCardGroup } from '@siteboss-frontend/shared/components';

// Mock data for demonstration
const mockChartData = {
    data: [
        {
            id: "Massage",
            data: [
                { x: "2023-06-01", y: 12 },
                { x: "2023-06-02", y: 15 },
                { x: "2023-06-03", y: 11 },
                { x: "2023-06-04", y: 18 },
                { x: "2023-06-05", y: 21 },
                { x: "2023-06-06", y: 19 },
                { x: "2023-06-07", y: 23 }
            ]
        },
        {
            id: "Personal Training",
            data: [
                { x: "2023-06-01", y: 8 },
                { x: "2023-06-02", y: 10 },
                { x: "2023-06-03", y: 9 },
                { x: "2023-06-04", y: 12 },
                { x: "2023-06-05", y: 15 },
                { x: "2023-06-06", y: 14 },
                { x: "2023-06-07", y: 16 }
            ]
        },
        {
            id: "Tutoring",
            data: [
                { x: "2023-06-01", y: 5 },
                { x: "2023-06-02", y: 7 },
                { x: "2023-06-03", y: 6 },
                { x: "2023-06-04", y: 8 },
                { x: "2023-06-05", y: 10 },
                { x: "2023-06-06", y: 9 },
                { x: "2023-06-07", y: 11 }
            ]
        }
    ]
};

const mockBookingsData = {
    columns: [
        { field: 'id', headerName: 'ID', width: 70 },
        { field: 'date', headerName: 'Date', width: 120 },
        { field: 'time', headerName: 'Time', width: 100 },
        { field: 'service', headerName: 'Service', width: 150 },
        { field: 'provider', headerName: 'Provider', width: 150 },
        { field: 'client', headerName: 'Client', width: 150 },
        { field: 'location', headerName: 'Location', width: 150 },
        { field: 'status', headerName: 'Status', width: 120 },
        { field: 'price', headerName: 'Price', width: 100, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
    ],
    rows: [
        { id: 1, date: '06/01/2023', time: '9:00 AM', service: 'Massage', provider: 'John Smith', client: 'Alice Johnson', location: 'Main Facility', status: 'Completed', price: 85.00 },
        { id: 2, date: '06/01/2023', time: '10:30 AM', service: 'Personal Training', provider: 'Sarah Davis', client: 'Bob Williams', location: 'Main Facility', status: 'Completed', price: 65.00 },
        { id: 3, date: '06/01/2023', time: '1:00 PM', service: 'Tutoring', provider: 'Michael Brown', client: 'Charlie Miller', location: 'East Wing', status: 'Completed', price: 45.00 },
        { id: 4, date: '06/02/2023', time: '9:00 AM', service: 'Massage', provider: 'John Smith', client: 'David Wilson', location: 'Main Facility', status: 'Completed', price: 85.00 },
        { id: 5, date: '06/02/2023', time: '10:30 AM', service: 'Personal Training', provider: 'Sarah Davis', client: 'Eve Taylor', location: 'Main Facility', status: 'Cancelled', price: 0.00 },
        { id: 6, date: '06/02/2023', time: '1:00 PM', service: 'Tutoring', provider: 'Michael Brown', client: 'Frank Anderson', location: 'East Wing', status: 'Completed', price: 45.00 },
        { id: 7, date: '06/03/2023', time: '9:00 AM', service: 'Massage', provider: 'John Smith', client: 'Grace Martinez', location: 'Main Facility', status: 'Completed', price: 85.00 },
    ]
};

const mockProvidersData = {
    columns: [
        { field: 'id', headerName: 'ID', width: 70 },
        { field: 'name', headerName: 'Provider Name', width: 150 },
        { field: 'service', headerName: 'Service Type', width: 150 },
        { field: 'bookings', headerName: 'Total Bookings', width: 120, type: 'number' },
        { field: 'hours', headerName: 'Hours Booked', width: 120, type: 'number' },
        { field: 'revenue', headerName: 'Revenue Generated', width: 150, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `$${value.toFixed(2)}` : '$0.00' },
        { field: 'rating', headerName: 'Avg. Rating', width: 120, type: 'number', valueFormatter: ({ value }) => value !== undefined && value !== null ? `${value.toFixed(1)}/5.0` : 'N/A' },
    ],
    rows: [
        { id: 1, name: 'John Smith', service: 'Massage', bookings: 45, hours: 45, revenue: 3825.00, rating: 4.8 },
        { id: 2, name: 'Sarah Davis', service: 'Personal Training', bookings: 38, hours: 38, revenue: 2470.00, rating: 4.7 },
        { id: 3, name: 'Michael Brown', service: 'Tutoring', bookings: 32, hours: 48, revenue: 2160.00, rating: 4.9 },
        { id: 4, name: 'Lisa Johnson', service: 'Massage', bookings: 41, hours: 41, revenue: 3485.00, rating: 4.6 },
        { id: 5, name: 'Robert Wilson', service: 'Personal Training', bookings: 35, hours: 35, revenue: 2275.00, rating: 4.5 },
        { id: 6, name: 'Emily Taylor', service: 'Tutoring', bookings: 28, hours: 42, revenue: 1890.00, rating: 4.8 },
        { id: 7, name: 'Daniel Martinez', service: 'Massage', bookings: 39, hours: 39, revenue: 3315.00, rating: 4.7 },
    ]
};

const ServicesReportsContent = () => {
    const { t, isMobile } = useOutletContext();
    const language = useSelector(state => state.language);

    // State for tabs and filters
    const [tabValue, setTabValue] = useState('bookings');
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [serviceType, setServiceType] = useState('all');
    const [provider, setProvider] = useState('all');
    const [location, setLocation] = useState('all');

    // Define tabs for services reports
    const reportTabs = useMemo(() => [
        {
            id: 'bookings',
            name: t('reports:services.bookings'),
            icon: <BookingsIcon />
        },
        {
            id: 'providers',
            name: t('reports:services.providers'),
            icon: <ProvidersIcon />
        },
        {
            id: 'performance',
            name: t('reports:services.performance'),
            icon: <PerformanceIcon />
        },
        {
            id: 'utilization',
            name: t('reports:services.utilization'),
            icon: <UtilizationIcon />
        }
    ], [t]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleFilterApply = () => {
        // In a real implementation, this would fetch filtered data
        console.log('Applying filters:', { startDate, endDate, serviceType, provider, location });
    };

    const handleExport = () => {
        // In a real implementation, this would export the data
        console.log('Exporting data');
    };

    const handlePrint = () => {
        // In a real implementation, this would print the report
        console.log('Printing report');
    };

    // Filter component - reused across tabs
    const FilterPanel = () => (
        <Paper sx={{ p: 2, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
                {t('reports:filters')}
            </Typography>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={6} lg={2}>
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                        <DatePicker
                            label={t('reports:startDate')}
                            value={startDate}
                            onChange={(newValue) => setStartDate(newValue)}
                            slotProps={{ textField: { fullWidth: true } }}
                        />
                    </LocalizationProvider>
                </Grid>
                <Grid item xs={12} sm={6} md={6} lg={2}>
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={language.code === 'es' ? es : enUS}>
                        <DatePicker
                            label={t('reports:endDate')}
                            value={endDate}
                            onChange={(newValue) => setEndDate(newValue)}
                            slotProps={{ textField: { fullWidth: true } }}
                        />
                    </LocalizationProvider>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2}>
                    <FormControl fullWidth>
                        <InputLabel>Service Type</InputLabel>
                        <Select
                            value={serviceType}
                            label="Service Type"
                            onChange={(e) => setServiceType(e.target.value)}
                        >
                            <MenuItem value="all">{t('reports:all')}</MenuItem>
                            <MenuItem value="massage">Massage</MenuItem>
                            <MenuItem value="personal-training">Personal Training</MenuItem>
                            <MenuItem value="tutoring">Tutoring</MenuItem>
                        </Select>
                    </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2}>
                    <FormControl fullWidth>
                        <InputLabel>Provider</InputLabel>
                        <Select
                            value={provider}
                            label="Provider"
                            onChange={(e) => setProvider(e.target.value)}
                        >
                            <MenuItem value="all">{t('reports:all')}</MenuItem>
                            <MenuItem value="john-smith">John Smith</MenuItem>
                            <MenuItem value="sarah-davis">Sarah Davis</MenuItem>
                            <MenuItem value="michael-brown">Michael Brown</MenuItem>
                        </Select>
                    </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2}>
                    <FormControl fullWidth>
                        <InputLabel>Location</InputLabel>
                        <Select
                            value={location}
                            label="Location"
                            onChange={(e) => setLocation(e.target.value)}
                        >
                            <MenuItem value="all">{t('reports:all')}</MenuItem>
                            <MenuItem value="main">Main Facility</MenuItem>
                            <MenuItem value="east">East Wing</MenuItem>
                            <MenuItem value="west">West Wing</MenuItem>
                        </Select>
                    </FormControl>
                </Grid>
                <Grid item xs={12} md={4} lg={2}>
                    <Button
                        variant="contained"
                        startIcon={<FilterIcon />}
                        onClick={handleFilterApply}
                        fullWidth
                    >
                        {t('reports:apply')}
                    </Button>
                </Grid>
            </Grid>
        </Paper>
    );

    // Summary stats component - reused across tabs
    const SummaryStats = () => (
        <MetricCardGroup>
            <MetricCard
                title="Total Bookings"
                value="258"
                icon={<TotalBookingsIcon />}
                color="green"
                trend="+12% from last month"
                width={3}
            />
            <MetricCard
                title="Total Revenue"
                value="$19,420"
                icon={<RevenueIcon />}
                color="blue"
                trend="+8% from last month"
                width={3}
            />
            <MetricCard
                title="Avg. Booking Value"
                value="$75.27"
                icon={<BookingValueIcon />}
                color="orange"
                trend="+3% from last month"
                width={3}
            />
            <MetricCard
                title="Utilization Rate"
                value="78%"
                icon={<UtilizationRateIcon />}
                color="purple"
                trend="+5% from last month"
                width={3}
            />
        </MetricCardGroup>
    );

    // Chart component
    const ServiceBookingsChart = () => (
        <Card sx={{ mb: 4 }}>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        Service Bookings by Type
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 300, minWidth: { xs: '600px', md: 'auto' }, overflow: 'auto' }}>
                    <LineChart
                        data={mockChartData}
                        curve="natural"
                        margin={{ top: 20, right: 20, bottom: 60, left: 80 }}
                        responsive={true}
                        xScale={{
                            type: 'time',
                            format: '%Y-%m-%d',
                            useUTC: false,
                            precision: 'day',
                        }}
                        xFormat="time:%m/%d/%Y"
                        yFormat=" >-d"
                        axisBottom={{
                            format: '%b %d',
                            tickValues: 'every 1 day',
                            legend: 'Date',
                            legendOffset: 36,
                            legendPosition: 'middle'
                        }}
                        axisLeft={{
                            legend: 'Bookings',
                            legendOffset: -60,
                            legendPosition: 'middle'
                        }}
                        enablePoints={true}
                        pointSize={8}
                        pointBorderWidth={1}
                        pointBorderColor={{ from: 'color', modifiers: [['darker', 0.3]] }}
                        useMesh={true}
                        enableSlices="x"
                        legends={[
                            {
                                anchor: 'bottom-right',
                                direction: 'row',
                                justify: false,
                                translateX: 0,
                                translateY: 50,
                                itemsSpacing: 0,
                                itemDirection: 'left-to-right',
                                itemWidth: 100,
                                itemHeight: 20,
                                symbolSize: 12,
                                symbolShape: 'circle',
                            }
                        ]}
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Bookings Table component
    const BookingsTable = () => (
        <Card>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        Service Bookings
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 400, overflow: 'auto' }}>
                    <DataTable
                        sx={{ minWidth: { xs: '800px', md: 'auto' } }}
                        columns={mockBookingsData.columns}
                        rows={mockBookingsData.rows}
                        pageSize={5}
                        rowsPerPageOptions={[5, 10, 25]}
                        checkboxSelection={false}
                        disableRowSelectionOnClick
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Providers Table component
    const ProvidersTable = () => (
        <Card>
            <CardContent>
                <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 2
                }}>
                    <Typography variant="h6" sx={{ mb: { xs: 1, sm: 0 } }}>
                        Service Providers
                    </Typography>
                    <Box>
                        <Button
                            startIcon={<DownloadIcon />}
                            onClick={handleExport}
                            sx={{ mr: 1 }}
                            size="small"
                        >
                            {t('reports:export')}
                        </Button>
                        <Button
                            startIcon={<PrintIcon />}
                            onClick={handlePrint}
                            size="small"
                        >
                            {t('reports:print')}
                        </Button>
                    </Box>
                </Box>
                <Box sx={{ height: 400, overflow: 'auto' }}>
                    <DataTable
                        sx={{ minWidth: { xs: '800px', md: 'auto' } }}
                        columns={mockProvidersData.columns}
                        rows={mockProvidersData.rows}
                        pageSize={5}
                        rowsPerPageOptions={[5, 10, 25]}
                        checkboxSelection={false}
                        disableRowSelectionOnClick
                    />
                </Box>
            </CardContent>
        </Card>
    );

    // Bookings Tab Content
    const BookingsTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <ServiceBookingsChart />
            <BookingsTable />
        </>
    );

    // Providers Tab Content
    const ProvidersTab = () => (
        <>
            <FilterPanel />
            <SummaryStats />
            <ProvidersTable />
        </>
    );

    // Performance Tab Content
    const PerformanceTab = () => (
        <>
            <FilterPanel />
            <Typography variant="h5" gutterBottom>
                Service Performance Report
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="body1">
                    This tab will display service performance reports including:
                </Typography>
                <ul>
                    <li>Revenue by Service Type</li>
                    <li>Revenue by Provider</li>
                    <li>Customer Satisfaction</li>
                    <li>Booking Trends</li>
                </ul>
            </Paper>
        </>
    );

    // Utilization Tab Content
    const UtilizationTab = () => (
        <>
            <FilterPanel />
            <Typography variant="h5" gutterBottom>
                Service Utilization Report
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="body1">
                    This tab will display service utilization reports including:
                </Typography>
                <ul>
                    <li>Provider Utilization Rates</li>
                    <li>Room/Facility Utilization</li>
                    <li>Peak Hours Analysis</li>
                    <li>Capacity Planning</li>
                </ul>
            </Paper>
        </>
    );

    return (
        <>
            <TabContext value={tabValue}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                    <TabList
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        aria-label="service report types"
                        sx={{
                            '& .MuiTab-root': {
                                display: 'flex',
                                flexDirection: isMobile ? 'column' : 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                minHeight: isMobile ? '72px' : '48px',
                            }
                        }}
                    >
                        {reportTabs.map((tab) => (
                            <Tab
                                key={tab.id}
                                label={tab.name}
                                value={tab.id}
                                icon={tab.icon}
                                iconPosition={isMobile ? "top" : "start"}
                            />
                        ))}
                    </TabList>
                </Box>

                <TabPanel value="bookings" sx={{ p: 0 }}>
                    <BookingsTab />
                </TabPanel>

                <TabPanel value="providers" sx={{ p: 0 }}>
                    <ProvidersTab />
                </TabPanel>

                <TabPanel value="performance" sx={{ p: 0 }}>
                    <PerformanceTab />
                </TabPanel>

                <TabPanel value="utilization" sx={{ p: 0 }}>
                    <UtilizationTab />
                </TabPanel>
            </TabContext>
        </>
    );
};

export default ServicesReportsContent;
