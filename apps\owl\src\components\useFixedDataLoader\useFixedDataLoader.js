import { 
    useCountriesData, 
    useStatesData, 
    useCitiesData, 
    useMerchantBillingMethodsData, 
    useMerchantBillingFrequenciesData, 
    useMerchantBillingTermsData, 
    useMerchantGroupByOptionsData,
    useContactTypesData,
    useCompanyTypesData,
    useFulfillmentTypesData,
    useUploadFormatsData,
    useOrderTypesData,
    
} from './hooks';

/**
 * Hook to load fixed data ONCE and store in Redux
 * Call this ONLY in App.jsx or root level
 */
export const useFixedDataLoader = () => {
    const { loading: countriesLoading } = useCountriesData();
    const { loading: statesLoading } = useStatesData();
    const { loading: citiesLoading } = useCitiesData();
    const { loading: merchantBillingMethodsLoading } = useMerchantBillingMethodsData();
    const { loading: merchantBillingFrequenciesLoading } = useMerchantBillingFrequenciesData();
    const { loading: merchantBillingTermsLoading } = useMerchantBillingTermsData();
    const { loading: merchantGroupByOptionsLoading } = useMerchantGroupByOptionsData();
    const { loading: contactTypesLoading } = useContactTypesData();
    const { loading: companyTypesLoading } = useCompanyTypesData();
    const { loading: fulfillmentTypesLoading } = useFulfillmentTypesData();
    const { loading: uploadFormatsLoading } = useUploadFormatsData();
    const { loading: orderTypesLoading } = useOrderTypesData();

    return {
        loading: countriesLoading || statesLoading || citiesLoading ||merchantBillingMethodsLoading || merchantBillingFrequenciesLoading ||
                merchantBillingTermsLoading || merchantGroupByOptionsLoading || contactTypesLoading || companyTypesLoading ||
                fulfillmentTypesLoading || uploadFormatsLoading || orderTypesLoading,
    };
};