import { lazy } from 'react';
import { layouts as headingLayouts, properties as headingProperties } from './Heading';
import { layouts as buttonLayouts, properties as buttonProperties } from './Button';
import { layouts as dividerLayouts, properties as dividerProperties } from './Divider';
import { layouts as countDownLayouts, properties as countDownProperties } from './CountDown';
import { layouts as listLayouts, properties as listProperties } from './List';
import { layouts as heroLayouts, properties as heroProperties } from './Hero';
import { layouts as galleryLayouts, properties as galleryProperties } from './Gallery';
import { layouts as customHtmlLayouts, properties as customHtmlProperties } from './CustomHtml';
import { layouts as menuLayouts, properties as menuProperties } from './Menu';
import { layouts as coreLayouts, properties as coreProperties } from './Core';
import { layouts as breadcrumbLayouts, properties as breadcrumbProperties } from './Breadcrumb';

import { layouts as blogPostLayouts, properties as blogPostProperties } from './BlogPost';

import { layouts as eventsLayouts, properties as eventsProperties } from './Events';
import { layouts as eventDetailLayouts, properties as eventDetailProperties } from './EventDetail';

import { layouts as headerLayouts, properties as headerProperties } from './Header';
import { layouts as bodyLayouts, properties as bodyProperties } from './Body';
import { layouts as footerLayouts, properties as footerProperties } from './Footer';

import { layouts as productsLayouts, properties as productsProperties } from './Products';
import { layouts as productDetailLayouts, properties as productDetailProperties } from './ProductDetail';
import { layouts as productFilterLayouts, properties as productFilterProperties } from './ProductFilter';
import { layouts as cartLayouts, properties as cartProperties } from './Cart';
import { layouts as posTotalsLayouts, properties as posTotalsProperties } from './PosTotals';
import { layouts as checkoutLayouts, properties as checkoutProperties } from './Checkout';
import { layouts as posSuccessLayouts, properties as posSuccessProperties } from './PosSuccess';

const lazyComponent = component => {
    const importModule = () => import(`./${component}/index.js`);
    
    return {
        id: component,
        name: component,
        component: lazy(() => importModule().then(module => ({ default: module[component] }))),
        widgetIcon: lazy(() => importModule().then(module => ({ 
            default: module?.widgetIcon || (typeof module.layouts[0].icon === 'function' ? module.layouts[0].icon() : (() => module.layouts[0].icon)),
        }))),
    };
};

const componentList = [
    {
        ...lazyComponent('Core'),
        layouts: coreLayouts,
        properties: coreProperties,
    },
    {
        ...lazyComponent('Heading'),
        layouts: headingLayouts,
        properties: headingProperties,
    },
    {
        ...lazyComponent('Button'),
        layouts: buttonLayouts,
        properties: buttonProperties,
    },
    {
        ...lazyComponent('List'),
        layouts: listLayouts,
        properties: listProperties,
    },
    {
        ...lazyComponent('Gallery'),
        layouts: galleryLayouts,
        properties: galleryProperties,
    },
    {
        ...lazyComponent('Hero'),
        layouts: heroLayouts,
        properties: heroProperties,
    },
    {
        ...lazyComponent('Divider'),
        layouts: dividerLayouts,
        properties: dividerProperties,
    },
    {
        ...lazyComponent('CountDown'),
        layouts: countDownLayouts,
        properties: countDownProperties,
    },
    {
        ...lazyComponent('CustomHtml'),
        layouts: customHtmlLayouts,
        properties: customHtmlProperties,
    },
    {
        ...lazyComponent('Menu'),
        layouts: menuLayouts,
        properties: menuProperties,
    },
    {
        ...lazyComponent('Breadcrumb'),
        layouts: breadcrumbLayouts,
        properties: breadcrumbProperties,
    },
];

const headerList = [
    {
        ...lazyComponent('Header'),
        layouts: headerLayouts,
        properties: headerProperties,
    },
];

const footerList = [
    {
        ...lazyComponent('Footer'),
        layouts: footerLayouts,
        properties: footerProperties,
    },
];

const bodyList = [
    {
        ...lazyComponent('Body'),
        layouts: bodyLayouts,
        properties: bodyProperties,
    },
];

const blogList = [
    {
        ...lazyComponent('BlogPost'),
        layouts: blogPostLayouts,
        properties: blogPostProperties,
    },
];

const eventList = [
    {
        ...lazyComponent('Events'),
        layouts: eventsLayouts,
        properties: eventsProperties,
    },
    {
        ...lazyComponent('EventDetail'),
        layouts: eventDetailLayouts,
        properties: eventDetailProperties,
    },
];

const posList = [
    {
        ...lazyComponent('Products'),
        layouts: productsLayouts,
        properties: productsProperties,
    },
    {
        ...lazyComponent('ProductDetail'),
        layouts: productDetailLayouts,
        properties: productDetailProperties,
    },
    {
        ...lazyComponent('ProductFilter'),
        layouts: productFilterLayouts,
        properties: productFilterProperties,
    },
    {
        ...lazyComponent('Cart'),
        layouts: cartLayouts,
        properties: cartProperties,
    },
    {
        ...lazyComponent('PosTotals'),
        layouts: posTotalsLayouts,
        properties: posTotalsProperties,
    },
    {
        ...lazyComponent('Checkout'),
        layouts: checkoutLayouts,
        properties: checkoutProperties,
    },
    {
        ...lazyComponent('PosSuccess'),
        layouts: posSuccessLayouts,
        properties: posSuccessProperties,
    },
];

const fullList = [...headerList, ...bodyList, ...footerList, ...componentList, ...blogList, ...eventList, ...posList];

export {
    componentList, headerList, footerList, bodyList, fullList, blogList, eventList, posList,  
};
