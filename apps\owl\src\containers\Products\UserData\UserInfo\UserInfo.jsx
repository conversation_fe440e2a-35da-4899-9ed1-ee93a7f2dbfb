import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Box, Stack, Button, Container } from '@mui/material';
import { toCamelCase } from '@siteboss-frontend/shared/utils';
import { ToolbarUserAvatar as Avatar, Title, GroupChips } from '@siteboss-frontend/shared/components';

/*
this component is used to display the user's basic card
props:
    userData: the user data to display
    onDelete: function to be called when the delete button is clicked
    onEdit: function to be called when the edit button is clicked
    loading: loading state for the parent component
*/
export const UserInfo = ({userData, onDelete, onEdit, loading, ...props}) => {
    const { t, isMobile } = useOutletContext();

    return (
        <Stack component={Container} direction={{xs: "column", lg: "row"}} spacing={2} alignItems='flex-start'>
            <Avatar size='xxl' hideStatus hideOptions userData={userData} />
            <Stack direction='column' spacing={1} alignItems={isMobile ? 'center' : 'flex-start'}>
                <Title 
                    title={`${userData.first_name} ${userData.last_name}`} 
                    subtitle={userData?.roles?.map(a => t(`user:roles.${toCamelCase(a.name)}`)).join(', ')}
                    sx={{pt: 1}}
                >
                    <GroupChips sx={{mt: 1}} groups={userData.groups} />
                </Title>
                <Box sx={{pt: 2}}>
                    {onEdit &&
                        <Button size='small' variant={isMobile ? 'contained': 'outlined'} color='primary' onClick={e => onEdit(e, userData)} disabled={loading}>
                            {t('general:edit')}
                        </Button>
                    }
                    {onDelete &&
                        <Button size='small' variant={isMobile ? 'contained': 'text'} color='inherit' sx={{ml: 1}} onClick={e => onDelete(e, userData)} disabled={loading}>
                            {t('general:delete')}
                        </Button>
                    }
                </Box>
            </Stack>
        </Stack>
    );
}