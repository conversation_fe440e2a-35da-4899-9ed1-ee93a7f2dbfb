import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useApi } from '@siteboss-frontend/shared';

import { tenantConfig } from "../../KeycloakProvider/tenantConfig";
import { setInfo } from '../../../store/reducers/fixedDataSlice';

const xTenant = tenantConfig()?.name || import.meta.env.VITE_TENANT;

const apiParams = {
    enableCache: true,
    params: {endpoint: '/cities', method: 'GET', config: {headers: { 'X-Tenant': xTenant }}}
};

export const useCitiesData = () => {
    const dispatch = useDispatch();
    const loaded = useSelector(state => state.fixedData.loaded.cities);
    const token = useSelector(state => state.user.token);

    const { fetchData, loading } = useApi(apiParams);

    useEffect(() => {
        if (!loaded && token) {
            fetchData()
                .then(result => {
                    const cities = result?.data || [];
                    dispatch(setInfo({ cities }));
                })
                .catch(error => {
                    console.error('Error fetching cities:', error);
                });
        }
    }, [loaded, fetchData, dispatch, token]);

    return { loading };
};
