import React, { useState, useCallback, useEffect } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { Box, Container, Typography, Divider } from '@mui/material';

import ExpandableText from '../../../../../../components/ExpandableText';
import EventInfo from './EventInfo';
import EventUsers from './EventUsers';

/*
type = event type (meta or regular)
*/
export const Event = ({ layouts = {events: null, eventUsers: { layoutType: "checkbox" }}, event, type, showBasicInfo, selectedUsers: selected, slotProps, onUserSelect, ...props }) => {    
    const currentShopItem = useSelector(state => state.currentShopItem, shallowEqual);

    const [selectedUsers, setSelectedUsers] = useState((selected?.length ? selected : currentShopItem?.forUserIds) || []);

    const handleUserSelection = useCallback((user, limit = null) => {
        let _users = [...selectedUsers];
        // remove extra entries from the bottom of the array
        if (limit && selectedUsers.length >= limit) {
            _users = _users.slice(0, limit - 1);
        }

        const index = selectedUsers.findIndex(a => +a.id === +user.id);
        if (index > -1) _users = _users.filter(a => +a.id !== +user.id);
        else _users.push(user);

        setSelectedUsers(_users);
        if (onUserSelect) onUserSelect(_users);
    }, [onUserSelect, selectedUsers]);


    // filter out users not in forUserIds (in case the validations dont pass in custom fields)
    useEffect(() => {
        setSelectedUsers(prev => {
            return prev.filter(a => currentShopItem?.forUserIds?.find(u => +u.id === +a.id));
        });
    }, [currentShopItem?.forUserIds]);

    if (!event) return null;

    return (
        <Box>
            {!layouts.events?.hide &&
                <>
                    {showBasicInfo && <EventInfo event={event} type={type} slotProps={slotProps?.events} />}
                    <ExpandableText variant="body1" component="div" lines={1} sx={{width: '100%', mb: 2}}>
                        <Typography component="div" variant="body1" dangerouslySetInnerHTML={{__html: event.metadata.description}} />
                    </ExpandableText>
                </>
            }
            {!layouts.eventUsers?.hide &&
                <>
                    <Container disableGutters sx={{mb: 1}}>
                        <EventUsers 
                            {...layouts?.eventUsers}
                            event={event}
                            slotProps={slotProps.eventUsers}
                            selectedUsers={selectedUsers}
                            onSelect={handleUserSelection}
                            allowLogin
                        />
                    </Container>
                    {showBasicInfo && <Divider />}
                </>
            }
        </Box>
    );
};