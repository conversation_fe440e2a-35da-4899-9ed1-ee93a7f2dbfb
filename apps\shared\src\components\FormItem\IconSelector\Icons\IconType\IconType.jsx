import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { RadioGroup, FormControlLabel, Radio, FormControl, FormLabel } from '@mui/material';

const iconStyles = [
    { slug: 'icon:styles.filled', id: 'filled' },
    { slug: 'icon:styles.outlined', id: 'outlined' },
    { slug: 'icon:styles.rounded', id: 'rounded' },
    { slug: 'icon:styles.twoTone', id: 'twoTone' },
    { slug: 'icon:styles.sharp', id: 'sharp' },
];

export const IconType = ({
    onStyleChange,
    ...props
}) => {
    const { t } = useTranslation();
    const [value, setValue] = useState('outlined');

    const handleChange = useCallback(e => {
        setValue(e.target.value);
        if (onStyleChange) onStyleChange(e.target.value);
    }, [onStyleChange]);

    return (
        <FormControl>
            <FormLabel id="icon-style">{t("icon:iconStyle")}</FormLabel>
            <RadioGroup
                aria-labelledby="demo-controlled-radio-buttons-group"
                name="controlled-radio-buttons-group"
                value={value}
                onChange={handleChange}
            >
                {iconStyles.map(style => (
                    <FormControlLabel key={style.id} value={style.id} control={<Radio />} label={t(style.slug)} />
                ))}
            </RadioGroup>
        </FormControl>
    );
}