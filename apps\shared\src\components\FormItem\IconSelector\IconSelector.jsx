import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Box, useMediaQuery, Stack } from '@mui/material';
import * as MuiIcons from '@mui/icons-material';

import Modal from '../../Modal';
import Icons from './Icons';

const Icon = ({ name, size, ...props }) => {
    const IconComponent = MuiIcons[name];
    return <IconComponent {...props} fontSize={size || 'medium'} />;
}

export const IconSelector = ({
    label, // the label for the button
    name, // the name of the button
    icon, // the icon for the button
    variant, // the variant of the button
    color, // the color of the button
    fullWidth, // if the button should be full width
    errors, // the errors for the button
    loading, // the loading status of the form so we can disable the button
    onChange, // the function to be called when the field value changes. The function itself is defined in the form and should be calling change handler if the useForm hook
    onBlur, // the function to be called when the field is blurred
    size, // the size of the button
    value,
    ...props
}) => {
    const { t } = useTranslation();
    const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));

    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(value);

    const handleClickOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    const handleSelection = useCallback(icon => {
        setSelected(icon);
        
        const clonedE = {
            preventDefault: () => {},
            stopPropagation: () => {},
            target: { name, value: icon }
        };

        if (onChange) onChange(clonedE);
        if (onBlur) {
            Promise.resolve().then(() => {
                onBlur(clonedE);
            });
        }

        handleClose();
    }, [onChange, onBlur, name, handleClose]);

    const handleRemove = useCallback(e => {
        e.stopPropagation();
        handleSelection(null);
    }, [handleSelection]);

    const marginSx = {
        'dense': {mt: 0.5, mb: 0.5},
        'normal': {mt: 1, mb: 0.5},
        'none': {m: 0},
    };

    return (
        <Box sx={{width: '100%', ...marginSx[props?.margin || 'normal']}}>
            <Stack direction="row" spacing={2} useFlexGap alignItems="center">
                {selected && 
                    <Icon name={selected} size="large"/> 
                }
                <Stack direction="row" spacing={1} useFlexGap flexGrow={1}>
                    <Button 
                        variant={variant || 'contained' }
                        color={color || 'inherit'}
                        fullWidth={fullWidth}
                        disabled={loading}
                        size={size || undefined}
                        onClick={handleClickOpen}
                    >
                        {t(selected ? 'icon:changeIcon' : 'icon:addIcon')}
                    </Button>
                    {selected &&
                        <Button 
                            variant="text"
                            color={color || 'inherit'} 
                            fullWidth={fullWidth}
                            disabled={loading} 
                            size={size || undefined} 
                            onClick={handleRemove}
                        >
                            {t('icon:removeIcon')}
                        </Button>
                    }
                </Stack>
            </Stack>
            <Modal 
                open={open} 
                onClose={handleClose} 
                //title={t(label || 'icon:icon')} 
                maxWidth='lg'
                slotProps={{paperProps: isMobile ? undefined : {
                    style: {
                        height: `calc(100% - 64px)`
                    }
                }}}
            >
                <Icons onIconSelection={handleSelection} />
            </Modal>
        </Box>
    );
}
