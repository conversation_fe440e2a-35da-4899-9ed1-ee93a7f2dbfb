import React from 'react';
import { Stack } from '@mui/material';

import { prepareComponent, CmsContainer } from '../../utils/CmsContainer';
import { layouts, widgetIcon } from './layouts';
import { properties } from './properties';

import { MenuProvider, Menu as CommonMenu, ToggleButton } from '../common/Menu';

export const Menu = ({
    id,
    items = [],
    fetchPages = false,
    //type = 'horizontal',
    layoutId,
    slotProps = {
        cmsContainer: {},   // MUI container props
        cmsStack: {},       // MUI stack props
        menu: {},          // MUI List props
        item: {},          // MUI ListItem props
    },
    isBuilder = false,      // renders the builder wrapper around the element
    condition = null,       // condition to render the element
    children,
    builderProps,
    ...props
}) => {
    const { slotProps: updatedSlotProps, isMobile, canRender, customCss, noContent } = prepareComponent({name: "menu", layoutId, layouts, slotProps, isBuilder, condition, widgetIcon});
    slotProps = updatedSlotProps;

    const type = slotProps?.menu?.type || (layoutId === 1 ? "responsive" : (layoutId === 2 ? "vertical" : "horizontal"));

    if (!canRender) return null;

    return (
        <CmsContainer 
            {...slotProps?.cmsContainer} 
            isBuilder={isBuilder} 
            layouts={isBuilder ? layouts : undefined}
            layoutId={isBuilder ? layoutId: undefined}
            properties={isBuilder ? properties : undefined}
            sectionId={id}
            {...builderProps}
        >
            {customCss}
            <Stack {...slotProps?.cmsStack}>
                {!items.length ? noContent :
                    <MenuProvider items={items} type={type} fetchPages={fetchPages} isBuilder={isBuilder}>
                        {isMobile && type === "responsive" &&
                            <div><ToggleButton /></div>
                        }
                        <CommonMenu slotProps={slotProps?.menu} disabled={isBuilder} />
                    </MenuProvider>
                }
            </Stack>
        </CmsContainer>
    );
};