export const es = {
    dashboard: {
        welcome: "¡Bienvenido, {{name}}!",
        roleBasedMessage: "Has iniciado sesión como {{role}}. Tu panel ha sido personalizado para tu rol.",
        loading: "Cargando tu panel personalizado...",
        defaultWidgetsLoaded: "Los widgets predeterminados se han cargado según tu rol.",
        dashboard: "Panel",
        totalOrders: "Pedidos Totales",
    },
    general: {
        loading: "Cargando...",
        send: "Enviar",
        save: "Guardar",
        saveAndPublish: "Guardar y Publicar",
        saveChanges: "Guardar Cambios",
        saveAsDraft: "Guardar como Borrador",
        saveAs: "Guardar como",
        cancel: "Cancelar",
        delete: "Eliminar",
        edit: "Editar",
        add: "Añadir",
        addItems: "Añadir Elementos",
        addItem: "Añadir Elemento",
        addNew: "Añadir Nuevo",
        addField: "Añadir Campo",
        exit: "Salir",
        close: "Cerrar",
        open: "Abrir",
        create: "Crear",
        update: "Actualizar",
        remove: "Quitar",
        duplicate: "Duplicar",
        move: "Mover",
        moveUp: "Mover Arriba",
        moveDown: "Mover Abajo",
        moveLeft: "Mover Izquierda",
        moveRight: "Mover Derecha",
        clear: "Limpiar",
        confirm: "Confirmar",
        undo: "Deshacer",
        redo: "Rehacer",
        maximize: "Maximizar",
        minimize: "Minimizar",
        restore: "Restaurar",
        settings: "Configuración",
        properties: "Propiedades",
        crop: "Recortar",
        rotate: "Girar",
        zoom: "Zoom",
        reset: "Restablecer",
        upload: "Subir",
        browse: "Examinar",
        download: "Descargar",
        preview: "Vista Previa",
        device: "Dispositivo",
        previewMode: "Modo de Vista Previa",
        yes: "Sí",
        no: "No",
        ok: "Aceptar",
        continue: "Continuar",
        next: "Siguiente",
        back: "Atrás",
        done: "Hecho",
        search: "Buscar",
        filter: "Filtrar",
        select: "Seleccionar",
        actions: "Acciones",
        noResults: "No hay resultados",
        noContent: "No hay contenido para mostrar",
        changeLanguage: "Cambiar idioma",
        lightMode: "Modo claro",
        darkMode: "Modo oscuro",
        print: "Imprimir",
        printedOn: "Impreso el",
        phone: "Teléfono",
        email: "Correo Electrónico",
        address: "Dirección",
        name: "Nombre",
        description: "Descripción",
        date: "Fecha",
        joined: "Unido",
        id: "ID",
        hide: "Ocultar",
        show: "Mostrar",
        more: "Más",
        less: "Menos",
        showLess: "Mostrar Menos",
        showMore: "Mostrar Más",
        location: "Ubicación",
        confirmMessage: "¿Estás seguro de que quieres realizar esta acción?",
        selectAll: "Seleccionar Todo",
        unselectAll: "Deseleccionar Todo",
        checkAll: "Marcar Todo",
        uncheckAll: "Desmarcar Todo",
        clearAll: "Limpiar Todo",
        none: "Ninguno",
        all: "Todos",
        option: "Opción",
        expand: "Expandir",
        collapse: "Contraer",
        expandAll: "Expandir Todo",
        collapseAll: "Contraer Todo",
        lastUpdated: "Última Actualización",
        default: "Predeterminado",
        other: "Otro",
        extra: "Extra",
        additionalInfo: "Información Adicional",
        showing: "Mostrando",
        of: "de",
        perPage: "Por Página",
        time: {
            second: "Segundo",
            seconds: "Segundos",
            secondsShort: "seg",
            secondsShortest: "s",
            minute: "Minuto",
            minutes: "Minutos",
            minutesShort: "min",
            minutesShortest: "m",
            hour: "Hora",
            hours: "Horas",
            hoursShort: "hr",
            hoursShortest: "h",
            day: "Día",
            days: "Días",
            daysShort: "día",
            daysShortest: "d",
            week: "Semana",
            weeks: "Semanas",
            weeksShort: "sem",
            weeksShortest: "s",
            month: "Mes",
            months: "Meses",
            monthsShort: "mes",
            monthsShortest: "m",
            year: "Año",
            years: "Años",
            yearsShort: "año",
            yearsShortest: "a",
        },
        timeUp: "¡Se acabó el tiempo!",
        learnMore: "Aprende más",
        records: "Registros",
        slug: "Slug",
        editor: "Editor",
        list: "Lista",
        by: "Por",
        cart: "Carrito",
    },

    module: {
        module: "Módulo",
        modules: "Módulos",
        siteBoss: {
            name: "SiteBoss",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                orders: "Pedidos",
                users: "Usuarios",
                events: "Eventos",
                programs: "Programas",
                discounts: "Descuentos",
                services: "Servicios",
                reports: "Informes",
                chat: "Chat",
            },
        },
        owl: {
            name: "OWL",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                products: "Productos",
                merchants: "Comerciantes",
                orders: "Pedidos",
                reports: "Informes",
                billingInvoices: "Facturación y Facturas",
                customerManagement: "Gestión de Clientes",
                carrierConfigurations: "Configuraciones de Transportistas",
                marketingTools: "Herramientas de Marketing y Comunicación",
                serviceStatus: "Estado del Servicio",
            },
        },
        siteBossAdmin: {
            name: "Administrador de SiteBoss",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                chat: "Chat",
            },
        },
        pos: {
            name: "Cajas Registradoras",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                chat: "Chat",
            },
        },
        wms: {
            name: "Almacén",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                chat: "Chat",
            },
        },
        cms: {
            name: "CMS",
            menu: {
                home: "Inicio",
                dashboard: "Panel",
                websites: "Sitios Web",
                builder: "Constructor",
                themes: "Temas",
                chat: "Chat",
            },
        },
    },

    datePicker: {
        today: "Hoy",
        clear: "Limpiar",
        close: "Cerrar",
        reset: "Restablecer",
        thisWeek: "Esta Semana",
        nextWeek: "Próxima Semana",
        lastWeek: "Semana Pasada",
        thisMonth: "Este Mes",
        nextMonth: "Próximo Mes",
        lastMonth: "Mes Pasado",
        thisYear: "Este Año",
        nextYear: "Próximo Año",
        lastYear: "Año Pasado",
        tomorrow: "Mañana",
        yesterday: "Ayer",
        thisWeekend: "Este Fin de Semana",
        nextWeekend: "Próximo Fin de Semana",
        lastWeekend: "Fin de Semana Pasado",
        start: "Inicio",
        end: "Fin",
        time: "Hora",
        date: "Fecha",
    },

    menu: {
        documentation: "Documentación",
        help: "Ayuda",
    },

    /*dashboard: {
        dashboard: "Dashboard",
        totalOrders: "Total Orders",
        totalCustomers: "Total Customers",
        totalSales: "Total Sales",
        totalRevenue: "Total Revenue",
    },*/

    login: {
        title: "Iniciar Sesión en el Portal",
        login: "Iniciar Sesión",
        username: "Nombre de Usuario",
        password: "Contraseña",
        rememberMe: "Recuérdame",
        forgotPassword: "Olvidé mi contraseña",
        loginWith: "Iniciar sesión con",
        loginError: "Correo electrónico o contraseña no válidos",
        or: "o",
        logout: "Cerrar Sesión",
        logBackIn: "Volver a iniciar sesión",
    },

    order: {
        order: "Pedido",
        orders: "Pedidos",
        customer: "Cliente",
        date: "Fecha",
        total: "Total",
        status: "Estado",
        price: "Precio",
        expected: "Esperado",
        qty: "Cant.",
        item: "Artículo",
        items: "Artículos",
        details: "Detalles",
        product: "Producto",
        payment: "Pago",
        payments: "Pagos",
        balance: "Saldo",
        paymentMethod: "Método de Pago",
        endingIn: "terminado en",
        shipping: "Envío",
        amount: "Monto",
        tax: "Impuesto",
        subtotal: "Subtotal",
        tip: "Propina",
        discount: "Descuento",
        cashDiscount: "Descuento por Efectivo",
        creditCard: "Tarjeta de Crédito",
        cash: "Efectivo",
        check: "Cheque",
        giftCard: "Tarjeta de Regalo",
        adminAuthorized: "Autorizado por Administrador",
        tendered: "Entregado",
        change: "Cambio",
        adminFee: "Tarifa Administrativa",
        viewCart: "Ver Carrito",
        cartEmpty: "Tu carrito está vacío",
        continueShopping: "Continuar Comprando",
        orderSummary: "Resumen del Pedido",
        payNow: "Pagar Ahora",
        sendEmail: "Enviar Correo Electrónico",
        sendEmailText: "Ingresa la dirección de correo electrónico a la que deseas enviar el pedido.",
        formats: {
            fullPage: "Página Completa",
            ticket: "Ticket",
            kitchenTicket: "Ticket de Cocina",
        },
        empty: "No se encontraron pedidos",
        emptyPendingCharges: "No se encontraron cargos pendientes",
        transactionId: "ID de Transacción",
        ordersDashboard: "Panel de Pedidos",
        ordersByGroup: "Pedidos por Grupo",
        groups: "grupos",
        group: "grupo",
        merchant: "Comerciante",
        orderNumber: "Pedido #",
        noOrdersFound: "No se encontraron pedidos",
        noOrdersForStatus: "No se encontraron pedidos para {{status}}",
        statuses: {
            invalid: "Inválido",
            new: "Nuevo",
            fulfilling: "En Preparación",
            shipped: "Enviado",
            return: "Devolución",
        },
    },

    status: {
        all: "Todos",
        total: "Total",
        pending: "Pendiente",
        completed: "Completado",
        paid: "Pagado",
        overdue: "Vencido",
        outstanding: "Pendiente de Pago",
        unpaid: "No Pagado",
        shipped: "Enviado",
        canceled: "Cancelado",
        refunded: "Reembolsado",
        returned: "Devuelto",
        allOrders: "Todos los Pedidos",
        others: "Resto",
        failed: "Fallido",
        inFlight: "En tránsito",
        expired: "Expirado",
        voided: "Anulado",
        captured: "Capturado",
        uncaptured: "No Capturado",
        adminAuthorized: "Autorizado por Administrador",
        completePendingSettlement: "Completado / Pendiente de Liquidación",
    },

    user: {
        user: "Usuario",
        users: "Usuarios",
        newUser: "Nuevo Usuario",
        newFamilyMember: "Nuevo Miembro de la Familia",
        newGroupMember: "Nuevo Miembro del Grupo",
        familyMember: "Miembro de la Familia",
        groupMember: "Miembro del Grupo",
        editUser: "Editar Usuario",
        search: "Buscar Usuarios",
        profile: "Perfil de Usuario",
        myProfile: "Mi Perfil",
        myTransactions: "Mis Transacciones",
        personalSettings: "Configuración Personal",
        logout: "Cerrar Sesión",
        fullName: "Nombre Completo",
        firstName: "Nombre",
        lastName: "Apellido",
        middleName: "Segundo Nombre",
        mobilePhone: "Teléfono Móvil",
        homePhone: "Teléfono Fijo",
        email: "Correo Electrónico",
        address: "Dirección",
        address2: "Línea de Dirección #2",
        city: "Ciudad",
        state: "Estado/Provincia",
        zip: "Código Postal",
        country: "País",
        username: "Nombre de Usuario",
        password: "Contraseña",
        confirmPassword: "Confirmar Contraseña",
        dateOfBirth: "Fecha de Nacimiento",
        role: "Rol",
        relationship: "Relación",
        deleteTitle: "Eliminar Usuario(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los usuario(s) seleccionado(s)?",
        saved: "Información del usuario guardada correctamente",
        deleted: "Usuario eliminado correctamente",
        updated: "Información del usuario actualizada correctamente",
        created: "Usuario creado correctamente",
        lastLogin: "Último Inicio de Sesión",
        toolbar: {
            profile: "Perfil",
            orders: "Pedidos",
            events: "Eventos",
            subscriptions: "Suscripciones",
            family: "Familia",
            groups: "Grupos",
            pendingCharges: "Cargos Pendientes",
            wallet: "Monedero",
        },
        notes: "Notas",
        addNote: "Añadir una nota...",
        noteStatus: {
            public: "Público",
            adminOnly: "Solo Administradores",
            author: "Solo yo",
        },
        roles: {
            siteBossMasterAdmin: "Administrador Maestro de SiteBoss",
            siteBossSuperAdmin: "Superadministrador de SiteBoss",
            companyOwner: "Propietario de la Empresa",
            companyAdmin: "Administrador de la Empresa",
            staff: "Personal",
            nonStaffManager: "Gerente (No Personal)",
            patron: "Cliente",
        },
        relationships: {
            admin: "Administrador",
            parent: "Padre/Madre",
            child: "Hijo/Hija",
            guardian: "Tutor",
            friend: "Amigo/Amiga",
            spouse: "Cónyuge",
            partner: "Pareja",
            sibling: "Hermano/Hermana",
            other: "Otro",
        },
    },

    wallet: {
        wallet: "Monedero",
        wallets: "Monederos",
        balance: "Saldo",
        transactions: "Transacciones",
        empty: "No se encontró información del monedero",
        tokens: "Fichas",
        token: "Ficha",
        emptyTokens: "No se encontraron fichas",
        expirationDate: "Fecha de Caducidad",
        noExpirationDate: "Sin fecha de caducidad",
        useDate: "Fecha de Uso",
    },

    reports: {
        reports: "Informes",
        quickAccess: "Acceso Rápido",
        recentReports: "Informes Recientes",
        favorites: "Favoritos",
        reportCategories: "Categorías de Informes",
        filters: "Filtros",
        startDate: "Fecha de Inicio",
        endDate: "Fecha de Fin",
        cashier: "Cajero",
        apply: "Aplicar",
        totalSales: "Ventas Totales",
        transactions: "Transacciones",
        averageSale: "Venta Promedio",
        salesByPaymentMethod: "Ventas por Método de Pago",
        dailyRegisterSummary: "Resumen Diario de Caja",
        export: "Exportar",
        print: "Imprimir",
        all: "Todos",
        categories: {
            overview: {
                name: "Resumen General",
                description: "Ver todos los informes disponibles"
            },
            register: {
                name: "Cajas Registradoras",
                description: "Ver informes de transacciones de caja, flujo de efectivo y liquidaciones"
            },
            sales: {
                name: "Ventas",
                description: "Ver informes de ventas por producto, categoría y método de pago"
            },
            events: {
                name: "Eventos",
                description: "Ver informes de inscripciones a eventos, asistencia e ingresos"
            },
            outstanding: {
                name: "Pendientes",
                description: "Ver informes de pagos y facturas pendientes"
            },
            memberships: {
                name: "Suscripciones",
                description: "Ver informes de membresías y actividad de suscripciones"
            },
            users: {
                name: "Usuarios",
                description: "Ver informes de actividad de usuarios y datos demográficos"
            },
            usage: {
                name: "Uso",
                description: "Ver informes de uso y rendimiento del sistema"
            },
            services: {
                name: "Servicios",
                description: "Ver informes de reservas de servicios, proveedores y rendimiento"
            }
        },
        register: {
            name: "Caja Registradora",
            dailySales: "Ventas Diarias",
            settlements: "Liquidaciones",
            cashFlow: "Flujo de Efectivo",
            transactions: "Transacciones"
        },
        registerReports: "Informes de Caja Registradora",
        salesReports: "Informes de Ventas",
        eventReports: "Informes de Eventos",
        outstandingReports: "Informes de Pendientes",
        membershipReports: "Informes de Membresías y Suscripciones",
        userReports: "Informes de Usuarios",
        usageReports: "Informes de Uso",
        servicesReports: "Informes de Servicios",
        services: {
            bookings: "Reservas",
            providers: "Proveedores",
            performance: "Rendimiento",
            utilization: "Utilización"
        },
        sales: {
            byProduct: "Por Producto",
            byCategory: "Por Categoría",
            byPaymentMethod: "Por Método de Pago",
            trends: "Tendencias",
            topProducts: "Productos Más Vendidos",
            revenue: "Ingresos",
            profit: "Ganancia",
            margin: "Margen",
            quantity: "Cantidad",
            productPerformance: "Rendimiento del Producto",
            categoryPerformance: "Rendimiento de la Categoría",
            salesForecast: "Previsión de Ventas",
            comparePeriods: "Comparar Períodos",
            productType: "Tipo de Producto",
            timeFrame: "Período de Tiempo",
            daily: "Diario",
            weekly: "Semanal",
            monthly: "Mensual",
            quarterly: "Trimestral",
            yearly: "Anual",
            compareWith: "Comparar Con",
            previousPeriod: "Período Anterior",
            samePeroidLastYear: "Mismo Período del Año Pasado",
            custom: "Personalizado",
            salesBreakdown: "Desglose de Ventas",
            salesTrend: "Tendencia de Ventas",
            salesGrowth: "Crecimiento de Ventas",
            salesVelocity: "Velocidad de Ventas",
            averageOrderValue: "Valor Promedio del Pedido",
            conversionRate: "Tasa de Conversión",
            customerAcquisitionCost: "Costo de Adquisición de Clientes",
            customerLifetimeValue: "Valor de Vida del Cliente",
            returnOnInvestment: "Retorno de la Inversión",
            netProfit: "Ganancia Neta",
            grossProfit: "Ganancia Bruta"
        }
    },

    discount: {
        discounts: "Descuentos",
        discount: "Descuento",
        discountManager: "Gestor de Descuentos",
        addDiscount: "Añadir Descuento",
        editDiscount: "Editar Descuento",
        newDiscount: "Nuevo Descuento",
        name: "Nombre",
        code: "Código",
        type: "Tipo",
        validFrom: "Válido Desde",
        validUntil: "Válido Hasta",
        status: "Estado",
        active: "Activo",
        inactive: "Inactivo",
        autoApply: "Aplicar Automáticamente",
        noEndDate: "Sin Fecha de Fin",
        noDiscounts: "No se encontraron descuentos",
        deleteConfirmTitle: "Eliminar Descuento",
        deleteConfirmMessage: "¿Estás seguro de que quieres eliminar el descuento '{{name}}'?",
        success: "¡Descuento guardado correctamente!",
        wizard: {
            name: {
                title: "Nombre y Descripción",
                subtitle: "Información básica sobre el descuento",
                description: "Ingresa un nombre y una descripción para tu descuento",
                label: "Nombre del Descuento"
            },
            description: {
                label: "Descripción"
            },
            auto: {
                title: "Método de Aplicación",
                subtitle: "Cómo se aplicará el descuento",
                description: "Elige si este descuento se aplicará automáticamente o requerirá un código de cupón",
                label: "Método de Aplicación",
                autoApply: "Aplicar automáticamente a los pedidos que califiquen",
                couponCode: "Requerir un código de cupón",
                codeLabel: "Código de Cupón",
                codeHelper: "Ingresa un código único que los clientes usarán para aplicar este descuento"
            },
            maxUses: {
                title: "Límites de Uso",
                subtitle: "Cuántas veces se puede usar este descuento",
                description: "Establece si este descuento tiene usos ilimitados o un límite específico",
                label: "Límite de Uso",
                unlimited: "Usos ilimitados",
                limited: "Número limitado de usos",
                maxLabel: "Número Máximo de Usos",
                maxHelper: "Ingresa el número máximo de veces que se puede usar este descuento"
            },
            dates: {
                title: "Fechas de Validez",
                subtitle: "Cuándo es válido este descuento",
                description: "Establece el rango de fechas en que se puede usar este descuento",
                fromLabel: "Válido Desde",
                untilLabel: "Válido Hasta",
                noEndDate: "Sin fecha de fin"
            },
            type: {
                title: "Tipo de Descuento",
                subtitle: "Cómo se calcula el monto del descuento",
                description: "Elige si este es un descuento porcentual o de monto fijo",
                label: "Tipo de Descuento",
                percentage: "Descuento porcentual",
                fixed: "Descuento de monto fijo",
                percentLabel: "Porcentaje",
                amountLabel: "Monto"
            },
            applyTo: {
                title: "Ámbito de Aplicación",
                subtitle: "A qué se aplica el descuento",
                description: "Elige si este descuento se aplica a todo el pedido o a artículos específicos",
                label: "Aplicar A",
                entireOrder: "Pedido Completo",
                specificItems: "Artículos Específicos"
            },
            combo: {
                title: "Reglas de Combinación",
                subtitle: "Si este descuento se puede combinar con otros",
                description: "Elige si este descuento se puede usar en combinación con otros descuentos",
                label: "¿Se puede combinar este descuento con otros?",
                yes: "Sí",
                no: "No"
            },
            conditions: {
                title: "Condiciones",
                subtitle: "Requisitos para que se aplique este descuento",
                description: "Establece las condiciones que deben cumplirse para que se aplique este descuento",
                label: "Condiciones"
            },
            summary: {
                title: "Resumen",
                subtitle: "Revisa y activa tu descuento",
                description: "Revisa los detalles de tu descuento y establece su estado",
                statusLabel: "Estado del Descuento",
                active: "Activo",
                inactive: "Inactivo"
            }
        }
    },

    waiver: {
        waiver: "Exención de Responsabilidad",
        waivers: "Exenciones de Responsabilidad",
        notSigned: "{{first_name}} no ha firmado su exención de responsabilidad",
        signed: "{{first_name}} ha firmado su exención de responsabilidad",
        requiredMessage: "{{company_name}} requiere una exención de responsabilidad firmada para participar en actividades y servicios",
        signNow: "Firmar ahora",
        signLater: "Firmar más tarde",
    },

    subscription: {
        subscription: "Suscripción",
        subscriptions: "Suscripciones",
        intervals: "Intervalos",
        interval: "Intervalo",
        nextBillDate: "Próxima Fecha de Facturación",
        lastBillDate: "Última Fecha de Facturación",
        firstBillDate: "Primera Fecha de Facturación",
        finalBillDate: "Fecha Final de Facturación",
        restartBillDate: "Fecha de Reanudación de Facturación",
        intervalQuantity: "Cantidad de Intervalo",
        every: "Cada",
        days: "Días",
        day: "Día",
        weeks: "Semanas",
        week: "Semana",
        months: "Meses",
        month: "Mes",
        years: "Años",
        year: "Año",
        starting: "Comenzando",
        ending: "Terminando",
        on: "el",
        of: "de",
        for: "por",
        this: "este",
        billed: "Facturado",
        billedEvery: "Facturado cada",
        intervalTypes: {
            daily: "Diario",
            weekly: "Semanal",
            monthly: "Mensual",
            yearly: "Anual",
        },
        status: {
            active: "Activa",
            suspended: "Suspendida",
            cancelled: "Cancelada",
            expired: "Expirada",
        },
        empty: "No se encontraron suscripciones",
        cycle: "Ciclo",
        cycles: "Ciclos",
    },

    program: {
        program: "Programa",
        programs: "Programas",
        newProgram: "Nuevo Programa",
        editProgram: "Editar Programa",
        search: "Buscar Programas",
        empty: "No se encontraron programas",
        name: "Nombre",
        type: "Tipo",
        groupTypeName: "Tipos de Grupo",
        shortDescription: "Descripción Corta",
        description: "Descripción",
        tags: "Etiquetas",
        images: "Imágenes del Programa",
        selectImages: "Seleccionar Imágenes",
        deleteTitle: "Eliminar Programa(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los programa(s) seleccionado(s)?",
        success: "Programa guardado correctamente",
        deleted: "Programa eliminado correctamente",
        childEvents: "{{count}} eventos secundarios",
        toolbar: {
            basic: "Información del Programa",
            groupTypes: "Tipos de Grupos",
            images: "Imágenes",
        },
        groupTypes: {
            list: "Tipos de Grupos Disponibles",
            assigned: "Asignados al programa",
            add: "Añadir Tipo de Grupo",
            remove: "Quitar Tipo de Grupo",
            create: "Nuevo Tipo de Grupo",
            addAll: "Añadir Todos",
            removeAll: "Quitar Todos",
            emptyList: "No se encontraron tipos de grupo",
            emptyAssigned: "No hay tipos de grupo asignados",
        },
    },

    event: {
        event: "Evento",
        events: "Eventos",
        upcomingEvents: "Próximos Eventos",
        pastEvents: "Eventos Pasados",
        newEvent: "Nuevo Evento",
        editEvent: "Editar Evento",
        search: "Buscar Eventos",
        empty: "No se encontraron eventos",
        name: "Nombre",
        type: "Tipo",
        status: "Estado",
        startDate: "Fecha de Inicio",
        endDate: "Fecha de Fin",
        dateTime: "Fecha y Hora",
        location: "Ubicación",
        attendeesColumn: "Asistentes",
        pricing: "Precios",
        media: "Multimedia",
        defaultPrice: "Predeterminado",
        additionalVariants: "más opciones",
        to: "a",
        deleteTitle: "Eliminar Evento(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los evento(s) seleccionado(s)?",
        success: "Evento guardado correctamente",
        deleted: "Evento eliminado correctamente",
        belowAge: "Demasiado joven para este evento",
        aboveAge: "Demasiado mayor para este evento",
        eventCapacityFull: "Este evento está lleno",
        eventCapacityUnder: "Este evento está por debajo de la capacidad mínima",
        eventNotAvailable: "Este evento ha finalizado o no está disponible",
        types: {
            class: "Clase",
            practice: "Práctica",
            reservation: "Reserva",
            game: "Juego",
            series: "Serie",
            clubSignUps: "Inscripciones al Club",
            camp: "Campamento",
            tournament: "Torneo",
            serviceBooking: "Reserva de Servicio",
        },
        statuses: {
            pending: "Pendiente",
            confirmed: "Confirmado",
            postponed: "Pospuesto",
            cancelled: "Cancelado",
            reservedPrivate: "Reservado / Privado",
            archived: "Archivado",
            inCart: "En el Carrito",
            expired: "Expirado",
        },
        roles: {
            owner: "Propietario",
            manager: "Gerente",
            attendee: "Asistente",
        },
        toolbar: {
            basic: "Información del Evento",
            groups: "Grupos",
            tree: "Árbol del Evento",
            attendees: "Asistentes",
            statistics: "Estadísticas",
            images: "Imágenes",
            customFields: "Campos Personalizados",
        },
        groups: {
            list: "Grupos Disponibles",
            assigned: "Asignados al evento",
            add: "Añadir Grupo",
            remove: "Quitar Grupo",
            create: "Nuevo Grupo",
            addAll: "Añadir Todos",
            removeAll: "Quitar Todos",
            emptyList: "No se encontraron grupos",
            emptyAssigned: "No hay grupos asignados",
            showTags: "Mostrar Etiquetas",
            hideTags: "Ocultar Etiquetas",
            members: "Miembros",
            memberType: {
                allUsers: "Todos los Usuarios",
                registeredUsers: "Usuarios Registrados",
            },
        },
        attendees: {
            list: {
                all: "Todos",
                pending: "Pendiente",
                attending: "Asistiendo",
                notAttending: "No Asistiendo",
                tentative: "Tentativo",
                invite: "Invitar",
            },
            empty: "No se encontraron asistentes",
            invite: "Invitar personas o grupos",
            notRequired: "No se requieren asistentes para este evento",
        },
        wizard: {
            details: {
                title: "Detalles del Evento",
                subtitle: "Información básica",
                description: "Añade información básica sobre tu evento, como el nombre y la descripción."
            },
            parentEvent: {
                title: "Evento Principal / Programa",
                subtitle: "Evento o programa principal",
                description: "Si el evento forma parte de otro evento, por ejemplo, un torneo o una serie, puedes seleccionar un evento principal.\n\nO selecciona un programa si este es el evento principal de un programa.",
            },
            eventTags: {
                title: "Etiquetas del Evento",
                subtitle: "Añadir etiquetas",
                description: "Añade etiquetas que describan tu evento. Esto ayudará a los asistentes a encontrarlo más fácilmente.",
            },
            eventImages: {
                title: "Imágenes del Evento",
                subtitle: "Añadir imágenes",
                description: "Añade imágenes para mostrar tu evento. Puedes añadir varias imágenes. El formato debe ser JPG, PNG o GIF.",
            },
            eventType: {
                title: "Tipo de Evento",
                subtitle: "Selecciona el tipo de evento",
                description: "Selecciona el tipo de evento que estás creando. Esto ayudará a categorizar tu evento y facilitará que los asistentes lo encuentren."
            },
            eventDate: {
                title: "Fecha y Hora del Evento",
                subtitle: "Selecciona la fecha y la hora",
                description: "Selecciona la fecha y la hora de tu evento. Si tu evento tiene varias fechas, el evento se creará en cada una de estas fechas a la misma hora.",
            },
            timeSheet: {
                title: "Franjas Horarias Disponibles",
                subtitle: "Selecciona las franjas horarias para tu evento",
                description: "Selecciona las franjas horarias en las que tu evento estará disponible. Puedes seleccionar una franja por día de la semana si la franja está disponible.",
                error: "Por favor, selecciona una ubicación, fecha y rango horario para tu evento."
            },
            ageRequirement: {
                title: "Requisito de Edad",
                subtitle: "Establece el requisito de edad",
                description: "Selecciona el requisito de edad para tu evento. Si el evento no tiene requisito de edad, déjalo en blanco.",
                stepLabel: "año(s) de edad",
            },
            registrationRequirement: {
                title: "Requisito de Inscripción",
                subtitle: "Establece el requisito de inscripción",
                description: "Selecciona si los asistentes necesitan inscribirse para tu evento y, de ser así, si necesitan inscribirse para cada evento o solo una vez.",
            },
            registrationMessages: {
                title: "Mensajes de Inscripción",
                subtitle: "Personaliza los mensajes de inscripción",
                description: "Personaliza los mensajes que los asistentes verán cuando se inscriban en tu evento. Puedes personalizar el mensaje de inscripción, el mensaje de confirmación y los mensajes para asistentes por debajo y por encima del requisito de edad.",
            },
            customFields: {
                title: "Campos Personalizados",
                subtitle: "Añadir campos personalizados",
                description: "Define cualquier campo personalizado que los asistentes necesiten completar al inscribirse en tu evento. Puedes añadir campos de texto, listas desplegables y más.",
                empty: "Este evento no tiene campos personalizados",
            },
            paymentOptions: {
                title: "Precios",
                subtitle: "Precios y planes",
                description: "Si tu evento requiere un pago, puedes añadir planes de pago que los asistentes pueden elegir. Puedes crear diferentes planes, como pagos únicos, cuotas, etc.",
            },
            locations: {
                title: "Ubicaciones y Fechas",
                subtitle: "Dónde y cuándo",
                description: "Añade ubicaciones y las fechas/horas para tu evento. Puedes añadir múltiples ubicaciones y fechas si están disponibles.",
            },
            requirements: {
                title: "Requisitos y Restricciones",
                subtitle: "Lo que el asistente necesita",
                description: "Establece requisitos y restricciones para tu evento, como si requiere inscripción, una entrada, un límite de edad, etc.",
            },
            review: {
                title: "Revisar y Publicar",
                subtitle: "Revisión final",
                description: "Revisa los detalles de tu evento y publícalo. Siempre puedes editarlo más tarde.",
            },
            name: "Nombre del Evento",
            shortDescription: "Descripción Corta",
            description: "Descripción",
            tags: "Etiquetas",
            images: "Imágenes",
            type: "Tipo",
            parent: "Evento Principal",
            program: "Programa",
            location: "Ubicación",
            timeSheets: "Franjas Horarias",
            time: "Hora",
            date: "Fecha",
            startDate: "Fecha de Inicio",
            endDate: "Fecha de Fin",
            recurring: "Este es un evento recurrente",
            ageRange: "Rango de Edad",
            registration: "Requiere Inscripción",
            registrationType: "Tipo de Inscripción",
            registrationPerEvent: "Por Evento",
            registrationAllEvents: "Todos los Eventos",
            registrationMessage: "Mensaje de Inscripción",
            confirmationMessage: "Mensaje de Confirmación",
            belowAgeMessage: "Mensaje para Menores de Edad",
            aboveAgeMessage: "Mensaje para Mayores de Edad",
            customFieldLabel: "Etiqueta / Marcador de posición",
            customFieldType: "Tipo de Campo",
            customFieldTypeText: "Texto",
            customFieldTypeSelect: "Lista Desplegable",
            customFieldRequired: "Requerido",
            customFieldOptions: "Opciones",
            customFieldOptionValue: "Valor de Opción #{{number}}",
            customFieldOptionLabel: "Etiqueta de Opción #{{number}}",
            pricingFullFee: "Tarifa Completa",
            pricingRecurring: "Cargo Recurrente",
            pricingName: "Nombre del Plan",
            pricingUpfront: "Tarifa Inicial",
            pricingPrice: "Tarifa de Cargo",
            addCustomField: "Añadir Campo Personalizado",
            addPaymentOption: "Añadir Plan de Pago",
        },
        charts: {
            signUps: {
                title: "Inscripciones",
                subtitle: "Inscripciones al evento por día",
            },
            paymentDistribution: {
                title: "Distribución de Pagos",
                subtitle: "Distribución de pagos para el evento",
            },
        },
    },

    eventType: {
        eventType: "Tipo de Evento",
        eventTypes: "Tipos de Evento",
        newEventType: "Nuevo Tipo de Evento",
        editEventType: "Editar Tipo de Evento",
        search: "Buscar Tipos de Evento",
        empty: "No se encontraron tipos de evento",
        name: "Nombre",
        description: "Descripción",
        isMeta: "Meta Evento",
        isProgram: "Programa",
        deleteTitle: "Eliminar Tipo(s) de Evento",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los tipo(s) de evento seleccionado(s)?",
        saved: "Tipo de evento guardado correctamente",
        deleted: "Tipo de evento eliminado correctamente",
        updated: "Tipo de evento actualizado correctamente",
        created: "Tipo de evento creado correctamente",
    },

    group: {
        group: "Grupo",
        groups: "Grupos",
        newGroup: "Nuevo Grupo",
        editGroup: "Editar Grupo",
        search: "Buscar Grupos",
        addMember: "Añadir Miembro",
        addMembers: "Añadir Miembros",
        memberList: "Lista de Miembros",
        inviteMember: "Invitar Miembro",
        relationship: "Relación",
        relationships: "Relaciones",
        member: "Miembro",
        members: "Miembros",
        join: "Unirse",
        leave: "Salir",
        accept: "Aceptar",
        reject: "Rechazar",
        empty: "No se encontraron grupos",
        emptyFamily: "No se encontró grupo familiar",
        type: {
            department: "Departamento",
            team: "Equipo",
            friends: "Amigos",
            family: "Familia",
            league: "Liga",
            club: "Club",
            class: "Clase",
            employee: "Empleado",
            organization: "Organización",
            business: "Negocio",
            other: "Otro",
        },
        status: {
            active: "Activo",
            pending: "Pendiente",
            inactive: "Inactivo",
            suspended: "Suspendido",
            banned: "Expulsado",
        },
        name: "Nombre",
        groupType: "Tipo de Grupo",
        tags: "Etiquetas",
        deleteTitle: "Eliminar Grupo(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los grupo(s) seleccionado(s)?",
        saved: "Información del grupo guardada correctamente",
        deleted: "Grupo eliminado correctamente",
        updated: "Información del grupo actualizada correctamente",
        created: "Grupo creado correctamente",
    },

    groupType: {
        groupType: "Tipo de Grupo",
        groupTypes: "Tipos de Grupo",
        newGroupType: "Nuevo Tipo de Grupo",
        editGroupType: "Editar Tipo de Grupo",
        search: "Buscar Tipos de Grupo",
        empty: "No se encontraron tipos de grupo",
        name: "Nombre",
        description: "Descripción",
        deleteTitle: "Eliminar Tipo(s) de Grupo",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los tipo(s) de grupo seleccionado(s)?",
        saved: "Tipo de grupo guardado correctamente",
        deleted: "Tipo de grupo eliminado correctamente",
        updated: "Tipo de grupo actualizado correctamente",
        created: "Tipo de grupo creado correctamente",
        customFields: "Campos Personalizados",
    },

    website: {
        website: "Sitio Web",
        websites: "Sitios Web",
        newWebsite: "Nuevo Sitio Web",
        editWebsite: "Editar Sitio Web",
        search: "Buscar Sitios Web",
        empty: "No se encontraron sitios web",
        name: "Nombre",
        description: "Descripción",
        keywords: "Palabras Clave",
        theme: "Tema",
        deleteTitle: "Eliminar Sitio(s) Web",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los sitio(s) web seleccionado(s)?",
        success: "Sitio web guardado correctamente",
        deleted: "Sitio web eliminado correctamente",
        page: {
            empty: "No se encontraron páginas",
            newPage: "Nueva Página",
            subtitle: "Añade una nueva página al sitio web seleccionado",
            batchCreate: "Creación por Lotes",
            batchCreateSubtitle: "¡Crea todas las páginas que necesites de una vez!",
            generatePages: "Generar Páginas",
            type: {
                category: {
                    new: "Nueva Categoría",
                    name: "Nombre de la Categoría",
                    parentId: "Categoría Principal",
                    slug: "Slug",
                    toolbar: {
                        general: "Categoría",
                    },
                    deleteTitle: "Eliminar Categorías",
                    deleteMessage: "¿Estás seguro de que quieres eliminar las categorías seleccionadas?",
                },
                template: {
                    new: "Nueva Plantilla",
                    newSubtitle: "Añade una nueva plantilla que puedas usar para tus páginas",
                },
                blog: {
                    new: "Nuevo Blog",
                    newSubtitle: "Añade un nuevo blog para compartir tus pensamientos, ideas y más",
                    editSubtitle: "Gestiona las entradas, categorías, páginas y configuraciones de tu blog",
                    newPage: "Nueva Página de Blog",
                    newPageSubtitle: "Añade una nueva página con capacidades de blog",
                    form: {
                        name: "Nombre del Blog",
                        nameHelper: "El nombre del blog",
                        slug: "Slug del Blog",
                        slugHelper: "El slug utilizado para acceder al blog",
                        customFields: "Campos Personalizados",
                        customFieldsHelper: "Añade campos personalizados a las entradas de tu blog",
                        index: "Slug de la Página de Inicio",
                        indexHelper: "El slug utilizado para acceder a la página de inicio del blog",
                        post: "Slug de la Página de Entrada",
                        postHelper: "El slug utilizado para acceder a una entrada del blog",
                        posts: "Slug del Listado de Entradas",
                        postsHelper: "El slug utilizado para acceder a la lista de entradas del blog",
                        search: "Slug de la Página de Búsqueda",
                        searchHelper: "El slug utilizado para acceder a la página de búsqueda",
                        toolbar: {
                            general: "Configuración del Blog",
                            slugs: "Páginas",
                            customFields: "Campos Personalizados",
                        },
                    },
                    entries: {
                        title: "Título de la Entrada del Blog",
                        slug: "Slug de la Entrada del Blog",
                        slugHelper: "El slug utilizado para acceder a la entrada del blog",
                        content: "Contenido",
                        images: "Imágenes",
                        attachments: "Archivos Adjuntos",
                        categories: "Categorías",
                        tags: "Etiquetas",
                        authors: "Autores",
                        status: "Estado",
                        publishDate: "Fecha de Publicación",
                        endDate: "Fecha de Fin",
                        relatedPosts: "Entradas Relacionadas",
                        metaTitle: "Meta Título",
                        metaDescription: "Meta Descripción",
                        metaKeywords: "Meta Palabras Clave",
                        toolbar: {
                            general: "Entrada del Blog",
                            media: "Multimedia",
                            meta: "Metadatos",
                            customFields: "Campos Personalizados",
                            properties: "Propiedades",
                            advanced: "Avanzado",
                        },
                        deleteTitle: "Eliminar Entrada(s) del Blog",
                        deleteMessage: "¿Estás seguro de que quieres eliminar la(s) entrada(s) del blog seleccionada(s)?",
                    },
                },
                ecommerce: {
                    new: "Nueva Tienda",
                    newSubtitle: "Añade una nueva tienda para vender productos, servicios, registrarse en eventos, etc. Cada tienda tiene sus propias páginas",
                    editSubtitle: "Gestiona las páginas y configuraciones de tu tienda",
                    newPage: "Nueva Página de Comercio Electrónico",
                    newPageSubtitle: "Añade una nueva página con capacidades de comercio electrónico",
                    form: {
                        name: "Nombre de la Tienda",
                        nameHelper: "El nombre de la tienda",
                        slug: "Slug de la Tienda",
                        slugHelper: "El slug utilizado para acceder a la tienda",
                        index: "Slug de la Página de Inicio",
                        indexHelper: "El slug utilizado para acceder a la página de inicio de la tienda",
                        cart: "Slug de la Página del Carrito",
                        cartHelper: "El slug utilizado para acceder a la página del carrito",
                        checkout: "Slug de la Página de Pago",
                        checkoutHelper: "El slug utilizado para acceder a la página de pago",
                        confirmation: "Slug de Confirmación de Pago",
                        confirmationHelper: "El slug utilizado para acceder a la página de confirmación de pago",
                        product: "Slug de la Página de Producto",
                        productHelper: "El slug utilizado para acceder a los detalles del producto",
                        products: "Slug de la Página de Listado de Productos",
                        productsHelper: "El slug utilizado para acceder a la página de listado de productos",
                        category: "Slug de la Página de Categoría",
                        categoryHelper: "El slug utilizado para acceder a la página de categoría",
                        event: "Slug de la Página de Evento",
                        eventHelper: "El slug utilizado para acceder a la página del evento",
                        events: "Slug de la Página de Listado de Eventos",
                        eventsHelper: "El slug utilizado para acceder a la página de listado de eventos",
                        registerId: "ID del Registro",
                        registerIdHelper: "El ID del registro a usar para esta tienda",
                        toolbar: {
                            general: "Configuración de la Tienda",
                            slugs: "Páginas",
                        },
                    },
                },
            },
            entries: "Entradas",
            categories: "Categorías",
            tags: "Etiquetas",
            statuses: {
                draft: "Borrador",
                published: "Publicado",
                archived: "Archivado",
                deleted: "Eliminado",
            },
        },
        toolbar: {
            basic: "Información del Sitio Web",
            urls: "URLs",
            pages: "Páginas",
            formSubmissions: "Envíos de Formularios",
            templates: "Plantillas",
            storedWidgets: "Widgets Guardados",
            ecommerce: "Comercio Electrónico",
            blog: "Blog",
            wiki: "Wiki",
            articles: "Artículos",
            contracts: "Contratos",
            sitemap: "Mapa del Sitio",
        },
        urls: {
            url: "URL",
            urls: "URLs",
            domain: "Dominio",
            subdomain: "Subdominio",
            indexPage: "Página de Inicio",
            remove: "Quitar URL",
            add: "Añadir URL",
        },
    },

    builder: {
        name: "Constructor",
        addSection: "Añadir Nueva Sección",
        mobileWarning: "El constructor de sitios web no es compatible con tu dispositivo móvil.",
        actions: "Acciones",
        empty: "¡Un lienzo de infinitas posibilidades te espera!\nSelecciona un bloque para empezar a construir.",
        emptyTemplate: "¡Empecemos!\nAjusta la plantilla para que se adapte a tu sitio web.",
        loadHistory: "¿Estás seguro de que quieres cargar la versión de página seleccionada?",
        closeBuilder: "Cualquier cambio no guardado se perderá. ¿Estás seguro de que quieres cerrar el constructor?",
        contentGoesHere: "¡Este espacio se llenará de maravillas!",
        toolbar: {
            chat: "Chat",
            widgets: "Widgets",
            blocks: "Bloques",
            settings: "Configuración",
            themes: "Temas",
            sourceCode: "Código Fuente",
            history: "Historial",
            boxModel: "Modelo de Caja",
        },
        settings: {
            showHeader: "Mostrar Encabezado",
            showFooter: "Mostrar Pie de Página",
            addToMenu: "Añadir al Menú",
            redirectTo: "Redirigir a",
        },
        storedWidget: {
            save: "Guardar como widget almacenado",
            load: "Cargar widget almacenado",
        },
        page: {
            title: "Título de la Página",
            slug: "Slug de la Página",
            keywords: "Palabras Clave",
            template: "Plantilla",
        },
        component: {
            layouts: "Diseños",
            properties: "Propiedades",
            margin: "Margen",
            padding: "Relleno",
            maxWidth: "Ancho Máximo",
            width: "Ancho",
            background: "Color de Fondo",
            center: "Centrar",
            inner: "interno",
            outer: "externo",
            boxModel: "Modelo de Caja",
            types: {
                general: "General",
                ecommerce: "Comercio Electrónico",
                pos: "Comercio Electrónico",
                event: "Eventos",
                blog: "Blog",
                cms: "General",
            },
            header: {
                name: "Encabezado",
                layouts: {
                    basic: "Básico",
                    centered: "Centrado",
                    logo: "Solo Logo",
                },
                title: 'Título',
                logo: 'Logo',
                showMenu: 'Mostrar Menú',
                sticky: 'Fijo',
            },
            footer: {
                name: "Pie de Página",
                layouts: {
                    basic: "Básico",
                },
                title: 'Título',
                logo: 'Logo',
                showMenu: 'Mostrar Menú',
                copyRight: 'Derechos de Autor',
                socialMedia: 'Redes Sociales',
            },
            body: {
                name: "Cuerpo",
                layouts: {
                    default: "Predeterminado",
                    centered: "Centrado",
                },
                cols: "Columnas",
                colsHelper: "El número de columnas a usar para esta sección",
            },
            core: {
                name: "Núcleo",
                layouts: {
                    centered: "Centrado",
                    mediaFirst: "Multimedia Primero",
                    mediaRight: "Multimedia Derecha",
                    mediaLeft: "Multimedia Izquierda",
                    mediaOverlay: "Superposición de Multimedia",
                },
                mediaType: "Tipo de Multimedia",
                mediaTypes: {
                    image: "Imagen",
                    video: "Video",
                },
                videoUrl: "URL del Video",
                autoplay: "Reproducción Automática",
                muted: "Silenciado",
                controls: "Mostrar Controles",
                images: "Imágenes",
                buttons: "Botones",
            },
            heading: {
                name: "Texto",
                layouts: {
                    default: "Predeterminado",
                    caption: "Leyenda",
                },
                title: "Título",
                subtitle: "Subtítulo",
                body: "Texto",
                type: "Tipo",
                types: {
                    h1: "Encabezado 1",
                    h2: "Encabezado 2",
                    h3: "Encabezado 3",
                    h4: "Encabezado 4",
                    h5: "Encabezado 5",
                    h6: "Encabezado 6",
                    subtitle1: "Subtítulo 1",
                    subtitle2: "Subtítulo 2",
                    subtitle3: "Subtítulo 3",
                    body1: "Cuerpo 1",
                    body2: "Cuerpo 2",
                    body3: "Cuerpo 3",
                    caption: "Leyenda",
                    overline: "Sobrelínea",
                    code: "Código",
                    p: "Párrafo",
                    span: "Span",
                    div: "Div",
                }
            },
            button: {
                name: "Botón",
                layouts: {
                    button: "Botón",
                    link: "Enlace",
                    icon: "Icono",
                },
                url: "URL",
                label: "Etiqueta",
                icon: {
                    title: "Icono",
                    helperText: "Nombre del icono de Material Icons\nhttps://mui.com/material-ui/material-icons/",
                },
                iconPosition: {
                    title: "Posición del Icono",
                    start: "Inicio",
                    end: "Fin",
                    default: "Predeterminado",
                },
                variant: {
                    title: "Variante",
                    contained: "Contenido",
                    outlined: "Contorneado",
                    text: "Texto",
                },
                color: {
                    title: "Color",
                    primary: "Primario",
                    secondary: "Secundario",
                    success: "Éxito",
                    error: "Error",
                    info: "Información",
                    warning: "Advertencia",
                    inherit: "Heredar",
                },
                size: {
                    title: "Tamaño",
                    small: "Pequeño",
                    medium: "Mediano",
                    large: "Grande",
                },
                target: {
                    title: "Destino",
                    blank: "Nueva Pestaña",
                    self: "Misma Pestaña",
                    popup: "Ventana Emergente",
                },
            },
            divider: {
                name: "Divisor",
                layouts: {
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                orientation: "Orientación",
                orientations: {
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                flexItem: "Elemento Flexible",
                flexItemHelper: "Si está marcado, el divisor tendrá la altura correcta cuando se use en un contenedor flexible.",
                variant: "Variante",
                variants: {
                    fullWidth: "Ancho Completo",
                    inset: "Sangrado",
                    middle: "Medio",
                },
                textAlign: "Alineación del Texto",
                aligns: {
                    left: "Izquierda",
                    center: "Centro",
                    right: "Derecha",
                },
            },
            countDown: {
                name: "Cuenta Regresiva",
                layouts: {
                    default: "Predeterminado",
                },
                dateTime: "Fecha y Hora de Finalización",
                variant: "Variante",
                timeUpMessage: "Mensaje de tiempo agotado",
                interval: "Intervalo",
                intervalType: "Tipo de Intervalo",
                format: "Formato",
                formats: {
                    long: "Largo",
                    short: "Corto",
                    shortest: "Más Corto",
                },
            },
            list: {
                name: "Lista",
                layouts: {
                    default: "Predeterminado",
                },
                type: "Tipo",
                dense: "Denso",
                disablePadding: "Desactivar Relleno",
                types: {
                    none: "Ninguno",
                    icon: "Icono",
                    avatar: "Avatar",
                    bullet: "● Viñeta",
                    circle: "○ Círculo",
                    square: "■ Cuadrado",
                    decimal: "1. Números",
                    decimalLeadingZero: "01. Números con Cero Inicial",
                    lowerAlpha: "a. Letras Minúsculas",
                    upperAlpha: "A. Letras Mayúsculas",
                    lowerRoman: "i. Números Romanos Minúsculos",
                    upperRoman: "I. Números Romanos Mayúsculos",
                }
            },
            hero: {
                name: "Bloque Hero",
                layouts: {
                    centered: "Centrado",
                    left: "Izquierda",
                    right: "Derecha",
                },
                image: "Imagen de Fondo",
                imageHeight: "Altura de la Imagen",
                title: "Frase Pegadiza",
                body: "Descripción Corta",
                callToActionUrl: "URL de Llamada a la Acción",
                callToActionText: "Etiqueta de Llamada a la Acción",
            },
            gallery: {
                name: "Galería",
                layouts: {
                    carousel: "Carrusel",
                    list: "Lista",
                    masonry: "Mampostería",
                },
                images: "Imágenes",
                imageSize: "Tamaño de Imagen",
                objectPosition: "Posición de Imagen",
                objectFit: "Ajuste de Imagen",
            },
            breadcrumb: {
                name: "Migas de Pan",
                layouts: {
                    default: "Predeterminado",
                },
                separator: "Separador",
                maxItems: "Máximo de Elementos",
                underline: "Subrayado",
                variant: "Variante",
                alignItems: "Alinear Elementos",
                underlines: {
                    none: "Ninguno",
                    hover: "Al Pasar el Ratón",
                    always: "Siempre",
                },
                aligns: {
                    center: "Centro",
                    left: "Izquierda",
                    right: "Derecha",
                },
            },
            events: {
                name: "Eventos",
                layouts: {
                    schedule: "Horario",
                    month: "Mes",
                    week: "Semana",
                    day: "Día",
                },
                shopId: "Sitio de Comercio Electrónico",
                type: "Tipo de evento",
                types: {
                    upcoming: "Próximos Eventos",
                    past: "Eventos Pasados",
                    all: "Todos los Eventos",
                },
            },
            eventDetail: {
                name: "Evento",
                layouts: {
                    default: "Predeterminado",
                },
                id: "ID del Evento",
                type: "Carga del Evento",
                types: {
                    automatic: "Automático",
                    manual: "Manual",
                    helperText: "Automático carga el evento desde el slug de la URL. Manual lo carga desde el selector de eventos.",
                },
            },
            products: {
                shopId: "Sitio de Comercio Electrónico",
                itemsToLoad: "Elementos a cargar a la vez",
                layoutType: "Tipo de Diseño",
                details: "Detalles",
                fullPage: "Redirigir a la página del producto al hacer clic en un elemento",
                modalSize: "Tamaño del Modal",
                galleryLayoutType: "Diseño de Galería",
                variantsLayoutType: "Diseño de Variantes",
                addonsLayoutType: "Diseño de Complementos",
                eventUsersLayoutType: "Diseño de Usuarios del Evento",
                memoLayoutType: "Diseño de Memo",
                showCategories: "Mostrar Categorías",
                categories: "Categorías",
            },
            productDetail: {
                name: "Producto",
                layouts: {
                    default: "Predeterminado",
                },
                id: "ID del Producto",
                variantId: "ID de Variante",
                type: "Carga del Producto",
                types: {
                    automatic: "Automático",
                    manual: "Manual",
                    helperText: "Automático carga el producto desde el slug de la URL. Manual lo carga desde el selector de productos.",
                },
            },
            cart: {
                name: "Carrito",
                layouts: {
                    list: "Lista",
                    table: "Tabla",
                },
                redirectToProductPage: "Redirigir a la página del producto al hacer clic en un elemento",
            },
            checkout: {
                name: "Pago",
                layouts: {
                    default: "Predeterminado",
                },
                showTips: "Mostrar Propinas",
                showTotals: "Mostrar Totales",
                totalItems: "Elementos en totales",
                paymentMethods: "Métodos de Pago",
                allowMultiplePayments: "Permitir múltiples pagos",
                redirectToSuccess: "Redirigir a la página de éxito después del pago",
            },
            posTotals: {
                name: "Totales",
                layouts: {
                    default: "Predeterminado",
                    sticky: "Fijo",
                },
                showTotals: "Mostrar Totales",
                showButton: "Mostrar Botón",
                showCheckoutInModal: "Mostrar Pago en Modal",
            },
            posSuccess: {
                name: "Compra Exitosa",
                layouts: {
                    default: "Predeterminado",
                },
                showOrderPreview: "Mostrar Vista Previa del Pedido",
                showTransactions: "Mostrar Transacciones",
                showPrintButton: "Mostrar Botón de Imprimir",
            },
            blogPost: {
                name: "Entrada de Blog",
                layouts: {
                    default: "Predeterminado",
                },
                id: "ID de Entrada de Blog",
                type: "Carga de Entrada de Blog",
                types: {
                    automatic: "Automático",
                    manual: "Manual",
                    helperText: "Automático carga la entrada de blog desde el slug de la URL. Manual la carga desde el selector de entradas de blog.",
                },
            },
            pos: {
                sizes: {
                    extraSmall: "Extra Pequeño",
                    small: "Pequeño",
                    medium: "Mediano",
                    large: "Grande",
                    extraLarge: "Extra Grande",
                },
                layoutTypes: {
                    card: "Tarjetas",
                    list: "Listas",
                    grid: "Cuadrícula",
                    masonry: "Mampostería",
                    button: "Botones",
                    icon: "Iconos",
                    link: "Enlaces",
                    checkbox: "Casillas de Verificación",
                    radio: "Botones de Radio",
                },
                orderNumber: {
                    buttonType: "Tipo de Botón",
                },
                product: {
                    items: "Artículos",
                    itemsToLoad: "Artículos a Cargar",
                    layoutType: "Tipo de Diseño",
                    details: "Detalles",
                    fullPage: "Página Completa",
                    modalSize: "Tamaño del Modal",
                    gallery: "Galería",
                    variants: "Variantes",
                    addons: "Complementos",
                    eventUsers: "Usuarios del Evento",
                    memo: "Memo",
                },
                productFilter: {
                    ids: "IDs",
                    filterType: "Tipo de Filtro",
                    types: {
                        category: "Categoría",
                        type: "Tipo de Producto",
                    }
                },


            },
            customHtml: {
                name: "HTML",
                content: "Contenido HTML",
                layouts: {
                    default: "Predeterminado",
                },
            },
            menu: {
                name: "Menú",
                layouts: {
                    responsive: "Adaptable",
                    horizontal: "Horizontal",
                    vertical: "Vertical",
                },
                fetchPages: "Añadir páginas del sitio web al menú",
                title: "Título",
                url: "URL",
                showCart: "Mostrar Carrito",
                showProfile: "Mostrar Perfil",
                optionType: "Tipo de Opción",
                optionTypes: {
                    icon: "Icono",
                    menu: "Menú",
                },
                shopId: "Sitio de Comercio Electrónico",
                shopIdHelperText: "El ID del sitio de la tienda a usar para este menú",
                customItems: "Elementos Personalizados",
            },
            video: {
                name: "Video",
                layouts: {
                    default: "Predeterminado",
                    videoOnly: "Solo Video",
                },
                placeholderImage: "Imagen de Marcador de Posición",
                title: "Título",
                subtitle: "Subtítulo",
                body: "Descripción",
                videoUrl: "URL del Video",
                autoplay: "Reproducción Automática",
                muted: "Silenciado",
                controls: "Mostrar Controles",
                ctas: "Llamadas a la Acción",
                button: "Botón",
            },
        },
    },

    breakpoints: {
        xs: "Extra Pequeño",
        sm: "Pequeño",
        md: "Mediano",
        lg: "Grande",
        xl: "Extra Grande",
    },

    file: {
        dropFileOrClick: "Suelta un archivo o haz clic aquí...",
        dropImageOrClick: "Suelta una imagen o haz clic aquí...",
        dropVideoOrClick: "Suelta un video o haz clic aquí...",
        dropAudioOrClick: "Suelta un archivo de audio o haz clic aquí...",
        dropDocumentOrClick: "Suelta un documento o haz clic aquí...",
        dropFile: "Suelta un archivo aquí...",
        dropImage: "Suelta una imagen aquí...",
        dropVideo: "Suelta un video aquí...",
        dropAudio: "Suelta un archivo de audio aquí...",
        dropDocument: "Suelta un documento aquí...",
        uploadFile: "Subir archivo",
        uploadFiles: "Subir archivos",
        upload: "Subir",
        browse: "Examinar",
        download: "Descargar",
        preview: "Vista Previa",
        file: "Archivo",
        files: "Archivos",
        size: "Tamaño",
        type: "Tipo",
        allowed: "Permitido",
        maxSize: "Tamaño máximo",
        photo: "Foto",
        photos: "Fotos",
        image: "Imagen",
        images: "Imágenes",
        video: "Video",
        videos: "Videos",
        audio: "Audio",
        audios: "Audios",
        document: "Documento",
        documents: "Documentos",
        pdf: "PDF",
        logo: "Logo",
    },

    icon: {
        iconStyle: "Estilo de Icono",
        styles: {
            filled: "Relleno",
            outlined: "Contorneado",
            rounded: "Redondeado",
            twoTone: "Dos Tonos",
            sharp: "Nítido",
        },
        icon: "Icono",
        icons: "Iconos",
        empty: "No se encontraron iconos",
        changeIcon: "Cambiar Icono",
        addIcon: "Añadir Icono",
        removeIcon: "Quitar Icono",
    },

    calendar: {
        calendar: "Calendario",
        today: "Hoy",
        month: "Mes",
        week: "Semana",
        day: "Día",
        year: "Año",
        schedule: "Horario",
        event: "Evento",
        events: "Eventos",
        newEvent: "Nuevo Evento",
        editEvent: "Editar Evento",
        title: "Título",
        startDate: "Fecha de Inicio",
        endDate: "Fecha de Fin",
        startTime: "Hora de Inicio",
        endTime: "Hora de Fin",
        allDay: "Todo el Día",
        repeat: "Repetir",
        to: "a",
        upTo: "Hasta",
        andUp: "y más",
        ageRequirement: "Requisito de Edad",
        age: "Edad",
        yearsOld: "años",
        requiresRegistration: "Requiere Inscripción",
        requiresMembership: "Requiere Membresía",
        noRegistration: "No se requiere inscripción",
        noMembership: "No se requiere membresía",
        signUp: "Inscribirse",
        eventFee: "Tarifa del Evento",
        startingFrom: "Desde",
        where: "Dónde",
        when: "Cuándo",
        noEvents: "No se encontraron eventos",
    },

    pos: {
        preferences: "Preferencias",
        item: "Artículo",
        price: "Precio",
        quantity: "Cantidad",
        add: "Añadir",
        addToCart: "Añadir al Carrito",
        saveCart: "Guardar Carrito",
        addons: "Complementos",
        variants: "Variantes",
        relatedProducts: "Productos Relacionados",
        priceStartingFrom: "Desde",
        total: "Total",
        tax: "Impuesto",
        subtotal: "Subtotal",
        priceAdjustments: "Ajustes de Precio",
        tip: "Propina",
        noTip: "Sin Propina",
        discount: "Descuento",
        selectDiscounts: "Seleccionar Descuentos",
        noDiscounts: "No hay descuentos disponibles",
        payment: "Pago",
        payments: "Pagos",
        paymentType: "Tipo de Pago",
        balance: "Saldo",
        paymentMethod: "Método de Pago",
        endingIn: "terminado en",
        shipping: "Envío",
        amount: "Monto",
        customAmount: "Monto Personalizado",
        tendered: "Entregado",
        change: "Cambio",
        memo: "Nota",
        processPartialPayment: "Procesar Pago Parcial",
        processPayment: "Procesar Pago",
        processDiscount: "Procesar Descuento",
        void: "Anular",
        refund: "Reembolso",
        return: "Devolución",
        closeOrder: "Cerrar Pedido",
        clearUser: "Limpiar Usuario",
        changeUser: "Cambiar Usuario",
        newUser: "Nuevo Usuario",
        selectUser: "Debes seleccionar un usuario primero",
        checkout: "Pagar",
        viewCart: "Ver Carrito",
        payNow: "Pagar Ahora",
        seachOrders: "Buscar Pedidos", //Mantener error tipográfico si es intencional en el original
        registerReports: "Informes de Caja",
        register: "Caja",
        openOrders: "Pedidos Abiertos",
        print: "Imprimir",
        printReceipt: "Imprimir Recibo",
        printKitchenTicket: "Imprimir Ticket de Cocina",
        printTicket: "Imprimir Ticket",
        printFullPage: "Imprimir Página Completa",
        forUser: "Para Usuario",
        plusTax: "Más Impuesto",
        whoWillRegisterForEvent: "¿Quién se registrará para el evento?",
        whoWillReceiveGiftCard: "¿Quién recibirá la tarjeta de regalo?",
        fullPrice: "Precio Completo",
        itemMemo: "Instrucciones Especiales",
        checkIn: "Registrar Entrada",
        lastCheckIn: "Última Entrada",
        editProfile: "Editar Perfil",
        order: {
            temporary: "Número temporal hasta que se añada un artículo",
            toolbar: {
                openOrders: "Pedidos Abiertos",
                printOrder: "Imprimir Pedido",
                closeOrder: "Cerrar Pedido",
            },
        },
        paymentMethods: {
            creditCard: "Tarjeta de Crédito",
            scanCard: "Escanear Tarjeta",
            cash: "Efectivo",
            check: "Cheque",
            giftCard: "Tarjeta de Regalo",
            managerDiscount: "Descuento del Gerente",
            token: "Ficha",
            walletBalance: "Saldo del Monedero",
        },
        warnings: {
            removeCashDiscount: "Procesar este pago eliminará el descuento por efectivo de {{amount}}",
            forUserAlert: "Los artículos en el carrito vinculados al usuario seleccionado serán eliminados.\n\n¿Deseas continuar?",
        },
        success: {
            thankYou: "¡Gracias!",
            continueShopping: "Continuar Comprando",
            description: "Tu pedido ha sido realizado correctamente.\nRecibirás una confirmación por correo electrónico en breve.",
        },
    },

    giftCard: {
        addRecipient: "Añadir Destinatario",
        recipient: "Destinatario de la Tarjeta de Regalo",
        recipientFullName: "Nombre del Destinatario",
        recipientEmail: "Correo Electrónico del Destinatario",
        deliveryDate: "Fecha de Entrega",
        message: "Mensaje",
        customAmount: "Monto Personalizado",
        code: "Código de la Tarjeta de Regalo",
        invalidCode: "Código de Tarjeta de Regalo no válido",
        balance: "Saldo",
    },

    check: {
        number: "Número de Cheque",
        name: "Nombre en el Cheque",
        bank: "Banco",
        routing: "Número de Ruta",
        account: "Número de Cuenta",
        accountType: "Tipo de Cuenta",
        types: {
            checking: "Corriente",
            savings: "Ahorros",
        },
        front: "Anverso del Cheque",
        back: "Reverso del Cheque",
    },

    creditCard: {
        cardNumber: "Número de Tarjeta",
        expiration: "Caducidad",
        cvv: "CVV",
        zip: "Código Postal",
        cardHolder: "Nombre en la Tarjeta",
        cardType: "Tipo de Tarjeta",
        types: {
            visa: "Visa",
            mastercard: "Mastercard",
            amex: "American Express",
            discover: "Discover",
        },
        gatewayTransaction: "ID de Transacción de la Pasarela",
        gatewayNotConfigured: "La pasarela de tarjeta de crédito no está configurada",
        invalidCard: "Tarjeta de crédito no válida",
        invalidField: "Valor no válido",
    },

    tag: {
        tag: "Etiqueta",
        tags: "Etiquetas",
        newTag: "Nueva Etiqueta",
        editTag: "Editar Etiqueta",
        search: "Buscar Etiquetas",
        empty: "No se encontraron etiquetas",
        name: "Nombre de la Etiqueta",
        description: "Descripción",
        deleteTitle: "Eliminar Etiqueta(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar la(s) etiqueta(s) seleccionada(s)?",
        saved: "Etiqueta guardada correctamente",
        deleted: "Etiqueta eliminada correctamente",
        updated: "Etiqueta actualizada correctamente",
        created: "Etiqueta creada correctamente",
        cloud: "Nube de Etiquetas",
    },

    category: {
        category: "Categoría",
        categories: "Categorías",
        newCategory: "Nueva Categoría",
        editCategory: "Editar Categoría",
        search: "Buscar Categorías",
        empty: "No se encontraron categorías",
        name: "Nombre de la Categoría",
        description: "Descripción",
        slug: "Slug",
        sortOrder: "Orden de Clasificación",
        parentId: "Categoría Principal",
        deleteTitle: "Eliminar Categorías",
        deleteMessage: "¿Estás seguro de que quieres eliminar las categorías seleccionadas?",
        saved: "Categoría guardada correctamente",
        deleted: "Categoría eliminada correctamente",
        updated: "Categoría actualizada correctamente",
        created: "Categoría creada correctamente",
    },

    error: {
        default: "Ocurrió un error. Por favor, inténtalo de nuevo más tarde",
        simple: "Ocurrió un error",
        required: "Este campo es obligatorio",
        conflict: "Hay un conflicto con los datos que ingresaste",
        codeError: "Hay un error de código",
        invalid: "El valor ingresado no es válido",
        tryAgain: "Intentar de nuevo",
        retry: "Reintentar",
        noConnection: "Sin conexión",
        connectionError: "Error de conexión",
        accessDenied: "Acceso denegado",
        notFound: "No se encontró el recurso",
        serverError: "El servidor encontró un error",
        unauthorized: "No estás autorizado para acceder a este recurso",
        forbidden: "No tienes permiso para acceder a este recurso",
        invalidFileType: "Tipo de archivo no válido",
        invalidFileSize: "Tamaño de archivo no válido",
        unknownError: "Error desconocido",
        invalidDateRange: "El rango de fechas no es válido",
        invalidTimeRange: "El rango de tiempo no es válido",
        invalidEmail: "Dirección de correo electrónico no válida",
        invalidPhone: "Número de teléfono no válido",
        invalidPassword: "Contraseña no válida",
        weakPassword: "La contraseña es demasiado débil",
        invalidUsername: "Nombre de usuario no válido",
        userNameTaken: "El nombre de usuario no está disponible",
        oops: "¡Ups!",
        backHome: "Volver al Inicio",
        exists: "El valor ingresado ya existe",
        viewErrorDetails: "Ver Detalles del Error",
        contactUs: "Ocurrió un error. Por favor, contáctanos para obtener ayuda",
    },

    success: {
        default: "Éxito",
        saved: "Datos guardados correctamente",
        deleted: "Eliminado correctamente",
        updated: "Datos actualizados correctamente",
        created: "Creado correctamente",
        sent: "Enviado correctamente",
    },

    aiChat: {
        you: "Tú",
        send: "Enviar",
        message: "Mensaje",
        scrollBottom: "Desplazarse hacia abajo",
        isTyping: "está escribiendo...",
    },

    widget: {
        addWidget: "Añadir Widget",
        settings: "Configuración del Widget",
        noSettings: "No hay configuraciones disponibles para este widget",
        gridConfig: "Configuración de la Cuadrícula",
        exitEditMode: "Salir del Modo de Edición",

        analytics: {
            title: "Analíticas",
            day: "Día",
            week: "Semana",
            month: "Mes",
            visitors: "Visitantes",
            pageViews: "Vistas de Página"
        },
        users: {
            title: "Usuarios",
            showActive: "Activos",
            showInactive: "Inactivos",
            roleDistribution: "Distribución de Roles"
        },
        sites: {
            title: "Sitios Web",
            status: "Estado",
            active: "Activos",
            inactive: "Inactivos"
        },
        inventory: {
            title: "Inventario",
            showLowStock: "Stock Bajo",
            showOutOfStock: "Sin Stock",
            items: "Artículos"
        },
        subscriptions: {
            title: "Suscripciones",
            showExpiring: "Mostrar Próximas a Vencer",
            active: "Activas",
            expired: "Vencidas",
            expiringSoon: "Próximas a Vencer"
        },
        tasks: {
            title: "Tareas",
            all: "Todas",
            highPriority: "Prioridad Alta",
            today: "Hoy",
            complete: "Completas",
            noTasks: "No se encontraron tareas",
            noTasksDescription: "No hay tareas que coincidan con tus filtros"
        },
        notifications: {
            title: "Notificaciones",
            noNotifications: "No hay notificaciones"
        },
        editWidget: "Editar Widget",
        enterEditMode: "Entrar en Modo de Edición",
        width: "Ancho",
        height: "Alto",
        size: "Tamaño",
        weather: {
            title: "Clima",
            weatherIn: "Clima en",
            location: "Ubicación",
            temperature: "Temperatura",
            units: "Unidades",
        },
        sales: {
            title: "Ventas",
            total: "Total",
            date: "Fecha",
            type: "Tipo",
            chartTypes: {
                line: "Línea",
                bar: "Barra",
                pie: "Circular",
                doughnut: "Dona",
                radar: "Radar",
                polar: "Polar",
                bubble: "Burbuja",
                scatter: "Dispersión",
                area: "Área",
            }
        },
        calendar: {
            title: "Calendario",
            type: "Tipo",
            calendarTypes: {
                schedule: "Horario",
                day: "Día",
                week: "Semana",
                month: "Mes",
                year: "Año",
            },
        },
    },

    media: {
        empty: "No se encontraron archivos para este tipo de multimedia",
        types: {
            image: "Imagen",
            video: "Video",
            audio: "Audio",
            document: "Documento",
            logo: "Logo",
            waiver: "Exención de Responsabilidad",
            theme: "Multimedia del Tema",
            other: "Otro"
        },
    },

    measurement: {
        width: "Ancho",
        height: "Altura",
        length: "Longitud",
        depth: "Profundidad",
        weight: "Peso",
        volume: "Volumen",
        area: "Área",
        radius: "Radio",
        diameter: "Diámetro",
        kg: "kg",
        mg: "mg",
        mm: "mm",
        cm: "cm",
        m: "m",
        km: "km",
        g: "g",
        gr: "gr",
        oz: "oz",
        ml: "ml",
        l: "l",
        lb: "lb",
        ft: "ft",
        yd: "yd",
        in: "in",
        mi: "mi",
        ac: "ac",
    },

    position: {
        top: "Arriba",
        bottom: "Abajo",
        left: "Izquierda",
        right: "Derecha",
        center: "Centro",
        start: "Inicio",
        end: "Fin",
        default: "Predeterminado",
        topCenter: "Arriba Centro",
        topLeft: "Arriba Izquierda",
        topRight: "Arriba Derecha",
        bottomCenter: "Abajo Centro",
        bottomLeft: "Abajo Izquierda",
        bottomRight: "Abajo Derecha",
        centerLeft: "Centro Izquierda",
        centerRight: "Centro Derecha",
    },

    fit: {
        cover: "Cubrir",
        contain: "Contener",
        fill: "Rellenar",
        scaleDown: "Reducir Escala",
        none: "Ninguno",
    },

    markdown: {
        type: "Escribe",
        toGet: "Para Obtener",
        useSample: "Usa un ejemplo",
        backticks: "con acentos graves",
        codeBlock: "3 acentos graves (y opcionalmente el lenguaje a resaltar)",
        h1: "Últimas Noticias Deportivas",
        h2: "Fútbol",
        h3: "Momentos Destacados del Partido",
        h4: "Mejores Goles",
        italic: "Regla del Fuera de Juego",
        bold: "Horario de Entrenamiento",
        link: "¡Haz clic aquí!",
        image: "Descripción de la imagen",
        list1: "Plátano",
        list2: "Fresa",
        list3: "Lácteos",
        list4: "Yogur",
        list5: "Leche",
        divider: "Divisor horizontal",
        code: "Código en línea",
        header1: "Artículo",
        header2: "Descripción",
        headerHelp: "Usa : al principio o al final de la celda para alinear el contenido",
        row1: "Fila 1",
        row2: "Fila 2",
        check1: "Desayunar",
        check2: "Hacer ejercicio"
    },

    billing: {
        billingAndInvoices: "Facturación y Facturas",
        myInvoices: "Mis Facturas",
        customerInvoices: "Facturas de Clientes",
        paymentMethods: "Métodos de Pago",
        downloadStatement: "Descargar Estado de Cuenta",
        createInvoice: "Crear Factura",
        addPaymentMethod: "Añadir Método de Pago",
        invoiceNumber: "Nº de Factura",
        date: "Fecha",
        amount: "Monto",
        status: "Estado",
        actions: "Acciones",
        view: "Ver",
        pay: "Pagar",
        edit: "Editar",
        customer: "Cliente",
    },

    customers: {
        customerManagement: "Gestión de Clientes",
        customers: "Clientes",
        addCustomer: "Añadir Cliente",
        customerName: "Nombre del Cliente",
        contactPerson: "Persona de Contacto",
        email: "Correo Electrónico",
        phone: "Teléfono",
        status: "Estado",
        actions: "Acciones",
        view: "Ver",
        edit: "Editar",
        customerDetails: "Detalles del Cliente",
        contactInformation: "Información de Contacto",
        billingInformation: "Información de Facturación",
        recentActivity: "Actividad Reciente",
        editCustomer: "Editar Cliente",
        createInvoice: "Crear Factura",
    },

    carriers: {
        carrierConfigurations: "Configuraciones de Transportistas",
        carriers: "Transportistas",
        addCarrier: "Añadir Transportista",
        rateTables: "Tablas de Tarifas",
        addRateTable: "Añadir Tabla de Tarifas",
        serviceOptions: "Opciones de Servicio",
        configureOptions: "Configurar Opciones",
        carrier: "Transportista",
        services: "Servicios",
        status: "Estado",
        lastUpdated: "Última Actualización",
        actions: "Acciones",
        configure: "Configurar",
        active: "Activo",
        inactive: "Inactivo",
        name: "Nombre",
        effectiveDate: "Fecha de Entrada en Vigor",
        expirationDate: "Fecha de Caducidad",
        globalSettings: "Configuración Global",
    },

    marketing: {
        marketingTools: "Herramientas de Marketing y Comunicación",
        emailCampaigns: "Campañas de Correo Electrónico",
        smsNotifications: "Notificaciones por SMS",
        templates: "Plantillas",
        analytics: "Analíticas",
        createCampaign: "Crear Campaña",
        configureSMS: "Configurar SMS",
        createTemplate: "Crear Plantilla",
        exportReport: "Exportar Informe",
        campaignName: "Nombre de la Campaña",
        status: "Estado",
        sentDate: "Fecha de Envío",
        performance: "Rendimiento",
        actions: "Acciones",
        view: "Ver",
        edit: "Editar",
        duplicate: "Duplicar",
        smsUsage: "Uso de SMS",
        cost: "Costo",
        messagesSent: "Mensajes enviados este mes",
        currentMonthCharges: "Cargos del mes actual",
        activeSMSCampaigns: "Campañas de SMS Activas",
        messageTemplates: "Plantillas de Mensajes",
        templateName: "Nombre de la Plantilla",
        type: "Tipo",
        lastUsed: "Último Uso",
        marketingAnalytics: "Analíticas de Marketing",
        performanceOverview: "Resumen de Rendimiento",
    },

    service: {
        services: "Servicios",
        service: "Servicio",
        serviceDetails: "Detalles del Servicio",
        newService: "Nuevo Servicio",
        editService: "Editar Servicio",
        search: "Buscar Servicios",
        name: "Nombre",
        description: "Descripción",
        shortDescription: "Descripción Corta",
        price: "Precio",
        duration: "Duración",
        status: "Estado",
        active: "Activo",
        inactive: "Inactivo",
        locations: "Ubicaciones",
        noLocations: "No hay ubicaciones seleccionadas",
        managers: "Gerentes",
        noManagers: "No hay gerentes seleccionados",
        bookingNotice: "Aviso de Reserva",
        minutes: "minutos",
        hours: "horas",
        days: "días",
        startDate: "Fecha de Inicio",
        endDate: "Fecha de Fin",
        availability: "Disponibilidad",
        id: "ID",
        deleteConfirmTitle: "Eliminar Servicio",
        deleteConfirmMessage: "¿Estás seguro de que quieres eliminar el servicio '{name}'?",
        deleteSuccess: "Servicio eliminado correctamente",
        success: "Servicio guardado correctamente",

        wizard: {
            name: {
                title: "Nombre y Descripción",
                subtitle: "Información básica del servicio",
                description: "Ingresa la información básica sobre el servicio",
                label: "Nombre del Servicio"
            },
            shortDescription: {
                label: "Descripción Corta"
            },
            description: {
                label: "Descripción Completa"
            },
            availability: {
                title: "Disponibilidad",
                subtitle: "Período de disponibilidad del servicio",
                description: "Establece el período de tiempo en que este servicio está disponible",
                hasStartDate: "Establecer una fecha de inicio",
                startDate: "Fecha de Inicio",
                hasEndDate: "Establecer una fecha de fin",
                endDate: "Fecha de Fin"
            },
            increments: {
                title: "Incrementos de Tiempo",
                subtitle: "Configuración del tiempo de reserva del servicio",
                description: "Configura cómo funcionan las franjas horarias para este servicio",
                blockMinutes: "Minutos de Bloqueo",
                minTimeslots: "Franjas Horarias Mínimas",
                maxTimeslots: "Franjas Horarias Máximas",
                timeslotsForToken: "Franjas Horarias por Ficha",
                minBookingNotice: "Aviso Mínimo de Reserva (minutos)"
            },
            location: {
                title: "Ubicaciones",
                subtitle: "Dónde está disponible el servicio",
                description: "Selecciona las ubicaciones donde se ofrece este servicio",
                locations: "Ubicaciones del Servicio"
            },
            manager: {
                title: "Gerentes",
                subtitle: "Gerentes del servicio",
                description: "Selecciona el personal que gestiona este servicio",
                managers: "Gerentes del Servicio"
            },
            payment: {
                title: "Opciones de Pago",
                subtitle: "Precios y fichas del servicio",
                description: "Configura las opciones de pago para este servicio",
                defaultPrice: "Precio Predeterminado",
                defaultTokenName: "Nombre de la Ficha"
            },
            cancellation: {
                title: "Política de Cancelación",
                subtitle: "Reglas de cancelación del servicio",
                description: "Establece la política de cancelación para este servicio",
                policy: "Política de Cancelación",
                hours: "Aviso de Cancelación (horas)"
            },
            summary: {
                title: "Resumen",
                subtitle: "Revisar detalles del servicio",
                description: "Revisa todos los detalles del servicio antes de guardar",
                statusLabel: "Estado del Servicio",
                active: "Activo",
                inactive: "Inactivo"
            }
        }
    },

    services: {
        serviceStatus: "Estado del Servicio",
        systemStatus: "Estado del Sistema",
        activeIncidents: "Incidentes Activos",
        allSystemsOperational: "Todos los Sistemas Operativos",
        operationalServices: "Servicios Operativos",
        degradedServices: "Servicios Degradados",
        servicesWithIncidents: "Servicios con Incidentes",
        investigating: "Investigando",
        monitoring: "Monitoreando",
        operational: "Operacional",
        degradedPerformance: "Rendimiento Degradado",
        incident: "Incidente",
        service: "Servicio",
        uptime: "Tiempo de Actividad",
        lastIncident: "Último Incidente",
        updates: "Actualizaciones",
        started: "Iniciado",
    },

    product: {
        product: "Producto",
        products: "Productos",
        productDetails: "Detalles del Producto",
        newProduct: "Nuevo Producto",
        editProduct: "Editar Producto",
        search: "Buscar Productos",
        name: "Nombre",
        description: "Descripción",
        shortDescription: "Descripción Corta",
        longDescription: "Descripción Larga",
        labelDescription: "Descripción de Etiqueta",
        price: "Precio",
        retailPrice: "Precio de Venta",
        cost: "Costo",
        sku: "SKU",
        altSku: "SKU Alternativo",
        changeSku: "Cambiar SKU",
        addSku: "Añadir SKU",
        changeSkuWarning: "Cambiar un SKU es una acción significativa. Esto solo debe hacerse cuando sea absolutamente necesario.",
        confirmChangeSku: "Entiendo y confirmo este cambio",
        barcode: "Código de Barras",
        category: "Categoría",
        productType: "Tipo de Producto",
        variants: "Variantes",
        addOns: "Complementos",
        notFound: "Producto no encontrado",
        brand: "Marca",
        model: "Modelo",
        color: "Color",
        size: "Talla",
        weight: "Peso",
        dimensions: "Dimensiones",
        images: "Imágenes",
        selectImages: "Seleccionar Imágenes",
        deleteTitle: "Eliminar Producto(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los producto(s) seleccionado(s)?",
        success: "Producto guardado correctamente",
        deleted: "Producto eliminado correctamente",
        slotCount: "Recuento de Ranuras",
        slotEquivalent: "Equivalente de Ranura",
        skuList: "Lista de SKU",
        inventory: "Inventario",
        stockQuantity: "Cantidad en Stock",
        reservedQuantity: "Cantidad Reservada",
        availableQuantity: "Cantidad Disponible",
        lowStockThreshold: "Umbral de Stock Bajo",

        toolbar: {
            basic: "Información del Producto",
            descriptions: "Descripciones detalladas",
            properties: "Propiedades",
            handling: "Manejo",
        },

        productTypes: {
            title: "Tipos de Producto",
            all: "Todos los tipos de producto",
            subscription: "Suscripción",
            physical: "Físico",
            digital: "Digital",
            service: "Servicio",
            event: "Evento",
            bundle: "Paquete",
            rental: "Alquiler",
            foodDrink: "Comida y Bebida",
            token: "Token",
            cancellationFee: "Tarifa de Cancelación",
            featureProduct	: "Producto Destacado",
            giftCard: "Tarjeta de Regalo",
            wine: "Vino",
            kit: "Kit",
            merchandise: "Artículos",
            collateral: "Colateral",
            food: "Comida",
            speciality: "Especialidad",
        },

        productCategories: {
            title: "Categorías",
            all: "Todas las categorías",
        },

        wine: {
            type: "Tipo",
            vintage: "Añada",
            origin: "Origen",
            vineyard: "Viñedo",
            cola: "COLA #",
            slotCount: "Recuento de Ranuras",
            slotEquivalent: "Equivalente de Ranura",
            alcoholByVolume: "Alcohol por Volumen",
            varietal: "Variedad",
            bottleSize: "Tamaño de Botella",
            registrationNumber: "Número de Registro",
            printOnPacking: "Imprimir en Embalaje",
            types: {
                still: "Seco",
                nonAlcoholic: "No Alcohólico",
                sparkling: "Espumante",
                fortified: "Fortificado",
            },
        },

        merchandise: {
            shipAlone: "Enviar Solo",
        },
        collateral: {
            type: "Tipo",
        },
        food: {
            type: "Tipo",
            origin: "Origen",
            perishable: "Perecedero",
            refrigeration: "Refrigeración",
        },
        speciality: {
            type: "Tipo",
        },
        kit: {
            type: "Tipo",
        },

        handling: {
            title: "Parámetros de Manipulación",
            container: "Contenedor",
            backorderWarning: "Advertencia de Pedido Pendiente",
            serviceFee: "Tarifa de Servicio",
            amountPerContainer: "Cantidad por Contenedor",
            handlingFee: "Tarifa de Manipulación",
            fulfillmentFee: "Tarifa de Cumplimiento",
        },
    },

    merchant: {
        merchant: "Comerciante",
        merchants: "Comerciantes",
        merchantDetails: "Detalles del Comerciante",
        newMerchant: "Nuevo Comerciante",
        editMerchant: "Editar Comerciante",
        createMerchant: "Crear Comerciante",
        updateMerchant: "Actualizar Comerciante",
        search: "Buscar Comerciantes",
        code: "Código",
        codeHelperText: "Código de comerciante de 5 caracteres",
        name: "Nombre",
        email: "Correo Electrónico",
        phone: "Teléfono",
        contact: "Contacto",
        type: "Tipo",
        addressLine1: "Dirección Línea 1",
        addressLine2: "Dirección Línea 2",
        location: "Ubicación",
        city: "Ciudad",
        state: "Estado",
        zipcode: "Código Postal",
        country: "País",
        notes: "Notas",
        primary: "Hacer Principal",
        deleteTitle: "Eliminar Comerciante(s)",
        deleteMessage: "¿Estás seguro de que quieres eliminar el/los comerciante(s) seleccionado(s)?",
        success: "Comerciante guardado correctamente",
        updated: "Comerciante actualizado correctamente",
        saved: "Comerciante guardado correctamente",
        deleted: "Comerciante eliminado correctamente",
        toolbar: {
            basic: "Información Básica",
        },
        types: {
            producer: "Productor",
            retailer: "Minorista",
            marketer: "Comercializador",
            club: "Club",
            ecommerce: "Comercio Electrónico",
        },
        tabs: {
            info: "Información",
            setup: "Configuración",
            contacts: "Contactos",
            licenses: "Licencias",
            billing: "Facturación",
            brands: "Marcas",
            shipping: "Envío",
            packaging: "Empaque",
            email: "Correo",
        },
        // License-related translations
        addLicense: "Agregar Licencia",
        editLicense: "Editar Licencia",
        licenseState: "Estado de Licencia",
        licenseBrand: "Marca de Licencia",
        licenseName: "Número de Licencia",
        licenseExpirationDate: "Fecha de Expiración",
        allowShipperManualUpload: "Permitir Carga Manual del Transportista",
        enableWeightBasedQA: "Habilitar QA Basado en Peso",
        renameOrderOnCancel: "Renombrar Pedido al Cancelar",
        rejectUnknownSKUs: "Rechazar SKUs Desconocidos",
        orderFileFormat: "Formato de Archivo de Pedido",
        // Contact-related translations
        addContact: "Agregar Contacto",
        editContact: "Editar Contacto",
        contactFirstName: "Nombre",
        contactLastName: "Apellido",
        contactTitle: "Título",
        contactDepartment: "Departamento",
        contactEmail: "Correo Electrónico",
        contactPhone: "Teléfono",
        contactMobile: "Móvil",
        contactFax: "Fax",
        contactRole: "Rol",
        contactPreferredMethod: "Método de Contacto Preferido",
        contactNotes: "Notas",
        contactRoles: {
            primary: "Primario",
            billing: "Facturación",
            shipping: "Envío",
            technical: "Técnico",
            sales: "Ventas",
            support: "Soporte",
        },
        contactMethods: {
            email: "Correo Electrónico",
            phone: "Teléfono",
            mobile: "Móvil",
            fax: "Fax",
        },
        contactType: "Tipo de Contacto",
        contactTypes: {
            billing: "Facturación",
            inventory: "Inventario",
            club: "Club",
            business: "Negocio",
            other: "Otro",
        },
        billingMethod: "Método de Facturación",
        billingFrequency: "Frecuencia de Facturación",        
        sendTo: "Enviar A",
        groupBy: "Agrupar Por",
        creditLimit: "Límite de Crédito",
        billingMethods: {
            dropInMail: "Correo de Entrega",
            email: "Correo Electrónico",
            fax: "Fax",
            print: "Imprimir",
            webPortal: "Portal Web",
        },
        billingFrequencies: {
            weekly: "Semanal",
            monthly: "Mensual",
            biMonthly: "Bimensual",
            never: "Nunca",
        },
        billingTerms: {
            title: "Términos de Facturación",
            net7: "NETO 7",
            net14: "NETO 14",
            net15: "NETO 15",
            net30: "NETO 30",
            net60: "NETO 60",
            net90: "NETO 90",
        },
        sendToOptions: {
            business: "Negocio",
            billing: "Facturación",
            other: "Otro",
        },
        groupByOptions: {
            manifestLocation: "Ubicación del Manifiesto",
            brandName: "Nombre de Marca",
            fulfillmentCenter: "Centro de Cumplimiento",
            orderType: "Tipo de Pedido",
        },
        addBrand: "Añadir Marca",
        editBrand: "Editar Marca",
        brandName: "Nombre de Marca",
        brandCode: "Código de Marca",
        brandNotes: "Notas de Marca",
        brandPhone: "Teléfono de Marca",
        brandEmail: "Nombre para Correos Marcados",
        brandEmailName: "Correo para Correos Marcados",
    },

    emailTemplates: {
        title: "Plantillas",
        configuration: "Configuración",
        addEmailTemplate: "Añadir Plantilla",
        editEmailTemplate: "Editar Plantilla",
        noEmailTemplates: "No se encontraron plantillas de correo electrónico",
        orderType: "Tipo de Pedido",
        emailSubject: "Asunto del Correo Electrónico",
        bcc: "BCC",
        template: "Plantilla",
        fromEmailAddress: "DesdeCorreo Electrónico",
        fromName: "Desde Nombre",
        ccRecipient: "Recipiente CC",
        bccRecipient: "Recipiente BCC",
        templateName: "Nombre de Plantilla",
        templateType: "Tipo de Plantilla",
        status: "Estado",
        smtpConfiguration: "Configuración SMTP",
        smtpServer: "Servidor SMTP",
        smtpPort: "Puerto SMTP",
        smtpUsername: "Nombre de usuario SMTP",
        smtpPassword: "Contraseña SMTP",
        emailTemplateTypes: {
            processed: "Procesado",
            shipped: "Enviado",
            outForDelivery: "En camino",
            deliveryAttempt: "Intento de Entrega",
            exception: "Excepción",
            returned: "Devuelto",
            delivered: "Entregado",
        },        
    },    

    geographic: {
        country: "País",
        state: "Estado",
        city: "Ciudad",
        county: "Condado",
        area: "Área",
        neighborhood: "Vecindario",
        latitude: "Latitud",
        longitude: "Longitud",
    },
};