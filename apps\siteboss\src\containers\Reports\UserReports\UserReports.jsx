import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Paper } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

export const UserReports = () => {
    const { t } = useOutletContext();

    return (
        <Container>
            <Title
                title={t('reports:userReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:userReports')}
                ]}
            />

            <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h5" gutterBottom>
                    User Reports Content
                </Typography>
                <Typography variant="body1">
                    This page will contain user reports including:
                </Typography>
                <ul>
                    <li>User Activity</li>
                    <li>User Demographics</li>
                    <li>User Acquisition</li>
                    <li>User Retention</li>
                    <li>User Engagement</li>
                </ul>
            </Paper>
        </Container>
    );
};

export default UserReports;
