import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { But<PERSON>, Stack, Table as MuiTable, TableBody, TableCell, TableHead, TableRow, Typography, Divider, Tooltip } from '@mui/material';

import { createCurrencyFormatter, toCamelCase, formatDate, capitalize, formatRecurringItem } from '../../../../../utils';
import { NumberField } from '../../../../../components';

export const List = ({items = [], onRowClick, onDelete, onEdit, onDuplicate, slotProps, isBuilder, ...props}) => {
    const { t } = useTranslation();
    //const isMobile = useMediaQuery(theme => theme.breakpoints.down('md'));
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);

    const handleQuantityChange = useCallback(item => e => {
        e.preventDefault();
        if (onEdit) {
            const newValue = {...item, qty: +e.target.value || 0};
            onEdit(item.id, newValue)(e);
        }
    }, [onEdit]);

    const columns = useMemo(() => [
        { id: 'name', label: 'item', align: 'left', valueFormatter: (value, row) => {
            const mediaItem = row?.variantMedia?.[0] || row?.productMedia?.[0] || row?.eventMedia?.[0];
            let _value = [<Typography variant="bold" component="div">{value}</Typography>];
            let recurringInfo = formatRecurringItem(row?.metadata);
            
            if (row?.metadata?.variant_name) { // variant info
                _value.push(<Typography variant="caption">{row.metadata.variant_name}</Typography>);
            }
            if (recurringInfo.length > 0) {  // recurring info
                _value.push(
                    <Typography variant="caption">
                        +{+row?.metadata?.price > 0 && currencyFormatter.format(row.metadata.price, currency)}&nbsp;
                        {capitalize(recurringInfo?.map(info => t(info))?.join(' ').toLocaleLowerCase())}
                    </Typography>);
            }
            if (row?.addons?.length > 0) { // addons
                _value.push(
                    <>
                        <Typography variant="caption" component="div" sx={{mt: 1}}>{t("pos:addons")}</Typography>
                        <Typography variant="caption" component="ul">
                            {row.addons.map(a => <li key={a.id}>{a.name || a?.metadata?.name}</li>)}
                        </Typography>
                    </>
                );                
            }
            if (row?.forUserIds?.length > 0) { // for users
                _value.push(
                    <>
                        <Typography variant="caption" component="div" sx={{mt: 1}}>{t("pos:forUser")}</Typography>
                        <Typography variant="caption" component="ul">
                            {row.forUserIds.map(a => (
                                <li key={a.id}>
                                    {a.firstName}
                                    {row?.customFields?.filter?.(b => b.userId === a.id).map(cf => (
                                        <Stack direction="column" spacing={0} useFlexGap key={`cf-${a.id}`} sx={{pl: 2}}>
                                            {Object.entries(cf)
                                                .filter(([key]) => key.startsWith('cf_'))
                                                .map(([key, value]) => {
                                                    const definition = row?.metadata?.custom_fields?.find(def => def.name === key);
                                                    return (
                                                        <Stack direction="row" spacing={0.5} useFlexGap key={key}>
                                                            <Typography variant="caption" component="span">
                                                                {definition?.placeholder_text ? `${definition.placeholder_text}: ` : ''}
                                                            </Typography>
                                                            <Typography variant="caption" component="span">
                                                                {value}
                                                            </Typography>
                                                        </Stack>
                                                    );
                                                })}
                                        </Stack>
                                    ))}
                                </li>
                            ))}
                        </Typography>
                    </>
                );
            }
            if (row?.giftCardRecipient?.full_name) { // gift card recipient
                _value.push(
                    <>
                        <Typography variant="caption" component="div" sx={{mt: 1}}>{t("giftCard:recipient")}</Typography>
                        <Typography variant="caption" component="div" sx={{pl: 4}}>
                            {row.giftCardRecipient.full_name}<br/>
                            {row.giftCardRecipient?.email && 
                                <>
                                    {row.giftCardRecipient?.email}
                                    <br/>
                                </>
                            }
                            {row.giftCardRecipient?.delivery_date && 
                                <>
                                    {t("giftCard:deliveryDate")}: {formatDate(row.giftCardRecipient?.delivery_date, language)}
                                    <br/>
                                </>
                            }
                            {row.giftCardRecipient?.message && <Typography variant="caption" component="i">{row.giftCardRecipient?.message}</Typography>}
                        </Typography>
                    </>
                );
            }
            // memo
            if (row?.memo) _value.push(<Typography variant="caption" component="i" sx={{mt: 1}}><q>{row.memo}</q></Typography>);
            return (
                <Stack direction="row" useFlexGap flexWrap="wrap" spacing={3} sx={{cursor: 'pointer'}} onClick={isBuilder ? undefined : onRowClick(row.id)}>
                    {mediaItem && <img src={mediaItem?.path} alt={row?.productName || ""} style={{width: 180, height: 180, objectFit: "cover"}}/>}
                    <Stack direction="column" useFlexGap flexWrap="wrap" flexGrow={1} sx={{minHeight: 120}}>
                        <div style={{flexGrow: 1}}>{_value.map((v, i) => <div key={i}>{v}</div>)}</div>
                        <Divider component="div" sx={{my: 1}}/>
                        <Stack direction="row" spacing={1} useFlexGap alignItems="center" sx={{cursor: 'default'}} onClick={ e=> {
                            e.preventDefault();
                            e.stopPropagation();
                        }}>
                            {!row?.eventId &&
                                <>
                                    <NumberField 
                                        value={+row?.qty || 1} 
                                        size="small"
                                        margin="none"  
                                        disabled={isBuilder} 
                                        showControls
                                        min={0}
                                        max={99}
                                        sx={{width: 100}}
                                        onChange={handleQuantityChange(row)}
                                        onClick={e => { 
                                            e.stopPropagation();
                                            e.preventDefault(); 
                                        }}
                                    />
                                    <Divider orientation="vertical" sx={{height: 16}} />
                                </>
                            }
                            <Button size="xs" variant="text" color="primary" disabled={isBuilder} onClick={onDuplicate(row.id)}>{t('general:duplicate')}</Button>
                            <Divider orientation="vertical" sx={{height: 16}} />
                            <Button size="xs" variant="text" color="primary" disabled={isBuilder} onClick={onDelete(row.id)}>{t('general:remove')}</Button>
                        </Stack>
                    </Stack>
                </Stack>
            );
        }},
        { id: 'price', label: 'price', width: 110, align: 'right', valueFormatter: (value, row) => {
            let _value = +value;
            if (+row?.metadata?.activation_fee > 0) _value = +row.metadata.activation_fee;
            row?.addons?.forEach(a => _value += +a.price);
            _value = _value * (+row?.qty || 1);
            return (
                <Typography variant="subtitle1">
                    <Typography variant="h6" component="span">
                        {currencyFormatter.format(_value, currency)}
                    </Typography>
                </Typography>
            );
        }},
    ], [t, currencyFormatter, currency, language]);



    return (
        <MuiTable aria-label={t("general:cart")} {...slotProps}>
            <TableHead>
                <TableRow>
                    {columns.map((column) => (
                        <TableCell
                            key={column.id}
                            align={column.align}
                            sx={{ flex: column.flex, bgcolor: 'transparent' }}
                        >
                            {t(`order:${toCamelCase(column.label)}`, column.label)}
                        </TableCell>
                    ))}
                </TableRow>
            </TableHead>            
            <TableBody>
                {items.map(row => (
                    <TableRow hover={!isBuilder} role="checkbox" tabIndex={-1} key={row.id} disabled={isBuilder}>
                        {columns.map(column => {
                            const value = row?.metadata?.[column.id] || row?.[column.id];
                            return (
                                <TableCell key={column.id} align={column.align} style={{ width: column.width, flex: column.flex, verticalAlign: 'top' }}>
                                    {column.valueFormatter ? column.valueFormatter(value, row) : value}
                                </TableCell>
                            );
                        })}
                    </TableRow>
                ))}
            </TableBody>
        </MuiTable>
    );
}
