export const properties = [
    {
        name: 'filterType',
        label: 'builder:component.pos.productFilter.filterType',
        component: "Select",
        options: [
            { id: 'category', slug: 'builder:component.pos.productFilter.types.category' },
            { id: 'type', slug: 'builder:component.pos.productFilter.types.type' },
        ],
        value: 'category',
        size: "small",
        margin: "normal",
    },  
    {
        name: 'categoryIds',
        label: 'builder:component.pos.productFilter.ids',
        component: "GeneralItemSelector",
        value: [],
        type: "autocomplete",
        selectedColor: "secondary",
        fetchParams: {
            enableCache: true, params: {endpoint: `/category`, method: 'POST', data: {max_records: 999}},
        },
        valueFormatter: value => {
            if (!value?.categories?.length) return null;
            return value.categories.filter(a => a.parent_id === null).map(v => ({id: v?.id, name: v?.name}));
        },
        size: "small",
        margin: "normal",
        condition: { field: 'filterType', value: 'category' },
    },
    {
        name: 'productTypeIds',
        label: 'builder:component.pos.productFilter.ids',
        component: "GeneralItemSelector",
        value: [],
        type: "autocomplete",
        selectedColor: "secondary",
        fetchParams: {
            enableCache: true, params: {endpoint: `/product/type`, method: 'GET'},
        },
        size: "small",
        margin: "normal",
        condition: { field: 'filterType', value: 'type' },
    },  
];