# OWL - Order & Warehouse Management

This application serves as the customer-facing portal for Order and Warehouse Management clients. It allows clients to:

- View and manage their billing and payments
- Bill and manage their own customers (e.g., shippers or 3PL clients)
- View and configure their services, including:
  - Carrier shipping rates and options
  - Marketing costs (email, SMS, etc.)
  - Status of operational services

## Getting Started

```bash
# Start the development server
npm run dev --workspace=@siteboss-frontend/owl

# Build for production
npm run build --workspace=@siteboss-frontend/owl

# Run Storybook
npm run storybook --workspace=@siteboss-frontend/owl
```

## Application Structure

The application follows the standard structure of other apps in the monorepo:

```
apps/owl/
├── src/
│   ├── api/           # API integration
│   ├── assets/        # Static assets
│   ├── components/    # Reusable components
│   ├── containers/    # Page containers
│   ├── store/         # Redux store
│   ├── App.jsx        # Main application component
│   ├── main.jsx       # Entry point
│   └── Routes.jsx     # Application routes
```

## Features

- Dashboard with key metrics and status information
- Billing & Invoices management
- Customer Management
- Carrier Configurations
- Marketing & Communication Tools
- Service Status/Monitoring
