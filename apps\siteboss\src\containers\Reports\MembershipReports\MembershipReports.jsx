import React from 'react';
import { useOutletContext } from 'react-router-dom';
import { Container, Typography, Paper } from '@mui/material';
import { Title } from '@siteboss-frontend/shared/components';

export const MembershipReports = () => {
    const { t } = useOutletContext();

    return (
        <Container>
            <Title
                title={t('reports:membershipReports')}
                breadcrumbs={[
                    {title: t('dashboard:dashboard'), to: '/'},
                    {title: t('reports:reports'), to: '/reports'},
                    {title: t('reports:membershipReports')}
                ]}
            />

            <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h5" gutterBottom>
                    Membership & Subscription Reports Content
                </Typography>
                <Typography variant="body1">
                    This page will contain membership and subscription reports including:
                </Typography>
                <ul>
                    <li>Active Memberships</li>
                    <li>Membership Revenue</li>
                    <li>Subscription Trends</li>
                    <li>Renewal Rates</li>
                    <li>Membership Demographics</li>
                </ul>
            </Paper>
        </Container>
    );
};

export default MembershipReports;
