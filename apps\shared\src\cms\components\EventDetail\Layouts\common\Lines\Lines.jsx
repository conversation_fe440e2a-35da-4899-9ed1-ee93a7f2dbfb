import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { isSameDay, format } from 'date-fns';
import { InfoOutlined as InfoIcon, TodayOutlined as TodayIcon, MonetizationOnOutlined as PriceIcon, SentimentSatisfiedAltOutlined as AgeIcon, LocationOnOutlined as LocationIcon }  from '@mui/icons-material';

import { createCurrencyFormatter, formatDate, formatDateTime } from '../../../../../../utils';
import Line from '../../../../common/pos/Line'

export const Lines = ({ 
    event,
    slotProps
}) => {
    const { t } = useTranslation();
    const language = useSelector(state => state.language);
    const currency = useSelector(state => state.company.currency);
    const currencyFormatter = createCurrencyFormatter(language, currency);
    
    const sameDay = isSameDay(event?.startDate, event?.endDate);

    const lines = useMemo(() => [
        {
            caption: t(sameDay ? 'general:date' : 'calendar:startDate'),
            value: sameDay ? `${formatDate(event?.startDate, language.code, "extended")}<br/>${format(event.startDate, "h:mmaa")} - ${format(event.endDate, "h:mmaa")}` :  formatDateTime(event.startDate, language.code),
            icon: <TodayIcon />,
        },
        {
            caption: t(`calendar:endDate`),
            value: sameDay ? null : formatDateTime(event.endDate, language.code),
            icon: <TodayIcon />,
        },
        {
            caption: t(`general:location`),
            value: event.metadata.location_name || null,
            icon: <LocationIcon />,
        },
        {
            caption: t(`calendar:ageRequirement`),
            value: (event.metadata.min_age || event.metadata.max_age) 
                ? (event.metadata.min_age > 0 && !event.metadata.max_age ? `${event.metadata.min_age} ${t('calendar:yearsOld')} ${t('calendar:andUp')}` : '') +
                    (!event.metadata.min_age && event.metadata.max_age > 0 ? `${t('calendar:upTo')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '') + 
                    (event.metadata.min_age > 0 && event.metadata.max_age > 0 ? `${event.metadata.min_age} ${t('calendar:to')} ${event.metadata.max_age} ${t('calendar:yearsOld')}` : '')
                : null,
            icon: <AgeIcon />,
        },
        {
            caption: t(event.metadata?.variants.length > 1 ? 'pos:priceStartingFrom' : `calendar:eventFee`),
            value: event.metadata.default_variant_price > 0 ? currencyFormatter.format(event.metadata.default_variant_price) : null,
            icon: <PriceIcon />,
            //textVariant: 'subtitle1',
        },
        {
            caption: t(`general:additionalInfo`),
            value: !event.metadata?.requires_registration && !event.metadata?.children?.length ? t(`calendar:noRegistration`) : null,
            icon: <InfoIcon />,
        },
    ], [event, language, currencyFormatter, sameDay]);

    return (
        <>
            {lines.filter(a=>a.value)?.map((line, i) => (
                <Line key={`event-line-${i}`} {...line} caption={line.caption} text={line.value} icon={line.icon} slotProps={slotProps} />
            ))}        
        </>
    );
}