import React, { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import { Stack, Typography, IconButton, Tooltip, Box, LinearProgress } from '@mui/material';
import * as MuiIcons from '@mui/icons-material';

const ICONS_PER_BATCH = 150;

export const IconList = ({
    iconStyle = 'outlined',
    searchText = '',
    onIconSelection,
    size = 'large'
}) => {
    const [displayedCount, setDisplayedCount] = useState(ICONS_PER_BATCH);
    const [loading, setLoading] = useState(false);
    const loaderRef = useRef(null);

    const filteredIcons = useMemo(() => {
        return Object.keys(MuiIcons).filter(name => {
            const matchesStyle = 
                (iconStyle === 'outlined' && name.endsWith('Outlined')) ||
                (iconStyle === 'filled' && !name.includes('Outlined') && !name.includes('Rounded') && !name.includes('Sharp') && !name.includes('TwoTone')) ||
                (iconStyle === 'rounded' && name.endsWith('Rounded')) ||
                (iconStyle === 'sharp' && name.endsWith('Sharp')) ||
                (iconStyle === 'twoTone' && name.endsWith('TwoTone'));

            const matchesSearch = searchText 
                ? name.toLowerCase().includes(searchText.toLowerCase())
                : true;

            return matchesStyle && matchesSearch;
        });
    }, [iconStyle, searchText]);

    const visibleIcons = useMemo(() => {
        return filteredIcons.slice(0, displayedCount);
    }, [filteredIcons, displayedCount]);

    const handleClick = useCallback(iconName => e => {
        e.preventDefault();
        if (onIconSelection) onIconSelection(iconName);
    }, [onIconSelection]);

    useEffect(() => {
        setDisplayedCount(ICONS_PER_BATCH);
    }, [searchText, iconStyle]);

    useEffect(() => {
        if (displayedCount >= filteredIcons.length) return;

        const handleObserver = (entities) => {
            const target = entities[0];
            if (target.isIntersecting && !loading) {
                setLoading(true);
                setTimeout(() => {
                    setDisplayedCount(prev => Math.min(prev + ICONS_PER_BATCH, filteredIcons.length));
                    setLoading(false);
                }, 100);
            }
        };

        const observer = new IntersectionObserver(handleObserver, {
            root: null,
            rootMargin: '20px',
            threshold: 1.0
        });

        if (loaderRef.current) {
            observer.observe(loaderRef.current);
        }

        return () => {
            if (loaderRef.current) {
                observer.unobserve(loaderRef.current);
                observer.disconnect();
            }
        };
    }, [displayedCount, filteredIcons.length, loading]);

    return (
        <Box sx={{ height: '100%', overflow: 'hidden', textAlign: 'center' }}>
            <Stack 
                direction="row" 
                flexWrap="wrap" 
                useFlexGap
                spacing={2}
                sx={{ 
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                }}
            >
                {visibleIcons.map(iconName => {
                    const IconComponent = MuiIcons[iconName];
                    return (
                        <Tooltip key={iconName} title={iconName.replace(/([A-Z])/g, ' $1').trim()}>
                            <Stack direction="column" alignItems="center" textAlign="center" useFlexGap spacing={0} sx={{width: 80}}>
                                <IconButton 
                                    size={size} 
                                    onClick={handleClick(iconName)}
                                    sx={{ p: 1 }}
                                >
                                    <IconComponent fontSize={size} />
                                </IconButton>
                                <Typography variant="caption" color="text.secondary" sx={{width: '100%', fontSize: '0.65rem'}} noWrap>{iconName}</Typography>
                            </Stack>
                        </Tooltip>
                    );
                })}
            </Stack>
            {loading && <LinearProgress color="secondary" sx={{width: '100%', height: '2px'}} />}
            <div
                ref={loaderRef}
                style={{ 
                    height: '20px',
                    width: '100%',
                    textAlign: 'center',
                }}
            />     
        </Box>
    );
};
